import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Button,
  Table,
  Tabs,
  Typography,
  Space,
  Tag,
  Statistic,
  Progress,
  Alert,
  Spin,
  Form,
  InputNumber,
  Switch,
  Slider,
  Descriptions,
} from 'antd'
import {
  Line<PERSON>hartOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  Play<PERSON>ircleOutlined,
  SettingOutlined,
  TrophyOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { Line, Column, Dual } from '@ant-design/plots'
import QuantitativeStrategy, { Strategy, BacktestResult, PortfolioOptimization } from '@/utils/quantitativeStrategy'
import { PriceData } from '@/utils/advancedIndicators'

const { Title, Text } = Typography
const { Option } = Select

interface BacktestConfig {
  strategy: 'momentum' | 'mean_reversion' | 'factor' | 'ml'
  lookbackPeriod: number
  holdingPeriod: number
  threshold: number
  initialCapital: number
  maxPositions: number
  rebalanceFrequency: 'daily' | 'weekly' | 'monthly'
  transactionCost: number
  slippage: number
}

const QuantitativeBacktest: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null)
  const [portfolioOptimization, setPortfolioOptimization] = useState<PortfolioOptimization | null>(null)
  const [activeTab, setActiveTab] = useState('config')
  const [form] = Form.useForm()

  const [config, setConfig] = useState<BacktestConfig>({
    strategy: 'momentum',
    lookbackPeriod: 20,
    holdingPeriod: 5,
    threshold: 2.0,
    initialCapital: 100000,
    maxPositions: 10,
    rebalanceFrequency: 'weekly',
    transactionCost: 0.001,
    slippage: 0.0005
  })

  // 模拟股票数据
  const mockData = React.useMemo(() => {
    const symbols = ['000001', '600519', '300750', '002594', '000002']
    const data: { [symbol: string]: PriceData[] } = {}
    
    symbols.forEach(symbol => {
      const stockData: PriceData[] = []
      let basePrice = 10 + Math.random() * 20
      
      for (let i = 0; i < 252; i++) { // 一年的交易日
        const date = new Date()
        date.setDate(date.getDate() - (252 - i))
        
        const change = (Math.random() - 0.5) * 0.04
        basePrice *= (1 + change)
        
        const open = basePrice
        const high = open * (1 + Math.random() * 0.02)
        const low = open * (1 - Math.random() * 0.02)
        const close = low + Math.random() * (high - low)
        const volume = Math.floor(Math.random() * 1000000 + 500000)
        
        stockData.push({
          open,
          high,
          low,
          close,
          volume,
          timestamp: date.toISOString().split('T')[0]
        })
        
        basePrice = close
      }
      
      data[symbol] = stockData
    })
    
    return data
  }, [])

  const runBacktest = async () => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 创建策略配置
      const strategy: Strategy = {
        id: 'test_strategy',
        name: `${config.strategy.toUpperCase()}策略`,
        description: `基于${config.strategy}的量化策略`,
        type: config.strategy,
        parameters: {
          lookbackPeriod: config.lookbackPeriod,
          holdingPeriod: config.holdingPeriod,
          threshold: config.threshold
        },
        universe: Object.keys(mockData),
        rebalanceFrequency: config.rebalanceFrequency,
        maxPositions: config.maxPositions,
        maxWeight: 0.2,
        minWeight: 0.05
      }
      
      // 运行回测
      const result = QuantitativeStrategy.backtestStrategy(strategy, mockData, config.initialCapital)
      setBacktestResult(result)
      
      // 投资组合优化
      const expectedReturns: { [symbol: string]: number } = {}
      const covarianceMatrix: { [symbol: string]: { [symbol: string]: number } } = {}
      
      Object.keys(mockData).forEach(symbol => {
        const returns = mockData[symbol].slice(1).map((d, i) => 
          (d.close - mockData[symbol][i].close) / mockData[symbol][i].close)
        expectedReturns[symbol] = returns.reduce((sum, ret) => sum + ret, 0) / returns.length * 252
        
        covarianceMatrix[symbol] = {}
        Object.keys(mockData).forEach(symbol2 => {
          const returns2 = mockData[symbol2].slice(1).map((d, i) => 
            (d.close - mockData[symbol2][i].close) / mockData[symbol2][i].close)
          
          const covariance = returns.reduce((sum, ret, i) => 
            sum + (ret - expectedReturns[symbol] / 252) * (returns2[i] - expectedReturns[symbol2] / 252), 0) / returns.length * 252
          
          covarianceMatrix[symbol][symbol2] = covariance
        })
      })
      
      const optimization = QuantitativeStrategy.optimizePortfolio(
        expectedReturns,
        covarianceMatrix,
        {
          maxWeight: 0.3,
          minWeight: 0.05,
          maxSectorWeight: 0.4
        },
        'mean_variance'
      )
      
      setPortfolioOptimization(optimization)
      setActiveTab('results')
      
    } catch (error) {
      console.error('Backtest failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const renderConfig = () => (
    <Card title="策略配置" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={config}
        onValuesChange={(changedValues, allValues) => setConfig(allValues)}
      >
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="策略类型" name="strategy">
              <Select>
                <Option value="momentum">动量策略</Option>
                <Option value="mean_reversion">均值回归</Option>
                <Option value="factor">因子策略</Option>
                <Option value="ml">机器学习</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="回看周期" name="lookbackPeriod">
              <InputNumber min={5} max={100} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="持有周期" name="holdingPeriod">
              <InputNumber min={1} max={30} />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="阈值" name="threshold">
              <InputNumber min={0.5} max={5} step={0.1} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="初始资金" name="initialCapital">
              <InputNumber min={10000} max={10000000} step={10000} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="最大持仓数" name="maxPositions">
              <InputNumber min={1} max={20} />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="调仓频率" name="rebalanceFrequency">
              <Select>
                <Option value="daily">每日</Option>
                <Option value="weekly">每周</Option>
                <Option value="monthly">每月</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="交易成本 (%)" name="transactionCost">
              <InputNumber min={0} max={0.01} step={0.0001} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="滑点 (%)" name="slippage">
              <InputNumber min={0} max={0.005} step={0.0001} />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item>
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={runBacktest}
            loading={loading}
            size="large"
          >
            开始回测
          </Button>
        </Form.Item>
      </Form>
    </Card>
  )

  const renderResults = () => {
    if (!backtestResult) return null

    const performanceData = backtestResult.dailyReturns.map(d => ({
      date: d.date,
      cumulative: d.cumulative * 100,
      daily: d.return * 100
    }))

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="策略表现" size="small">
            <Line
              data={performanceData}
              xField="date"
              yField="cumulative"
              height={300}
              smooth={true}
              color="#1890ff"
              point={{
                size: 2,
                shape: 'circle',
              }}
              tooltip={{
                formatter: (datum: any) => ({
                  name: '累计收益',
                  value: `${datum.cumulative.toFixed(2)}%`
                })
              }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="关键指标" size="small">
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Statistic
                  title="总收益"
                  value={backtestResult.totalReturn * 100}
                  precision={2}
                  suffix="%"
                  valueStyle={{ 
                    color: backtestResult.totalReturn > 0 ? '#3f8600' : '#cf1322' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="年化收益"
                  value={backtestResult.annualizedReturn * 100}
                  precision={2}
                  suffix="%"
                  valueStyle={{ 
                    color: backtestResult.annualizedReturn > 0 ? '#3f8600' : '#cf1322' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="夏普比率"
                  value={backtestResult.sharpeRatio}
                  precision={2}
                  valueStyle={{ 
                    color: backtestResult.sharpeRatio > 1 ? '#3f8600' : '#faad14' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="最大回撤"
                  value={Math.abs(backtestResult.maxDrawdown) * 100}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="胜率"
                  value={backtestResult.winRate * 100}
                  precision={1}
                  suffix="%"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="盈亏比"
                  value={backtestResult.profitFactor}
                  precision={2}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="交易记录" size="small">
            <Table
              dataSource={backtestResult.trades.slice(0, 20)}
              size="small"
              pagination={{ pageSize: 10 }}
              columns={[
                { title: '股票', dataIndex: 'symbol', key: 'symbol' },
                { 
                  title: '操作', 
                  dataIndex: 'action', 
                  key: 'action',
                  render: (action: string) => (
                    <Tag color={action === 'buy' ? 'green' : 'red'}>
                      {action === 'buy' ? '买入' : '卖出'}
                    </Tag>
                  )
                },
                { title: '数量', dataIndex: 'quantity', key: 'quantity' },
                { 
                  title: '价格', 
                  dataIndex: 'price', 
                  key: 'price',
                  render: (price: number) => `¥${price.toFixed(2)}`
                },
                { title: '时间', dataIndex: 'timestamp', key: 'timestamp' },
                { 
                  title: '盈亏', 
                  dataIndex: 'pnl', 
                  key: 'pnl',
                  render: (pnl: number) => pnl ? (
                    <span style={{ color: pnl > 0 ? '#3f8600' : '#cf1322' }}>
                      ¥{pnl.toFixed(2)}
                    </span>
                  ) : '-'
                }
              ]}
            />
          </Card>
        </Col>
      </Row>
    )
  }

  const renderOptimization = () => {
    if (!portfolioOptimization) return null

    const weightsData = Object.entries(portfolioOptimization.optimizedWeights).map(([symbol, weight]) => ({
      symbol,
      weight: weight * 100
    }))

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="优化权重分配" size="small">
            <Column
              data={weightsData}
              xField="symbol"
              yField="weight"
              height={250}
              color="#52c41a"
              label={{
                position: 'top',
                formatter: (datum: any) => `${datum.weight.toFixed(1)}%`
              }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="组合指标" size="small">
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="预期收益">
                {(portfolioOptimization.expectedPortfolioReturn * 100).toFixed(2)}%
              </Descriptions.Item>
              <Descriptions.Item label="预期风险">
                {(portfolioOptimization.expectedPortfolioRisk * 100).toFixed(2)}%
              </Descriptions.Item>
              <Descriptions.Item label="夏普比率">
                {portfolioOptimization.sharpeRatio.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="优化方法">
                {portfolioOptimization.method === 'mean_variance' ? '均值方差' : portfolioOptimization.method}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24}>
          <Alert
            message="投资组合优化建议"
            description={`基于${portfolioOptimization.method}优化方法，建议的投资组合预期年化收益为${(portfolioOptimization.expectedPortfolioReturn * 100).toFixed(2)}%，风险为${(portfolioOptimization.expectedPortfolioRisk * 100).toFixed(2)}%，夏普比率为${portfolioOptimization.sharpeRatio.toFixed(2)}。`}
            type="info"
            showIcon
          />
        </Col>
      </Row>
    )
  }

  const tabItems = [
    {
      key: 'config',
      label: (
        <span>
          <SettingOutlined />
          策略配置
        </span>
      ),
      children: renderConfig(),
    },
    {
      key: 'results',
      label: (
        <span>
          <LineChartOutlined />
          回测结果
        </span>
      ),
      children: renderResults(),
    },
    {
      key: 'optimization',
      label: (
        <span>
          <TrophyOutlined />
          组合优化
        </span>
      ),
      children: renderOptimization(),
    },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>量化策略回测</Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => window.location.reload()}
          >
            重置
          </Button>
        </Space>
      </div>

      {loading && activeTab === 'config' ? (
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在运行回测...</Text>
          </div>
        </div>
      ) : (
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      )}
    </div>
  )
}

export default QuantitativeBacktest
