#!/usr/bin/env python3
"""
数据质量功能测试脚本
"""

import asyncio
import sys
import os
from datetime import date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.logging import setup_logging, get_logger
from app.services.data_validator import DataValidator, DataCleaner
from app.services.data_quality import DataQualityMonitor, DataQualityService
from app.services.data_fetcher import DataFetcherManager

logger = get_logger(__name__)


async def test_data_validator():
    """测试数据验证器"""
    logger.info("=== 测试数据验证器 ===")
    
    validator = DataValidator()
    
    # 测试股票数据验证
    valid_stock = {
        "stock_code": "000001",
        "stock_name": "平安银行",
        "market": "SZ",
        "industry": "银行"
    }
    
    invalid_stock = {
        "stock_code": "abc123",  # 无效代码
        "stock_name": "",        # 空名称
        "market": "XX"           # 无效市场
    }
    
    is_valid, errors = validator.validate_stock_data(valid_stock)
    logger.info(f"有效股票数据验证: {is_valid}, 错误: {errors}")
    
    is_valid, errors = validator.validate_stock_data(invalid_stock)
    logger.info(f"无效股票数据验证: {is_valid}, 错误: {errors}")
    
    # 测试K线数据验证
    valid_kline = {
        "stock_code": "000001",
        "trade_date": date.today(),
        "open_price": 10.0,
        "high_price": 11.0,
        "low_price": 9.5,
        "close_price": 10.5,
        "volume": 1000000,
        "turnover": 10500000.0
    }
    
    invalid_kline = {
        "stock_code": "000001",
        "trade_date": date.today(),
        "open_price": 10.0,
        "high_price": 9.0,  # 最高价低于开盘价
        "low_price": 11.0,  # 最低价高于开盘价
        "close_price": 10.5,
        "volume": -100,     # 负成交量
        "turnover": 10500000.0
    }
    
    is_valid, errors = validator.validate_kline_data(valid_kline)
    logger.info(f"有效K线数据验证: {is_valid}, 错误: {errors}")
    
    is_valid, errors = validator.validate_kline_data(invalid_kline)
    logger.info(f"无效K线数据验证: {is_valid}, 错误: {errors}")


async def test_data_cleaner():
    """测试数据清洗器"""
    logger.info("=== 测试数据清洗器 ===")
    
    cleaner = DataCleaner()
    
    # 测试股票数据清洗
    dirty_stock = {
        "stock_code": "1",      # 需要补零
        "stock_name": "  平安银行  ",  # 需要去空格
        "market": "sz",         # 需要大写
        "industry": "  银行  "
    }
    
    cleaned_stock = cleaner.clean_stock_data(dirty_stock)
    logger.info(f"股票数据清洗前: {dirty_stock}")
    logger.info(f"股票数据清洗后: {cleaned_stock}")
    
    # 测试K线数据清洗
    dirty_kline = {
        "stock_code": "1",
        "open_price": "10.123456",  # 字符串价格，需要转换和四舍五入
        "high_price": 11.987654,    # 需要四舍五入
        "volume": "1000000.5",      # 字符串成交量，需要转换为整数
        "change_percent": 5.123456  # 需要四舍五入
    }
    
    cleaned_kline = cleaner.clean_kline_data(dirty_kline)
    logger.info(f"K线数据清洗前: {dirty_kline}")
    logger.info(f"K线数据清洗后: {cleaned_kline}")
    
    # 测试去重功能
    duplicate_data = [
        {"stock_code": "000001", "name": "平安银行"},
        {"stock_code": "000002", "name": "万科A"},
        {"stock_code": "000001", "name": "平安银行"},  # 重复
        {"stock_code": "000003", "name": "国农科技"}
    ]
    
    unique_data = cleaner.remove_duplicates(duplicate_data, ["stock_code"])
    logger.info(f"去重前: {len(duplicate_data)} 条")
    logger.info(f"去重后: {len(unique_data)} 条")


async def test_quality_monitor():
    """测试数据质量监控"""
    logger.info("=== 测试数据质量监控 ===")
    
    monitor = DataQualityMonitor()
    
    # 测试数据新鲜度检查
    logger.info("检查数据新鲜度...")
    freshness = await monitor.check_data_freshness()
    logger.info(f"数据新鲜度: {freshness}")
    
    # 测试数据完整性检查（使用测试股票代码）
    test_stock = "000001"
    end_date = date.today()
    start_date = end_date - timedelta(days=7)
    
    logger.info(f"检查股票 {test_stock} 数据完整性...")
    completeness = await monitor.check_data_completeness(test_stock, start_date, end_date)
    logger.info(f"数据完整性: {completeness}")
    
    # 测试数据一致性检查
    logger.info(f"检查股票 {test_stock} 数据一致性...")
    consistency = await monitor.check_data_consistency(test_stock, limit=10)
    logger.info(f"数据一致性: {consistency}")


async def test_quality_service():
    """测试数据质量服务"""
    logger.info("=== 测试数据质量服务 ===")
    
    service = DataQualityService()
    
    # 测试质量仪表板
    logger.info("获取数据质量仪表板...")
    dashboard = await service.get_quality_dashboard()
    
    if "error" in dashboard:
        logger.error(f"获取仪表板失败: {dashboard['error']}")
    else:
        logger.info("数据质量仪表板:")
        logger.info(f"- 总股票数: {dashboard.get('overview', {}).get('total_stocks', 0)}")
        logger.info(f"- 总K线数: {dashboard.get('overview', {}).get('total_klines', 0)}")
        logger.info(f"- 完整性率: {dashboard.get('quality_metrics', {}).get('completeness_rate', 0)}")
        logger.info(f"- 一致性率: {dashboard.get('quality_metrics', {}).get('consistency_rate', 0)}")


async def test_cleaned_data_fetcher():
    """测试清洗后的数据获取"""
    logger.info("=== 测试清洗后的数据获取 ===")
    
    fetcher_manager = DataFetcherManager()
    
    # 测试获取清洗后的股票列表
    logger.info("获取清洗后的股票列表...")
    try:
        stocks = await fetcher_manager.get_cleaned_stock_list()
        logger.info(f"获取到 {len(stocks)} 只清洗后的股票")
        
        if stocks:
            # 显示前3只股票
            for i, stock in enumerate(stocks[:3]):
                logger.info(f"{i+1}. {stock['stock_code']} - {stock['stock_name']}")
    except Exception as e:
        logger.error(f"获取清洗后的股票列表失败: {e}")
    
    # 测试获取清洗后的K线数据
    test_stock = "000001"
    logger.info(f"获取股票 {test_stock} 清洗后的K线数据...")
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=5)
        
        klines = await fetcher_manager.get_cleaned_kline_data(
            test_stock, "daily", start_date, end_date
        )
        logger.info(f"获取到 {len(klines)} 条清洗后的K线数据")
        
        if klines:
            latest = klines[-1]
            logger.info(f"最新数据: {latest['trade_date']} 收盘价: {latest['close_price']}")
    except Exception as e:
        logger.error(f"获取清洗后的K线数据失败: {e}")


async def main():
    """主测试函数"""
    setup_logging()
    logger.info("🧹 开始数据清洗与验证功能测试")
    
    try:
        # 1. 测试数据验证器
        await test_data_validator()
        
        # 2. 测试数据清洗器
        await test_data_cleaner()
        
        # 3. 测试数据质量监控
        await test_quality_monitor()
        
        # 4. 测试数据质量服务
        await test_quality_service()
        
        # 5. 测试清洗后的数据获取
        await test_cleaned_data_fetcher()
        
        logger.info("✅ 所有数据质量测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
