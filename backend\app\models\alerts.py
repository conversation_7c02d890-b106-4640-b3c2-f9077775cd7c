"""
预警系统相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, DateTime, Date, Numeric, Integer, Text, JSON, Boolean, Index
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class AlertRule(Base):
    """预警规则表"""
    __tablename__ = "alert_rules"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, comment="用户ID")
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    
    # 规则基本信息
    rule_name: Mapped[str] = mapped_column(String(100), comment="规则名称")
    rule_type: Mapped[str] = mapped_column(String(20), comment="规则类型: price/indicator/ai/pattern/volume")
    alert_level: Mapped[str] = mapped_column(String(10), comment="预警级别: high/medium/low")
    
    # 预警条件
    condition_type: Mapped[str] = mapped_column(String(20), comment="条件类型: >/</=/between/cross")
    target_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 6), comment="目标值")
    target_value_max: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 6), comment="目标值上限")
    indicator_name: Mapped[Optional[str]] = mapped_column(String(50), comment="指标名称")
    
    # 规则配置
    rule_config: Mapped[Optional[dict]] = mapped_column(JSON, comment="规则详细配置")
    trigger_frequency: Mapped[str] = mapped_column(String(20), default="once", comment="触发频率: once/daily/always")
    
    # 通知设置
    notification_methods: Mapped[dict] = mapped_column(JSON, comment="通知方式配置")
    message_template: Mapped[Optional[str]] = mapped_column(Text, comment="消息模板")
    
    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    last_triggered_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后触发时间")
    trigger_count: Mapped[int] = mapped_column(Integer, default=0, comment="触发次数")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_alert_rule_user_stock', 'user_id', 'stock_code'),
        Index('ix_alert_rule_type_active', 'rule_type', 'is_active'),
    )


class AlertEvent(Base):
    """预警事件表"""
    __tablename__ = "alert_events"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    rule_id: Mapped[int] = mapped_column(Integer, index=True, comment="规则ID")
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    
    # 事件信息
    event_type: Mapped[str] = mapped_column(String(20), comment="事件类型")
    alert_level: Mapped[str] = mapped_column(String(10), comment="预警级别")
    event_title: Mapped[str] = mapped_column(String(200), comment="事件标题")
    event_message: Mapped[str] = mapped_column(Text, comment="事件消息")
    
    # 触发数据
    trigger_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 6), comment="触发值")
    trigger_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="触发时的数据")
    market_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="市场数据快照")
    
    # 处理状态
    status: Mapped[str] = mapped_column(String(20), default="pending", comment="处理状态: pending/sent/failed")
    notification_status: Mapped[Optional[dict]] = mapped_column(JSON, comment="通知状态")
    
    # 时间信息
    triggered_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="触发时间")
    processed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="处理时间")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_alert_event_rule_time', 'rule_id', 'triggered_at'),
        Index('ix_alert_event_stock_time', 'stock_code', 'triggered_at'),
        Index('ix_alert_event_status', 'status'),
    )


class AlertTemplate(Base):
    """预警模板表"""
    __tablename__ = "alert_templates"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    template_name: Mapped[str] = mapped_column(String(100), unique=True, comment="模板名称")
    template_type: Mapped[str] = mapped_column(String(20), comment="模板类型")
    
    # 模板内容
    title_template: Mapped[str] = mapped_column(String(200), comment="标题模板")
    message_template: Mapped[str] = mapped_column(Text, comment="消息模板")
    
    # 模板配置
    default_config: Mapped[dict] = mapped_column(JSON, comment="默认配置")
    variables: Mapped[dict] = mapped_column(JSON, comment="模板变量说明")
    
    # 状态
    is_system: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否系统模板")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class NotificationChannel(Base):
    """通知渠道表"""
    __tablename__ = "notification_channels"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, comment="用户ID")
    
    # 渠道信息
    channel_type: Mapped[str] = mapped_column(String(20), comment="渠道类型: email/sms/webhook/push")
    channel_name: Mapped[str] = mapped_column(String(100), comment="渠道名称")
    
    # 渠道配置
    channel_config: Mapped[dict] = mapped_column(JSON, comment="渠道配置")
    endpoint: Mapped[str] = mapped_column(String(500), comment="通知端点")
    
    # 状态
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否已验证")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    
    # 统计
    success_count: Mapped[int] = mapped_column(Integer, default=0, comment="成功次数")
    failure_count: Mapped[int] = mapped_column(Integer, default=0, comment="失败次数")
    last_used_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后使用时间")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_notification_channel_user', 'user_id'),
        Index('ix_notification_channel_type', 'channel_type'),
    )


class AlertStatistics(Base):
    """预警统计表"""
    __tablename__ = "alert_statistics"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stat_date: Mapped[date] = mapped_column(Date, index=True, comment="统计日期")
    
    # 统计维度
    rule_type: Mapped[Optional[str]] = mapped_column(String(20), comment="规则类型")
    alert_level: Mapped[Optional[str]] = mapped_column(String(10), comment="预警级别")
    
    # 统计数据
    total_rules: Mapped[int] = mapped_column(Integer, default=0, comment="规则总数")
    active_rules: Mapped[int] = mapped_column(Integer, default=0, comment="活跃规则数")
    total_events: Mapped[int] = mapped_column(Integer, default=0, comment="事件总数")
    successful_notifications: Mapped[int] = mapped_column(Integer, default=0, comment="成功通知数")
    failed_notifications: Mapped[int] = mapped_column(Integer, default=0, comment="失败通知数")
    
    # 详细统计
    statistics_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="详细统计数据")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_alert_stat_date_type', 'stat_date', 'rule_type'),
        Index('ix_alert_stat_date_level', 'stat_date', 'alert_level'),
    )


class AlertSubscription(Base):
    """预警订阅表"""
    __tablename__ = "alert_subscriptions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, comment="用户ID")
    
    # 订阅信息
    subscription_type: Mapped[str] = mapped_column(String(20), comment="订阅类型: stock/sector/market")
    target_code: Mapped[str] = mapped_column(String(20), comment="目标代码")
    subscription_name: Mapped[str] = mapped_column(String(100), comment="订阅名称")
    
    # 订阅配置
    alert_types: Mapped[list] = mapped_column(JSON, comment="预警类型列表")
    alert_levels: Mapped[list] = mapped_column(JSON, comment="预警级别列表")
    notification_channels: Mapped[list] = mapped_column(JSON, comment="通知渠道列表")
    
    # 过滤条件
    filter_conditions: Mapped[Optional[dict]] = mapped_column(JSON, comment="过滤条件")
    
    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_alert_subscription_user', 'user_id'),
        Index('ix_alert_subscription_target', 'target_code'),
    )
