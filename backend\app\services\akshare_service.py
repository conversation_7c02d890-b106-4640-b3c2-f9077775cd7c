"""
AKShare数据获取服务
"""

import logging
import asyncio
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from app.models.stock import (
    Stock, StockDetailInfo, SectorData, SectorStock, StockSpotData,
    MarketSummary, RegionTradingData, IndustryTradingData, StockBidAsk
)

# 导入akshare
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("AKShare库加载成功")
except ImportError:
    AKSHARE_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("AKShare库未安装，将使用模拟数据")

class AKShareService:
    """AKShare数据获取服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.use_real_api = AKSHARE_AVAILABLE
        
    async def fetch_stock_detail_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取个股详细信息 - 模拟AKShare stock_individual_info_em接口"""
        try:
            # 这里应该调用实际的AKShare API
            # 为了演示，我们返回模拟数据
            logger.info(f"模拟获取股票 {stock_code} 的详细信息")
            
            # 模拟数据结构
            mock_data = {
                "stock_code": stock_code,
                "stock_name": f"股票{stock_code}",
                "full_name": f"股票{stock_code}有限公司",
                "market": "深A" if stock_code.startswith("0") or stock_code.startswith("3") else "沪A",
                "exchange": "深圳证券交易所" if stock_code.startswith("0") or stock_code.startswith("3") else "上海证券交易所",
                "list_date": "2020-01-01",
                "total_shares": 1000000000.0,
                "float_shares": 800000000.0,
                "total_market_cap": 10000000000.0,
                "float_market_cap": 8000000000.0,
                "industry": "软件开发",
                "sector": "信息技术",
                "concept": "人工智能,大数据",
                "pe_ratio": 25.5,
                "pb_ratio": 3.2,
                "roe": 0.15,
                "roa": 0.08,
                "legal_representative": "张三",
                "general_manager": "李四",
                "secretary": "王五",
                "phone": "010-12345678",
                "email": "<EMAIL>",
                "website": "www.example.com",
                "main_business": "软件开发和技术服务",
                "business_scope": "软件开发、技术咨询、系统集成",
                "company_profile": "专业的软件开发公司",
                "employee_count": 5000,
                "registered_capital": *********.0,
                "actual_controller": "张三",
                "data_date": datetime.now().date().isoformat()
            }
            
            return mock_data
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 详细信息失败: {e}")
            return None
    
    async def save_stock_detail_info(self, stock_data: Dict[str, Any]) -> bool:
        """保存个股详细信息到数据库"""
        try:
            # 检查是否已存在
            result = await self.db.execute(
                select(StockDetailInfo).where(StockDetailInfo.stock_code == stock_data["stock_code"])
            )
            existing = result.scalar_one_or_none()
            
            if existing:
                # 更新现有记录
                for key, value in stock_data.items():
                    if hasattr(existing, key) and value is not None:
                        if key in ["list_date", "data_date"] and isinstance(value, str):
                            value = datetime.strptime(value, "%Y-%m-%d").date()
                        elif key in ["total_shares", "float_shares", "total_market_cap", "float_market_cap", 
                                   "pe_ratio", "pb_ratio", "roe", "roa", "registered_capital"]:
                            value = Decimal(str(value)) if value is not None else None
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
            else:
                # 创建新记录
                detail_info = StockDetailInfo(
                    stock_code=stock_data["stock_code"],
                    stock_name=stock_data.get("stock_name"),
                    full_name=stock_data.get("full_name"),
                    market=stock_data.get("market"),
                    exchange=stock_data.get("exchange"),
                    list_date=datetime.strptime(stock_data["list_date"], "%Y-%m-%d").date() if stock_data.get("list_date") else None,
                    total_shares=Decimal(str(stock_data["total_shares"])) if stock_data.get("total_shares") else None,
                    float_shares=Decimal(str(stock_data["float_shares"])) if stock_data.get("float_shares") else None,
                    total_market_cap=Decimal(str(stock_data["total_market_cap"])) if stock_data.get("total_market_cap") else None,
                    float_market_cap=Decimal(str(stock_data["float_market_cap"])) if stock_data.get("float_market_cap") else None,
                    industry=stock_data.get("industry"),
                    sector=stock_data.get("sector"),
                    concept=stock_data.get("concept"),
                    pe_ratio=Decimal(str(stock_data["pe_ratio"])) if stock_data.get("pe_ratio") else None,
                    pb_ratio=Decimal(str(stock_data["pb_ratio"])) if stock_data.get("pb_ratio") else None,
                    roe=Decimal(str(stock_data["roe"])) if stock_data.get("roe") else None,
                    roa=Decimal(str(stock_data["roa"])) if stock_data.get("roa") else None,
                    legal_representative=stock_data.get("legal_representative"),
                    general_manager=stock_data.get("general_manager"),
                    secretary=stock_data.get("secretary"),
                    phone=stock_data.get("phone"),
                    email=stock_data.get("email"),
                    website=stock_data.get("website"),
                    main_business=stock_data.get("main_business"),
                    business_scope=stock_data.get("business_scope"),
                    company_profile=stock_data.get("company_profile"),
                    employee_count=stock_data.get("employee_count"),
                    registered_capital=Decimal(str(stock_data["registered_capital"])) if stock_data.get("registered_capital") else None,
                    actual_controller=stock_data.get("actual_controller"),
                    data_date=datetime.strptime(stock_data["data_date"], "%Y-%m-%d").date() if stock_data.get("data_date") else None
                )
                self.db.add(detail_info)
            
            await self.db.commit()
            return True
            
        except Exception as e:
            logger.error(f"保存股票详细信息失败: {e}")
            await self.db.rollback()
            return False
    
    async def fetch_spot_data(self, market: str = "all") -> List[Dict[str, Any]]:
        """获取实时行情数据 - AKShare stock_zh_a_spot_em接口"""
        try:
            if self.use_real_api:
                logger.info(f"开始获取 {market} 市场实时行情数据")

                # 在异步环境中运行同步的akshare函数，增加超时设置
                loop = asyncio.get_event_loop()

                # 使用更长的超时时间，确保数据能够完全下载
                try:
                    df = await asyncio.wait_for(
                        loop.run_in_executor(None, ak.stock_zh_a_spot_em),
                        timeout=300.0  # 增加到300秒超时
                    )
                    logger.info(f"AKShare API调用成功，获取到数据形状: {df.shape if df is not None else 'None'}")
                except asyncio.TimeoutError:
                    logger.error("AKShare API调用超时（300秒），使用模拟数据")
                    return self._get_mock_spot_data()

                if df is not None and not df.empty:
                    # 限制返回数据量，避免过多数据
                    df = df.head(100)  # 增加到100条数据
                    logger.info(f"处理前{len(df)}条股票数据")

                    spot_data = []
                    for _, row in df.iterrows():
                        try:
                            # 安全地获取数据，处理可能的异常值
                            def safe_float(value, default=0.0):
                                try:
                                    if value == "-" or value is None or str(value).strip() == "":
                                        return default
                                    return float(value)
                                except (ValueError, TypeError):
                                    return default

                            def safe_int(value, default=0):
                                try:
                                    if value == "-" or value is None or str(value).strip() == "":
                                        return default
                                    return int(float(value))
                                except (ValueError, TypeError):
                                    return default

                            data = {
                                "stock_code": str(row.get("代码", "")).strip(),
                                "stock_name": str(row.get("名称", "")).strip(),
                                "current_price": safe_float(row.get("最新价")),
                                "open_price": safe_float(row.get("今开")),
                                "high_price": safe_float(row.get("最高")),
                                "low_price": safe_float(row.get("最低")),
                                "pre_close": safe_float(row.get("昨收")),
                                "change_amount": safe_float(row.get("涨跌额")),
                                "change_percent": safe_float(row.get("涨跌幅")),
                                "amplitude": safe_float(row.get("振幅")),
                                "volume": safe_int(row.get("成交量")),
                                "turnover": safe_float(row.get("成交额")),
                                "turnover_rate": safe_float(row.get("换手率")),
                                "volume_ratio": safe_float(row.get("量比")),
                                "pe_ratio": safe_float(row.get("市盈率-动态")),
                                "pb_ratio": safe_float(row.get("市净率")),
                                "total_market_cap": safe_float(row.get("总市值")),
                                "float_market_cap": safe_float(row.get("流通市值")),
                                "speed": safe_float(row.get("涨速")),
                                "change_5min": safe_float(row.get("5分钟涨跌")),
                                "change_60day": safe_float(row.get("60日涨跌幅")),
                                "change_ytd": safe_float(row.get("年初至今涨跌幅")),
                                "trade_date": datetime.now().date().isoformat(),
                                "update_time": datetime.now().isoformat()
                            }

                            # 只添加有效的股票代码
                            if data["stock_code"] and len(data["stock_code"]) == 6:
                                spot_data.append(data)

                        except Exception as e:
                            logger.warning(f"解析股票数据失败: {e}, 行数据: {row.to_dict()}")
                            continue

                    logger.info(f"成功解析 {len(spot_data)} 条有效实时行情数据")

                    if len(spot_data) > 0:
                        return spot_data
                    else:
                        logger.warning("解析后没有有效数据，使用模拟数据")
                        return self._get_mock_spot_data()
                else:
                    logger.warning("AKShare返回空数据或None，使用模拟数据")
                    return self._get_mock_spot_data()
            else:
                logger.info("AKShare不可用，使用模拟数据")
                return self._get_mock_spot_data()

        except Exception as e:
            logger.error(f"获取实时行情数据失败: {e}", exc_info=True)
            return self._get_mock_spot_data()

    def _get_mock_spot_data(self) -> List[Dict[str, Any]]:
        """获取模拟实时行情数据"""
        mock_stocks = ["000001", "000002", "600000", "600036", "300001"]
        spot_data = []

        for stock_code in mock_stocks:
            data = {
                "stock_code": stock_code,
                "stock_name": f"股票{stock_code}",
                "current_price": 10.50,
                "open_price": 10.20,
                "high_price": 10.80,
                "low_price": 10.10,
                "pre_close": 10.30,
                "change_amount": 0.20,
                "change_percent": 1.94,
                "amplitude": 6.80,
                "volume": 1000000,
                "turnover": 10500000.0,
                "turnover_rate": 2.5,
                "volume_ratio": 1.2,
                "pe_ratio": 25.5,
                "pb_ratio": 3.2,
                "total_market_cap": 10000000000.0,
                "float_market_cap": 8000000000.0,
                "speed": 0.5,
                "change_5min": 0.1,
                "change_60day": 15.5,
                "change_ytd": 8.2,
                "trade_date": datetime.now().date().isoformat(),
                "update_time": datetime.now().isoformat()
            }
            spot_data.append(data)

        return spot_data
    
    async def save_spot_data(self, spot_data_list: List[Dict[str, Any]]) -> bool:
        """保存实时行情数据到数据库"""
        try:
            for data in spot_data_list:
                # 检查是否已存在当日数据
                trade_date = datetime.strptime(data["trade_date"], "%Y-%m-%d").date()
                result = await self.db.execute(
                    select(StockSpotData).where(
                        and_(
                            StockSpotData.stock_code == data["stock_code"],
                            StockSpotData.trade_date == trade_date
                        )
                    )
                )
                existing = result.scalar_one_or_none()
                
                if existing:
                    # 更新现有记录
                    for key, value in data.items():
                        if hasattr(existing, key) and value is not None:
                            if key == "trade_date":
                                value = datetime.strptime(value, "%Y-%m-%d").date()
                            elif key == "update_time":
                                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            elif key in ["current_price", "open_price", "high_price", "low_price", "pre_close",
                                       "change_amount", "change_percent", "amplitude", "turnover", "turnover_rate",
                                       "volume_ratio", "pe_ratio", "pb_ratio", "total_market_cap", "float_market_cap",
                                       "speed", "change_5min", "change_60day", "change_ytd"]:
                                value = Decimal(str(value)) if value is not None else None
                            setattr(existing, key, value)
                else:
                    # 创建新记录
                    spot_data = StockSpotData(
                        stock_code=data["stock_code"],
                        stock_name=data["stock_name"],
                        current_price=Decimal(str(data["current_price"])),
                        open_price=Decimal(str(data["open_price"])),
                        high_price=Decimal(str(data["high_price"])),
                        low_price=Decimal(str(data["low_price"])),
                        pre_close=Decimal(str(data["pre_close"])),
                        change_amount=Decimal(str(data["change_amount"])),
                        change_percent=Decimal(str(data["change_percent"])),
                        amplitude=Decimal(str(data["amplitude"])),
                        volume=data["volume"],
                        turnover=Decimal(str(data["turnover"])),
                        turnover_rate=Decimal(str(data["turnover_rate"])),
                        volume_ratio=Decimal(str(data["volume_ratio"])),
                        pe_ratio=Decimal(str(data["pe_ratio"])) if data.get("pe_ratio") else None,
                        pb_ratio=Decimal(str(data["pb_ratio"])) if data.get("pb_ratio") else None,
                        total_market_cap=Decimal(str(data["total_market_cap"])) if data.get("total_market_cap") else None,
                        float_market_cap=Decimal(str(data["float_market_cap"])) if data.get("float_market_cap") else None,
                        speed=Decimal(str(data["speed"])) if data.get("speed") else None,
                        change_5min=Decimal(str(data["change_5min"])) if data.get("change_5min") else None,
                        change_60day=Decimal(str(data["change_60day"])) if data.get("change_60day") else None,
                        change_ytd=Decimal(str(data["change_ytd"])) if data.get("change_ytd") else None,
                        trade_date=datetime.strptime(data["trade_date"], "%Y-%m-%d").date(),
                        update_time=datetime.fromisoformat(data["update_time"].replace('Z', '+00:00'))
                    )
                    self.db.add(spot_data)
            
            await self.db.commit()
            return True
            
        except Exception as e:
            logger.error(f"保存实时行情数据失败: {e}")
            await self.db.rollback()
            return False
    
    async def fetch_sector_data(self, sector_type: str = "industry") -> List[Dict[str, Any]]:
        """获取板块数据 - AKShare板块数据接口"""
        try:
            if self.use_real_api:
                logger.info(f"获取 {sector_type} 板块数据")

                # 在异步环境中运行同步的akshare函数
                loop = asyncio.get_event_loop()

                if sector_type == "industry":
                    # 获取行业板块数据
                    df = await loop.run_in_executor(None, ak.stock_board_industry_name_em)
                else:
                    # 获取概念板块数据
                    df = await loop.run_in_executor(None, ak.stock_board_concept_name_em)

                if df is not None and not df.empty:
                    # 限制返回数据量
                    df = df.head(20)

                    sector_data = []
                    for _, row in df.iterrows():
                        try:
                            data = {
                                "sector_code": str(row.get("板块代码", f"BK{len(sector_data):04d}")),
                                "sector_name": str(row.get("板块名称", "")),
                                "sector_type": sector_type,
                                "stock_count": int(row.get("包含股票数", 0)),
                                "total_market_cap": float(row.get("总市值", 0)),
                                "avg_pe_ratio": float(row.get("平均市盈率", 0)) if row.get("平均市盈率") != "-" else 0.0,
                                "avg_pb_ratio": float(row.get("平均市净率", 0)) if row.get("平均市净率") != "-" else 0.0,
                                "up_count": int(row.get("上涨家数", 0)),
                                "down_count": int(row.get("下跌家数", 0)),
                                "flat_count": int(row.get("平盘家数", 0)),
                                "total_volume": float(row.get("总成交量", 0)),
                                "total_turnover": float(row.get("总成交额", 0)),
                                "avg_turnover_rate": float(row.get("平均换手率", 0)),
                                "sector_change": float(row.get("涨跌幅", 0)),
                                "sector_change_amount": float(row.get("涨跌额", 0)),
                                "trade_date": datetime.now().date().isoformat()
                            }
                            sector_data.append(data)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"解析板块数据失败: {e}")
                            continue

                    logger.info(f"成功获取 {len(sector_data)} 条板块数据")
                    return sector_data
                else:
                    logger.warning("AKShare返回空板块数据，使用模拟数据")
                    return self._get_mock_sector_data(sector_type)
            else:
                logger.info("AKShare不可用，使用模拟板块数据")
                return self._get_mock_sector_data(sector_type)

        except Exception as e:
            logger.error(f"获取板块数据失败: {e}")
            return self._get_mock_sector_data(sector_type)

    def _get_mock_sector_data(self, sector_type: str) -> List[Dict[str, Any]]:
        """获取模拟板块数据"""
        mock_sectors = [
            {
                "sector_code": "BK0001",
                "sector_name": "软件开发",
                "sector_type": sector_type,
                "stock_count": 50,
                "total_market_cap": *********000.0,
                "avg_pe_ratio": 28.5,
                "avg_pb_ratio": 4.2,
                "up_count": 30,
                "down_count": 15,
                "flat_count": 5,
                "total_volume": 1000000000.0,
                "total_turnover": *********00.0,
                "avg_turnover_rate": 3.5,
                "sector_change": 2.5,
                "sector_change_amount": 0.25,
                "trade_date": datetime.now().date().isoformat()
            },
            {
                "sector_code": "BK0002",
                "sector_name": "新能源汽车",
                "sector_type": sector_type,
                "stock_count": 80,
                "total_market_cap": 800000000000.0,
                "avg_pe_ratio": 35.2,
                "avg_pb_ratio": 5.8,
                "up_count": 45,
                "down_count": 25,
                "flat_count": 10,
                "total_volume": 1*********.0,
                "total_turnover": 7*********0.0,
                "avg_turnover_rate": 4.2,
                "sector_change": 3.8,
                "sector_change_amount": 0.38,
                "trade_date": datetime.now().date().isoformat()
            }
        ]

        return mock_sectors

    async def save_sector_data(self, sector_data_list: List[Dict[str, Any]]) -> bool:
        """保存板块数据到数据库"""
        try:
            for data in sector_data_list:
                trade_date = datetime.strptime(data["trade_date"], "%Y-%m-%d").date()

                # 检查是否已存在
                result = await self.db.execute(
                    select(SectorData).where(
                        and_(
                            SectorData.sector_code == data["sector_code"],
                            SectorData.trade_date == trade_date
                        )
                    )
                )
                existing = result.scalar_one_or_none()

                if existing:
                    # 更新现有记录
                    for key, value in data.items():
                        if hasattr(existing, key) and value is not None:
                            if key == "trade_date":
                                value = datetime.strptime(value, "%Y-%m-%d").date()
                            elif key in ["total_market_cap", "avg_pe_ratio", "avg_pb_ratio", "total_volume",
                                       "total_turnover", "avg_turnover_rate", "sector_change", "sector_change_amount"]:
                                value = Decimal(str(value)) if value is not None else None
                            setattr(existing, key, value)
                    existing.updated_at = datetime.utcnow()
                else:
                    # 创建新记录
                    sector_data = SectorData(
                        sector_code=data["sector_code"],
                        sector_name=data["sector_name"],
                        sector_type=data["sector_type"],
                        stock_count=data["stock_count"],
                        total_market_cap=Decimal(str(data["total_market_cap"])) if data.get("total_market_cap") else None,
                        avg_pe_ratio=Decimal(str(data["avg_pe_ratio"])) if data.get("avg_pe_ratio") else None,
                        avg_pb_ratio=Decimal(str(data["avg_pb_ratio"])) if data.get("avg_pb_ratio") else None,
                        up_count=data["up_count"],
                        down_count=data["down_count"],
                        flat_count=data["flat_count"],
                        total_volume=Decimal(str(data["total_volume"])) if data.get("total_volume") else None,
                        total_turnover=Decimal(str(data["total_turnover"])) if data.get("total_turnover") else None,
                        avg_turnover_rate=Decimal(str(data["avg_turnover_rate"])) if data.get("avg_turnover_rate") else None,
                        sector_change=Decimal(str(data["sector_change"])) if data.get("sector_change") else None,
                        sector_change_amount=Decimal(str(data["sector_change_amount"])) if data.get("sector_change_amount") else None,
                        trade_date=trade_date
                    )
                    self.db.add(sector_data)

            await self.db.commit()
            return True

        except Exception as e:
            logger.error(f"保存板块数据失败: {e}")
            await self.db.rollback()
            return False

    async def fetch_market_summary(self, exchange: str = "SSE") -> Optional[Dict[str, Any]]:
        """获取市场总貌数据 - AKShare市场总貌接口"""
        try:
            if self.use_real_api:
                logger.info(f"获取 {exchange} 市场总貌数据")

                # 在异步环境中运行同步的akshare函数
                loop = asyncio.get_event_loop()

                try:
                    # 获取上证指数信息作为市场总貌
                    df = await loop.run_in_executor(None, ak.stock_zh_index_spot_em, "上证指数")

                    if df is not None and not df.empty:
                        row = df.iloc[0]
                        data = {
                            "exchange": exchange,
                            "market_type": "主板",
                            "index_name": str(row.get("名称", "上证指数")),
                            "index_code": str(row.get("代码", "000001")),
                            "current_price": float(row.get("最新价", 0)),
                            "change_amount": float(row.get("涨跌额", 0)),
                            "change_percent": float(row.get("涨跌幅", 0)),
                            "open_price": float(row.get("今开", 0)),
                            "high_price": float(row.get("最高", 0)),
                            "low_price": float(row.get("最低", 0)),
                            "pre_close": float(row.get("昨收", 0)),
                            "volume": int(row.get("成交量", 0)),
                            "turnover": float(row.get("成交额", 0)),
                            "report_date": datetime.now().date().isoformat()
                        }

                        logger.info(f"成功获取 {exchange} 市场总貌数据")
                        return data
                    else:
                        logger.warning("AKShare返回空市场数据，使用模拟数据")
                        return self._get_mock_market_summary(exchange)

                except Exception as api_error:
                    logger.warning(f"AKShare API调用失败: {api_error}，使用模拟数据")
                    return self._get_mock_market_summary(exchange)
            else:
                logger.info("AKShare不可用，使用模拟市场数据")
                return self._get_mock_market_summary(exchange)

        except Exception as e:
            logger.error(f"获取市场总貌数据失败: {e}")
            return self._get_mock_market_summary(exchange)

    def _get_mock_market_summary(self, exchange: str) -> Dict[str, Any]:
        """获取模拟市场总貌数据"""
        return {
            "exchange": exchange,
            "market_type": "主板",
            "index_name": "上证指数",
            "index_code": "000001",
            "current_price": 3200.50,
            "change_amount": 15.30,
            "change_percent": 0.48,
            "open_price": 3185.20,
            "high_price": 3210.80,
            "low_price": 3180.10,
            "pre_close": 3185.20,
            "volume": 250000000,
            "turnover": 3*********00.0,
            "report_date": datetime.now().date().isoformat()
        }

    async def save_market_summary(self, summary_data: Dict[str, Any]) -> bool:
        """保存市场总貌数据到数据库"""
        try:
            report_date = datetime.strptime(summary_data["report_date"], "%Y-%m-%d").date()

            # 检查是否已存在
            result = await self.db.execute(
                select(MarketSummary).where(
                    and_(
                        MarketSummary.exchange == summary_data["exchange"],
                        MarketSummary.market_type == summary_data["market_type"],
                        MarketSummary.report_date == report_date
                    )
                )
            )
            existing = result.scalar_one_or_none()

            if existing:
                # 更新现有记录
                for key, value in summary_data.items():
                    if hasattr(existing, key) and value is not None:
                        if key == "report_date":
                            value = datetime.strptime(value, "%Y-%m-%d").date()
                        elif key in ["total_shares", "float_shares", "total_market_cap", "float_market_cap",
                                   "avg_pe_ratio", "turnover_amount", "turnover_volume"]:
                            value = Decimal(str(value)) if value is not None else None
                        setattr(existing, key, value)
            else:
                # 创建新记录
                market_summary = MarketSummary(
                    exchange=summary_data["exchange"],
                    market_type=summary_data["market_type"],
                    listed_count=summary_data["listed_count"],
                    stock_count=summary_data["stock_count"],
                    total_shares=Decimal(str(summary_data["total_shares"])),
                    float_shares=Decimal(str(summary_data["float_shares"])),
                    total_market_cap=Decimal(str(summary_data["total_market_cap"])),
                    float_market_cap=Decimal(str(summary_data["float_market_cap"])),
                    avg_pe_ratio=Decimal(str(summary_data["avg_pe_ratio"])) if summary_data.get("avg_pe_ratio") else None,
                    turnover_amount=Decimal(str(summary_data["turnover_amount"])) if summary_data.get("turnover_amount") else None,
                    turnover_volume=Decimal(str(summary_data["turnover_volume"])) if summary_data.get("turnover_volume") else None,
                    report_date=report_date
                )
                self.db.add(market_summary)

            await self.db.commit()
            return True

        except Exception as e:
            logger.error(f"保存市场总貌数据失败: {e}")
            await self.db.rollback()
            return False

    async def update_all_stock_details(self, stock_codes: List[str]) -> Dict[str, Any]:
        """批量更新股票详细信息"""
        try:
            results = {
                "total": len(stock_codes),
                "success": 0,
                "failed": 0,
                "errors": []
            }

            for stock_code in stock_codes:
                try:
                    # 获取股票详细信息
                    detail_data = await self.fetch_stock_detail_info(stock_code)
                    if detail_data:
                        # 保存到数据库
                        success = await self.save_stock_detail_info(detail_data)
                        if success:
                            results["success"] += 1
                        else:
                            results["failed"] += 1
                            results["errors"].append(f"保存股票 {stock_code} 详细信息失败")
                    else:
                        results["failed"] += 1
                        results["errors"].append(f"获取股票 {stock_code} 详细信息失败")

                    # 添加延迟避免API限制
                    await asyncio.sleep(0.1)

                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"处理股票 {stock_code} 时出错: {str(e)}")

            return results

        except Exception as e:
            logger.error(f"批量更新股票详细信息失败: {e}")
            return {"total": 0, "success": 0, "failed": 0, "errors": [str(e)]}

    async def update_all_spot_data(self) -> Dict[str, Any]:
        """更新所有实时行情数据"""
        try:
            logger.info("🚀 开始更新所有实时行情数据")
            results = {
                "total": 0,
                "success": 0,
                "failed": 0,
                "errors": []
            }

            # 获取沪深A股实时数据
            logger.info("📡 调用fetch_spot_data获取实时数据")
            spot_data = await self.fetch_spot_data("all")
            results["total"] = len(spot_data)
            logger.info(f"📊 获取到 {len(spot_data)} 条实时数据")

            if spot_data:
                # 检查是否是真实数据
                first_stock = spot_data[0]
                if first_stock.get("current_price") != 10.5:
                    logger.info("🎉 获取到真实数据，开始保存到数据库")
                else:
                    logger.warning("⚠️  获取到的仍然是模拟数据")

                logger.info("💾 开始保存实时行情数据到数据库")
                success = await self.save_spot_data(spot_data)
                if success:
                    results["success"] = len(spot_data)
                    logger.info(f"✅ 成功保存 {len(spot_data)} 条实时行情数据")
                else:
                    results["failed"] = len(spot_data)
                    results["errors"].append("保存实时行情数据失败")
                    logger.error("❌ 保存实时行情数据失败")
            else:
                logger.warning("⚠️  没有获取到实时数据")

            logger.info(f"📈 实时行情数据更新完成: {results}")
            return results

        except Exception as e:
            logger.error(f"❌ 更新实时行情数据失败: {e}", exc_info=True)
            return {"total": 0, "success": 0, "failed": 0, "errors": [str(e)]}

    async def update_all_sector_data(self) -> Dict[str, Any]:
        """更新所有板块数据"""
        try:
            results = {
                "total": 0,
                "success": 0,
                "failed": 0,
                "errors": []
            }

            # 更新不同类型的板块数据
            sector_types = ["industry", "concept", "region"]

            for sector_type in sector_types:
                try:
                    sector_data = await self.fetch_sector_data(sector_type)
                    results["total"] += len(sector_data)

                    if sector_data:
                        success = await self.save_sector_data(sector_data)
                        if success:
                            results["success"] += len(sector_data)
                        else:
                            results["failed"] += len(sector_data)
                            results["errors"].append(f"保存 {sector_type} 板块数据失败")

                    await asyncio.sleep(0.5)  # 添加延迟

                except Exception as e:
                    results["errors"].append(f"处理 {sector_type} 板块数据时出错: {str(e)}")

            return results

        except Exception as e:
            logger.error(f"更新板块数据失败: {e}")
            return {"total": 0, "success": 0, "failed": 0, "errors": [str(e)]}
