"""
专业级股票数据库初始化脚本
创建所有必要的表和初始数据
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from loguru import logger

from app.models.professional_stock_db import Base
from app.core.config import settings

# 创建异步数据库引擎
engine = create_async_engine(
    "sqlite+aiosqlite:///./professional_stock_data.db",
    echo=True
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

async def create_tables():
    """创建所有数据表"""
    try:
        logger.info("开始创建数据表...")
        
        async with engine.begin() as conn:
            # 删除所有现有表（如果存在）
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("已删除现有表")
            
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("已创建所有数据表")
            
        return True
        
    except Exception as e:
        logger.error(f"创建数据表失败: {e}")
        return False

async def init_basic_data():
    """初始化基础数据"""
    try:
        logger.info("开始初始化基础数据...")
        
        async with AsyncSessionLocal() as session:
            # 检查是否已有数据
            result = await session.execute(text("SELECT COUNT(*) FROM stock_basic_info"))
            count = result.scalar()
            
            if count > 0:
                logger.info(f"数据库已有 {count} 条股票基础信息，跳过初始化")
                return True
            
            # 插入示例股票数据
            sample_stocks = [
                {
                    'stock_code': '000001',
                    'stock_name': '平安银行',
                    'exchange': 'SZ',
                    'industry': '银行',
                    'sector': '金融',
                    'list_date': '1991-04-03',
                    'status': 'active',
                    'market_cap': 250000000000.00,
                    'float_market_cap': 200000000000.00,
                    'total_shares': 19405918198.00,
                    'float_shares': 15524734558.00
                },
                {
                    'stock_code': '000002',
                    'stock_name': '万科A',
                    'exchange': 'SZ',
                    'industry': '房地产',
                    'sector': '房地产',
                    'list_date': '1991-01-29',
                    'status': 'active',
                    'market_cap': 180000000000.00,
                    'float_market_cap': 160000000000.00,
                    'total_shares': 11039152123.00,
                    'float_shares': 9831236911.00
                },
                {
                    'stock_code': '600000',
                    'stock_name': '浦发银行',
                    'exchange': 'SH',
                    'industry': '银行',
                    'sector': '金融',
                    'list_date': '1999-11-10',
                    'status': 'active',
                    'market_cap': 220000000000.00,
                    'float_market_cap': 190000000000.00,
                    'total_shares': 29352050819.00,
                    'float_shares': 25449243697.00
                },
                {
                    'stock_code': '600036',
                    'stock_name': '招商银行',
                    'exchange': 'SH',
                    'industry': '银行',
                    'sector': '金融',
                    'list_date': '2002-04-09',
                    'status': 'active',
                    'market_cap': 1200000000000.00,
                    'float_market_cap': 1100000000000.00,
                    'total_shares': 25220645819.00,
                    'float_shares': 23698581237.00
                },
                {
                    'stock_code': '600519',
                    'stock_name': '贵州茅台',
                    'exchange': 'SH',
                    'industry': '食品饮料',
                    'sector': '消费',
                    'list_date': '2001-08-27',
                    'status': 'active',
                    'market_cap': 2500000000000.00,
                    'float_market_cap': 2300000000000.00,
                    'total_shares': 1256197800.00,
                    'float_shares': 1151577800.00
                }
            ]
            
            # 插入股票基础信息
            for stock_data in sample_stocks:
                insert_sql = """
                INSERT INTO stock_basic_info (
                    stock_code, stock_name, exchange, industry, sector, 
                    list_date, status, market_cap, float_market_cap, 
                    total_shares, float_shares
                ) VALUES (
                    :stock_code, :stock_name, :exchange, :industry, :sector,
                    :list_date, :status, :market_cap, :float_market_cap,
                    :total_shares, :float_shares
                )
                """
                await session.execute(text(insert_sql), stock_data)
            
            # 插入板块信息
            sector_data = [
                {
                    'sector_code': 'BANK',
                    'sector_name': '银行',
                    'sector_type': 'industry',
                    'level': 1,
                    'description': '银行业',
                    'stock_count': 3
                },
                {
                    'sector_code': 'REALESTATE',
                    'sector_name': '房地产',
                    'sector_type': 'industry',
                    'level': 1,
                    'description': '房地产业',
                    'stock_count': 1
                },
                {
                    'sector_code': 'FOOD',
                    'sector_name': '食品饮料',
                    'sector_type': 'industry',
                    'level': 1,
                    'description': '食品饮料业',
                    'stock_count': 1
                }
            ]
            
            for sector in sector_data:
                insert_sql = """
                INSERT INTO sector_info (
                    sector_code, sector_name, sector_type, level, 
                    description, stock_count
                ) VALUES (
                    :sector_code, :sector_name, :sector_type, :level,
                    :description, :stock_count
                )
                """
                await session.execute(text(insert_sql), sector)
            
            # 插入股票板块关联
            mapping_data = [
                {'stock_code': '000001', 'sector_code': 'BANK', 'join_date': '1991-04-03'},
                {'stock_code': '600000', 'sector_code': 'BANK', 'join_date': '1999-11-10'},
                {'stock_code': '600036', 'sector_code': 'BANK', 'join_date': '2002-04-09'},
                {'stock_code': '000002', 'sector_code': 'REALESTATE', 'join_date': '1991-01-29'},
                {'stock_code': '600519', 'sector_code': 'FOOD', 'join_date': '2001-08-27'},
            ]
            
            for mapping in mapping_data:
                insert_sql = """
                INSERT INTO stock_sector_mapping (
                    stock_code, sector_code, join_date, status
                ) VALUES (
                    :stock_code, :sector_code, :join_date, 'active'
                )
                """
                await session.execute(text(insert_sql), mapping)
            
            # 插入初始更新日志
            log_data = {
                'data_type': 'database_init',
                'status': 'success',
                'records_count': len(sample_stocks),
                'source': 'init_script',
                'duration': 0.0
            }
            
            insert_sql = """
            INSERT INTO data_update_logs (
                data_type, status, records_count, source, duration
            ) VALUES (
                :data_type, :status, :records_count, :source, :duration
            )
            """
            await session.execute(text(insert_sql), log_data)
            
            await session.commit()
            logger.info(f"成功初始化 {len(sample_stocks)} 条股票基础数据")
            
        return True
        
    except Exception as e:
        logger.error(f"初始化基础数据失败: {e}")
        return False

async def verify_database():
    """验证数据库创建结果"""
    try:
        logger.info("开始验证数据库...")
        
        async with AsyncSessionLocal() as session:
            # 检查表是否存在并有数据
            tables_to_check = [
                'stock_basic_info',
                'sector_info', 
                'stock_sector_mapping',
                'data_update_logs'
            ]
            
            for table_name in tables_to_check:
                result = await session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                logger.info(f"表 {table_name}: {count} 条记录")
            
            # 检查具体数据
            result = await session.execute(text("""
                SELECT stock_code, stock_name, exchange, industry 
                FROM stock_basic_info 
                LIMIT 5
            """))
            stocks = result.fetchall()
            
            logger.info("股票数据示例:")
            for stock in stocks:
                logger.info(f"  {stock[0]} - {stock[1]} ({stock[2]}) - {stock[3]}")
            
        logger.info("数据库验证完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库验证失败: {e}")
        return False

async def main():
    """主函数"""
    logger.info("开始初始化专业级股票数据库...")
    
    try:
        # 1. 创建数据表
        if not await create_tables():
            logger.error("创建数据表失败，退出")
            return False
        
        # 2. 初始化基础数据
        if not await init_basic_data():
            logger.error("初始化基础数据失败，退出")
            return False
        
        # 3. 验证数据库
        if not await verify_database():
            logger.error("数据库验证失败，退出")
            return False
        
        logger.info("专业级股票数据库初始化完成！")
        logger.info("数据库文件: professional_stock_data.db")
        logger.info("可以开始使用专业数据库管理系统了")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化过程中发生错误: {e}")
        return False
    
    finally:
        # 关闭数据库连接
        await engine.dispose()

if __name__ == "__main__":
    # 运行初始化
    success = asyncio.run(main())
    
    if success:
        print("\n✅ 数据库初始化成功！")
        print("🚀 现在可以启动后端服务并访问专业数据库管理系统")
    else:
        print("\n❌ 数据库初始化失败！")
        print("请检查错误日志并重试")
        sys.exit(1)
