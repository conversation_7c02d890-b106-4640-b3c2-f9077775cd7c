import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  Switch,
  Button,
  Select,
  InputNumber,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  Alert,
  message,
  Tabs,
  Badge,
  Tooltip,
  Modal,
  List,
  Progress,
  Statistic,
  Table,
  Tag,
  Timeline,
  DatePicker,
  Spin,
  Empty,
} from 'antd'
import {
  SettingOutlined,
  DatabaseOutlined,
  CloudOutlined,
  SaveOutlined,
  ReloadOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ApiOutlined,
  BarChartOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileTextOutlined,
  LineChartOutlined,
  MonitorOutlined,
  WarningOutlined,
} from '@ant-design/icons'
import { CONFIG, getConfigInfo, validateConfig } from '@/config/dataConfig'
import { stockDataService } from '@/services/stockDataService'
import { useStockDataStore } from '@/stores/stockDataStore'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { confirm } = Modal
const { RangePicker } = DatePicker

// 数据接口配置
interface DataAPIConfig {
  id: string
  name: string
  type: 'akshare' | 'tushare' | 'sina'
  enabled: boolean
  apiKey?: string
  endpoint?: string
  rateLimit: number
  timeout: number
  priority: number
  status: 'connected' | 'disconnected' | 'error'
  lastUpdate?: string
  dataCount?: number
}

// 数据库状态
interface DatabaseStatus {
  totalStocks: number
  todayUpdates: number
  lastUpdateTime: string
  dataSize: string
  newsCount: number
  newsUpdatedToday: number
}

// 更新任务
interface UpdateTask {
  id: string
  name: string
  type: 'stock_basic' | 'stock_price' | 'financial_news' | 'technical_indicators'
  status: 'running' | 'completed' | 'failed' | 'pending'
  progress: number
  startTime?: string
  endTime?: string
  recordsProcessed?: number
  totalRecords?: number
  errorMessage?: string
}

interface DataSourceSettings {
  useRealAPI: boolean
  apiBaseUrl: string
  wsUrl: string
  timeout: number
  retryCount: number
}

interface CacheSettings {
  stockDataTTL: number
  priceDataTTL: number
  indicatorsTTL: number
  maxCacheSize: number
  enableCache: boolean
}

interface MockDataSettings {
  networkDelay: number
  simulateErrors: boolean
  errorProbability: number
  defaultStocks: string[]
}

interface PerformanceSettings {
  debounceDelay: number
  throttleDelay: number
  autoRefreshInterval: number
  maxSearchHistory: number
}

const DataSettings: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('datasource')
  const [configInfo, setConfigInfo] = useState(getConfigInfo())
  const [isConfigValid, setIsConfigValid] = useState(true)

  // 从store获取当前设置
  const { clearCache } = useStockDataStore()
  const dataSourceSettings = useDataSourceSettings()
  const cacheSettings = useCacheSettings()
  const mockDataSettings = useMockDataSettings()

  const {
    updateDataSourceSettings,
    updateCacheSettings,
    updateMockDataSettings,
    resetToDefaults,
    validateSettings,
    exportSettings,
    importSettings
  } = useSettingsActions()

  useEffect(() => {
    // 验证配置
    const validation = validateSettings()
    setIsConfigValid(validation.isValid)
    setConfigInfo(getConfigInfo())

    if (!validation.isValid) {
      console.warn('设置验证失败:', validation.errors)
    }
  }, [dataSourceSettings, cacheSettings, mockDataSettings])

  // 保存设置
  const handleSave = async () => {
    try {
      setLoading(true)

      // 验证设置
      const validation = validateSettings()
      if (!validation.isValid) {
        message.error(`设置验证失败: ${validation.errors.join(', ')}`)
        return
      }

      // 模拟保存延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      message.success('设置已保存成功！')

      // 更新配置信息
      setConfigInfo(getConfigInfo())

    } catch (error) {
      message.error('保存设置失败，请重试')
      console.error('Save settings error:', error)
    } finally {
      setLoading(false)
    }
  }

  // 重置设置
  const handleReset = () => {
    confirm({
      title: '确认重置',
      icon: <ExclamationCircleOutlined />,
      content: '这将重置所有设置为默认值，确定继续吗？',
      onOk() {
        resetToDefaults()
        message.success('设置已重置为默认值')
      },
    })
  }

  // 清除缓存
  const handleClearCache = () => {
    confirm({
      title: '确认清除缓存',
      icon: <ExclamationCircleOutlined />,
      content: '这将清除所有缓存数据，下次访问时需要重新加载，确定继续吗？',
      onOk() {
        stockDataService.clearCache()
        clearCache()
        localStorage.removeItem('stock-data-store')
        message.success('缓存已清除')
      },
    })
  }

  // 测试连接
  const handleTestConnection = async () => {
    if (!dataSourceSettings.useRealAPI) {
      message.info('当前使用模拟数据，无需测试连接')
      return
    }
    
    setLoading(true)
    try {
      // 这里可以调用API测试连接
      await new Promise(resolve => setTimeout(resolve, 2000))
      message.success('API连接测试成功！')
    } catch (error) {
      message.error('API连接测试失败，请检查设置')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <SettingOutlined /> 数据设置
      </Title>
      
      {/* 当前配置概览 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Badge 
                status={configInfo.dataSource === 'Real API' ? 'success' : 'default'} 
                text={configInfo.dataSource} 
              />
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">数据源</Text>
              </div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Badge 
                status={isConfigValid ? 'success' : 'error'} 
                text={isConfigValid ? '配置正常' : '配置异常'} 
              />
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">配置状态</Text>
              </div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Badge 
                status={configInfo.cacheEnabled ? 'success' : 'default'} 
                text={configInfo.cacheEnabled ? '已启用' : '已禁用'} 
              />
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">缓存状态</Text>
              </div>
            </div>
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Badge 
                status={configInfo.debugMode ? 'processing' : 'default'} 
                text={configInfo.debugMode ? '调试模式' : '生产模式'} 
              />
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">运行模式</Text>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 设置选项卡 */}
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        items={[
          {
            key: 'datasource',
            label: (
              <span>
                <DatabaseOutlined />
                数据源设置
              </span>
            ),
            children: (
              <Card>
                <Form layout="vertical">
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Form.Item 
                        label="数据源类型" 
                        tooltip="选择使用真实API还是模拟数据"
                      >
                        <Switch
                          checked={dataSourceSettings.useRealAPI}
                          onChange={(checked) => updateDataSourceSettings({ useRealAPI: checked })}
                          checkedChildren="真实API"
                          unCheckedChildren="模拟数据"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="API基础URL">
                        <Input
                          value={dataSourceSettings.apiBaseUrl}
                          onChange={(e) => updateDataSourceSettings({ apiBaseUrl: e.target.value })}
                          placeholder="http://localhost:8000/api"
                          disabled={!dataSourceSettings.useRealAPI}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="WebSocket URL">
                        <Input
                          value={dataSourceSettings.wsUrl}
                          onChange={(e) => updateDataSourceSettings({ wsUrl: e.target.value })}
                          placeholder="ws://localhost:8000/ws"
                          disabled={!dataSourceSettings.useRealAPI}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="请求超时时间 (毫秒)">
                        <InputNumber
                          value={dataSourceSettings.timeout}
                          onChange={(value) => updateDataSourceSettings({ timeout: value || 30000 })}
                          min={1000}
                          max={120000}
                          style={{ width: '100%' }}
                          disabled={!dataSourceSettings.useRealAPI}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="重试次数">
                        <InputNumber
                          value={dataSourceSettings.retryCount}
                          onChange={(value) => updateDataSourceSettings({ retryCount: value || 3 })}
                          min={0}
                          max={10}
                          style={{ width: '100%' }}
                          disabled={!dataSourceSettings.useRealAPI}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item>
                        <Button 
                          type="primary" 
                          onClick={handleTestConnection}
                          loading={loading}
                          disabled={!dataSourceSettings.useRealAPI}
                        >
                          <CloudOutlined /> 测试连接
                        </Button>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </Card>
            )
          },
          {
            key: 'cache',
            label: (
              <span>
                <DatabaseOutlined />
                缓存设置
              </span>
            ),
            children: (
              <Card>
                <Form layout="vertical">
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Form.Item label="启用缓存">
                        <Switch
                          checked={cacheSettings.enableCache}
                          onChange={(checked) => updateCacheSettings({ enableCache: checked })}
                          checkedChildren="启用"
                          unCheckedChildren="禁用"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="股票数据缓存时间 (分钟)">
                        <InputNumber
                          value={cacheSettings.stockDataTTL}
                          onChange={(value) => setCacheSettings(prev => ({ ...prev, stockDataTTL: value || 5 }))}
                          min={1}
                          max={60}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="价格数据缓存时间 (分钟)">
                        <InputNumber
                          value={cacheSettings.priceDataTTL}
                          onChange={(value) => setCacheSettings(prev => ({ ...prev, priceDataTTL: value || 1 }))}
                          min={1}
                          max={30}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="技术指标缓存时间 (分钟)">
                        <InputNumber
                          value={cacheSettings.indicatorsTTL}
                          onChange={(value) => setCacheSettings(prev => ({ ...prev, indicatorsTTL: value || 10 }))}
                          min={1}
                          max={120}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="最大缓存条目数">
                        <InputNumber
                          value={cacheSettings.maxCacheSize}
                          onChange={(value) => setCacheSettings(prev => ({ ...prev, maxCacheSize: value || 100 }))}
                          min={10}
                          max={1000}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item>
                        <Button 
                          danger 
                          onClick={handleClearCache}
                        >
                          <DeleteOutlined /> 清除所有缓存
                        </Button>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </Card>
            )
          }
        ]}
      />

      {/* 操作按钮 */}
      <Card style={{ marginTop: '24px' }}>
        <Space>
          <Button 
            type="primary" 
            icon={<SaveOutlined />} 
            onClick={handleSave}
            loading={loading}
          >
            保存设置
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
          >
            重置为默认
          </Button>
          <Button
            onClick={() => {
              const settings = exportSettings()
              const blob = new Blob([settings], { type: 'application/json' })
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = `stock-analysis-settings-${new Date().toISOString().split('T')[0]}.json`
              a.click()
              URL.revokeObjectURL(url)
              message.success('设置已导出')
            }}
          >
            导出设置
          </Button>
          <Button
            onClick={() => {
              const input = document.createElement('input')
              input.type = 'file'
              input.accept = '.json'
              input.onchange = (e) => {
                const file = (e.target as HTMLInputElement).files?.[0]
                if (file) {
                  const reader = new FileReader()
                  reader.onload = (e) => {
                    const content = e.target?.result as string
                    if (importSettings(content)) {
                      message.success('设置导入成功')
                    } else {
                      message.error('设置导入失败，请检查文件格式')
                    }
                  }
                  reader.readAsText(file)
                }
              }
              input.click()
            }}
          >
            导入设置
          </Button>
          <Button 
            type="link" 
            icon={<InfoCircleOutlined />}
            onClick={() => {
              Modal.info({
                title: '配置信息',
                content: (
                  <div>
                    <p><strong>环境:</strong> {configInfo.environment}</p>
                    <p><strong>数据源:</strong> {configInfo.dataSource}</p>
                    <p><strong>API地址:</strong> {configInfo.apiUrl}</p>
                    <p><strong>WebSocket:</strong> {configInfo.wsUrl}</p>
                    <p><strong>调试模式:</strong> {configInfo.debugMode ? '是' : '否'}</p>
                  </div>
                ),
              })
            }}
          >
            查看配置信息
          </Button>
        </Space>
      </Card>

      {/* 配置状态提示 */}
      {!isConfigValid && (
        <Alert
          message="配置验证失败"
          description="当前配置存在问题，请检查设置并保存"
          type="error"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}
    </div>
  )
}

export default DataSettings
