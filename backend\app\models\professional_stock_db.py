"""
专业级股票数据库模型
支持完整的A股数据管理
"""

from sqlalchemy import Column, Integer, String, Float, Date, DateTime, Text, Boolean, ForeignKey, Index, DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Optional
from datetime import date, datetime

Base = declarative_base()

# ================================
# 1. 核心基础表
# ================================

class StockBasicInfo(Base):
    """股票基础信息表 - 核心主表"""
    __tablename__ = "stock_basic_info"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), unique=True, index=True, comment="股票代码")
    stock_name = Column(String(50), comment="股票名称")
    exchange = Column(String(10), comment="交易所: SH/SZ")
    industry = Column(String(50), comment="所属行业")
    sector = Column(String(50), comment="所属板块")
    list_date = Column(Date, comment="上市日期")
    delist_date = Column(Date, nullable=True, comment="退市日期")
    status = Column(String(20), default="active", comment="状态: active/delisted/suspended")
    market_cap = Column(DECIMAL(20, 2), comment="总市值")
    float_market_cap = Column(DECIMAL(20, 2), comment="流通市值")
    total_shares = Column(DECIMAL(15, 2), comment="总股本")
    float_shares = Column(DECIMAL(15, 2), comment="流通股本")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    kline_data = relationship("StockKlineData", back_populates="stock")
    realtime_data = relationship("StockRealtimeData", back_populates="stock")
    financial_data = relationship("StockFinancialData", back_populates="stock")
    
    __table_args__ = (
        Index('idx_stock_exchange_status', 'exchange', 'status'),
        Index('idx_stock_industry', 'industry'),
    )

class DataUpdateLog(Base):
    """数据更新日志表"""
    __tablename__ = "data_update_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    data_type = Column(String(50), comment="数据类型")
    stock_code = Column(String(10), nullable=True, comment="股票代码")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")
    status = Column(String(20), comment="更新状态: success/failed/partial")
    records_count = Column(Integer, default=0, comment="更新记录数")
    error_message = Column(Text, nullable=True, comment="错误信息")
    source = Column(String(50), comment="数据源")
    duration = Column(Float, comment="更新耗时(秒)")
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_update_log_type_time', 'data_type', 'update_time'),
        Index('idx_update_log_stock', 'stock_code'),
    )

# ================================
# 2. 交易数据表
# ================================

class StockKlineData(Base):
    """K线数据表 - 支持多周期"""
    __tablename__ = "stock_kline_data"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), ForeignKey('stock_basic_info.stock_code'), comment="股票代码")
    trade_date = Column(Date, comment="交易日期")
    period = Column(String(10), comment="周期: 1m/5m/15m/30m/1h/1d/1w/1M")
    open_price = Column(DECIMAL(10, 3), comment="开盘价")
    high_price = Column(DECIMAL(10, 3), comment="最高价")
    low_price = Column(DECIMAL(10, 3), comment="最低价")
    close_price = Column(DECIMAL(10, 3), comment="收盘价")
    volume = Column(DECIMAL(15, 2), comment="成交量")
    turnover = Column(DECIMAL(20, 2), comment="成交额")
    change_amount = Column(DECIMAL(10, 3), comment="涨跌额")
    change_pct = Column(DECIMAL(8, 4), comment="涨跌幅(%)")
    turnover_rate = Column(DECIMAL(8, 4), comment="换手率(%)")
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    stock = relationship("StockBasicInfo", back_populates="kline_data")
    
    __table_args__ = (
        Index('idx_kline_stock_date_period', 'stock_code', 'trade_date', 'period'),
        Index('idx_kline_date', 'trade_date'),
    )

class StockRealtimeData(Base):
    """实时行情表"""
    __tablename__ = "stock_realtime_data"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), ForeignKey('stock_basic_info.stock_code'), comment="股票代码")
    current_price = Column(DECIMAL(10, 3), comment="当前价")
    change_amount = Column(DECIMAL(10, 3), comment="涨跌额")
    change_pct = Column(DECIMAL(8, 4), comment="涨跌幅(%)")
    volume = Column(DECIMAL(15, 2), comment="成交量")
    turnover = Column(DECIMAL(20, 2), comment="成交额")
    turnover_rate = Column(DECIMAL(8, 4), comment="换手率(%)")
    bid_price = Column(DECIMAL(10, 3), comment="买一价")
    ask_price = Column(DECIMAL(10, 3), comment="卖一价")
    bid_volume = Column(DECIMAL(12, 2), comment="买一量")
    ask_volume = Column(DECIMAL(12, 2), comment="卖一量")
    high_price = Column(DECIMAL(10, 3), comment="今日最高")
    low_price = Column(DECIMAL(10, 3), comment="今日最低")
    open_price = Column(DECIMAL(10, 3), comment="今日开盘")
    pre_close = Column(DECIMAL(10, 3), comment="昨收价")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    stock = relationship("StockBasicInfo", back_populates="realtime_data")
    
    __table_args__ = (
        Index('idx_realtime_stock', 'stock_code'),
        Index('idx_realtime_update_time', 'update_time'),
    )

# ================================
# 3. 情绪数据表
# ================================

class StockMoneyFlow(Base):
    """资金流向表"""
    __tablename__ = "stock_money_flow"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), comment="股票代码")
    trade_date = Column(Date, comment="交易日期")
    main_inflow = Column(DECIMAL(20, 2), comment="主力流入")
    main_outflow = Column(DECIMAL(20, 2), comment="主力流出")
    main_net_inflow = Column(DECIMAL(20, 2), comment="主力净流入")
    retail_inflow = Column(DECIMAL(20, 2), comment="散户流入")
    retail_outflow = Column(DECIMAL(20, 2), comment="散户流出")
    retail_net_inflow = Column(DECIMAL(20, 2), comment="散户净流入")
    super_large_inflow = Column(DECIMAL(20, 2), comment="超大单流入")
    super_large_outflow = Column(DECIMAL(20, 2), comment="超大单流出")
    large_inflow = Column(DECIMAL(20, 2), comment="大单流入")
    large_outflow = Column(DECIMAL(20, 2), comment="大单流出")
    medium_inflow = Column(DECIMAL(20, 2), comment="中单流入")
    medium_outflow = Column(DECIMAL(20, 2), comment="中单流出")
    small_inflow = Column(DECIMAL(20, 2), comment="小单流入")
    small_outflow = Column(DECIMAL(20, 2), comment="小单流出")
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_money_flow_stock_date', 'stock_code', 'trade_date'),
    )

class NorthboundCapital(Base):
    """北向资金表"""
    __tablename__ = "northbound_capital"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), comment="股票代码")
    trade_date = Column(Date, comment="交易日期")
    buy_amount = Column(DECIMAL(20, 2), comment="买入金额")
    sell_amount = Column(DECIMAL(20, 2), comment="卖出金额")
    net_amount = Column(DECIMAL(20, 2), comment="净买入金额")
    holding_amount = Column(DECIMAL(20, 2), comment="持股金额")
    holding_shares = Column(DECIMAL(15, 2), comment="持股数量")
    holding_ratio = Column(DECIMAL(8, 4), comment="持股比例(%)")
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_northbound_stock_date', 'stock_code', 'trade_date'),
    )

# ================================
# 4. 板块数据表
# ================================

class SectorInfo(Base):
    """板块信息表"""
    __tablename__ = "sector_info"
    
    id = Column(Integer, primary_key=True, index=True)
    sector_code = Column(String(20), unique=True, comment="板块代码")
    sector_name = Column(String(100), comment="板块名称")
    sector_type = Column(String(20), comment="板块类型: industry/concept/region")
    parent_sector = Column(String(20), nullable=True, comment="父板块代码")
    level = Column(Integer, default=1, comment="板块层级")
    description = Column(Text, nullable=True, comment="板块描述")
    stock_count = Column(Integer, default=0, comment="包含股票数")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_sector_type', 'sector_type'),
        Index('idx_sector_parent', 'parent_sector'),
    )

class StockSectorMapping(Base):
    """股票板块关联表"""
    __tablename__ = "stock_sector_mapping"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), comment="股票代码")
    sector_code = Column(String(20), comment="板块代码")
    weight = Column(DECIMAL(8, 4), nullable=True, comment="权重")
    join_date = Column(Date, comment="加入日期")
    leave_date = Column(Date, nullable=True, comment="离开日期")
    status = Column(String(20), default="active", comment="状态")
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_stock_sector_mapping', 'stock_code', 'sector_code'),
    )

# ================================
# 5. 基本面数据表
# ================================

class StockFinancialData(Base):
    """财务数据表"""
    __tablename__ = "stock_financial_data"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), ForeignKey('stock_basic_info.stock_code'), comment="股票代码")
    report_date = Column(Date, comment="报告期")
    report_type = Column(String(10), comment="报告类型: Q1/Q2/Q3/Q4")
    announce_date = Column(Date, comment="公告日期")
    
    # 利润表数据
    revenue = Column(DECIMAL(20, 2), comment="营业收入")
    net_profit = Column(DECIMAL(20, 2), comment="净利润")
    gross_profit = Column(DECIMAL(20, 2), comment="毛利润")
    operating_profit = Column(DECIMAL(20, 2), comment="营业利润")
    
    # 资产负债表数据
    total_assets = Column(DECIMAL(20, 2), comment="总资产")
    total_equity = Column(DECIMAL(20, 2), comment="股东权益")
    total_liabilities = Column(DECIMAL(20, 2), comment="总负债")
    current_assets = Column(DECIMAL(20, 2), comment="流动资产")
    current_liabilities = Column(DECIMAL(20, 2), comment="流动负债")
    
    # 现金流量表数据
    operating_cash_flow = Column(DECIMAL(20, 2), comment="经营现金流")
    investing_cash_flow = Column(DECIMAL(20, 2), comment="投资现金流")
    financing_cash_flow = Column(DECIMAL(20, 2), comment="筹资现金流")
    
    # 财务比率
    roe = Column(DECIMAL(8, 4), comment="净资产收益率")
    roa = Column(DECIMAL(8, 4), comment="总资产收益率")
    debt_ratio = Column(DECIMAL(8, 4), comment="资产负债率")
    current_ratio = Column(DECIMAL(8, 4), comment="流动比率")
    quick_ratio = Column(DECIMAL(8, 4), comment="速动比率")
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    stock = relationship("StockBasicInfo", back_populates="financial_data")
    
    __table_args__ = (
        Index('idx_financial_stock_date', 'stock_code', 'report_date'),
    )

class CompanyInfo(Base):
    """公司信息表"""
    __tablename__ = "company_info"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String(10), unique=True, comment="股票代码")
    company_name = Column(String(200), comment="公司名称")
    english_name = Column(String(200), nullable=True, comment="英文名称")
    legal_representative = Column(String(50), comment="法定代表人")
    registered_capital = Column(DECIMAL(20, 2), comment="注册资本")
    established_date = Column(Date, comment="成立日期")
    business_scope = Column(Text, comment="经营范围")
    main_business = Column(Text, comment="主营业务")
    address = Column(String(500), comment="注册地址")
    office_address = Column(String(500), comment="办公地址")
    website = Column(String(200), nullable=True, comment="公司网站")
    email = Column(String(100), nullable=True, comment="联系邮箱")
    phone = Column(String(50), nullable=True, comment="联系电话")
    employee_count = Column(Integer, comment="员工人数")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_company_name', 'company_name'),
    )

# ================================
# 6. 自定义功能表
# ================================

class CustomIndicator(Base):
    """自定义技术指标表"""
    __tablename__ = "custom_indicators"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), comment="指标名称")
    description = Column(Text, comment="指标描述")
    formula = Column(Text, comment="计算公式")
    parameters = Column(Text, comment="参数配置(JSON)")
    data_requirements = Column(Text, comment="数据需求(JSON)")
    chart_config = Column(Text, comment="图表配置(JSON)")
    color_scheme = Column(Text, comment="颜色方案(JSON)")
    category = Column(String(50), comment="指标分类")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_by = Column(String(50), nullable=True, comment="创建者")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('idx_indicator_category', 'category'),
    )

class StockPattern(Base):
    """股票形态表"""
    __tablename__ = "stock_patterns"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), comment="形态名称")
    description = Column(Text, comment="形态描述")
    pattern_type = Column(String(50), comment="形态类型")
    recognition_rules = Column(Text, comment="识别规则(JSON)")
    success_rate = Column(DECIMAL(5, 2), comment="成功率(%)")
    risk_level = Column(String(20), comment="风险等级")
    time_frame = Column(String(20), comment="适用时间框架")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_by = Column(String(50), nullable=True, comment="创建者")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class BacktestStrategy(Base):
    """回测策略表"""
    __tablename__ = "backtest_strategies"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), comment="策略名称")
    description = Column(Text, comment="策略描述")
    strategy_code = Column(Text, comment="策略代码")
    parameters = Column(Text, comment="策略参数(JSON)")
    entry_conditions = Column(Text, comment="入场条件(JSON)")
    exit_conditions = Column(Text, comment="出场条件(JSON)")
    risk_management = Column(Text, comment="风险管理(JSON)")
    backtest_results = Column(Text, nullable=True, comment="回测结果(JSON)")
    performance_metrics = Column(Text, nullable=True, comment="性能指标(JSON)")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_by = Column(String(50), nullable=True, comment="创建者")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('idx_strategy_active', 'is_active'),
    )
