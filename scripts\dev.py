#!/usr/bin/env python3
"""
开发环境启动脚本
"""

import subprocess
import sys
import os
import time
from pathlib import Path


def run_command(command, cwd=None, background=False):
    """运行命令"""
    print(f"Running: {command}")
    if background:
        return subprocess.Popen(command, shell=True, cwd=cwd)
    else:
        result = subprocess.run(command, shell=True, cwd=cwd)
        return result.returncode == 0


def check_dependencies():
    """检查依赖是否安装"""
    print("检查依赖...")
    
    # 检查Python依赖
    backend_dir = Path(__file__).parent.parent / "backend"
    if not (backend_dir / "venv").exists():
        print("Python虚拟环境不存在，请先运行: python -m venv backend/venv")
        return False
    
    # 检查Node.js依赖
    frontend_dir = Path(__file__).parent.parent / "frontend"
    if not (frontend_dir / "node_modules").exists():
        print("Node.js依赖未安装，请先运行: cd frontend && npm install")
        return False
    
    return True


def start_backend():
    """启动后端服务"""
    print("启动后端服务...")
    backend_dir = Path(__file__).parent.parent / "backend"
    
    # Windows
    if os.name == 'nt':
        activate_cmd = "venv\\Scripts\\activate"
        python_cmd = "venv\\Scripts\\python"
    else:
        activate_cmd = "source venv/bin/activate"
        python_cmd = "venv/bin/python"
    
    command = f"{python_cmd} -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    return run_command(command, cwd=backend_dir, background=True)


def start_frontend():
    """启动前端服务"""
    print("启动前端服务...")
    frontend_dir = Path(__file__).parent.parent / "frontend"
    command = "npm run dev"
    return run_command(command, cwd=frontend_dir, background=True)


def main():
    """主函数"""
    print("🚀 启动A股智能分析系统开发环境")
    
    if not check_dependencies():
        sys.exit(1)
    
    processes = []
    
    try:
        # 启动后端
        backend_process = start_backend()
        if backend_process:
            processes.append(backend_process)
            print("✅ 后端服务启动成功 (http://localhost:8000)")
        
        # 等待后端启动
        time.sleep(3)
        
        # 启动前端
        frontend_process = start_frontend()
        if frontend_process:
            processes.append(frontend_process)
            print("✅ 前端服务启动成功 (http://localhost:3000)")
        
        print("\n🎉 开发环境启动完成!")
        print("📖 API文档: http://localhost:8000/docs")
        print("🌐 前端应用: http://localhost:3000")
        print("\n按 Ctrl+C 停止所有服务")
        
        # 等待用户中断
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        for process in processes:
            process.terminate()
        print("✅ 所有服务已停止")


if __name__ == "__main__":
    main()
