"""
数据分类管理API
提供细粒度的数据分类管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel
from loguru import logger

from app.api.professional_db_management import get_professional_db
from app.models.professional_stock_db import (
    StockBasicInfo, StockKlineData, StockRealtimeData, StockMoneyFlow,
    NorthboundCapital, SectorInfo, StockSectorMapping, StockFinancialData,
    CompanyInfo, DataUpdateLog
)

router = APIRouter()

# 数据分类定义
DATA_CATEGORIES = {
    "trading": {
        "name": "交易数据",
        "description": "股票交易相关的价格、成交量等数据",
        "subcategories": {
            "kline": {"name": "K线数据", "table": "stock_kline_data", "model": StockKlineData},
            "realtime": {"name": "实时行情", "table": "stock_realtime_data", "model": StockRealtimeData},
            "money_flow": {"name": "资金流向", "table": "stock_money_flow", "model": StockMoneyFlow},
            "northbound": {"name": "北向资金", "table": "northbound_capital", "model": NorthboundCapital}
        }
    },
    "fundamental": {
        "name": "基本面数据", 
        "description": "公司基本信息、财务数据等",
        "subcategories": {
            "basic_info": {"name": "基础信息", "table": "stock_basic_info", "model": StockBasicInfo},
            "financial": {"name": "财务数据", "table": "stock_financial_data", "model": StockFinancialData},
            "company": {"name": "公司信息", "table": "company_info", "model": CompanyInfo}
        }
    },
    "sector": {
        "name": "板块数据",
        "description": "行业板块、概念板块等分类数据", 
        "subcategories": {
            "sector_info": {"name": "板块信息", "table": "sector_info", "model": SectorInfo},
            "sector_mapping": {"name": "股票板块关联", "table": "stock_sector_mapping", "model": StockSectorMapping}
        }
    },
    "system": {
        "name": "系统数据",
        "description": "系统运行、更新日志等数据",
        "subcategories": {
            "update_logs": {"name": "更新日志", "table": "data_update_logs", "model": DataUpdateLog}
        }
    }
}

class DataCategoryInfo(BaseModel):
    """数据分类信息"""
    category_key: str
    category_name: str
    description: str
    subcategories: List[Dict[str, Any]]
    total_records: int
    last_update: Optional[datetime]

class SubcategoryStats(BaseModel):
    """子分类统计信息"""
    subcategory_key: str
    subcategory_name: str
    table_name: str
    record_count: int
    latest_record_time: Optional[datetime]
    data_size_mb: float
    data_quality_score: float

@router.get("/categories", response_model=List[DataCategoryInfo])
async def get_data_categories(db: AsyncSession = Depends(get_professional_db)):
    """获取所有数据分类及统计信息"""
    try:
        categories = []
        
        for category_key, category_info in DATA_CATEGORIES.items():
            total_records = 0
            last_update = None
            subcategories = []
            
            for subcat_key, subcat_info in category_info["subcategories"].items():
                # 获取记录数
                result = await db.execute(
                    text(f"SELECT COUNT(*) FROM {subcat_info['table']}")
                )
                count = result.scalar() or 0
                total_records += count
                
                # 获取最新更新时间
                try:
                    if subcat_info['table'] == 'data_update_logs':
                        time_result = await db.execute(
                            text(f"SELECT MAX(update_time) FROM {subcat_info['table']}")
                        )
                    else:
                        # 尝试不同的时间字段
                        time_fields = ['update_time', 'created_at', 'trade_date', 'date']
                        latest_time = None
                        for field in time_fields:
                            try:
                                time_result = await db.execute(
                                    text(f"SELECT MAX({field}) FROM {subcat_info['table']}")
                                )
                                latest_time = time_result.scalar()
                                if latest_time:
                                    break
                            except:
                                continue
                        
                        if latest_time and (not last_update or latest_time > last_update):
                            last_update = latest_time
                            
                except Exception as e:
                    logger.warning(f"获取表 {subcat_info['table']} 时间字段失败: {e}")
                
                subcategories.append({
                    "key": subcat_key,
                    "name": subcat_info["name"],
                    "table": subcat_info["table"],
                    "record_count": count
                })
            
            categories.append(DataCategoryInfo(
                category_key=category_key,
                category_name=category_info["name"],
                description=category_info["description"],
                subcategories=subcategories,
                total_records=total_records,
                last_update=last_update
            ))
        
        return categories
        
    except Exception as e:
        logger.error(f"获取数据分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据分类失败: {str(e)}")

@router.get("/categories/{category_key}/subcategories", response_model=List[SubcategoryStats])
async def get_subcategory_stats(
    category_key: str,
    db: AsyncSession = Depends(get_professional_db)
):
    """获取指定分类下的子分类详细统计"""
    try:
        if category_key not in DATA_CATEGORIES:
            raise HTTPException(status_code=404, detail="数据分类不存在")
        
        category_info = DATA_CATEGORIES[category_key]
        subcategory_stats = []
        
        for subcat_key, subcat_info in category_info["subcategories"].items():
            table_name = subcat_info["table"]
            
            # 获取记录数
            count_result = await db.execute(
                text(f"SELECT COUNT(*) FROM {table_name}")
            )
            record_count = count_result.scalar() or 0
            
            # 获取最新记录时间
            latest_time = None
            time_fields = ['update_time', 'created_at', 'trade_date', 'date']
            for field in time_fields:
                try:
                    time_result = await db.execute(
                        text(f"SELECT MAX({field}) FROM {table_name}")
                    )
                    latest_time = time_result.scalar()
                    if latest_time:
                        break
                except:
                    continue
            
            # 估算数据大小（简化计算）
            data_size_mb = record_count * 0.001  # 假设每条记录约1KB
            
            # 计算数据质量评分（简化）
            quality_score = 100.0
            if record_count == 0:
                quality_score = 0.0
            elif not latest_time:
                quality_score = 60.0
            elif isinstance(latest_time, (datetime, date)):
                # 根据数据新鲜度调整评分
                if isinstance(latest_time, date):
                    latest_time = datetime.combine(latest_time, datetime.min.time())
                days_old = (datetime.now() - latest_time).days
                if days_old > 30:
                    quality_score = max(40.0, 100.0 - days_old * 2)
            
            subcategory_stats.append(SubcategoryStats(
                subcategory_key=subcat_key,
                subcategory_name=subcat_info["name"],
                table_name=table_name,
                record_count=record_count,
                latest_record_time=latest_time,
                data_size_mb=round(data_size_mb, 2),
                data_quality_score=round(quality_score, 1)
            ))
        
        return subcategory_stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取子分类统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取子分类统计失败: {str(e)}")

@router.get("/categories/{category_key}/subcategories/{subcategory_key}/sample")
async def get_subcategory_sample_data(
    category_key: str,
    subcategory_key: str,
    limit: int = Query(5, ge=1, le=20),
    db: AsyncSession = Depends(get_professional_db)
):
    """获取子分类的样本数据"""
    try:
        if category_key not in DATA_CATEGORIES:
            raise HTTPException(status_code=404, detail="数据分类不存在")
        
        category_info = DATA_CATEGORIES[category_key]
        if subcategory_key not in category_info["subcategories"]:
            raise HTTPException(status_code=404, detail="子分类不存在")
        
        subcat_info = category_info["subcategories"][subcategory_key]
        table_name = subcat_info["table"]
        
        # 获取样本数据
        result = await db.execute(
            text(f"SELECT * FROM {table_name} LIMIT {limit}")
        )
        
        # 转换为字典列表
        columns = result.keys()
        rows = result.fetchall()
        
        sample_data = []
        for row in rows:
            row_dict = {}
            for i, value in enumerate(row):
                # 处理日期时间类型
                if isinstance(value, (datetime, date)):
                    row_dict[columns[i]] = value.isoformat()
                else:
                    row_dict[columns[i]] = value
            sample_data.append(row_dict)
        
        return {
            "category_key": category_key,
            "subcategory_key": subcategory_key,
            "table_name": table_name,
            "sample_count": len(sample_data),
            "total_columns": len(columns),
            "columns": list(columns),
            "sample_data": sample_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取样本数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取样本数据失败: {str(e)}")

@router.post("/categories/{category_key}/subcategories/{subcategory_key}/cleanup")
async def cleanup_subcategory_data(
    category_key: str,
    subcategory_key: str,
    cleanup_options: Dict[str, Any],
    db: AsyncSession = Depends(get_professional_db)
):
    """清理子分类数据"""
    try:
        if category_key not in DATA_CATEGORIES:
            raise HTTPException(status_code=404, detail="数据分类不存在")
        
        category_info = DATA_CATEGORIES[category_key]
        if subcategory_key not in category_info["subcategories"]:
            raise HTTPException(status_code=404, detail="子分类不存在")
        
        subcat_info = category_info["subcategories"][subcategory_key]
        table_name = subcat_info["table"]
        
        cleanup_count = 0
        
        # 根据清理选项执行不同的清理操作
        if cleanup_options.get("remove_duplicates", False):
            # 删除重复数据（简化实现）
            result = await db.execute(
                text(f"""
                DELETE FROM {table_name} 
                WHERE id NOT IN (
                    SELECT MIN(id) FROM {table_name} GROUP BY stock_code, trade_date
                )
                """)
            )
            cleanup_count += result.rowcount
        
        if cleanup_options.get("remove_old_data", False):
            days_to_keep = cleanup_options.get("days_to_keep", 365)
            # 删除过期数据
            result = await db.execute(
                text(f"""
                DELETE FROM {table_name} 
                WHERE trade_date < date('now', '-{days_to_keep} days')
                """)
            )
            cleanup_count += result.rowcount
        
        await db.commit()
        
        return {
            "message": "数据清理完成",
            "category_key": category_key,
            "subcategory_key": subcategory_key,
            "table_name": table_name,
            "cleaned_records": cleanup_count,
            "cleanup_options": cleanup_options
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"数据清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据清理失败: {str(e)}")
