"""
智能选股系统服务模块
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func, or_

from app.core.logging import get_logger
from app.core.database import AsyncSessionLocal
from app.models.stock_selection import StockScore, SelectionStrategy, SelectionResult
from app.services.indicator_storage import IndicatorStorageService
from app.services.ai_storage import AIPredictionStorageService
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


class StockScoringService:
    """股票评分服务"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def calculate_stock_score(self, stock_code: str, score_date: date = None) -> Dict[str, Any]:
        """计算单只股票的综合评分"""
        try:
            if not score_date:
                score_date = date.today()
            
            logger.info(f"开始计算股票 {stock_code} 的评分...")
            
            # 获取技术指标数据
            async with IndicatorStorageService() as indicator_storage:
                indicators = await indicator_storage.get_indicators(stock_code, "daily", limit=20)
            
            # 获取AI预测数据
            async with AIPredictionStorageService() as ai_storage:
                latest_prediction = await ai_storage.get_latest_prediction(stock_code)
            
            # 获取K线数据
            async with DataStorageService() as data_storage:
                klines = await data_storage.get_kline_data(stock_code, "daily", limit=30)
            
            if not indicators or not klines:
                logger.warning(f"股票 {stock_code} 缺少必要数据，无法评分")
                return {"error": "缺少必要数据"}
            
            # 计算各维度评分
            technical_score = self._calculate_technical_score(indicators)
            ai_score = self._calculate_ai_score(latest_prediction) if latest_prediction else 50.0
            momentum_score = self._calculate_momentum_score(indicators, klines)
            trend_score = self._calculate_trend_score(indicators)
            volume_score = self._calculate_volume_score(klines)
            volatility_score = self._calculate_volatility_score(klines)
            
            # 计算综合评分（加权平均）
            weights = {
                "technical": 0.25,
                "ai": 0.20,
                "momentum": 0.20,
                "trend": 0.15,
                "volume": 0.10,
                "volatility": 0.10
            }
            
            total_score = (
                technical_score * weights["technical"] +
                ai_score * weights["ai"] +
                momentum_score * weights["momentum"] +
                trend_score * weights["trend"] +
                volume_score * weights["volume"] +
                volatility_score * weights["volatility"]
            )
            
            # 确定评分等级
            score_level = self._get_score_level(total_score)
            
            # 确定推荐等级
            recommendation, confidence = self._get_recommendation(total_score, technical_score, ai_score)
            
            score_result = {
                "stock_code": stock_code,
                "score_date": score_date.isoformat(),
                "total_score": round(total_score, 2),
                "score_level": score_level,
                "technical_score": round(technical_score, 2),
                "ai_score": round(ai_score, 2),
                "momentum_score": round(momentum_score, 2),
                "trend_score": round(trend_score, 2),
                "volume_score": round(volume_score, 2),
                "volatility_score": round(volatility_score, 2),
                "recommendation": recommendation,
                "confidence": round(confidence, 4),
                "score_details": {
                    "weights": weights,
                    "indicators_count": len(indicators),
                    "has_ai_prediction": latest_prediction is not None,
                    "klines_count": len(klines)
                }
            }
            
            logger.info(f"股票 {stock_code} 评分完成: {total_score:.2f} ({score_level})")
            return score_result
            
        except Exception as e:
            logger.error(f"计算股票 {stock_code} 评分失败: {e}")
            return {"error": str(e)}
    
    def _calculate_technical_score(self, indicators: List) -> float:
        """计算技术面评分"""
        if not indicators:
            return 50.0
        
        latest = indicators[-1]
        score = 50.0  # 基础分
        
        try:
            # RSI评分 (30分)
            if latest.rsi:
                rsi = float(latest.rsi)
                if 30 <= rsi <= 70:  # 正常区间
                    score += 15
                elif 20 <= rsi < 30 or 70 < rsi <= 80:  # 轻微超买超卖
                    score += 10
                elif rsi < 20 or rsi > 80:  # 严重超买超卖
                    score += 5
            
            # MACD评分 (20分)
            if latest.macd and latest.macd_signal:
                macd = float(latest.macd)
                signal = float(latest.macd_signal)
                if macd > signal:  # 金叉
                    score += 15
                elif macd > 0:  # MACD在零轴上方
                    score += 10
                else:
                    score += 5
            
            # 均线评分 (20分)
            if latest.ma5 and latest.ma20:
                ma5 = float(latest.ma5)
                ma20 = float(latest.ma20)
                if ma5 > ma20:  # 短期均线在长期均线上方
                    score += 15
                else:
                    score += 5
            
            # 布林带评分 (10分)
            if latest.boll_upper and latest.boll_lower and len(indicators) >= 2:
                current_price = float(indicators[-1].ma20) if indicators[-1].ma20 else 0
                upper = float(latest.boll_upper)
                lower = float(latest.boll_lower)
                if current_price and lower < current_price < upper:
                    score += 8
                else:
                    score += 3
            
        except Exception as e:
            logger.error(f"计算技术面评分失败: {e}")
        
        return min(max(score, 0), 100)
    
    def _calculate_ai_score(self, prediction) -> float:
        """计算AI预测评分"""
        if not prediction:
            return 50.0
        
        try:
            confidence = float(prediction.confidence_score)
            direction = prediction.trend_direction
            
            base_score = confidence * 100  # 置信度转换为分数
            
            # 根据预测方向调整
            if direction == "up":
                return min(base_score + 10, 100)
            elif direction == "down":
                return max(base_score - 10, 0)
            else:  # sideways
                return base_score
                
        except Exception as e:
            logger.error(f"计算AI评分失败: {e}")
            return 50.0
    
    def _calculate_momentum_score(self, indicators: List, klines: List) -> float:
        """计算动量评分"""
        if len(indicators) < 5 or len(klines) < 5:
            return 50.0
        
        try:
            score = 50.0
            
            # 价格动量 (50分)
            recent_prices = [float(k.close_price) for k in klines[-5:]]
            if len(recent_prices) >= 5:
                price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                if price_change > 0.05:  # 5天涨幅超过5%
                    score += 25
                elif price_change > 0.02:  # 5天涨幅超过2%
                    score += 15
                elif price_change > 0:  # 正收益
                    score += 10
                else:  # 负收益
                    score -= 10
            
            # RSI动量 (30分)
            if len(indicators) >= 3:
                rsi_values = [float(ind.rsi) for ind in indicators[-3:] if ind.rsi]
                if len(rsi_values) >= 3:
                    rsi_trend = rsi_values[-1] - rsi_values[0]
                    if rsi_trend > 5:
                        score += 20
                    elif rsi_trend > 0:
                        score += 10
                    else:
                        score += 5
            
            # 成交量动量 (20分)
            recent_volumes = [k.volume for k in klines[-5:]]
            if len(recent_volumes) >= 5:
                avg_volume = sum(recent_volumes) / len(recent_volumes)
                latest_volume = recent_volumes[-1]
                if latest_volume > avg_volume * 1.5:  # 放量
                    score += 15
                elif latest_volume > avg_volume:
                    score += 10
                else:
                    score += 5
            
        except Exception as e:
            logger.error(f"计算动量评分失败: {e}")
        
        return min(max(score, 0), 100)
    
    def _calculate_trend_score(self, indicators: List) -> float:
        """计算趋势评分"""
        if len(indicators) < 10:
            return 50.0
        
        try:
            score = 50.0
            
            # 均线趋势 (60分)
            ma20_values = [float(ind.ma20) for ind in indicators[-10:] if ind.ma20]
            if len(ma20_values) >= 10:
                # 计算趋势斜率
                trend_slope = (ma20_values[-1] - ma20_values[0]) / len(ma20_values)
                if trend_slope > 0.1:  # 强上升趋势
                    score += 30
                elif trend_slope > 0:  # 上升趋势
                    score += 20
                elif trend_slope > -0.1:  # 横盘
                    score += 10
                else:  # 下降趋势
                    score += 5
            
            # 均线排列 (40分)
            latest = indicators[-1]
            if latest.ma5 and latest.ma10 and latest.ma20:
                ma5 = float(latest.ma5)
                ma10 = float(latest.ma10)
                ma20 = float(latest.ma20)
                
                if ma5 > ma10 > ma20:  # 多头排列
                    score += 25
                elif ma5 > ma10 or ma10 > ma20:  # 部分多头
                    score += 15
                else:  # 空头排列
                    score += 5
            
        except Exception as e:
            logger.error(f"计算趋势评分失败: {e}")
        
        return min(max(score, 0), 100)
    
    def _calculate_volume_score(self, klines: List) -> float:
        """计算成交量评分"""
        if len(klines) < 10:
            return 50.0
        
        try:
            score = 50.0
            
            # 成交量趋势 (50分)
            volumes = [k.volume for k in klines[-10:]]
            recent_avg = sum(volumes[-5:]) / 5
            earlier_avg = sum(volumes[:5]) / 5
            
            if recent_avg > earlier_avg * 1.2:  # 成交量放大
                score += 25
            elif recent_avg > earlier_avg:
                score += 15
            else:
                score += 10
            
            # 量价配合 (50分)
            prices = [float(k.close_price) for k in klines[-5:]]
            volumes = [k.volume for k in klines[-5:]]
            
            if len(prices) >= 5 and len(volumes) >= 5:
                price_change = prices[-1] - prices[0]
                volume_change = volumes[-1] - volumes[0]
                
                if price_change > 0 and volume_change > 0:  # 价涨量增
                    score += 25
                elif price_change > 0:  # 价涨
                    score += 15
                else:
                    score += 10
            
        except Exception as e:
            logger.error(f"计算成交量评分失败: {e}")
        
        return min(max(score, 0), 100)
    
    def _calculate_volatility_score(self, klines: List) -> float:
        """计算波动性评分"""
        if len(klines) < 10:
            return 50.0
        
        try:
            score = 50.0
            
            # 计算波动率
            prices = [float(k.close_price) for k in klines[-10:]]
            if len(prices) >= 10:
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
                
                # 适中的波动性得分更高
                if 0.01 <= volatility <= 0.03:  # 1%-3%的日波动率
                    score += 25
                elif 0.005 <= volatility <= 0.05:  # 0.5%-5%的日波动率
                    score += 15
                else:
                    score += 5
            
            # 价格稳定性 (50分)
            highs = [float(k.high_price) for k in klines[-5:]]
            lows = [float(k.low_price) for k in klines[-5:]]
            
            if highs and lows:
                price_range = (max(highs) - min(lows)) / min(lows)
                if price_range < 0.1:  # 价格区间小于10%
                    score += 25
                elif price_range < 0.2:  # 价格区间小于20%
                    score += 15
                else:
                    score += 10
            
        except Exception as e:
            logger.error(f"计算波动性评分失败: {e}")
        
        return min(max(score, 0), 100)
    
    def _get_score_level(self, score: float) -> str:
        """根据分数确定等级"""
        if score >= 90:
            return "A+"
        elif score >= 80:
            return "A"
        elif score >= 70:
            return "B+"
        elif score >= 60:
            return "B"
        elif score >= 50:
            return "C+"
        elif score >= 40:
            return "C"
        else:
            return "D"
    
    def _get_recommendation(self, total_score: float, technical_score: float, ai_score: float) -> Tuple[str, float]:
        """根据评分确定推荐等级和置信度"""
        confidence = min(max((total_score - 50) / 50, 0), 1)
        
        if total_score >= 85 and technical_score >= 80:
            return "强烈买入", confidence
        elif total_score >= 70 and technical_score >= 65:
            return "买入", confidence
        elif total_score >= 55:
            return "持有", confidence
        elif total_score >= 40:
            return "卖出", confidence * 0.8
        else:
            return "强烈卖出", confidence * 0.6
    
    async def save_stock_score(self, score_data: Dict[str, Any]) -> int:
        """保存股票评分"""
        try:
            stock_score = StockScore(
                stock_code=score_data["stock_code"],
                score_date=datetime.fromisoformat(score_data["score_date"]).date(),
                total_score=Decimal(str(score_data["total_score"])),
                score_level=score_data["score_level"],
                technical_score=Decimal(str(score_data["technical_score"])),
                ai_score=Decimal(str(score_data["ai_score"])) if score_data["ai_score"] else None,
                momentum_score=Decimal(str(score_data["momentum_score"])),
                trend_score=Decimal(str(score_data["trend_score"])),
                volume_score=Decimal(str(score_data["volume_score"])),
                volatility_score=Decimal(str(score_data["volatility_score"])),
                score_details=score_data.get("score_details", {}),
                recommendation=score_data["recommendation"],
                confidence=Decimal(str(score_data["confidence"]))
            )
            
            self.session.add(stock_score)
            await self.session.commit()
            
            logger.info(f"股票 {score_data['stock_code']} 评分保存成功，ID: {stock_score.id}")
            return stock_score.id
            
        except Exception as e:
            logger.error(f"保存股票评分失败: {e}")
            await self.session.rollback()
            return 0


class StockSelectionService:
    """智能选股服务"""

    def __init__(self):
        self.session: Optional[AsyncSession] = None
        self.scoring_service = StockScoringService()

    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def run_selection_strategy(self, strategy_name: str, stock_list: List[str] = None) -> Dict[str, Any]:
        """执行选股策略"""
        try:
            start_time = time.time()
            logger.info(f"开始执行选股策略: {strategy_name}")

            # 获取策略配置
            strategy = await self._get_strategy_by_name(strategy_name)
            if not strategy:
                return {"error": f"策略 {strategy_name} 不存在"}

            # 获取股票列表
            if not stock_list:
                stock_list = await self._get_active_stocks()

            if not stock_list:
                return {"error": "没有可用的股票数据"}

            logger.info(f"开始处理 {len(stock_list)} 只股票...")

            # 批量计算股票评分
            scored_stocks = []
            processed_count = 0

            async with StockScoringService() as scoring_service:
                for stock_code in stock_list:
                    try:
                        score_result = await scoring_service.calculate_stock_score(stock_code)
                        if "error" not in score_result:
                            scored_stocks.append(score_result)
                            # 保存评分
                            await scoring_service.save_stock_score(score_result)
                        processed_count += 1

                        if processed_count % 10 == 0:
                            logger.info(f"已处理 {processed_count}/{len(stock_list)} 只股票")

                    except Exception as e:
                        logger.error(f"处理股票 {stock_code} 失败: {e}")
                        continue

            # 应用筛选条件
            filtered_stocks = self._apply_filter_conditions(scored_stocks, strategy.filter_conditions)

            # 排序和选择
            selected_stocks = self._select_top_stocks(filtered_stocks, strategy.max_stocks)

            # 计算统计信息
            stats = self._calculate_selection_stats(selected_stocks)

            execution_time = time.time() - start_time

            # 保存选股结果
            result_data = {
                "strategy_id": strategy.id,
                "selection_date": date.today(),
                "selected_stocks": [
                    {
                        "stock_code": stock["stock_code"],
                        "total_score": stock["total_score"],
                        "score_level": stock["score_level"],
                        "recommendation": stock["recommendation"],
                        "confidence": stock["confidence"]
                    }
                    for stock in selected_stocks
                ],
                "total_count": len(selected_stocks),
                "avg_score": stats["avg_score"],
                "score_distribution": stats["score_distribution"],
                "execution_time": round(execution_time, 3),
                "processed_stocks": processed_count
            }

            await self._save_selection_result(result_data)

            logger.info(f"选股策略 {strategy_name} 执行完成，选出 {len(selected_stocks)} 只股票，耗时 {execution_time:.2f} 秒")

            return {
                "strategy_name": strategy_name,
                "execution_time": execution_time,
                "processed_stocks": processed_count,
                "selected_stocks": selected_stocks,
                "statistics": stats,
                "selection_date": date.today().isoformat()
            }

        except Exception as e:
            logger.error(f"执行选股策略失败: {e}")
            return {"error": str(e)}

    async def _get_strategy_by_name(self, strategy_name: str):
        """根据名称获取策略"""
        try:
            stmt = select(SelectionStrategy).where(
                and_(
                    SelectionStrategy.strategy_name == strategy_name,
                    SelectionStrategy.is_active == True
                )
            )
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取策略失败: {e}")
            return None

    async def _get_active_stocks(self) -> List[str]:
        """获取活跃股票列表"""
        try:
            # 这里可以从股票基础信息表获取，暂时返回一些测试股票
            return [
                "000001", "000002", "000858", "000876", "002415",
                "600000", "600036", "600519", "600887", "000858"
            ]
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def _apply_filter_conditions(self, stocks: List[Dict], conditions: Dict) -> List[Dict]:
        """应用筛选条件"""
        try:
            filtered = []

            for stock in stocks:
                # 检查最低评分要求
                if stock["total_score"] < conditions.get("min_total_score", 0):
                    continue

                # 检查技术面评分要求
                if stock["technical_score"] < conditions.get("min_technical_score", 0):
                    continue

                # 检查AI评分要求
                if conditions.get("require_ai_score", False) and stock["ai_score"] < conditions.get("min_ai_score", 0):
                    continue

                # 检查推荐等级
                required_recommendations = conditions.get("required_recommendations", [])
                if required_recommendations and stock["recommendation"] not in required_recommendations:
                    continue

                # 检查评分等级
                required_levels = conditions.get("required_score_levels", [])
                if required_levels and stock["score_level"] not in required_levels:
                    continue

                filtered.append(stock)

            logger.info(f"筛选后剩余 {len(filtered)} 只股票")
            return filtered

        except Exception as e:
            logger.error(f"应用筛选条件失败: {e}")
            return stocks

    def _select_top_stocks(self, stocks: List[Dict], max_count: int) -> List[Dict]:
        """选择评分最高的股票"""
        try:
            # 按总分排序
            sorted_stocks = sorted(stocks, key=lambda x: x["total_score"], reverse=True)

            # 选择前N只
            selected = sorted_stocks[:max_count]

            logger.info(f"选择评分最高的 {len(selected)} 只股票")
            return selected

        except Exception as e:
            logger.error(f"选择股票失败: {e}")
            return stocks[:max_count] if stocks else []

    def _calculate_selection_stats(self, stocks: List[Dict]) -> Dict[str, Any]:
        """计算选股统计信息"""
        try:
            if not stocks:
                return {
                    "avg_score": 0,
                    "score_distribution": {},
                    "recommendation_distribution": {}
                }

            # 平均分
            avg_score = sum(stock["total_score"] for stock in stocks) / len(stocks)

            # 评分分布
            score_distribution = {}
            for stock in stocks:
                level = stock["score_level"]
                score_distribution[level] = score_distribution.get(level, 0) + 1

            # 推荐分布
            recommendation_distribution = {}
            for stock in stocks:
                rec = stock["recommendation"]
                recommendation_distribution[rec] = recommendation_distribution.get(rec, 0) + 1

            return {
                "avg_score": round(avg_score, 2),
                "score_distribution": score_distribution,
                "recommendation_distribution": recommendation_distribution,
                "max_score": max(stock["total_score"] for stock in stocks),
                "min_score": min(stock["total_score"] for stock in stocks)
            }

        except Exception as e:
            logger.error(f"计算统计信息失败: {e}")
            return {}

    async def _save_selection_result(self, result_data: Dict[str, Any]) -> int:
        """保存选股结果"""
        try:
            selection_result = SelectionResult(
                strategy_id=result_data["strategy_id"],
                selection_date=result_data["selection_date"],
                selected_stocks=result_data["selected_stocks"],
                total_count=result_data["total_count"],
                avg_score=Decimal(str(result_data["avg_score"])),
                score_distribution=result_data["score_distribution"],
                execution_time=Decimal(str(result_data["execution_time"])),
                processed_stocks=result_data["processed_stocks"]
            )

            self.session.add(selection_result)
            await self.session.commit()

            logger.info(f"选股结果保存成功，ID: {selection_result.id}")
            return selection_result.id

        except Exception as e:
            logger.error(f"保存选股结果失败: {e}")
            await self.session.rollback()
            return 0

    async def create_default_strategies(self):
        """创建默认选股策略"""
        try:
            strategies = [
                {
                    "strategy_name": "技术面强势股",
                    "strategy_type": "technical",
                    "description": "基于技术指标筛选强势股票",
                    "strategy_logic": "重点关注技术面评分，要求RSI、MACD、均线等指标表现良好",
                    "filter_conditions": {
                        "min_total_score": 70,
                        "min_technical_score": 75,
                        "required_recommendations": ["买入", "强烈买入"],
                        "required_score_levels": ["A+", "A", "B+"]
                    },
                    "scoring_weights": {
                        "technical": 0.4,
                        "ai": 0.15,
                        "momentum": 0.2,
                        "trend": 0.15,
                        "volume": 0.05,
                        "volatility": 0.05
                    },
                    "min_score": 70,
                    "max_stocks": 15
                },
                {
                    "strategy_name": "AI智能推荐",
                    "strategy_type": "ai",
                    "description": "基于AI预测结果筛选股票",
                    "strategy_logic": "重点关注AI预测评分，结合技术面分析",
                    "filter_conditions": {
                        "min_total_score": 65,
                        "min_ai_score": 70,
                        "require_ai_score": True,
                        "required_recommendations": ["买入", "强烈买入", "持有"]
                    },
                    "scoring_weights": {
                        "technical": 0.2,
                        "ai": 0.35,
                        "momentum": 0.2,
                        "trend": 0.15,
                        "volume": 0.05,
                        "volatility": 0.05
                    },
                    "min_score": 65,
                    "max_stocks": 20
                },
                {
                    "strategy_name": "动量突破股",
                    "strategy_type": "momentum",
                    "description": "基于动量指标筛选突破股票",
                    "strategy_logic": "重点关注价格动量和成交量，寻找突破机会",
                    "filter_conditions": {
                        "min_total_score": 60,
                        "min_technical_score": 60,
                        "required_score_levels": ["A+", "A", "B+", "B"]
                    },
                    "scoring_weights": {
                        "technical": 0.25,
                        "ai": 0.15,
                        "momentum": 0.35,
                        "trend": 0.15,
                        "volume": 0.1,
                        "volatility": 0.0
                    },
                    "min_score": 60,
                    "max_stocks": 25
                }
            ]

            for strategy_data in strategies:
                # 检查策略是否已存在
                existing = await self._get_strategy_by_name(strategy_data["strategy_name"])
                if existing:
                    logger.info(f"策略 {strategy_data['strategy_name']} 已存在，跳过创建")
                    continue

                strategy = SelectionStrategy(
                    strategy_name=strategy_data["strategy_name"],
                    strategy_type=strategy_data["strategy_type"],
                    description=strategy_data["description"],
                    strategy_logic=strategy_data["strategy_logic"],
                    filter_conditions=strategy_data["filter_conditions"],
                    scoring_weights=strategy_data["scoring_weights"],
                    min_score=Decimal(str(strategy_data["min_score"])),
                    max_stocks=strategy_data["max_stocks"]
                )

                self.session.add(strategy)

            await self.session.commit()
            logger.info("默认选股策略创建完成")

        except Exception as e:
            logger.error(f"创建默认策略失败: {e}")
            await self.session.rollback()
