#!/usr/bin/env python3
"""
监控数据库变化
"""

import sqlite3
import time
from datetime import datetime

def monitor_database():
    """监控数据库变化"""
    try:
        print("🔍 开始监控数据库变化...")
        
        # 连接数据库
        conn = sqlite3.connect('data/stock_analyzer.db')
        cursor = conn.cursor()
        
        # 记录初始状态
        cursor.execute("SELECT COUNT(*) FROM stock_spot_data")
        initial_count = cursor.fetchone()[0]
        print(f"📊 初始记录数: {initial_count}")
        
        # 记录初始的最新记录
        cursor.execute("""
            SELECT stock_code, stock_name, current_price, update_time 
            FROM stock_spot_data 
            ORDER BY update_time DESC 
            LIMIT 3
        """)
        initial_records = cursor.fetchall()
        print("📈 初始最新3条记录:")
        for i, record in enumerate(initial_records):
            print(f"  {i+1}. {record[0]} {record[1]}: ¥{record[2]} - {record[3]}")
        
        print("\n⏰ 开始监控... (按Ctrl+C停止)")
        
        last_count = initial_count
        last_update_time = initial_records[0][3] if initial_records else None
        
        while True:
            time.sleep(2)  # 每2秒检查一次
            
            # 检查记录数变化
            cursor.execute("SELECT COUNT(*) FROM stock_spot_data")
            current_count = cursor.fetchone()[0]
            
            if current_count != last_count:
                print(f"\n🔄 记录数变化: {last_count} -> {current_count}")
                last_count = current_count
            
            # 检查最新记录变化
            cursor.execute("""
                SELECT stock_code, stock_name, current_price, update_time 
                FROM stock_spot_data 
                ORDER BY update_time DESC 
                LIMIT 1
            """)
            latest_record = cursor.fetchone()
            
            if latest_record and latest_record[3] != last_update_time:
                print(f"📝 新记录: {latest_record[0]} {latest_record[1]}: ¥{latest_record[2]} - {latest_record[3]}")
                last_update_time = latest_record[3]
                
                # 检查是否是模拟数据
                if float(latest_record[2]) == 10.5:
                    print("⚠️  检测到模拟数据插入!")
                else:
                    print("✅ 检测到真实数据插入")
        
    except KeyboardInterrupt:
        print("\n⏹️  监控停止")
    except Exception as e:
        print(f"❌ 监控失败: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("=" * 60)
    print("👁️  数据库变化监控工具")
    print("=" * 60)
    
    monitor_database()
    
    print("=" * 60)
