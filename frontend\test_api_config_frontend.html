<!DOCTYPE html>
<html>
<head>
    <title>API配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 10px 15px; }
    </style>
</head>
<body>
    <h1>API配置功能测试</h1>
    
    <div>
        <button onclick="testGetConfigs()">获取配置列表</button>
        <button onclick="testUpdateConfig()">更新配置</button>
        <button onclick="testConnection()">测试连接</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:8001/api/v1/data-management';
        
        function addResult(message, isSuccess = true) {
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }
        
        async function testGetConfigs() {
            try {
                const response = await fetch(`${API_BASE}/api-configs`);
                if (response.ok) {
                    const configs = await response.json();
                    addResult(`✅ 获取配置成功，共${configs.length}个配置`);
                    console.log('配置列表:', configs);
                } else {
                    addResult(`❌ 获取配置失败: ${response.status}`, false);
                }
            } catch (error) {
                addResult(`❌ 获取配置错误: ${error.message}`, false);
            }
        }
        
        async function testUpdateConfig() {
            try {
                const testConfig = {
                    id: 'akshare',
                    name: 'AKShare测试',
                    type: 'akshare',
                    enabled: true,
                    api_key: null,
                    endpoint: null,
                    rate_limit: 100,
                    timeout: 30000,
                    priority: 1
                };
                
                const response = await fetch(`${API_BASE}/api-configs/akshare`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testConfig)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addResult(`✅ 更新配置成功: ${result.message}`);
                } else {
                    const errorText = await response.text();
                    addResult(`❌ 更新配置失败: ${response.status} - ${errorText}`, false);
                }
            } catch (error) {
                addResult(`❌ 更新配置错误: ${error.message}`, false);
            }
        }
        
        async function testConnection() {
            try {
                const response = await fetch(`${API_BASE}/api-configs/akshare/test`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const status = result.success ? '✅ 成功' : '❌ 失败';
                    addResult(`${status} AKShare连接测试: ${result.message}`);
                } else {
                    addResult(`❌ 连接测试失败: ${response.status}`, false);
                }
            } catch (error) {
                addResult(`❌ 连接测试错误: ${error.message}`, false);
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            addResult('🚀 开始API配置功能测试...');
            setTimeout(testGetConfigs, 500);
        };
    </script>
</body>
</html>
