"""
选股系统相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, DateTime, Date, Numeric, Integer, Text, JSON, Boolean, Index
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class StockScore(Base):
    """股票评分表"""
    __tablename__ = "stock_scores"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    score_date: Mapped[date] = mapped_column(Date, index=True, comment="评分日期")
    
    # 综合评分
    total_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), comment="总分")
    score_level: Mapped[str] = mapped_column(String(10), comment="评分等级: A+/A/B+/B/C+/C/D")
    
    # 各维度评分
    technical_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), comment="技术面评分")
    ai_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2), comment="AI预测评分")
    momentum_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), comment="动量评分")
    trend_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), comment="趋势评分")
    volume_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), comment="成交量评分")
    volatility_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), comment="波动性评分")
    
    # 评分详情
    score_details: Mapped[Optional[dict]] = mapped_column(JSON, comment="评分详细信息")
    
    # 推荐信息
    recommendation: Mapped[str] = mapped_column(String(20), comment="推荐等级: 强烈买入/买入/持有/卖出/强烈卖出")
    confidence: Mapped[Decimal] = mapped_column(Numeric(5, 4), comment="推荐置信度")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_stock_score_date', 'stock_code', 'score_date'),
        Index('ix_score_level_date', 'score_level', 'score_date'),
        Index('ix_total_score_date', 'total_score', 'score_date'),
    )


class SelectionStrategy(Base):
    """选股策略表"""
    __tablename__ = "selection_strategies"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    strategy_name: Mapped[str] = mapped_column(String(50), unique=True, comment="策略名称")
    strategy_type: Mapped[str] = mapped_column(String(20), comment="策略类型: technical/ai/momentum/value")
    
    # 策略描述
    description: Mapped[str] = mapped_column(Text, comment="策略描述")
    strategy_logic: Mapped[str] = mapped_column(Text, comment="策略逻辑说明")
    
    # 筛选条件
    filter_conditions: Mapped[dict] = mapped_column(JSON, comment="筛选条件配置")
    scoring_weights: Mapped[dict] = mapped_column(JSON, comment="评分权重配置")
    
    # 策略参数
    min_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), default=60, comment="最低评分要求")
    max_stocks: Mapped[int] = mapped_column(Integer, default=20, comment="最大选股数量")
    
    # 策略状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class SelectionResult(Base):
    """选股结果表"""
    __tablename__ = "selection_results"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    strategy_id: Mapped[int] = mapped_column(Integer, index=True, comment="策略ID")
    selection_date: Mapped[date] = mapped_column(Date, index=True, comment="选股日期")
    
    # 选股结果
    selected_stocks: Mapped[dict] = mapped_column(JSON, comment="选中的股票列表")
    total_count: Mapped[int] = mapped_column(Integer, comment="选中股票总数")
    
    # 统计信息
    avg_score: Mapped[Decimal] = mapped_column(Numeric(5, 2), comment="平均评分")
    score_distribution: Mapped[dict] = mapped_column(JSON, comment="评分分布")
    
    # 执行信息
    execution_time: Mapped[Decimal] = mapped_column(Numeric(8, 3), comment="执行耗时(秒)")
    processed_stocks: Mapped[int] = mapped_column(Integer, comment="处理的股票总数")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_selection_strategy_date', 'strategy_id', 'selection_date'),
        Index('ix_selection_date', 'selection_date'),
    )


class StockWatchlist(Base):
    """股票关注列表"""
    __tablename__ = "stock_watchlists"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, comment="用户ID")
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    
    # 关注信息
    watchlist_name: Mapped[str] = mapped_column(String(50), default="默认关注", comment="关注列表名称")
    add_reason: Mapped[Optional[str]] = mapped_column(Text, comment="添加理由")
    target_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="目标价格")
    stop_loss: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="止损价格")
    
    # 提醒设置
    price_alert_enabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="价格提醒")
    score_alert_enabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="评分提醒")
    ai_alert_enabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="AI信号提醒")
    
    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否有效")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_watchlist_user_stock', 'user_id', 'stock_code'),
        Index('ix_watchlist_name', 'watchlist_name'),
    )


class SelectionAlert(Base):
    """选股提醒表"""
    __tablename__ = "selection_alerts"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    alert_type: Mapped[str] = mapped_column(String(20), comment="提醒类型: score/price/ai/pattern")
    alert_date: Mapped[date] = mapped_column(Date, index=True, comment="提醒日期")
    
    # 提醒内容
    alert_title: Mapped[str] = mapped_column(String(100), comment="提醒标题")
    alert_message: Mapped[str] = mapped_column(Text, comment="提醒内容")
    alert_level: Mapped[str] = mapped_column(String(10), comment="提醒级别: high/medium/low")
    
    # 触发条件
    trigger_condition: Mapped[dict] = mapped_column(JSON, comment="触发条件")
    trigger_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="触发值")
    
    # 状态
    is_read: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否已读")
    is_processed: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否已处理")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_alert_stock_date', 'stock_code', 'alert_date'),
        Index('ix_alert_type_date', 'alert_type', 'alert_date'),
        Index('ix_alert_level', 'alert_level'),
    )


class SelectionBacktestResult(Base):
    """回测结果表"""
    __tablename__ = "selection_backtest_results"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    strategy_id: Mapped[int] = mapped_column(Integer, index=True, comment="策略ID")
    backtest_name: Mapped[str] = mapped_column(String(100), comment="回测名称")
    
    # 回测参数
    start_date: Mapped[date] = mapped_column(Date, comment="回测开始日期")
    end_date: Mapped[date] = mapped_column(Date, comment="回测结束日期")
    initial_capital: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="初始资金")
    
    # 回测结果
    final_capital: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="最终资金")
    total_return: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="总收益率")
    annual_return: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="年化收益率")
    max_drawdown: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="最大回撤")
    sharpe_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="夏普比率")
    
    # 交易统计
    total_trades: Mapped[int] = mapped_column(Integer, comment="总交易次数")
    win_trades: Mapped[int] = mapped_column(Integer, comment="盈利交易次数")
    win_rate: Mapped[Decimal] = mapped_column(Numeric(5, 4), comment="胜率")
    
    # 详细结果
    performance_details: Mapped[dict] = mapped_column(JSON, comment="详细表现数据")
    trade_records: Mapped[dict] = mapped_column(JSON, comment="交易记录")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_backtest_strategy', 'strategy_id'),
        Index('ix_backtest_date_range', 'start_date', 'end_date'),
    )
