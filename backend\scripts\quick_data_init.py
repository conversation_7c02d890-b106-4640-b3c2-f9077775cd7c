#!/usr/bin/env python3
"""
快速数据初始化脚本
添加一些基础的股票数据用于测试
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import AsyncSessionLocal
from app.models.stock import Stock, KlineData
# from app.core.logging import setup_logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试股票数据
TEST_STOCKS = [
    {"code": "000001", "name": "平安银行", "sector": "金融", "market": "深圳"},
    {"code": "000002", "name": "万科A", "sector": "房地产", "market": "深圳"},
    {"code": "600000", "name": "浦发银行", "sector": "金融", "market": "上海"},
    {"code": "600036", "name": "招商银行", "sector": "金融", "market": "上海"},
    {"code": "600519", "name": "贵州茅台", "sector": "食品饮料", "market": "上海"},
    {"code": "000858", "name": "五粮液", "sector": "食品饮料", "market": "深圳"},
    {"code": "300015", "name": "爱尔眼科", "sector": "医疗保健", "market": "创业板"},
    {"code": "002415", "name": "海康威视", "sector": "电子", "market": "深圳"},
]

async def create_test_stocks(db: AsyncSession):
    """创建测试股票数据"""
    logger.info("开始创建测试股票数据...")
    
    for stock_data in TEST_STOCKS:
        # 检查股票是否已存在
        result = await db.execute(select(Stock).filter(Stock.stock_code == stock_data["code"]))
        existing_stock = result.scalar_one_or_none()
        if existing_stock:
            logger.info(f"股票 {stock_data['code']} 已存在，跳过")
            continue
            
        # 创建新股票
        stock = Stock(
            stock_code=stock_data["code"],
            stock_name=stock_data["name"],
            industry=stock_data["sector"],
            market=stock_data["market"],
            list_date=datetime.now().date(),
            is_active=True
        )
        
        db.add(stock)
        logger.info(f"创建股票: {stock_data['code']} - {stock_data['name']}")
    
    await db.commit()
    logger.info("测试股票数据创建完成")

def generate_price_data(base_price: float, days: int = 100) -> list:
    """生成模拟价格数据"""
    prices = []
    current_price = base_price
    
    for i in range(days):
        # 生成随机价格变动
        change_percent = random.uniform(-0.05, 0.05)  # ±5%
        
        open_price = current_price
        close_price = current_price * (1 + change_percent)
        
        # 生成高低价
        high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.02))
        low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.02))
        
        # 生成成交量
        volume = random.randint(1000000, 50000000)
        
        date = datetime.now().date() - timedelta(days=days-i-1)
        
        prices.append({
            'date': date,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': volume,
            'amount': round(volume * close_price, 2)
        })
        
        current_price = close_price
    
    return prices

async def create_test_price_data(db: AsyncSession):
    """创建测试价格数据"""
    logger.info("开始创建测试价格数据...")
    
    # 基础价格设置
    base_prices = {
        "000001": 12.50,   # 平安银行
        "000002": 18.30,   # 万科A
        "600000": 10.80,   # 浦发银行
        "600036": 44.50,   # 招商银行
        "600519": 1680.00, # 贵州茅台
        "000858": 125.00,  # 五粮液
        "300015": 45.20,   # 爱尔眼科
        "002415": 32.80,   # 海康威视
    }
    
    for stock_code, base_price in base_prices.items():
        # 检查股票是否存在
        result = await db.execute(select(Stock).filter(Stock.stock_code == stock_code))
        stock = result.scalar_one_or_none()
        if not stock:
            logger.warning(f"股票 {stock_code} 不存在，跳过价格数据生成")
            continue

        # 检查是否已有价格数据
        result = await db.execute(select(KlineData).filter(KlineData.stock_code == stock_code))
        existing_prices = len(result.all())
        if existing_prices > 0:
            logger.info(f"股票 {stock_code} 已有价格数据，跳过")
            continue
        
        # 生成价格数据
        price_data = generate_price_data(base_price, days=100)
        
        for price_info in price_data:
            price_record = KlineData(
                stock_code=stock_code,
                trade_date=price_info['date'],
                period='daily',
                open_price=price_info['open'],
                high_price=price_info['high'],
                low_price=price_info['low'],
                close_price=price_info['close'],
                volume=price_info['volume'],
                turnover=price_info['amount'],
                change=price_info['close'] - price_info['open'],
                change_percent=(price_info['close'] - price_info['open']) / price_info['open'] * 100
            )
            db.add(price_record)
        
        logger.info(f"为股票 {stock_code} 生成了 {len(price_data)} 条价格数据")
    
    await db.commit()
    logger.info("测试价格数据创建完成")

async def main():
    """主函数"""
    logger.info("🚀 开始快速数据初始化")

    async with AsyncSessionLocal() as db:
        try:
            # 创建测试股票
            await create_test_stocks(db)

            # 创建测试价格数据
            await create_test_price_data(db)

            logger.info("✅ 快速数据初始化完成")

            # 显示统计信息
            result = await db.execute(select(Stock))
            stock_count = len(result.all())

            result = await db.execute(select(KlineData))
            price_count = len(result.all())

            logger.info(f"📊 数据统计:")
            logger.info(f"   股票数量: {stock_count}")
            logger.info(f"   价格记录: {price_count}")

        except Exception as e:
            logger.error(f"❌ 数据初始化失败: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(main())
