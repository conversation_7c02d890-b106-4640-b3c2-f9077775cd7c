import React, { useState } from 'react'
import { Layout, <PERSON>u, Badge } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  DashboardOutlined,
  LineChartOutlined,
  RobotOutlined,
  SearchOutlined,
  BellOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  FundOutlined,
  StarOutlined,
  UserOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons'

const { Sider } = Layout

interface AppSiderProps {
  collapsed?: boolean
  onCollapse?: (collapsed: boolean) => void
}

const AppSider: React.FC<AppSiderProps> = ({
  collapsed: controlledCollapsed,
  onCollapse
}) => {
  const [internalCollapsed, setInternalCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  const collapsed = controlledCollapsed !== undefined ? controlledCollapsed : internalCollapsed

  const handleCollapse = (value: boolean) => {
    if (onCollapse) {
      onCollapse(value)
    } else {
      setInternalCollapsed(value)
    }
  }

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: 'market',
      icon: <LineChartOutlined />,
      label: '市场分析',
      children: [
        {
          key: '/integrated-stock-analysis',
          label: '个股分析',
        },

        {
          key: '/fundamental-analysis',
          label: '基本面分析',
        },
        {
          key: '/market-overview',
          label: '市场概览',
        },
        {
          key: '/sector-analysis',
          label: '板块分析',
        },
      ],
    },
    {
      key: '/ai-predict',
      icon: <RobotOutlined />,
      label: 'AI预测',
    },
    {
      key: '/screener',
      icon: <SearchOutlined />,
      label: '智能选股',
    },
    {
      key: '/alert-system',
      icon: <BellOutlined />,
      label: '实时预警',
    },
    {
      key: 'portfolio',
      icon: <FundOutlined />,
      label: '投资组合',
      children: [
        {
          key: '/portfolio',
          label: '我的组合',
        },
        {
          key: '/portfolio/create',
          label: '创建组合',
        },
        {
          key: '/portfolio/backtest',
          label: '策略回测',
        },
      ],
    },
    {
      key: '/analytics',
      icon: <BarChartOutlined />,
      label: '统计分析',
    },
    {
      key: 'system',
      icon: <SettingOutlined />,
      label: '系统管理',
      children: [
        {
          key: '/settings',
          label: '系统设置',
        },
        {
          key: '/data-settings',
          label: '数据设置',
        },
        {
          key: '/data-management',
          label: '数据管理',
        },
        {
          key: '/data-integrity',
          label: '数据完整性',
        },
        {
          key: '/akshare-data',
          label: 'AKShare数据',
        },
        {
          key: '/system-status',
          label: '系统状态',
        },
      ],
    },
    {
      key: '/help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
    },
    {
      key: '/alerts',
      icon: (
        <Badge count={3} size="small" offset={[10, 0]}>
          <BellOutlined />
        </Badge>
      ),
      label: '预警系统',
    },
    {
      key: '/watchlist',
      icon: <StarOutlined />,
      label: '自选股',
    },
    {
      type: 'divider',
    },
    {
      key: 'user',
      icon: <UserOutlined />,
      label: '用户中心',
      children: [
        {
          key: '/profile',
          label: '个人资料',
        },
        {
          key: '/subscription',
          label: '订阅管理',
        },
        {
          key: '/api-usage',
          label: 'API使用情况',
        },
      ],
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={handleCollapse}
      width={240}
      style={{
        overflow: 'auto',
        height: 'calc(100vh - 64px)',
        background: '#001529',
      }}
    >
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        defaultOpenKeys={['market', 'portfolio', 'user']}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          height: '100%',
          borderRight: 0,
          paddingTop: 16,
        }}
      />
    </Sider>
  )
}

export default AppSider
