#!/usr/bin/env python3
"""
创建重构后的数据库表
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
backend_root = project_root / "backend"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(backend_root))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from loguru import logger

# 设置数据库路径
DATABASE_URL = "sqlite+aiosqlite:///backend/data/stock_analyzer.db"

async def create_tables():
    """创建数据库表"""
    try:
        logger.info("开始创建重构后的数据库表...")
        
        # 导入模型
        from app.models.stock_redesign import Base
        
        # 创建异步引擎
        engine = create_async_engine(
            DATABASE_URL,
            echo=True,  # 显示SQL语句
            future=True
        )
        
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("✅ 重构后的数据库表创建成功")
        
        # 验证表是否创建成功
        await verify_tables(engine)
        
        await engine.dispose()
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        raise


async def verify_tables(engine):
    """验证表是否创建成功"""
    try:
        logger.info("验证数据库表...")
        
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as session:
            # 检查表是否存在
            from sqlalchemy import text
            
            tables_to_check = [
                'stock_basic_info',
                'stock_kline_daily', 
                'stock_realtime_data',
                'stock_financial_data',
                'custom_indicators',
                'stock_patterns',
                'backtest_strategies',
                'data_update_logs'
            ]
            
            for table_name in tables_to_check:
                result = await session.execute(
                    text("SELECT name FROM sqlite_master WHERE type='table' AND name=:table_name"),
                    {"table_name": table_name}
                )
                exists = result.fetchone() is not None
                status = "✅ 存在" if exists else "❌ 不存在"
                logger.info(f"  {table_name}: {status}")
        
        logger.info("✅ 数据库表验证完成")
        
    except Exception as e:
        logger.error(f"❌ 验证数据库表失败: {e}")
        raise


async def insert_sample_data():
    """插入示例数据"""
    try:
        logger.info("开始插入示例数据...")
        
        engine = create_async_engine(DATABASE_URL)
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as session:
            # 插入示例股票基础信息
            from app.models.stock_redesign import StockBasicInfo
            from datetime import date
            
            sample_stocks = [
                {
                    "stock_code": "000001",
                    "stock_name": "平安银行",
                    "exchange": "SZ",
                    "industry": "银行",
                    "list_date": date(1991, 4, 3),
                    "status": "active"
                },
                {
                    "stock_code": "000002", 
                    "stock_name": "万科A",
                    "exchange": "SZ",
                    "industry": "房地产开发",
                    "list_date": date(1991, 1, 29),
                    "status": "active"
                },
                {
                    "stock_code": "600000",
                    "stock_name": "浦发银行", 
                    "exchange": "SH",
                    "industry": "银行",
                    "list_date": date(1999, 11, 10),
                    "status": "active"
                }
            ]
            
            for stock_data in sample_stocks:
                # 检查是否已存在
                from sqlalchemy import select
                result = await session.execute(
                    select(StockBasicInfo).where(StockBasicInfo.stock_code == stock_data["stock_code"])
                )
                existing = result.scalar_one_or_none()
                
                if not existing:
                    stock = StockBasicInfo(**stock_data)
                    session.add(stock)
            
            await session.commit()
            logger.info(f"✅ 插入了 {len(sample_stocks)} 条示例股票数据")
        
        await engine.dispose()
        
    except Exception as e:
        logger.error(f"❌ 插入示例数据失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        logger.info("🚀 开始创建重构后的数据库...")
        
        # 确保数据目录存在
        os.makedirs("backend/data", exist_ok=True)
        
        # 1. 创建数据库表
        await create_tables()
        
        # 2. 插入示例数据
        await insert_sample_data()
        
        logger.info("🎉 重构后的数据库创建完成!")
        
    except Exception as e:
        logger.error(f"💥 数据库创建失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
