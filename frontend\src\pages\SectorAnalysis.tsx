import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Table,
  Tag,
  Progress,
  Space,
  Typography,
  Select,
  Spin,
  Statistic,
} from 'antd'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select

interface SectorInfo {
  name: string
  code: string
  change: number
  changePercent: number
  volume: string
  marketCap: string
  pe: number
  pb: number
  leadingStocks: string[]
  stockCount: number
}

const SectorAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [sectors, setSectors] = useState<SectorInfo[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState('1d')

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const mockSectors: SectorInfo[] = [
        {
          name: '新能源汽车',
          code: 'NEV',
          change: 45.67,
          changePercent: 3.2,
          volume: '456亿',
          marketCap: '2.3万亿',
          pe: 28.5,
          pb: 3.2,
          leadingStocks: ['比亚迪', '宁德时代', '理想汽车'],
          stockCount: 156,
        },
        {
          name: '人工智能',
          code: 'AI',
          change: -12.34,
          changePercent: -1.8,
          volume: '234亿',
          marketCap: '1.8万亿',
          pe: 35.2,
          pb: 4.1,
          leadingStocks: ['科大讯飞', '海康威视', '大华股份'],
          stockCount: 89,
        },
        {
          name: '生物医药',
          code: 'BIO',
          change: 23.45,
          changePercent: 2.1,
          volume: '189亿',
          marketCap: '1.5万亿',
          pe: 42.8,
          pb: 2.9,
          leadingStocks: ['恒瑞医药', '药明康德', '迈瑞医疗'],
          stockCount: 234,
        },
        {
          name: '半导体',
          code: 'SEMI',
          change: -34.56,
          changePercent: -2.5,
          volume: '345亿',
          marketCap: '1.2万亿',
          pe: 48.3,
          pb: 3.8,
          leadingStocks: ['中芯国际', '韦尔股份', '兆易创新'],
          stockCount: 67,
        },
        {
          name: '新材料',
          code: 'MAT',
          change: 15.67,
          changePercent: 1.3,
          volume: '123亿',
          marketCap: '0.9万亿',
          pe: 32.1,
          pb: 2.7,
          leadingStocks: ['隆基绿能', '天齐锂业', '赣锋锂业'],
          stockCount: 145,
        },
        {
          name: '消费电子',
          code: 'CE',
          change: -8.90,
          changePercent: -0.7,
          volume: '167亿',
          marketCap: '1.1万亿',
          pe: 25.6,
          pb: 2.3,
          leadingStocks: ['立讯精密', '歌尔股份', '京东方A'],
          stockCount: 98,
        },
      ]

      setSectors(mockSectors)
      setLoading(false)
    }

    loadData()
  }, [selectedPeriod])

  const columns = [
    {
      title: '板块名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: SectorInfo) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.stockCount}只股票
          </Text>
        </Space>
      ),
    },
    {
      title: '涨跌幅',
      dataIndex: 'changePercent',
      key: 'changePercent',
      render: (value: number, record: SectorInfo) => (
        <Space direction="vertical" size={0}>
          <Tag color={value >= 0 ? 'green' : 'red'} icon={value >= 0 ? <RiseOutlined /> : <FallOutlined />}>
            {value >= 0 ? '+' : ''}{value.toFixed(2)}%
          </Tag>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {value >= 0 ? '+' : ''}{record.change.toFixed(2)}
          </Text>
        </Space>
      ),
      sorter: (a: SectorInfo, b: SectorInfo) => a.changePercent - b.changePercent,
    },
    {
      title: '成交额',
      dataIndex: 'volume',
      key: 'volume',
    },
    {
      title: '市值',
      dataIndex: 'marketCap',
      key: 'marketCap',
    },
    {
      title: 'PE/PB',
      key: 'valuation',
      render: (record: SectorInfo) => (
        <Space direction="vertical" size={0}>
          <Text>PE: {record.pe.toFixed(1)}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            PB: {record.pb.toFixed(1)}
          </Text>
        </Space>
      ),
    },
    {
      title: '龙头股',
      dataIndex: 'leadingStocks',
      key: 'leadingStocks',
      render: (stocks: string[]) => (
        <Space direction="vertical" size={0}>
          {stocks.slice(0, 2).map((stock, index) => (
            <Text key={index} style={{ fontSize: '12px' }}>
              {stock}
            </Text>
          ))}
          {stocks.length > 2 && (
            <Text type="secondary" style={{ fontSize: '11px' }}>
              +{stocks.length - 2}只
            </Text>
          )}
        </Space>
      ),
    },
  ]

  const gainers = sectors.filter(s => s.changePercent > 0).length
  const losers = sectors.filter(s => s.changePercent < 0).length
  const totalVolume = sectors.reduce((sum, s) => sum + parseFloat(s.volume.replace('亿', '')), 0)

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>正在加载板块数据...</Text>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>板块分析</Title>
        <Select
          value={selectedPeriod}
          onChange={setSelectedPeriod}
          style={{ width: 120 }}
        >
          <Option value="1d">今日</Option>
          <Option value="1w">本周</Option>
          <Option value="1m">本月</Option>
          <Option value="3m">三个月</Option>
        </Select>
      </div>

      {/* 概览统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="上涨板块"
              value={gainers}
              suffix={`/ ${sectors.length}`}
              valueStyle={{ color: '#3f8600' }}
              prefix={<ArrowUpOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="下跌板块"
              value={losers}
              suffix={`/ ${sectors.length}`}
              valueStyle={{ color: '#cf1322' }}
              prefix={<ArrowDownOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="总成交额"
              value={totalVolume.toFixed(0)}
              suffix="亿"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="涨跌比"
              value={losers > 0 ? (gainers / losers).toFixed(2) : '∞'}
              valueStyle={{ 
                color: gainers > losers ? '#3f8600' : '#cf1322' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 板块表现进度条 */}
      <Card title="板块表现分布" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {sectors.map((sector) => (
            <Col xs={24} sm={12} md={8} key={sector.code}>
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text strong>{sector.name}</Text>
                  <Tag color={sector.changePercent >= 0 ? 'green' : 'red'}>
                    {sector.changePercent >= 0 ? '+' : ''}{sector.changePercent.toFixed(2)}%
                  </Tag>
                </div>
                <Progress
                  percent={Math.abs(sector.changePercent) * 10}
                  strokeColor={sector.changePercent >= 0 ? '#52c41a' : '#ff4d4f'}
                  showInfo={false}
                  size="small"
                />
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 详细数据表格 */}
      <Card title="板块详细数据">
        <Table
          dataSource={sectors}
          columns={columns}
          rowKey="code"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个板块`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  )
}

export default SectorAnalysis
