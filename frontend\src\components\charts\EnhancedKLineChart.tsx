/**
 * 增强版K线图组件
 * 支持时间周期切换、优化的拖动和缩放功能
 */

import React, { useMemo, useState, useCallback, useRef } from 'react'
import { Card, Space, Segmented, Radio, Switch, Tooltip, Button } from 'antd'
import { FullscreenOutlined, SettingOutlined, ReloadOutlined } from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { CANDLESTICK_COLORS } from '@/config/colors'

interface PriceData {
  timestamp: string
  date: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

interface EnhancedKLineChartProps {
  data: PriceData[]
  title?: string
  height?: number
  showVolume?: boolean
  showMA?: boolean
  onTimeframeChange?: (timeframe: string) => void
  onRefresh?: () => void
}

// 时间周期选项
const TIME_FRAME_OPTIONS = [
  { label: '分时', value: '1m' },
  { label: '5分钟', value: '5m' },
  { label: '15分钟', value: '15m' },
  { label: '30分钟', value: '30m' },
  { label: '1小时', value: '1h' },
  { label: '日线', value: '1D' },
  { label: '周线', value: '1W' },
  { label: '月线', value: '1M' }
]

// 图表类型选项
const CHART_TYPE_OPTIONS = [
  { label: 'K线图', value: 'candlestick' },
  { label: '分时图', value: 'line' },
  { label: '面积图', value: 'area' }
]

const EnhancedKLineChart: React.FC<EnhancedKLineChartProps> = ({
  data,
  title = 'K线图',
  height = 400,
  showVolume = true,
  showMA = true,
  onTimeframeChange,
  onRefresh
}) => {
  const [timeFrame, setTimeFrame] = useState<string>('1D')
  const [chartType, setChartType] = useState<string>('candlestick')
  const [showVolumeState, setShowVolumeState] = useState(showVolume)
  const [showMAState, setShowMAState] = useState(showMA)
  const [fullscreen, setFullscreen] = useState(false)
  
  const chartRef = useRef<any>(null)

  // 处理时间周期变化
  const handleTimeframeChange = useCallback((value: string) => {
    setTimeFrame(value)
    onTimeframeChange?.(value)
  }, [onTimeframeChange])

  // 计算移动平均线
  const calculateMA = useCallback((data: number[], period: number) => {
    const result: (number | null)[] = []
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(null)
      } else {
        const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
        result.push(sum / period)
      }
    }
    return result
  }, [])

  // 准备图表数据
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null

    const candlestickData = data.map(item => [
      item.timestamp,
      item.open,
      item.close,
      item.low,
      item.high
    ])

    const volumeData = data.map(item => [
      item.timestamp,
      item.volume
    ])

    const closePrices = data.map(item => item.close)
    const ma5 = showMAState ? calculateMA(closePrices, 5) : []
    const ma10 = showMAState ? calculateMA(closePrices, 10) : []
    const ma20 = showMAState ? calculateMA(closePrices, 20) : []

    return {
      candlestickData,
      volumeData,
      ma5: ma5.map((value, index) => [data[index].timestamp, value]),
      ma10: ma10.map((value, index) => [data[index].timestamp, value]),
      ma20: ma20.map((value, index) => [data[index].timestamp, value])
    }
  }, [data, showMAState, calculateMA])

  // ECharts配置
  const echartsOption = useMemo(() => {
    if (!chartData) return {}

    return {
      backgroundColor: CANDLESTICK_COLORS.BACKGROUND,
      animation: false, // 禁用动画提高性能
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          animation: false,
          label: {
            backgroundColor: '#505765'
          }
        },
        formatter: function (params: any[]) {
          if (!params || params.length === 0) return ''
          
          const candleParam = params.find(p => p.seriesType === 'candlestick')
          if (!candleParam) return ''
          
          const [open, close, low, high] = candleParam.data.slice(1)
          const change = close - open
          const changePercent = ((change / open) * 100).toFixed(2)
          const color = change >= 0 ? CANDLESTICK_COLORS.UP_COLOR : CANDLESTICK_COLORS.DOWN_COLOR
          
          let content = `<div style="margin-bottom: 8px; font-weight: bold;">${candleParam.axisValue}</div>`
          content += `<div style="margin: 4px 0; color: ${color};">
            <div>开盘: ¥${open.toFixed(2)}</div>
            <div>收盘: ¥${close.toFixed(2)} <span>(${change >= 0 ? '+' : ''}${change.toFixed(2)} / ${changePercent}%)</span></div>
            <div>最高: ¥${high.toFixed(2)}</div>
            <div>最低: ¥${low.toFixed(2)}</div>
          </div>`
          
          return content
        }
      },
      grid: showVolumeState ? [
        {
          left: '3%',
          right: '4%',
          top: '10%',
          height: '60%'
        },
        {
          left: '3%',
          right: '4%',
          top: '75%',
          height: '20%'
        }
      ] : [
        {
          left: '3%',
          right: '4%',
          top: '10%',
          bottom: '10%'
        }
      ],
      xAxis: showVolumeState ? [
        {
          type: 'category',
          gridIndex: 0,
          boundaryGap: true,
          axisLine: { show: false },
          axisLabel: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        },
        {
          type: 'category',
          gridIndex: 1,
          boundaryGap: true,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: {
            color: '#8392A5',
            fontSize: 10
          },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ] : [
        {
          type: 'category',
          boundaryGap: true,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: {
            color: '#8392A5',
            fontSize: 10
          },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ],
      yAxis: showVolumeState ? [
        {
          scale: true,
          axisLine: { show: false },
          axisLabel: {
            color: '#8392A5',
            formatter: '¥{value}'
          },
          axisTick: { show: false },
          splitLine: {
            lineStyle: {
              color: '#E6E8EB',
              type: 'dashed'
            }
          }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ] : [
        {
          scale: true,
          axisLine: { show: false },
          axisLabel: {
            color: '#8392A5',
            formatter: '¥{value}'
          },
          axisTick: { show: false },
          splitLine: {
            lineStyle: {
              color: '#E6E8EB',
              type: 'dashed'
            }
          }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: showVolumeState ? [0, 1] : [0],
          start: 70,
          end: 100,
          filterMode: 'none', // 防止数据过滤导致的重置
          throttle: 50 // 节流，提高性能
        },
        {
          show: true,
          xAxisIndex: showVolumeState ? [0, 1] : [0],
          type: 'slider',
          top: showVolumeState ? '96%' : '90%',
          start: 70,
          end: 100,
          height: 20,
          filterMode: 'none',
          throttle: 50
        }
      ],
      series: [
        // K线图
        {
          name: 'K线',
          type: chartType === 'candlestick' ? 'candlestick' : 'line',
          data: chartType === 'candlestick' ? chartData.candlestickData : chartData.candlestickData.map(item => [item[0], item[2]]),
          xAxisIndex: 0,
          yAxisIndex: 0,
          ...(chartType === 'candlestick' ? {
            itemStyle: {
              color: CANDLESTICK_COLORS.UP_COLOR,
              color0: CANDLESTICK_COLORS.DOWN_COLOR,
              borderColor: CANDLESTICK_COLORS.UP_COLOR,
              borderColor0: CANDLESTICK_COLORS.DOWN_COLOR,
              borderWidth: CANDLESTICK_COLORS.BORDER_WIDTH
            }
          } : {
            lineStyle: { color: CANDLESTICK_COLORS.UP_COLOR, width: 2 },
            symbol: 'none',
            smooth: true
          })
        },
        // 移动平均线
        ...(showMAState ? [
          {
            name: 'MA5',
            type: 'line',
            data: chartData.ma5,
            xAxisIndex: 0,
            yAxisIndex: 0,
            lineStyle: { color: '#FF6B6B', width: 1 },
            symbol: 'none',
            smooth: true
          },
          {
            name: 'MA10',
            type: 'line',
            data: chartData.ma10,
            xAxisIndex: 0,
            yAxisIndex: 0,
            lineStyle: { color: '#4ECDC4', width: 1 },
            symbol: 'none',
            smooth: true
          },
          {
            name: 'MA20',
            type: 'line',
            data: chartData.ma20,
            xAxisIndex: 0,
            yAxisIndex: 0,
            lineStyle: { color: '#45B7D1', width: 1 },
            symbol: 'none',
            smooth: true
          }
        ] : []),
        // 成交量
        ...(showVolumeState ? [{
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: chartData.volumeData,
          itemStyle: {
            color: function (params: any) {
              const dataIndex = params.dataIndex
              if (dataIndex < data.length) {
                const current = data[dataIndex]
                return current.close >= current.open ? CANDLESTICK_COLORS.UP_COLOR : CANDLESTICK_COLORS.DOWN_COLOR
              }
              return '#ccc'
            }
          },
          barWidth: '60%'
        }] : [])
      ]
    }
  }, [chartData, chartType, showVolumeState, showMAState, data])

  if (!data || data.length === 0) {
    return (
      <Card title={title}>
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          暂无K线数据
        </div>
      </Card>
    )
  }

  return (
    <Card
      title={
        <Space>
          <span>{title}</span>
          <span style={{ fontSize: '12px', color: '#999' }}>
            ({timeFrame})
          </span>
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            size="small"
            onClick={onRefresh}
          >
            刷新
          </Button>
          <Button
            icon={<FullscreenOutlined />}
            size="small"
            onClick={() => setFullscreen(!fullscreen)}
          >
            全屏
          </Button>
        </Space>
      }
      size="small"
    >
      {/* 工具栏 */}
      <div style={{ marginBottom: '16px', padding: '8px', backgroundColor: '#fafafa', borderRadius: '4px' }}>
        <Space wrap>
          <div>
            <span style={{ marginRight: '8px', fontSize: '12px', color: '#666' }}>时间周期:</span>
            <Segmented
              options={TIME_FRAME_OPTIONS}
              value={timeFrame}
              onChange={handleTimeframeChange}
              size="small"
            />
          </div>
          
          <div>
            <span style={{ marginRight: '8px', fontSize: '12px', color: '#666' }}>图表类型:</span>
            <Radio.Group
              value={chartType}
              onChange={(e) => setChartType(e.target.value)}
              size="small"
            >
              <Radio.Button value="candlestick">K线图</Radio.Button>
              <Radio.Button value="line">分时图</Radio.Button>
              <Radio.Button value="area">面积图</Radio.Button>
            </Radio.Group>
          </div>
          
          <div>
            <Space>
              <Tooltip title="显示成交量">
                <Switch
                  size="small"
                  checked={showVolumeState}
                  onChange={setShowVolumeState}
                />
                <span style={{ marginLeft: '4px', fontSize: '12px', color: '#666' }}>成交量</span>
              </Tooltip>
              
              <Tooltip title="显示移动平均线">
                <Switch
                  size="small"
                  checked={showMAState}
                  onChange={setShowMAState}
                />
                <span style={{ marginLeft: '4px', fontSize: '12px', color: '#666' }}>均线</span>
              </Tooltip>
            </Space>
          </div>
        </Space>
      </div>

      {/* 图表 */}
      <ReactECharts
        ref={chartRef}
        option={echartsOption}
        style={{ height: fullscreen ? '80vh' : height }}
        opts={{ 
          renderer: 'canvas',
          useDirtyRect: true // 启用脏矩形优化
        }}
        notMerge={true} // 防止配置合并导致的问题
        lazyUpdate={true} // 延迟更新提高性能
      />
    </Card>
  )
}

export default EnhancedKLineChart
