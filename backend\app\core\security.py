"""
安全认证模块
"""

import secrets
from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext

from app.core.config import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7


def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def create_refresh_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """创建刷新令牌"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
        return user_id
    except JWTError:
        return None


def verify_refresh_token(token: str) -> Optional[str]:
    """验证刷新令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        token_type: str = payload.get("type")
        if user_id is None or token_type != "refresh":
            return None
        return user_id
    except JWTError:
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def generate_password_reset_token(email: str) -> str:
    """生成密码重置令牌"""
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email}, 
        settings.SECRET_KEY, 
        algorithm=ALGORITHM
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """验证密码重置令牌"""
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        return decoded_token["sub"]
    except JWTError:
        return None


def generate_api_key() -> str:
    """生成API密钥"""
    return secrets.token_urlsafe(32)


def generate_verification_code() -> str:
    """生成验证码"""
    return secrets.token_hex(3).upper()  # 6位验证码


class PasswordValidator:
    """密码验证器"""
    
    @staticmethod
    def validate_password(password: str) -> tuple[bool, str]:
        """验证密码强度"""
        if len(password) < 8:
            return False, "密码长度至少8位"
        
        if len(password) > 128:
            return False, "密码长度不能超过128位"
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        strength_count = sum([has_upper, has_lower, has_digit, has_special])
        
        if strength_count < 3:
            return False, "密码必须包含大写字母、小写字母、数字、特殊字符中的至少3种"
        
        # 检查常见弱密码
        weak_passwords = [
            "12345678", "password", "qwerty123", "abc123456", 
            "password123", "admin123", "123456789", "qwertyuiop"
        ]
        
        if password.lower() in weak_passwords:
            return False, "密码过于简单，请使用更复杂的密码"
        
        return True, "密码强度符合要求"
    
    @staticmethod
    def get_password_strength(password: str) -> dict:
        """获取密码强度评分"""
        score = 0
        feedback = []
        
        # 长度评分
        if len(password) >= 8:
            score += 1
        else:
            feedback.append("密码长度至少8位")
        
        if len(password) >= 12:
            score += 1
            
        # 字符类型评分
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if has_upper:
            score += 1
        else:
            feedback.append("包含大写字母")
            
        if has_lower:
            score += 1
        else:
            feedback.append("包含小写字母")
            
        if has_digit:
            score += 1
        else:
            feedback.append("包含数字")
            
        if has_special:
            score += 1
        else:
            feedback.append("包含特殊字符")
        
        # 复杂度评分
        unique_chars = len(set(password))
        if unique_chars >= len(password) * 0.7:
            score += 1
        
        # 确定强度等级
        if score <= 2:
            strength = "弱"
            color = "red"
        elif score <= 4:
            strength = "中等"
            color = "orange"
        elif score <= 6:
            strength = "强"
            color = "green"
        else:
            strength = "很强"
            color = "darkgreen"
        
        return {
            "score": score,
            "max_score": 7,
            "strength": strength,
            "color": color,
            "feedback": feedback
        }


class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def mask_email(email: str) -> str:
        """邮箱脱敏"""
        if "@" not in email:
            return email
        
        local, domain = email.split("@", 1)
        if len(local) <= 2:
            masked_local = "*" * len(local)
        else:
            masked_local = local[0] + "*" * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """手机号脱敏"""
        if len(phone) != 11:
            return phone
        
        return phone[:3] + "****" + phone[-4:]
    
    @staticmethod
    def mask_id_card(id_card: str) -> str:
        """身份证号脱敏"""
        if len(id_card) != 18:
            return id_card
        
        return id_card[:6] + "********" + id_card[-4:]
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """验证手机号格式"""
        import re
        pattern = r'^1[3-9]\d{9}$'
        return re.match(pattern, phone) is not None
    
    @staticmethod
    def generate_username(email: str) -> str:
        """根据邮箱生成用户名"""
        if "@" in email:
            base_username = email.split("@")[0]
        else:
            base_username = email
        
        # 移除特殊字符
        import re
        username = re.sub(r'[^a-zA-Z0-9_]', '', base_username)
        
        # 确保用户名长度
        if len(username) < 3:
            username = f"user_{secrets.token_hex(3)}"
        elif len(username) > 20:
            username = username[:20]
        
        return username.lower()


class RateLimiter:
    """简单的内存速率限制器"""
    
    def __init__(self):
        self.requests = {}
    
    def is_allowed(self, key: str, max_requests: int, window_seconds: int) -> bool:
        """检查是否允许请求"""
        now = datetime.utcnow()
        
        if key not in self.requests:
            self.requests[key] = []
        
        # 清理过期请求
        cutoff = now - timedelta(seconds=window_seconds)
        self.requests[key] = [req_time for req_time in self.requests[key] if req_time > cutoff]
        
        # 检查请求数量
        if len(self.requests[key]) >= max_requests:
            return False
        
        # 记录新请求
        self.requests[key].append(now)
        return True
    
    def get_remaining_time(self, key: str, window_seconds: int) -> int:
        """获取剩余等待时间（秒）"""
        if key not in self.requests or not self.requests[key]:
            return 0
        
        oldest_request = min(self.requests[key])
        elapsed = (datetime.utcnow() - oldest_request).total_seconds()
        remaining = max(0, window_seconds - elapsed)
        return int(remaining)


# 全局速率限制器实例
rate_limiter = RateLimiter()
