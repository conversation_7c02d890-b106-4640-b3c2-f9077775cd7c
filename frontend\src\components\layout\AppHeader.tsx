import React, { useState, useEffect } from 'react'
import { Layout, Menu, Dropdown, Avatar, Space, Typography, Button, Badge, AutoComplete, Input } from 'antd'
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuFoldOutlined,
  <PERSON>uUnfoldOutlined,
  SearchOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  ArrowDownOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'
import NotificationCenter from '@/components/NotificationCenter'
import { realtimeService } from '@/services/realtimeService'
import { stockService } from '@/services/apiService'

const { Header } = Layout
const { Text } = Typography

interface AppHeaderProps {
  collapsed?: boolean
  onToggle?: () => void
}

interface StockOption {
  value: string
  label: string
  code: string
  name: string
  price?: number
  change?: number
  changePercent?: number
}

const AppHeader: React.FC<AppHeaderProps> = ({ collapsed, onToggle }) => {
  const navigate = useNavigate()
  const { user, logout } = useAuthStore()
  const [notificationVisible, setNotificationVisible] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)

  // 股票搜索相关状态
  const [searchValue, setSearchValue] = useState('')
  const [searchOptions, setSearchOptions] = useState<StockOption[]>([])
  const [searchLoading, setSearchLoading] = useState(false)

  useEffect(() => {
    // 监听实时通知更新未读数量
    const handleNotificationUpdate = () => {
      setUnreadCount(prev => prev + 1)
    }

    realtimeService.on('alert', handleNotificationUpdate)
    realtimeService.on('ai_signal', handleNotificationUpdate)

    return () => {
      realtimeService.off('alert', handleNotificationUpdate)
      realtimeService.off('ai_signal', handleNotificationUpdate)
    }
  }, [])

  const handleLogout = async () => {
    await logout()
    navigate('/login')
  }

  const handleNotificationClick = () => {
    setNotificationVisible(true)
    setUnreadCount(0) // 打开通知中心时清零未读数量
  }

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue && searchValue.length >= 1) {
        performSearch(searchValue)
      } else {
        setSearchOptions([])
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchValue])

  // 执行搜索
  const performSearch = async (value: string) => {
    setSearchLoading(true)
    try {
      // 模拟搜索数据，实际应该调用API
      const mockStocks: StockOption[] = [
        {
          value: '000001',
          label: '平安银行',
          code: '000001',
          name: '平安银行',
          price: 14.39,
          change: 0.18,
          changePercent: 1.27
        },
        {
          value: '000002',
          label: '万科A',
          code: '000002',
          name: '万科A',
          price: 8.95,
          change: -0.12,
          changePercent: -1.32
        },
        {
          value: '600036',
          label: '招商银行',
          code: '600036',
          name: '招商银行',
          price: 35.68,
          change: 0.85,
          changePercent: 2.44
        },
        {
          value: '600519',
          label: '贵州茅台',
          code: '600519',
          name: '贵州茅台',
          price: 1678.50,
          change: 15.20,
          changePercent: 0.91
        },
        {
          value: '000858',
          label: '五粮液',
          code: '000858',
          name: '五粮液',
          price: 128.45,
          change: -2.35,
          changePercent: -1.80
        }
      ]

      // 过滤匹配的股票
      const filtered = mockStocks.filter(stock =>
        stock.name.toLowerCase().includes(value.toLowerCase()) ||
        stock.code.includes(value)
      )

      // 如果没有匹配结果，添加一个提示选项
      if (filtered.length === 0) {
        setSearchOptions([{
          value: 'no-result',
          label: '未找到相关股票',
          code: '',
          name: '请尝试其他关键词'
        }])
      } else {
        setSearchOptions(filtered)
      }
    } catch (error) {
      console.error('搜索股票失败:', error)
      setSearchOptions([])
    } finally {
      setSearchLoading(false)
    }
  }

  // 选择股票后的处理
  const handleStockSelect = (value: string, option: any) => {
    // 如果是"无结果"选项，不进行任何操作
    if (value === 'no-result') {
      return
    }

    setSearchValue('')
    setSearchOptions([])
    // 导航到个股分析页面，传递股票代码
    navigate(`/integrated-stock-analysis?code=${option.code}&name=${encodeURIComponent(option.name)}`)
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ]

  return (
    <>
    <Header
      style={{
        padding: '0 24px',
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
      }}
    >
      {/* 左侧 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {onToggle && (
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={onToggle}
            style={{ marginRight: 16 }}
          />
        )}

        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ fontSize: 24, marginRight: 8 }}>📈</span>
          <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
            智能股票分析平台
          </Text>
        </div>
      </div>

      {/* 中间搜索区域 */}
      <div style={{ flex: 1, maxWidth: '500px', margin: '0 40px' }}>
        <AutoComplete
          style={{ width: '100%' }}
          value={searchValue}
          options={searchOptions.map(option => {
            if (option.value === 'no-result') {
              return {
                value: option.value,
                label: (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: '20px 16px',
                    color: '#8c8c8c',
                    fontSize: '14px',
                    cursor: 'default'
                  }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', marginBottom: '8px' }}>🔍</div>
                      <div>未找到相关股票</div>
                      <div style={{ fontSize: '12px', marginTop: '4px' }}>请尝试其他关键词</div>
                    </div>
                  </div>
                ),
                disabled: true
              }
            }

            return {
              value: option.value,
              label: (
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px 16px'
                }}>
                  <div>
                    <div style={{
                      fontWeight: '600',
                      fontSize: '15px',
                      color: '#262626',
                      marginBottom: '4px'
                    }}>
                      {option.name}
                    </div>
                    <div style={{
                      fontSize: '13px',
                      color: '#8c8c8c',
                      fontFamily: 'Monaco, Consolas, monospace'
                    }}>
                      {option.code}
                    </div>
                  </div>
                  {option.price && (
                    <div style={{ textAlign: 'right' }}>
                      <div style={{
                        fontSize: '15px',
                        fontWeight: '700',
                        color: '#262626',
                        marginBottom: '4px'
                      }}>
                        ¥{option.price.toFixed(2)}
                      </div>
                      <div style={{
                        fontSize: '13px',
                        color: (option.changePercent || 0) >= 0 ? '#f5222d' : '#52c41a',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        gap: '4px',
                        fontWeight: '600'
                      }}>
                        {(option.changePercent || 0) >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                        {(option.changePercent || 0) >= 0 ? '+' : ''}{(option.changePercent || 0).toFixed(2)}%
                      </div>
                    </div>
                  )}
                </div>
              ),
              code: option.code,
              name: option.name
            }
          })}
          onSearch={setSearchValue}
          onSelect={handleStockSelect}
          onChange={setSearchValue}
          placeholder="搜索股票代码或名称..."
          allowClear
          styles={{
            popup: {
              root: {
                maxHeight: '400px',
                overflow: 'auto',
                borderRadius: '12px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
                border: '1px solid #e8e8e8',
                padding: '8px 0'
              }
            }
          }}
        >
          <Input
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
            style={{
              height: '42px',
              borderRadius: '21px',
              fontSize: '14px',
              border: '1px solid #d9d9d9',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#1890ff'
              e.target.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)'
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d9d9d9'
              e.target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}

          />
        </AutoComplete>
      </div>

      {/* 右侧 */}
      <Space size="middle">
        {/* 通知铃铛 */}
        <Badge count={unreadCount} size="small">
          <Button
            type="text"
            icon={<BellOutlined />}
            onClick={handleNotificationClick}
          />
        </Badge>

        {/* 用户信息 */}
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          arrow
        >
          <Space style={{ cursor: 'pointer' }}>
            <Avatar
              size="small"
              icon={<UserOutlined />}
              src={user?.avatar}
            />
            <Text>{user?.full_name || user?.username || '用户'}</Text>
          </Space>
        </Dropdown>
      </Space>
    </Header>

    <NotificationCenter
      visible={notificationVisible}
      onClose={() => setNotificationVisible(false)}
    />
  </>
  )
}

export default AppHeader
