"""
技术指标相关API端点
"""

from typing import List, Optional
from datetime import date, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging import get_logger
from app.services.technical_indicators import IndicatorCalculationService
from app.services.indicator_storage import IndicatorStorageService
from app.tasks.analysis_tasks import calculate_technical_indicators_task

router = APIRouter()
logger = get_logger(__name__)


@router.get("/{stock_code}/calculate")
async def calculate_indicators(
    stock_code: str,
    background_tasks: BackgroundTasks,
    period: str = Query("daily", description="周期"),
    limit: int = Query(250, ge=50, le=1000, description="数据量限制")
):
    """计算技术指标"""
    try:
        # 启动后台任务计算指标
        task = calculate_technical_indicators_task.delay(stock_code, None)

        # 同时进行同步计算（用于立即返回结果）
        calculation_service = IndicatorCalculationService()
        indicators_data = await calculation_service.calculate_all_indicators(stock_code, period, limit)

        if indicators_data:
            # 保存到数据库
            async with IndicatorStorageService() as storage:
                await storage.save_indicators(stock_code, indicators_data)

        return {
            "code": 200,
            "message": "技术指标计算完成",
            "data": {
                "stock_code": stock_code,
                "period": period,
                "task_id": task.id,
                "indicators_count": len(indicators_data.get("indicators", {})),
                "calculated_at": indicators_data.get("calculated_at")
            }
        }
    except Exception as e:
        logger.error(f"计算技术指标失败: {e}")
        raise HTTPException(status_code=500, detail="计算技术指标失败")


@router.get("/{stock_code}/ma")
async def get_ma_indicator(
    stock_code: str,
    period: int = Query(20, ge=1, le=250, description="MA周期"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取移动平均线指标"""
    try:
        async with IndicatorStorageService() as storage:
            indicators = await storage.get_indicators(
                stock_code=stock_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                limit=500
            )

        # 提取MA指标数据
        ma_data = []
        for indicator in indicators:
            ma_value = None
            if period == 5:
                ma_value = indicator.ma5
            elif period == 10:
                ma_value = indicator.ma10
            elif period == 20:
                ma_value = indicator.ma20
            elif period == 60:
                ma_value = indicator.ma60

            if ma_value is not None:
                ma_data.append({
                    "date": indicator.trade_date.isoformat(),
                    "value": round(float(ma_value), 4)
                })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock_code,
                "indicator": f"MA{period}",
                "period": period,
                "values": ma_data
            }
        }
    except Exception as e:
        logger.error(f"获取MA指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取MA指标失败")


@router.get("/{stock_code}/rsi")
async def get_rsi_indicator(
    stock_code: str,
    period: int = Query(14, ge=1, le=100, description="RSI周期"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取RSI指标"""
    try:
        async with IndicatorStorageService() as storage:
            indicators = await storage.get_indicators(
                stock_code=stock_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                limit=500
            )

        # 提取RSI指标数据
        rsi_data = []
        for indicator in indicators:
            if indicator.rsi is not None:
                rsi_data.append({
                    "date": indicator.trade_date.isoformat(),
                    "value": round(float(indicator.rsi), 4)
                })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock_code,
                "indicator": "RSI",
                "period": period,
                "values": rsi_data
            }
        }
    except Exception as e:
        logger.error(f"获取RSI指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取RSI指标失败")


@router.get("/{stock_code}/macd")
async def get_macd_indicator(
    stock_code: str,
    fast_period: int = Query(12, ge=1, le=50, description="快线周期"),
    slow_period: int = Query(26, ge=1, le=100, description="慢线周期"),
    signal_period: int = Query(9, ge=1, le=50, description="信号线周期"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取MACD指标"""
    try:
        async with IndicatorStorageService() as storage:
            indicators = await storage.get_indicators(
                stock_code=stock_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                limit=500
            )

        # 提取MACD指标数据
        macd_data = []
        for indicator in indicators:
            if indicator.macd is not None:
                macd_data.append({
                    "date": indicator.trade_date.isoformat(),
                    "macd": round(float(indicator.macd), 6) if indicator.macd else None,
                    "signal": round(float(indicator.macd_signal), 6) if indicator.macd_signal else None,
                    "histogram": round(float(indicator.macd_histogram), 6) if indicator.macd_histogram else None
                })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock_code,
                "indicator": "MACD",
                "fast_period": fast_period,
                "slow_period": slow_period,
                "signal_period": signal_period,
                "values": macd_data
            }
        }
    except Exception as e:
        logger.error(f"获取MACD指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取MACD指标失败")


@router.get("/{stock_code}/all")
async def get_all_indicators(
    stock_code: str,
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=500, description="返回数量限制")
):
    """获取所有技术指标"""
    try:
        async with IndicatorStorageService() as storage:
            indicators = await storage.get_indicators(
                stock_code=stock_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                limit=limit
            )

        # 组织所有指标数据
        all_indicators = {
            "ma": {"ma5": [], "ma10": [], "ma20": [], "ma60": []},
            "ema": {"ema12": [], "ema26": []},
            "rsi": [],
            "macd": [],
            "bollinger": [],
            "kdj": [],
            "williams_r": [],
            "stochastic": [],
            "cci": [],
            "atr": [],
            "obv": [],
            "adx": []
        }

        for indicator in indicators:
            date_str = indicator.trade_date.isoformat()

            # MA指标
            if indicator.ma5: all_indicators["ma"]["ma5"].append({"date": date_str, "value": float(indicator.ma5)})
            if indicator.ma10: all_indicators["ma"]["ma10"].append({"date": date_str, "value": float(indicator.ma10)})
            if indicator.ma20: all_indicators["ma"]["ma20"].append({"date": date_str, "value": float(indicator.ma20)})
            if indicator.ma60: all_indicators["ma"]["ma60"].append({"date": date_str, "value": float(indicator.ma60)})

            # EMA指标
            if indicator.ema12: all_indicators["ema"]["ema12"].append({"date": date_str, "value": float(indicator.ema12)})
            if indicator.ema26: all_indicators["ema"]["ema26"].append({"date": date_str, "value": float(indicator.ema26)})

            # RSI指标
            if indicator.rsi: all_indicators["rsi"].append({"date": date_str, "value": float(indicator.rsi)})

            # MACD指标
            if indicator.macd:
                all_indicators["macd"].append({
                    "date": date_str,
                    "macd": float(indicator.macd),
                    "signal": float(indicator.macd_signal) if indicator.macd_signal else None,
                    "histogram": float(indicator.macd_histogram) if indicator.macd_histogram else None
                })

            # 布林带
            if indicator.boll_middle:
                all_indicators["bollinger"].append({
                    "date": date_str,
                    "upper": float(indicator.boll_upper) if indicator.boll_upper else None,
                    "middle": float(indicator.boll_middle),
                    "lower": float(indicator.boll_lower) if indicator.boll_lower else None
                })

            # KDJ指标
            if indicator.kdj_k:
                all_indicators["kdj"].append({
                    "date": date_str,
                    "k": float(indicator.kdj_k),
                    "d": float(indicator.kdj_d) if indicator.kdj_d else None,
                    "j": float(indicator.kdj_j) if indicator.kdj_j else None
                })

            # 其他指标
            if indicator.williams_r: all_indicators["williams_r"].append({"date": date_str, "value": float(indicator.williams_r)})
            if indicator.cci: all_indicators["cci"].append({"date": date_str, "value": float(indicator.cci)})
            if indicator.atr: all_indicators["atr"].append({"date": date_str, "value": float(indicator.atr)})
            if indicator.obv: all_indicators["obv"].append({"date": date_str, "value": float(indicator.obv)})
            if indicator.adx: all_indicators["adx"].append({"date": date_str, "value": float(indicator.adx)})

        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock_code,
                "period": "daily",
                "data_count": len(indicators),
                "indicators": all_indicators
            }
        }
    except Exception as e:
        logger.error(f"获取所有指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取所有指标失败")


@router.get("/{stock_code}/statistics")
async def get_indicator_statistics(stock_code: str):
    """获取指标统计信息"""
    try:
        async with IndicatorStorageService() as storage:
            stats = await storage.get_indicator_statistics(stock_code)

        return {
            "code": 200,
            "message": "success",
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取指标统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取指标统计信息失败")
