"""
数据相关的Celery任务
"""

import asyncio
from typing import List, Optional
from datetime import datetime, date, timedelta
from celery import current_task
from celery.exceptions import Retry

from app.core.celery_app import celery_app
from app.core.logging import get_logger
from app.services.data_sync import DataSyncService
from app.services.data_storage import DataStorageService
from app.services.data_quality import DataQualityService

logger = get_logger(__name__)


def run_async_task(coro):
    """运行异步任务的辅助函数"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def sync_stock_list_task(self):
    """同步股票列表任务"""
    try:
        logger.info("开始执行股票列表同步任务...")
        
        async def _sync():
            sync_service = DataSyncService()
            return await sync_service.sync_stock_list()
        
        success = run_async_task(_sync())
        
        if success:
            logger.info("股票列表同步任务完成")
            return {"status": "success", "message": "股票列表同步成功"}
        else:
            logger.error("股票列表同步失败")
            raise Exception("股票列表同步失败")
            
    except Exception as exc:
        logger.error(f"股票列表同步任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def sync_kline_data_task(self, stock_codes: Optional[List[str]] = None, days: int = 1):
    """同步K线数据任务"""
    try:
        logger.info(f"开始执行K线数据同步任务，股票数量: {len(stock_codes) if stock_codes else '全部'}")
        
        async def _sync():
            sync_service = DataSyncService()
            
            if not stock_codes:
                # 获取活跃股票列表
                async with DataStorageService() as storage:
                    stocks = await storage.get_stocks(limit=50)  # 限制数量避免超时
                    codes = [stock.stock_code for stock in stocks]
            else:
                codes = stock_codes
            
            if codes:
                return await sync_service.sync_kline_data(codes, "daily", days)
            return False
        
        success = run_async_task(_sync())
        
        if success:
            logger.info("K线数据同步任务完成")
            return {"status": "success", "message": f"K线数据同步成功，处理{len(stock_codes) if stock_codes else '未知'}只股票"}
        else:
            logger.error("K线数据同步失败")
            raise Exception("K线数据同步失败")
            
    except Exception as exc:
        logger.error(f"K线数据同步任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=30)
def sync_realtime_data_task(self, stock_codes: Optional[List[str]] = None):
    """同步实时数据任务"""
    try:
        logger.info(f"开始执行实时数据同步任务，股票数量: {len(stock_codes) if stock_codes else '全部'}")
        
        async def _sync():
            sync_service = DataSyncService()
            
            if not stock_codes:
                # 获取热门股票列表
                async with DataStorageService() as storage:
                    stocks = await storage.get_stocks(limit=20)  # 限制数量
                    codes = [stock.stock_code for stock in stocks]
            else:
                codes = stock_codes
            
            if codes:
                return await sync_service.sync_realtime_data(codes)
            return False
        
        success = run_async_task(_sync())
        
        if success:
            logger.info("实时数据同步任务完成")
            return {"status": "success", "message": f"实时数据同步成功，处理{len(stock_codes) if stock_codes else '未知'}只股票"}
        else:
            logger.warning("实时数据同步未获取到数据")
            return {"status": "warning", "message": "实时数据同步未获取到数据"}
            
    except Exception as exc:
        logger.error(f"实时数据同步任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=30, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=2, default_retry_delay=300)
def data_quality_check_task(self):
    """数据质量检查任务"""
    try:
        logger.info("开始执行数据质量检查任务...")
        
        async def _check():
            quality_service = DataQualityService()
            dashboard = await quality_service.get_quality_dashboard()
            
            # 检查质量指标
            quality_metrics = dashboard.get("quality_metrics", {})
            completeness_rate = quality_metrics.get("completeness_rate", 0)
            consistency_rate = quality_metrics.get("consistency_rate", 0)
            
            issues = []
            if completeness_rate < 0.9:
                issues.append(f"数据完整性较低: {completeness_rate:.2%}")
            
            if consistency_rate < 0.95:
                issues.append(f"数据一致性较低: {consistency_rate:.2%}")
            
            return {
                "dashboard": dashboard,
                "issues": issues,
                "quality_score": (completeness_rate + consistency_rate) / 2
            }
        
        result = run_async_task(_check())
        
        logger.info(f"数据质量检查完成，质量评分: {result['quality_score']:.2%}")
        
        if result["issues"]:
            logger.warning(f"发现数据质量问题: {result['issues']}")
        
        return {
            "status": "success",
            "message": "数据质量检查完成",
            "quality_score": result["quality_score"],
            "issues": result["issues"]
        }
        
    except Exception as exc:
        logger.error(f"数据质量检查任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=300, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=2, default_retry_delay=600)
def cleanup_expired_data_task(self, days_to_keep: int = 365):
    """清理过期数据任务"""
    try:
        logger.info(f"开始执行数据清理任务，保留{days_to_keep}天数据...")
        
        async def _cleanup():
            cutoff_date = date.today() - timedelta(days=days_to_keep)
            
            async with DataStorageService() as storage:
                session = storage.session
                
                # 清理过期的实时数据（保留最近30天）
                from app.models.stock import RealtimeData
                from sqlalchemy import delete
                
                realtime_cutoff = datetime.now() - timedelta(days=30)
                delete_stmt = delete(RealtimeData).where(RealtimeData.timestamp < realtime_cutoff)
                result = await session.execute(delete_stmt)
                deleted_realtime = result.rowcount
                
                await session.commit()
                
                return {
                    "deleted_realtime": deleted_realtime,
                    "cutoff_date": cutoff_date.isoformat()
                }
        
        result = run_async_task(_cleanup())
        
        logger.info(f"数据清理完成，删除{result['deleted_realtime']}条实时数据")
        
        return {
            "status": "success",
            "message": "数据清理完成",
            "deleted_realtime": result["deleted_realtime"]
        }
        
    except Exception as exc:
        logger.error(f"数据清理任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=600, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def full_data_sync_task(self):
    """完整数据同步任务"""
    try:
        logger.info("开始执行完整数据同步任务...")
        
        async def _full_sync():
            sync_service = DataSyncService()
            return await sync_service.full_sync()
        
        success = run_async_task(_full_sync())
        
        if success:
            logger.info("完整数据同步任务完成")
            return {"status": "success", "message": "完整数据同步成功"}
        else:
            logger.error("完整数据同步失败")
            raise Exception("完整数据同步失败")
            
    except Exception as exc:
        logger.error(f"完整数据同步任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=30)
def incremental_data_sync_task(self):
    """增量数据同步任务"""
    try:
        logger.info("开始执行增量数据同步任务...")
        
        async def _incremental_sync():
            sync_service = DataSyncService()
            return await sync_service.incremental_sync()
        
        success = run_async_task(_incremental_sync())
        
        if success:
            logger.info("增量数据同步任务完成")
            return {"status": "success", "message": "增量数据同步成功"}
        else:
            logger.warning("增量数据同步未获取到新数据")
            return {"status": "warning", "message": "增量数据同步未获取到新数据"}
            
    except Exception as exc:
        logger.error(f"增量数据同步任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=30, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}
