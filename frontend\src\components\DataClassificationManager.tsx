/**
 * 数据分类管理组件
 * 提供细粒度的数据分类管理功能
 */

import React, { useState, useEffect } from 'react'
import {
  Card, Row, Col, Statistic, Table, Tag, Progress, Button, Modal, 
  Descriptions, Alert, Space, Tooltip, Badge, Tabs, message,
  Popconfirm, Select, InputNumber, Checkbox
} from 'antd'
import {
  DatabaseOutlined, FolderOutlined, FileTextOutlined, 
  CleanupOutlined, EyeOutlined, WarningOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons'
import { dataClassificationService } from '../services/dataClassificationService'

const { TabPane } = Tabs

interface DataCategory {
  category_key: string
  category_name: string
  description: string
  subcategories: Array<{
    key: string
    name: string
    table: string
    record_count: number
  }>
  total_records: number
  last_update: string | null
}

interface SubcategoryStats {
  subcategory_key: string
  subcategory_name: string
  table_name: string
  record_count: number
  latest_record_time: string | null
  data_size_mb: number
  data_quality_score: number
}

const DataClassificationManager: React.FC = () => {
  const [categories, setCategories] = useState<DataCategory[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [subcategoryStats, setSubcategoryStats] = useState<SubcategoryStats[]>([])
  const [sampleData, setSampleData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [sampleModalVisible, setSampleModalVisible] = useState(false)
  const [cleanupModalVisible, setCleanupModalVisible] = useState(false)
  const [cleanupOptions, setCleanupOptions] = useState({
    remove_duplicates: false,
    remove_old_data: false,
    days_to_keep: 365
  })

  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    if (selectedCategory) {
      loadSubcategoryStats(selectedCategory)
    }
  }, [selectedCategory])

  const loadCategories = async () => {
    try {
      setLoading(true)
      const data = await dataClassificationService.getCategories()
      setCategories(data)
      if (data.length > 0 && !selectedCategory) {
        setSelectedCategory(data[0].category_key)
      }
    } catch (error) {
      message.error('加载数据分类失败')
      console.error('加载数据分类失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSubcategoryStats = async (categoryKey: string) => {
    try {
      setLoading(true)
      const stats = await dataClassificationService.getSubcategoryStats(categoryKey)
      setSubcategoryStats(stats)
    } catch (error) {
      message.error('加载子分类统计失败')
      console.error('加载子分类统计失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleViewSample = async (categoryKey: string, subcategoryKey: string) => {
    try {
      setLoading(true)
      const sample = await dataClassificationService.getSampleData(categoryKey, subcategoryKey, 5)
      setSampleData(sample)
      setSampleModalVisible(true)
    } catch (error) {
      message.error('获取样本数据失败')
      console.error('获取样本数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCleanup = async (categoryKey: string, subcategoryKey: string) => {
    try {
      setLoading(true)
      await dataClassificationService.cleanupData(categoryKey, subcategoryKey, cleanupOptions)
      message.success('数据清理完成')
      setCleanupModalVisible(false)
      loadSubcategoryStats(selectedCategory)
    } catch (error) {
      message.error('数据清理失败')
      console.error('数据清理失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getQualityColor = (score: number) => {
    if (score >= 90) return '#52c41a'
    if (score >= 70) return '#faad14'
    if (score >= 50) return '#fa8c16'
    return '#f5222d'
  }

  const getQualityStatus = (score: number) => {
    if (score >= 90) return 'success'
    if (score >= 70) return 'warning'
    return 'exception'
  }

  const categoryColumns = [
    {
      title: '分类名称',
      dataIndex: 'category_name',
      key: 'category_name',
      render: (text: string, record: DataCategory) => (
        <Space>
          <FolderOutlined style={{ color: '#1890ff' }} />
          <span>{text}</span>
          <Tag color="blue">{record.subcategories.length} 个子类</Tag>
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '总记录数',
      dataIndex: 'total_records',
      key: 'total_records',
      render: (count: number) => (
        <Statistic 
          value={count} 
          valueStyle={{ fontSize: '14px' }}
          formatter={(value) => value?.toLocaleString()}
        />
      )
    },
    {
      title: '最后更新',
      dataIndex: 'last_update',
      key: 'last_update',
      render: (time: string | null) => (
        time ? new Date(time).toLocaleString() : '无数据'
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: DataCategory) => (
        <Button 
          type="primary" 
          size="small"
          onClick={() => setSelectedCategory(record.category_key)}
        >
          查看详情
        </Button>
      )
    }
  ]

  const subcategoryColumns = [
    {
      title: '子分类',
      dataIndex: 'subcategory_name',
      key: 'subcategory_name',
      render: (text: string, record: SubcategoryStats) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
          <Tag color="geekblue">{record.table_name}</Tag>
        </Space>
      )
    },
    {
      title: '记录数',
      dataIndex: 'record_count',
      key: 'record_count',
      render: (count: number) => count.toLocaleString()
    },
    {
      title: '数据大小',
      dataIndex: 'data_size_mb',
      key: 'data_size_mb',
      render: (size: number) => `${size} MB`
    },
    {
      title: '数据质量',
      dataIndex: 'data_quality_score',
      key: 'data_quality_score',
      render: (score: number) => (
        <Space>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={getQualityColor(score)}
            status={getQualityStatus(score)}
          />
          <span>{score}%</span>
        </Space>
      )
    },
    {
      title: '最新数据',
      dataIndex: 'latest_record_time',
      key: 'latest_record_time',
      render: (time: string | null) => (
        time ? new Date(time).toLocaleDateString() : '无数据'
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: SubcategoryStats) => (
        <Space>
          <Tooltip title="查看样本数据">
            <Button 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleViewSample(selectedCategory, record.subcategory_key)}
            />
          </Tooltip>
          <Tooltip title="数据清理">
            <Button 
              icon={<CleanupOutlined />} 
              size="small"
              onClick={() => setCleanupModalVisible(true)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const renderOverview = () => {
    const totalRecords = categories.reduce((sum, cat) => sum + cat.total_records, 0)
    const totalSubcategories = categories.reduce((sum, cat) => sum + cat.subcategories.length, 0)
    const avgQuality = subcategoryStats.length > 0 
      ? subcategoryStats.reduce((sum, stat) => sum + stat.data_quality_score, 0) / subcategoryStats.length
      : 0

    return (
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据分类"
              value={categories.length}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="子分类"
              value={totalSubcategories}
              prefix={<FolderOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总记录数"
              value={totalRecords}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
              formatter={(value) => value?.toLocaleString()}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均质量"
              value={avgQuality}
              suffix="%"
              precision={1}
              prefix={avgQuality >= 80 ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
              valueStyle={{ color: avgQuality >= 80 ? '#52c41a' : '#faad14' }}
            />
          </Card>
        </Col>
      </Row>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card title="数据分类管理" extra={
        <Button onClick={loadCategories} loading={loading}>
          刷新数据
        </Button>
      }>
        {renderOverview()}
        
        <Tabs activeKey={selectedCategory} onChange={setSelectedCategory}>
          {categories.map(category => (
            <TabPane 
              tab={
                <Space>
                  <FolderOutlined />
                  {category.category_name}
                  <Badge count={category.subcategories.length} size="small" />
                </Space>
              } 
              key={category.category_key}
            >
              <Alert
                message={category.description}
                type="info"
                showIcon
                style={{ marginBottom: '16px' }}
              />
              
              <Table
                columns={subcategoryColumns}
                dataSource={subcategoryStats}
                rowKey="subcategory_key"
                loading={loading}
                size="small"
                pagination={false}
              />
            </TabPane>
          ))}
        </Tabs>
      </Card>

      {/* 样本数据模态框 */}
      <Modal
        title="样本数据"
        open={sampleModalVisible}
        onCancel={() => setSampleModalVisible(false)}
        footer={null}
        width={800}
      >
        {sampleData && (
          <div>
            <Descriptions size="small" column={2} style={{ marginBottom: '16px' }}>
              <Descriptions.Item label="表名">{sampleData.table_name}</Descriptions.Item>
              <Descriptions.Item label="样本数量">{sampleData.sample_count}</Descriptions.Item>
              <Descriptions.Item label="总字段数">{sampleData.total_columns}</Descriptions.Item>
            </Descriptions>
            
            <Table
              columns={sampleData.columns?.map((col: string) => ({
                title: col,
                dataIndex: col,
                key: col,
                ellipsis: true
              }))}
              dataSource={sampleData.sample_data}
              rowKey={(record, index) => index}
              size="small"
              scroll={{ x: true }}
              pagination={false}
            />
          </div>
        )}
      </Modal>

      {/* 数据清理模态框 */}
      <Modal
        title="数据清理"
        open={cleanupModalVisible}
        onOk={() => handleCleanup(selectedCategory, 'current')}
        onCancel={() => setCleanupModalVisible(false)}
        confirmLoading={loading}
      >
        <Alert
          message="数据清理操作不可逆，请谨慎操作"
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <Checkbox
            checked={cleanupOptions.remove_duplicates}
            onChange={(e) => setCleanupOptions({
              ...cleanupOptions,
              remove_duplicates: e.target.checked
            })}
          >
            删除重复数据
          </Checkbox>
          
          <Checkbox
            checked={cleanupOptions.remove_old_data}
            onChange={(e) => setCleanupOptions({
              ...cleanupOptions,
              remove_old_data: e.target.checked
            })}
          >
            删除过期数据
          </Checkbox>
          
          {cleanupOptions.remove_old_data && (
            <div style={{ marginLeft: '24px' }}>
              <span>保留天数：</span>
              <InputNumber
                value={cleanupOptions.days_to_keep}
                onChange={(value) => setCleanupOptions({
                  ...cleanupOptions,
                  days_to_keep: value || 365
                })}
                min={1}
                max={3650}
                style={{ marginLeft: '8px' }}
              />
            </div>
          )}
        </Space>
      </Modal>
    </div>
  )
}

export default DataClassificationManager
