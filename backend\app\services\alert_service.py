"""
预警系统服务模块
"""

import asyncio
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func, or_

from app.core.logging import get_logger
from app.core.database import AsyncSessionLocal
from app.models.alerts import AlertRule, AlertEvent, AlertTemplate, NotificationChannel
from app.services.data_storage import DataStorageService
from app.services.indicator_storage import IndicatorStorageService
from app.services.ai_storage import AIPredictionStorageService

logger = get_logger(__name__)


class AlertRuleEngine:
    """预警规则引擎"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def evaluate_price_alerts(self, stock_code: str) -> List[Dict[str, Any]]:
        """评估价格预警"""
        try:
            logger.info(f"评估股票 {stock_code} 的价格预警...")
            
            # 获取价格预警规则
            rules = await self._get_active_rules(stock_code, "price")
            if not rules:
                return []
            
            # 获取最新价格数据
            async with DataStorageService() as data_storage:
                klines = await data_storage.get_kline_data(stock_code, "daily", limit=1)
                realtime = await data_storage.get_realtime_data(stock_code)
            
            if not klines and not realtime:
                logger.warning(f"股票 {stock_code} 没有价格数据")
                return []
            
            current_price = float(realtime.current_price) if realtime else float(klines[-1].close_price)
            
            triggered_events = []
            
            for rule in rules:
                try:
                    event = await self._evaluate_price_rule(rule, current_price, klines, realtime)
                    if event:
                        triggered_events.append(event)
                except Exception as e:
                    logger.error(f"评估价格规则 {rule.id} 失败: {e}")
            
            return triggered_events
            
        except Exception as e:
            logger.error(f"评估价格预警失败: {e}")
            return []
    
    async def evaluate_indicator_alerts(self, stock_code: str) -> List[Dict[str, Any]]:
        """评估技术指标预警"""
        try:
            logger.info(f"评估股票 {stock_code} 的技术指标预警...")
            
            # 获取指标预警规则
            rules = await self._get_active_rules(stock_code, "indicator")
            if not rules:
                return []
            
            # 获取技术指标数据
            async with IndicatorStorageService() as indicator_storage:
                indicators = await indicator_storage.get_indicators(stock_code, "daily", limit=5)
            
            if not indicators:
                logger.warning(f"股票 {stock_code} 没有技术指标数据")
                return []
            
            triggered_events = []
            
            for rule in rules:
                try:
                    event = await self._evaluate_indicator_rule(rule, indicators)
                    if event:
                        triggered_events.append(event)
                except Exception as e:
                    logger.error(f"评估指标规则 {rule.id} 失败: {e}")
            
            return triggered_events
            
        except Exception as e:
            logger.error(f"评估技术指标预警失败: {e}")
            return []
    
    async def evaluate_ai_alerts(self, stock_code: str) -> List[Dict[str, Any]]:
        """评估AI信号预警"""
        try:
            logger.info(f"评估股票 {stock_code} 的AI信号预警...")
            
            # 获取AI预警规则
            rules = await self._get_active_rules(stock_code, "ai")
            if not rules:
                return []
            
            # 获取AI预测数据
            async with AIPredictionStorageService() as ai_storage:
                predictions = await ai_storage.get_predictions(stock_code=stock_code, limit=3)
                patterns = await ai_storage.get_pattern_recognitions(stock_code=stock_code, limit=3)
            
            if not predictions and not patterns:
                logger.warning(f"股票 {stock_code} 没有AI预测数据")
                return []
            
            triggered_events = []
            
            for rule in rules:
                try:
                    event = await self._evaluate_ai_rule(rule, predictions, patterns)
                    if event:
                        triggered_events.append(event)
                except Exception as e:
                    logger.error(f"评估AI规则 {rule.id} 失败: {e}")
            
            return triggered_events
            
        except Exception as e:
            logger.error(f"评估AI信号预警失败: {e}")
            return []
    
    async def evaluate_volume_alerts(self, stock_code: str) -> List[Dict[str, Any]]:
        """评估成交量预警"""
        try:
            logger.info(f"评估股票 {stock_code} 的成交量预警...")
            
            # 获取成交量预警规则
            rules = await self._get_active_rules(stock_code, "volume")
            if not rules:
                return []
            
            # 获取成交量数据
            async with DataStorageService() as data_storage:
                klines = await data_storage.get_kline_data(stock_code, "daily", limit=20)
            
            if not klines:
                logger.warning(f"股票 {stock_code} 没有成交量数据")
                return []
            
            triggered_events = []
            
            for rule in rules:
                try:
                    event = await self._evaluate_volume_rule(rule, klines)
                    if event:
                        triggered_events.append(event)
                except Exception as e:
                    logger.error(f"评估成交量规则 {rule.id} 失败: {e}")
            
            return triggered_events
            
        except Exception as e:
            logger.error(f"评估成交量预警失败: {e}")
            return []
    
    async def _get_active_rules(self, stock_code: str, rule_type: str) -> List[AlertRule]:
        """获取活跃的预警规则"""
        try:
            stmt = select(AlertRule).where(
                and_(
                    AlertRule.stock_code == stock_code,
                    AlertRule.rule_type == rule_type,
                    AlertRule.is_active == True
                )
            )
            result = await self.session.execute(stmt)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"获取预警规则失败: {e}")
            return []
    
    async def _evaluate_price_rule(self, rule: AlertRule, current_price: float, klines: List, realtime) -> Optional[Dict[str, Any]]:
        """评估价格规则"""
        try:
            target_value = float(rule.target_value) if rule.target_value else 0
            condition_type = rule.condition_type
            
            triggered = False
            trigger_message = ""
            
            if condition_type == ">":
                if current_price > target_value:
                    triggered = True
                    trigger_message = f"价格突破上方 {target_value}，当前价格 {current_price}"
            
            elif condition_type == "<":
                if current_price < target_value:
                    triggered = True
                    trigger_message = f"价格跌破下方 {target_value}，当前价格 {current_price}"
            
            elif condition_type == "between":
                target_max = float(rule.target_value_max) if rule.target_value_max else target_value
                if target_value <= current_price <= target_max:
                    triggered = True
                    trigger_message = f"价格进入区间 [{target_value}, {target_max}]，当前价格 {current_price}"
            
            elif condition_type == "change_pct":
                # 价格变化百分比预警
                if len(klines) >= 2:
                    prev_price = float(klines[-2].close_price)
                    change_pct = (current_price - prev_price) / prev_price * 100
                    if abs(change_pct) >= target_value:
                        triggered = True
                        trigger_message = f"价格变化 {change_pct:.2f}%，超过阈值 {target_value}%"
            
            if triggered:
                # 检查触发频率
                if await self._should_trigger(rule):
                    return {
                        "rule_id": rule.id,
                        "stock_code": rule.stock_code,
                        "event_type": "price",
                        "alert_level": rule.alert_level,
                        "event_title": f"{rule.rule_name} - {rule.stock_code}",
                        "event_message": trigger_message,
                        "trigger_value": Decimal(str(current_price)),
                        "trigger_data": {
                            "current_price": current_price,
                            "target_value": target_value,
                            "condition_type": condition_type
                        },
                        "market_data": {
                            "realtime": realtime.__dict__ if realtime else None,
                            "latest_kline": klines[-1].__dict__ if klines else None
                        }
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"评估价格规则失败: {e}")
            return None
    
    async def _evaluate_indicator_rule(self, rule: AlertRule, indicators: List) -> Optional[Dict[str, Any]]:
        """评估技术指标规则"""
        try:
            if not indicators:
                return None
            
            latest_indicator = indicators[-1]
            indicator_name = rule.indicator_name
            target_value = float(rule.target_value) if rule.target_value else 0
            condition_type = rule.condition_type
            
            # 获取指标值
            indicator_value = None
            if hasattr(latest_indicator, indicator_name):
                indicator_value = getattr(latest_indicator, indicator_name)
                if indicator_value:
                    indicator_value = float(indicator_value)
            
            if indicator_value is None:
                return None
            
            triggered = False
            trigger_message = ""
            
            if condition_type == ">":
                if indicator_value > target_value:
                    triggered = True
                    trigger_message = f"{indicator_name.upper()} 突破上方 {target_value}，当前值 {indicator_value:.4f}"
            
            elif condition_type == "<":
                if indicator_value < target_value:
                    triggered = True
                    trigger_message = f"{indicator_name.upper()} 跌破下方 {target_value}，当前值 {indicator_value:.4f}"
            
            elif condition_type == "cross":
                # 金叉死叉预警
                if len(indicators) >= 2 and indicator_name in ["macd", "kdj_k"]:
                    prev_indicator = indicators[-2]
                    if indicator_name == "macd" and latest_indicator.macd_signal and prev_indicator.macd_signal:
                        current_macd = float(latest_indicator.macd)
                        current_signal = float(latest_indicator.macd_signal)
                        prev_macd = float(prev_indicator.macd)
                        prev_signal = float(prev_indicator.macd_signal)
                        
                        if prev_macd <= prev_signal and current_macd > current_signal:
                            triggered = True
                            trigger_message = f"MACD 金叉信号，MACD: {current_macd:.6f}, Signal: {current_signal:.6f}"
                        elif prev_macd >= prev_signal and current_macd < current_signal:
                            triggered = True
                            trigger_message = f"MACD 死叉信号，MACD: {current_macd:.6f}, Signal: {current_signal:.6f}"
            
            if triggered and await self._should_trigger(rule):
                return {
                    "rule_id": rule.id,
                    "stock_code": rule.stock_code,
                    "event_type": "indicator",
                    "alert_level": rule.alert_level,
                    "event_title": f"{rule.rule_name} - {rule.stock_code}",
                    "event_message": trigger_message,
                    "trigger_value": Decimal(str(indicator_value)),
                    "trigger_data": {
                        "indicator_name": indicator_name,
                        "indicator_value": indicator_value,
                        "target_value": target_value,
                        "condition_type": condition_type
                    }
                }
            
            return None
            
        except Exception as e:
            logger.error(f"评估技术指标规则失败: {e}")
            return None
    
    async def _evaluate_ai_rule(self, rule: AlertRule, predictions: List, patterns: List) -> Optional[Dict[str, Any]]:
        """评估AI信号规则"""
        try:
            if not predictions and not patterns:
                return None
            
            rule_config = rule.rule_config or {}
            target_value = float(rule.target_value) if rule.target_value else 0.7
            
            triggered = False
            trigger_message = ""
            trigger_data = {}
            
            # 检查AI预测信号
            if predictions and rule_config.get("check_predictions", True):
                latest_prediction = predictions[-1]
                confidence = float(latest_prediction.confidence_score) if latest_prediction.confidence_score else 0
                
                if confidence >= target_value:
                    direction = latest_prediction.trend_direction
                    if rule_config.get("direction") == direction or not rule_config.get("direction"):
                        triggered = True
                        trigger_message = f"AI预测信号: {direction} 方向，置信度 {confidence:.4f}"
                        trigger_data["prediction"] = {
                            "direction": direction,
                            "confidence": confidence,
                            "predicted_price": float(latest_prediction.predicted_price) if latest_prediction.predicted_price else None
                        }
            
            # 检查形态识别信号
            if patterns and rule_config.get("check_patterns", True):
                latest_pattern = patterns[-1]
                confidence = float(latest_pattern.confidence) if latest_pattern.confidence else 0
                
                if confidence >= target_value:
                    pattern_type = latest_pattern.pattern_type
                    if rule_config.get("pattern_type") == pattern_type or not rule_config.get("pattern_type"):
                        triggered = True
                        trigger_message = f"形态识别信号: {latest_pattern.pattern_name}，置信度 {confidence:.4f}"
                        trigger_data["pattern"] = {
                            "pattern_name": latest_pattern.pattern_name,
                            "pattern_type": pattern_type,
                            "confidence": confidence
                        }
            
            if triggered and await self._should_trigger(rule):
                return {
                    "rule_id": rule.id,
                    "stock_code": rule.stock_code,
                    "event_type": "ai",
                    "alert_level": rule.alert_level,
                    "event_title": f"{rule.rule_name} - {rule.stock_code}",
                    "event_message": trigger_message,
                    "trigger_value": Decimal(str(target_value)),
                    "trigger_data": trigger_data
                }
            
            return None
            
        except Exception as e:
            logger.error(f"评估AI信号规则失败: {e}")
            return None
    
    async def _evaluate_volume_rule(self, rule: AlertRule, klines: List) -> Optional[Dict[str, Any]]:
        """评估成交量规则"""
        try:
            if len(klines) < 2:
                return None
            
            latest_kline = klines[-1]
            current_volume = latest_kline.volume
            target_value = float(rule.target_value) if rule.target_value else 0
            condition_type = rule.condition_type
            
            triggered = False
            trigger_message = ""
            
            if condition_type == "volume_spike":
                # 成交量放大预警
                avg_volume = sum(k.volume for k in klines[-10:]) / min(10, len(klines))
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
                
                if volume_ratio >= target_value:
                    triggered = True
                    trigger_message = f"成交量放大 {volume_ratio:.2f} 倍，当前成交量 {current_volume:,}"
            
            elif condition_type == ">":
                if current_volume > target_value:
                    triggered = True
                    trigger_message = f"成交量突破 {target_value:,}，当前成交量 {current_volume:,}"
            
            if triggered and await self._should_trigger(rule):
                return {
                    "rule_id": rule.id,
                    "stock_code": rule.stock_code,
                    "event_type": "volume",
                    "alert_level": rule.alert_level,
                    "event_title": f"{rule.rule_name} - {rule.stock_code}",
                    "event_message": trigger_message,
                    "trigger_value": Decimal(str(current_volume)),
                    "trigger_data": {
                        "current_volume": current_volume,
                        "target_value": target_value,
                        "condition_type": condition_type
                    }
                }
            
            return None
            
        except Exception as e:
            logger.error(f"评估成交量规则失败: {e}")
            return None
    
    async def _should_trigger(self, rule: AlertRule) -> bool:
        """检查是否应该触发预警"""
        try:
            if rule.trigger_frequency == "always":
                return True
            
            if rule.trigger_frequency == "once" and rule.last_triggered_at:
                return False
            
            if rule.trigger_frequency == "daily" and rule.last_triggered_at:
                last_trigger_date = rule.last_triggered_at.date()
                today = date.today()
                if last_trigger_date >= today:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查触发频率失败: {e}")
            return False


class AlertNotificationService:
    """预警通知服务"""

    def __init__(self):
        self.session: Optional[AsyncSession] = None

    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def send_alert_notification(self, event_data: Dict[str, Any]) -> bool:
        """发送预警通知"""
        try:
            logger.info(f"发送预警通知: {event_data['event_title']}")

            # 创建预警事件记录
            event = AlertEvent(
                rule_id=event_data["rule_id"],
                stock_code=event_data["stock_code"],
                event_type=event_data["event_type"],
                alert_level=event_data["alert_level"],
                event_title=event_data["event_title"],
                event_message=event_data["event_message"],
                trigger_value=event_data.get("trigger_value"),
                trigger_data=event_data.get("trigger_data", {}),
                market_data=event_data.get("market_data", {}),
                triggered_at=datetime.utcnow()
            )

            self.session.add(event)
            await self.session.commit()

            # 获取预警规则的通知配置
            rule = await self._get_alert_rule(event_data["rule_id"])
            if not rule:
                logger.error(f"找不到预警规则 {event_data['rule_id']}")
                return False

            # 发送通知
            notification_success = await self._send_notifications(rule, event_data)

            # 更新规则触发状态
            await self._update_rule_trigger_status(rule)

            # 更新事件处理状态
            event.status = "sent" if notification_success else "failed"
            event.processed_at = datetime.utcnow()
            await self.session.commit()

            return notification_success

        except Exception as e:
            logger.error(f"发送预警通知失败: {e}")
            return False

    async def _get_alert_rule(self, rule_id: int) -> Optional[AlertRule]:
        """获取预警规则"""
        try:
            stmt = select(AlertRule).where(AlertRule.id == rule_id)
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取预警规则失败: {e}")
            return None

    async def _send_notifications(self, rule: AlertRule, event_data: Dict[str, Any]) -> bool:
        """发送通知到各个渠道"""
        try:
            notification_methods = rule.notification_methods or {}
            success_count = 0
            total_count = 0

            # 邮件通知
            if notification_methods.get("email", {}).get("enabled", False):
                total_count += 1
                if await self._send_email_notification(rule, event_data):
                    success_count += 1

            # 短信通知
            if notification_methods.get("sms", {}).get("enabled", False):
                total_count += 1
                if await self._send_sms_notification(rule, event_data):
                    success_count += 1

            # Webhook通知
            if notification_methods.get("webhook", {}).get("enabled", False):
                total_count += 1
                if await self._send_webhook_notification(rule, event_data):
                    success_count += 1

            # 推送通知
            if notification_methods.get("push", {}).get("enabled", False):
                total_count += 1
                if await self._send_push_notification(rule, event_data):
                    success_count += 1

            # 如果没有配置通知方式，记录日志
            if total_count == 0:
                logger.info(f"预警规则 {rule.id} 未配置通知方式，仅记录事件")
                return True

            return success_count > 0

        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            return False

    async def _send_email_notification(self, rule: AlertRule, event_data: Dict[str, Any]) -> bool:
        """发送邮件通知"""
        try:
            # 这里实现邮件发送逻辑
            # 暂时返回模拟结果
            logger.info(f"模拟发送邮件通知: {event_data['event_title']}")
            return True
        except Exception as e:
            logger.error(f"发送邮件通知失败: {e}")
            return False

    async def _send_sms_notification(self, rule: AlertRule, event_data: Dict[str, Any]) -> bool:
        """发送短信通知"""
        try:
            # 这里实现短信发送逻辑
            # 暂时返回模拟结果
            logger.info(f"模拟发送短信通知: {event_data['event_title']}")
            return True
        except Exception as e:
            logger.error(f"发送短信通知失败: {e}")
            return False

    async def _send_webhook_notification(self, rule: AlertRule, event_data: Dict[str, Any]) -> bool:
        """发送Webhook通知"""
        try:
            # 这里实现Webhook发送逻辑
            # 暂时返回模拟结果
            logger.info(f"模拟发送Webhook通知: {event_data['event_title']}")
            return True
        except Exception as e:
            logger.error(f"发送Webhook通知失败: {e}")
            return False

    async def _send_push_notification(self, rule: AlertRule, event_data: Dict[str, Any]) -> bool:
        """发送推送通知"""
        try:
            # 这里实现推送通知逻辑
            # 暂时返回模拟结果
            logger.info(f"模拟发送推送通知: {event_data['event_title']}")
            return True
        except Exception as e:
            logger.error(f"发送推送通知失败: {e}")
            return False

    async def _update_rule_trigger_status(self, rule: AlertRule):
        """更新规则触发状态"""
        try:
            rule.last_triggered_at = datetime.utcnow()
            rule.trigger_count += 1
            await self.session.commit()
        except Exception as e:
            logger.error(f"更新规则触发状态失败: {e}")


class AlertManagementService:
    """预警管理服务"""

    def __init__(self):
        self.session: Optional[AsyncSession] = None

    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def create_alert_rule(self, rule_data: Dict[str, Any]) -> int:
        """创建预警规则"""
        try:
            logger.info(f"创建预警规则: {rule_data.get('rule_name')}")

            alert_rule = AlertRule(
                user_id=rule_data.get("user_id"),
                stock_code=rule_data["stock_code"],
                rule_name=rule_data["rule_name"],
                rule_type=rule_data["rule_type"],
                alert_level=rule_data.get("alert_level", "medium"),
                condition_type=rule_data["condition_type"],
                target_value=Decimal(str(rule_data["target_value"])) if rule_data.get("target_value") else None,
                target_value_max=Decimal(str(rule_data["target_value_max"])) if rule_data.get("target_value_max") else None,
                indicator_name=rule_data.get("indicator_name"),
                rule_config=rule_data.get("rule_config", {}),
                trigger_frequency=rule_data.get("trigger_frequency", "once"),
                notification_methods=rule_data.get("notification_methods", {}),
                message_template=rule_data.get("message_template")
            )

            self.session.add(alert_rule)
            await self.session.commit()

            logger.info(f"预警规则创建成功，ID: {alert_rule.id}")
            return alert_rule.id

        except Exception as e:
            logger.error(f"创建预警规则失败: {e}")
            await self.session.rollback()
            return 0

    async def get_alert_rules(self, user_id: Optional[int] = None, stock_code: Optional[str] = None,
                            rule_type: Optional[str] = None, is_active: Optional[bool] = None) -> List[AlertRule]:
        """获取预警规则列表"""
        try:
            stmt = select(AlertRule)

            conditions = []
            if user_id is not None:
                conditions.append(AlertRule.user_id == user_id)
            if stock_code:
                conditions.append(AlertRule.stock_code == stock_code)
            if rule_type:
                conditions.append(AlertRule.rule_type == rule_type)
            if is_active is not None:
                conditions.append(AlertRule.is_active == is_active)

            if conditions:
                stmt = stmt.where(and_(*conditions))

            stmt = stmt.order_by(desc(AlertRule.created_at))

            result = await self.session.execute(stmt)
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"获取预警规则失败: {e}")
            return []

    async def get_alert_events(self, rule_id: Optional[int] = None, stock_code: Optional[str] = None,
                             event_type: Optional[str] = None, alert_level: Optional[str] = None,
                             start_date: Optional[date] = None, end_date: Optional[date] = None,
                             limit: int = 100) -> List[AlertEvent]:
        """获取预警事件列表"""
        try:
            stmt = select(AlertEvent)

            conditions = []
            if rule_id:
                conditions.append(AlertEvent.rule_id == rule_id)
            if stock_code:
                conditions.append(AlertEvent.stock_code == stock_code)
            if event_type:
                conditions.append(AlertEvent.event_type == event_type)
            if alert_level:
                conditions.append(AlertEvent.alert_level == alert_level)
            if start_date:
                conditions.append(AlertEvent.triggered_at >= datetime.combine(start_date, datetime.min.time()))
            if end_date:
                conditions.append(AlertEvent.triggered_at <= datetime.combine(end_date, datetime.max.time()))

            if conditions:
                stmt = stmt.where(and_(*conditions))

            stmt = stmt.order_by(desc(AlertEvent.triggered_at)).limit(limit)

            result = await self.session.execute(stmt)
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"获取预警事件失败: {e}")
            return []

    async def create_default_templates(self):
        """创建默认预警模板"""
        try:
            templates = [
                {
                    "template_name": "价格突破预警",
                    "template_type": "price",
                    "title_template": "【价格预警】{stock_code} 价格突破",
                    "message_template": "股票 {stock_code} 价格 {trigger_value}，{condition_desc}。当前时间：{trigger_time}",
                    "default_config": {
                        "condition_type": ">",
                        "alert_level": "medium"
                    },
                    "variables": {
                        "stock_code": "股票代码",
                        "trigger_value": "触发值",
                        "condition_desc": "条件描述",
                        "trigger_time": "触发时间"
                    },
                    "is_system": True
                },
                {
                    "template_name": "技术指标预警",
                    "template_type": "indicator",
                    "title_template": "【指标预警】{stock_code} {indicator_name} 信号",
                    "message_template": "股票 {stock_code} 的 {indicator_name} 指标触发预警，当前值：{indicator_value}，{condition_desc}。",
                    "default_config": {
                        "condition_type": ">",
                        "alert_level": "medium"
                    },
                    "variables": {
                        "stock_code": "股票代码",
                        "indicator_name": "指标名称",
                        "indicator_value": "指标值",
                        "condition_desc": "条件描述"
                    },
                    "is_system": True
                },
                {
                    "template_name": "AI信号预警",
                    "template_type": "ai",
                    "title_template": "【AI预警】{stock_code} AI信号",
                    "message_template": "股票 {stock_code} 触发AI预警：{ai_signal}，置信度：{confidence}。建议关注。",
                    "default_config": {
                        "alert_level": "high"
                    },
                    "variables": {
                        "stock_code": "股票代码",
                        "ai_signal": "AI信号",
                        "confidence": "置信度"
                    },
                    "is_system": True
                }
            ]

            for template_data in templates:
                # 检查模板是否已存在
                stmt = select(AlertTemplate).where(AlertTemplate.template_name == template_data["template_name"])
                result = await self.session.execute(stmt)
                existing = result.scalar_one_or_none()

                if existing:
                    logger.info(f"模板 {template_data['template_name']} 已存在，跳过创建")
                    continue

                template = AlertTemplate(
                    template_name=template_data["template_name"],
                    template_type=template_data["template_type"],
                    title_template=template_data["title_template"],
                    message_template=template_data["message_template"],
                    default_config=template_data["default_config"],
                    variables=template_data["variables"],
                    is_system=template_data["is_system"]
                )

                self.session.add(template)

            await self.session.commit()
            logger.info("默认预警模板创建完成")

        except Exception as e:
            logger.error(f"创建默认模板失败: {e}")
            await self.session.rollback()
