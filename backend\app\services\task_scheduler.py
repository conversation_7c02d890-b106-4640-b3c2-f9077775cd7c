"""
任务调度器 - 实现自动化数据更新
支持定时任务、重试机制、任务监控
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.data_processor import DataProcessor
from app.core.config import settings

logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_configs: Dict[str, Dict] = {}
        self.auto_update_enabled = True
        
        # 默认任务配置
        self.default_configs = {
            "stock_basic_update": {
                "name": "股票基础信息更新",
                "interval_minutes": 60,  # 每小时更新一次
                "retry_count": 3,
                "enabled": True,
                "weekend_enabled": False
            },
            "stock_price_update": {
                "name": "股票价格更新",
                "interval_minutes": 5,   # 每5分钟更新一次
                "retry_count": 3,
                "enabled": True,
                "weekend_enabled": False
            },
            "financial_news_update": {
                "name": "财经新闻更新",
                "interval_minutes": 30,  # 每30分钟更新一次
                "retry_count": 2,
                "enabled": True,
                "weekend_enabled": True
            },
            "technical_indicators_update": {
                "name": "技术指标计算",
                "interval_minutes": 60,  # 每小时计算一次
                "retry_count": 2,
                "enabled": True,
                "weekend_enabled": False
            },
            "daily_full_update": {
                "name": "每日全量更新",
                "cron": "0 9 * * 1-5",  # 工作日早上9点
                "retry_count": 3,
                "enabled": True,
                "weekend_enabled": False
            }
        }
        
        self.load_task_configs()
    
    def load_task_configs(self):
        """加载任务配置"""
        # 从数据库或配置文件加载任务配置
        # 这里使用默认配置
        self.task_configs = self.default_configs.copy()
        logger.info("任务配置加载完成")
    
    async def start(self):
        """启动调度器"""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                await self.schedule_all_tasks()
                logger.info("任务调度器启动成功")
        except Exception as e:
            logger.error(f"启动任务调度器失败: {e}")
    
    async def stop(self):
        """停止调度器"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown()
            
            # 取消所有运行中的任务
            for task_id, task in self.running_tasks.items():
                if not task.done():
                    task.cancel()
                    logger.info(f"取消任务: {task_id}")
            
            self.running_tasks.clear()
            logger.info("任务调度器已停止")
        except Exception as e:
            logger.error(f"停止任务调度器失败: {e}")
    
    async def schedule_all_tasks(self):
        """调度所有任务"""
        if not self.auto_update_enabled:
            logger.info("自动更新已禁用，跳过任务调度")
            return
        
        for task_id, config in self.task_configs.items():
            if config.get("enabled", True):
                await self.schedule_task(task_id, config)
    
    async def schedule_task(self, task_id: str, config: Dict):
        """调度单个任务"""
        try:
            # 检查是否在周末且任务不支持周末运行
            if not config.get("weekend_enabled", False) and self.is_weekend():
                logger.info(f"跳过周末任务: {task_id}")
                return
            
            # 根据配置类型创建触发器
            if "cron" in config:
                # Cron表达式触发器
                trigger = CronTrigger.from_crontab(config["cron"])
            elif "interval_minutes" in config:
                # 间隔触发器
                trigger = IntervalTrigger(minutes=config["interval_minutes"])
            else:
                logger.warning(f"任务 {task_id} 配置无效，跳过调度")
                return
            
            # 添加任务到调度器
            self.scheduler.add_job(
                func=self.execute_task_with_retry,
                trigger=trigger,
                args=[task_id, config],
                id=task_id,
                name=config.get("name", task_id),
                replace_existing=True,
                max_instances=1  # 防止任务重叠执行
            )
            
            logger.info(f"任务 {task_id} 调度成功")
            
        except Exception as e:
            logger.error(f"调度任务 {task_id} 失败: {e}")
    
    async def execute_task_with_retry(self, task_id: str, config: Dict):
        """执行任务（带重试机制）"""
        retry_count = config.get("retry_count", 3)
        
        for attempt in range(retry_count + 1):
            try:
                logger.info(f"开始执行任务: {task_id} (尝试 {attempt + 1}/{retry_count + 1})")
                
                # 创建任务实例
                task = asyncio.create_task(self.execute_task(task_id))
                self.running_tasks[task_id] = task
                
                # 等待任务完成
                await task
                
                # 任务成功完成
                logger.info(f"任务 {task_id} 执行成功")
                break
                
            except asyncio.CancelledError:
                logger.info(f"任务 {task_id} 被取消")
                break
            except Exception as e:
                logger.error(f"任务 {task_id} 执行失败 (尝试 {attempt + 1}): {e}")
                
                if attempt < retry_count:
                    # 等待后重试
                    wait_time = min(60 * (2 ** attempt), 300)  # 指数退避，最大5分钟
                    logger.info(f"任务 {task_id} 将在 {wait_time} 秒后重试")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"任务 {task_id} 重试次数已用完，执行失败")
            finally:
                # 清理任务引用
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
    
    async def execute_task(self, task_id: str):
        """执行具体任务"""
        try:
            # 获取数据库会话
            db = next(get_db())
            processor = DataProcessor(db)
            
            # 创建模拟任务对象
            task_obj = type('Task', (), {
                'id': task_id,
                'progress': 0,
                'status': 'running',
                'records_processed': 0,
                'total_records': 0
            })()
            
            # 根据任务类型执行相应操作
            if task_id == "stock_basic_update":
                await processor.update_stock_basic_info(task_obj)
            elif task_id == "stock_price_update":
                await processor.update_stock_prices(task_obj)
            elif task_id == "financial_news_update":
                await processor.update_financial_news(task_obj)
            elif task_id == "technical_indicators_update":
                await processor.calculate_technical_indicators(task_obj)
            elif task_id == "daily_full_update":
                # 执行全量更新
                await processor.update_stock_basic_info(task_obj)
                await processor.update_stock_prices(task_obj)
                await processor.update_financial_news(task_obj)
                await processor.calculate_technical_indicators(task_obj)
            else:
                logger.warning(f"未知任务类型: {task_id}")
            
            db.close()
            
        except Exception as e:
            logger.error(f"执行任务 {task_id} 时发生错误: {e}")
            raise
    
    def update_task_config(self, task_id: str, config: Dict):
        """更新任务配置"""
        try:
            self.task_configs[task_id] = config
            
            # 重新调度任务
            if self.scheduler.running:
                # 移除旧任务
                try:
                    self.scheduler.remove_job(task_id)
                except:
                    pass
                
                # 添加新任务
                asyncio.create_task(self.schedule_task(task_id, config))
            
            logger.info(f"任务 {task_id} 配置更新成功")
            
        except Exception as e:
            logger.error(f"更新任务 {task_id} 配置失败: {e}")
    
    def enable_auto_update(self, enabled: bool):
        """启用/禁用自动更新"""
        self.auto_update_enabled = enabled
        
        if enabled:
            asyncio.create_task(self.schedule_all_tasks())
            logger.info("自动更新已启用")
        else:
            # 移除所有调度任务
            for task_id in self.task_configs.keys():
                try:
                    self.scheduler.remove_job(task_id)
                except:
                    pass
            logger.info("自动更新已禁用")
    
    def get_task_status(self) -> Dict[str, Dict]:
        """获取任务状态"""
        status = {}
        
        for task_id, config in self.task_configs.items():
            job = self.scheduler.get_job(task_id)
            is_running = task_id in self.running_tasks
            
            status[task_id] = {
                "name": config.get("name", task_id),
                "enabled": config.get("enabled", True),
                "is_running": is_running,
                "next_run_time": job.next_run_time.isoformat() if job and job.next_run_time else None,
                "last_run_time": None,  # 这里可以从数据库获取
                "config": config
            }
        
        return status
    
    def is_weekend(self) -> bool:
        """检查是否为周末"""
        return datetime.now().weekday() >= 5  # 5=Saturday, 6=Sunday
    
    def is_trading_hours(self) -> bool:
        """检查是否为交易时间"""
        now = datetime.now()
        
        # 周末不交易
        if self.is_weekend():
            return False
        
        # 交易时间: 9:30-11:30, 13:00-15:00
        current_time = now.time()
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()
        
        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)

# 全局调度器实例
task_scheduler = TaskScheduler()
