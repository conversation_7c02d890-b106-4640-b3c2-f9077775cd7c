"""
WebSocket路由
"""

import json
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from typing import Optional

from app.websocket.manager import manager, broadcaster
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None)
):
    """WebSocket主端点"""
    actual_client_id = await manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_client_message(actual_client_id, message)
            except json.JSONDecodeError:
                logger.error(f"客户端 {actual_client_id} 发送了无效的JSON数据: {data}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": "无效的JSON格式"
                }, actual_client_id)
            except Exception as e:
                logger.error(f"处理客户端消息失败: {e}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": "消息处理失败"
                }, actual_client_id)
                
    except WebSocketDisconnect:
        manager.disconnect(actual_client_id)
        logger.info(f"客户端 {actual_client_id} 断开连接")


async def handle_client_message(client_id: str, message: dict):
    """处理客户端消息"""
    message_type = message.get("type")
    
    if message_type == "subscribe":
        # 订阅股票
        stock_codes = message.get("stock_codes", [])
        for stock_code in stock_codes:
            manager.subscribe_stock(client_id, stock_code)
        
        await manager.send_personal_message({
            "type": "subscription_confirmed",
            "stock_codes": stock_codes,
            "message": f"已订阅 {len(stock_codes)} 只股票"
        }, client_id)
        
    elif message_type == "unsubscribe":
        # 取消订阅股票
        stock_codes = message.get("stock_codes", [])
        for stock_code in stock_codes:
            manager.unsubscribe_stock(client_id, stock_code)
        
        await manager.send_personal_message({
            "type": "unsubscription_confirmed",
            "stock_codes": stock_codes,
            "message": f"已取消订阅 {len(stock_codes)} 只股票"
        }, client_id)
        
    elif message_type == "ping":
        # 心跳检测
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": message.get("timestamp")
        }, client_id)
        
    elif message_type == "get_stats":
        # 获取连接统计
        stats = manager.get_connection_stats()
        await manager.send_personal_message({
            "type": "stats",
            "data": stats
        }, client_id)
        
    else:
        logger.warning(f"未知的消息类型: {message_type}")
        await manager.send_personal_message({
            "type": "error",
            "message": f"未知的消息类型: {message_type}"
        }, client_id)


@router.websocket("/ws/stock/{stock_code}")
async def stock_websocket_endpoint(
    websocket: WebSocket,
    stock_code: str,
    client_id: Optional[str] = Query(None)
):
    """特定股票的WebSocket端点"""
    actual_client_id = await manager.connect(websocket, client_id)
    
    # 自动订阅指定股票
    manager.subscribe_stock(actual_client_id, stock_code)
    
    try:
        await manager.send_personal_message({
            "type": "auto_subscribed",
            "stock_code": stock_code,
            "message": f"已自动订阅股票 {stock_code}"
        }, actual_client_id)
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_client_message(actual_client_id, message)
            except json.JSONDecodeError:
                logger.error(f"客户端 {actual_client_id} 发送了无效的JSON数据: {data}")
            except Exception as e:
                logger.error(f"处理客户端消息失败: {e}")
                
    except WebSocketDisconnect:
        manager.disconnect(actual_client_id)
        logger.info(f"客户端 {actual_client_id} 断开连接")


# 用于测试的HTTP端点
@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计"""
    return {
        "code": 200,
        "message": "WebSocket统计信息",
        "data": manager.get_connection_stats()
    }


@router.post("/ws/broadcast")
async def broadcast_test_message(message: dict):
    """广播测试消息（仅用于测试）"""
    await manager.broadcast({
        "type": "test_broadcast",
        "data": message,
        "timestamp": "2025-07-30T16:00:00"
    })
    
    return {
        "code": 200,
        "message": "测试消息已广播",
        "data": message
    }


@router.post("/ws/stock/{stock_code}/broadcast")
async def broadcast_stock_message(stock_code: str, message: dict):
    """向特定股票订阅者广播消息（仅用于测试）"""
    await manager.broadcast_to_stock_subscribers(stock_code, {
        "type": "test_stock_message",
        "stock_code": stock_code,
        "data": message,
        "timestamp": "2025-07-30T16:00:00"
    })
    
    return {
        "code": 200,
        "message": f"消息已广播给股票 {stock_code} 的订阅者",
        "data": message
    }
