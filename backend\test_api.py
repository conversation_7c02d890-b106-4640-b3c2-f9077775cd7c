#!/usr/bin/env python3
"""
简单的API测试脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_register():
    """测试用户注册"""
    url = f"{BASE_URL}/auth/register"
    data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "username": "testuser"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"注册响应状态码: {response.status_code}")
        print(f"注册响应内容: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"注册失败: {e}")
        return False

def test_login():
    """测试用户登录"""
    url = f"{BASE_URL}/auth/login"
    data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"登录响应状态码: {response.status_code}")
        result = response.json()
        print(f"登录响应内容: {result}")
        
        if response.status_code == 200 and 'data' in result:
            return result['data'].get('access_token')
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def test_protected_endpoint(token):
    """测试受保护的端点"""
    url = f"{BASE_URL}/auth/me"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"获取用户信息状态码: {response.status_code}")
        print(f"获取用户信息响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取用户信息失败: {e}")
        return False

def test_strategies():
    """测试获取策略列表"""
    url = f"{BASE_URL}/analytics/strategies"
    
    try:
        response = requests.get(url)
        print(f"获取策略列表状态码: {response.status_code}")
        print(f"获取策略列表响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取策略列表失败: {e}")
        return False

def main():
    print("开始API测试...")
    
    # 测试注册
    print("\n=== 测试用户注册 ===")
    register_success = test_register()
    
    # 测试登录
    print("\n=== 测试用户登录 ===")
    token = test_login()
    
    if token:
        # 测试受保护端点
        print("\n=== 测试受保护端点 ===")
        test_protected_endpoint(token)
    
    # 测试公开端点
    print("\n=== 测试公开端点 ===")
    test_strategies()
    
    print("\nAPI测试完成!")

if __name__ == "__main__":
    main()
