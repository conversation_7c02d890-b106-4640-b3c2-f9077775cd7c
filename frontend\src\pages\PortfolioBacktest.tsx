import React, { useState } from 'react'
import {
  Card,
  Form,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Space,
  Table,
  Statistic,
  Row,
  Col,
  Typography,
  Progress,
  Tag,
  message,
  Spin,
} from 'antd'
import {
  PlayCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface BacktestResult {
  totalReturn: number
  annualizedReturn: number
  volatility: number
  sharpeRatio: number
  maxDrawdown: number
  winRate: number
  trades: number
  benchmark: number
  alpha: number
  beta: number
}

interface PerformanceData {
  date: string
  portfolioValue: number
  benchmarkValue: number
  dailyReturn: number
}

const PortfolioBacktest: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<BacktestResult | null>(null)
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([])

  const handleBacktest = async (values: any) => {
    setLoading(true)
    try {
      // 模拟回测计算
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 模拟回测结果
      const mockResult: BacktestResult = {
        totalReturn: 45.67,
        annualizedReturn: 18.23,
        volatility: 22.45,
        sharpeRatio: 0.81,
        maxDrawdown: -15.34,
        winRate: 62.5,
        trades: 156,
        benchmark: 12.34,
        alpha: 5.89,
        beta: 1.12,
      }

      // 模拟性能数据
      const mockPerformance: PerformanceData[] = Array.from({ length: 252 }, (_, i) => {
        const date = dayjs().subtract(252 - i, 'day').format('YYYY-MM-DD')
        const portfolioValue = 100000 * (1 + (Math.random() - 0.4) * 0.02) * (1 + i * 0.001)
        const benchmarkValue = 100000 * (1 + (Math.random() - 0.45) * 0.015) * (1 + i * 0.0008)
        const dailyReturn = (Math.random() - 0.5) * 4
        
        return {
          date,
          portfolioValue,
          benchmarkValue,
          dailyReturn,
        }
      })

      setResult(mockResult)
      setPerformanceData(mockPerformance)
      message.success('回测完成！')
    } catch (error) {
      message.error('回测失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const performanceColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '组合价值',
      dataIndex: 'portfolioValue',
      key: 'portfolioValue',
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '基准价值',
      dataIndex: 'benchmarkValue',
      key: 'benchmarkValue',
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '日收益率',
      dataIndex: 'dailyReturn',
      key: 'dailyReturn',
      render: (value: number) => (
        <Tag color={value >= 0 ? 'green' : 'red'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Tag>
      ),
    },
  ]

  return (
    <div>
      <Title level={2}>策略回测</Title>
      
      <Card title="回测参数设置" style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleBacktest}
          initialValues={{
            strategy: 'balanced',
            benchmark: 'hs300',
            initialCapital: 100000,
            dateRange: [dayjs().subtract(1, 'year'), dayjs()],
            rebalanceFreq: 'monthly',
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <Form.Item
                label="投资策略"
                name="strategy"
                rules={[{ required: true, message: '请选择投资策略' }]}
              >
                <Select placeholder="请选择投资策略">
                  <Option value="growth">成长型策略</Option>
                  <Option value="value">价值型策略</Option>
                  <Option value="balanced">平衡型策略</Option>
                  <Option value="momentum">动量型策略</Option>
                  <Option value="meanReversion">均值回归策略</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label="基准指数"
                name="benchmark"
                rules={[{ required: true, message: '请选择基准指数' }]}
              >
                <Select placeholder="请选择基准指数">
                  <Option value="hs300">沪深300</Option>
                  <Option value="sz50">上证50</Option>
                  <Option value="zz500">中证500</Option>
                  <Option value="cyb">创业板指</Option>
                  <Option value="kc50">科创50</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label="初始资金"
                name="initialCapital"
                rules={[{ required: true, message: '请输入初始资金' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入初始资金"
                  min={10000}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label="回测时间"
                name="dateRange"
                rules={[{ required: true, message: '请选择回测时间范围' }]}
              >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label="再平衡频率"
                name="rebalanceFreq"
                rules={[{ required: true, message: '请选择再平衡频率' }]}
              >
                <Select placeholder="请选择再平衡频率">
                  <Option value="daily">每日</Option>
                  <Option value="weekly">每周</Option>
                  <Option value="monthly">每月</Option>
                  <Option value="quarterly">每季度</Option>
                  <Option value="yearly">每年</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label="手续费率"
                name="commission"
                initialValue={0.0003}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="手续费率"
                  min={0}
                  max={0.01}
                  step={0.0001}
                  formatter={value => `${(Number(value) * 100).toFixed(2)}%`}
                  parser={value => (parseFloat(value!.replace('%', '')) / 100).toString()}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              icon={<PlayCircleOutlined />}
              loading={loading}
              size="large"
            >
              开始回测
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {loading && (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>正在进行回测计算，请稍候...</Text>
            </div>
          </div>
        </Card>
      )}

      {result && !loading && (
        <>
          {/* 回测结果概览 */}
          <Card title={<><TrophyOutlined /> 回测结果概览</>} style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="总收益率"
                  value={result.totalReturn}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: result.totalReturn >= 0 ? '#3f8600' : '#cf1322' }}
                  prefix={result.totalReturn >= 0 ? <RiseOutlined /> : <FallOutlined />}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="年化收益率"
                  value={result.annualizedReturn}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: result.annualizedReturn >= 0 ? '#3f8600' : '#cf1322' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="波动率"
                  value={result.volatility}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="夏普比率"
                  value={result.sharpeRatio}
                  precision={2}
                  valueStyle={{ color: result.sharpeRatio >= 1 ? '#3f8600' : '#faad14' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="最大回撤"
                  value={result.maxDrawdown}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="胜率"
                  value={result.winRate}
                  precision={1}
                  suffix="%"
                  valueStyle={{ color: result.winRate >= 50 ? '#3f8600' : '#cf1322' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="交易次数"
                  value={result.trades}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="基准收益"
                  value={result.benchmark}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            </Row>
          </Card>

          {/* 风险指标 */}
          <Card title="风险分析" style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Card size="small" title="Alpha & Beta">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="Alpha"
                        value={result.alpha}
                        precision={2}
                        suffix="%"
                        valueStyle={{ color: result.alpha >= 0 ? '#3f8600' : '#cf1322' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="Beta"
                        value={result.beta}
                        precision={2}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card size="small" title="收益风险比">
                  <div style={{ marginBottom: 16 }}>
                    <Text>收益/风险比: </Text>
                    <Text strong style={{ color: '#3f8600' }}>
                      {(result.annualizedReturn / result.volatility).toFixed(2)}
                    </Text>
                  </div>
                  <Progress
                    percent={Math.min((result.annualizedReturn / result.volatility) * 50, 100)}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                </Card>
              </Col>
            </Row>
          </Card>

          {/* 详细表现数据 */}
          <Card title="详细表现数据">
            <Table
              dataSource={performanceData.slice(-30)} // 只显示最近30天
              columns={performanceColumns}
              rowKey="date"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              scroll={{ x: 600 }}
            />
          </Card>
        </>
      )}
    </div>
  )
}

export default PortfolioBacktest
