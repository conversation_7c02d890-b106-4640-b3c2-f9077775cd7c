/**
 * 股票数据管理服务
 * 对接重构后的股票数据管理API
 */

// ==================== 接口定义 ====================

export interface StockBasicInfo {
  stock_code: string
  stock_name: string
  exchange: string
  industry?: string
  status: string
  list_date?: string
  updated_at: string
}

export interface KlineData {
  trade_date: string
  open: number
  high: number
  low: number
  close: number
  volume: number
  turnover: number
  change_percent?: number
}

export interface RealtimeData {
  stock_code: string
  current_price: number
  change_amount: number
  change_percent: number
  open_price: number
  high_price: number
  low_price: number
  pre_close: number
  volume: number
  turnover: number
  pe_ratio?: number
  pb_ratio?: number
  total_market_cap?: number
  update_time: string
}

export interface UpdateResult {
  success: boolean
  message: string
  success_count: number
  error_count: number
  execution_time?: number
}

export interface BatchUpdateResult {
  total_success: number
  total_error: number
  details: Record<string, { success: number; error: number }>
}

export interface DataStats {
  total_stocks: number
  active_stocks: number
  last_update_time?: string
  kline_data_count: number
  realtime_data_count: number
}

export interface GetStockListParams {
  limit?: number
  offset?: number
  exchange?: string
  status?: string
}

// ==================== 服务类 ====================

class StockDataManagementService {
  private baseUrl = 'http://localhost:8000/api/v1/stock-data'

  // ==================== 数据查询 ====================

  /**
   * 获取股票列表
   */
  async getStockList(params: GetStockListParams = {}): Promise<StockBasicInfo[]> {
    try {
      const queryParams = new URLSearchParams()
      
      if (params.limit) queryParams.append('limit', params.limit.toString())
      if (params.offset) queryParams.append('offset', params.offset.toString())
      if (params.exchange) queryParams.append('exchange', params.exchange)
      if (params.status) queryParams.append('status', params.status)

      const response = await fetch(`${this.baseUrl}/stocks?${queryParams}`)
      
      if (!response.ok) {
        throw new Error(`获取股票列表失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('获取股票列表失败:', error)
      throw error
    }
  }

  /**
   * 获取股票K线数据
   */
  async getStockKline(stockCode: string, days: number = 30): Promise<KlineData[]> {
    try {
      const response = await fetch(
        `${this.baseUrl}/stocks/${stockCode}/kline?days=${days}`
      )
      
      if (!response.ok) {
        throw new Error(`获取K线数据失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error(`获取K线数据失败 ${stockCode}:`, error)
      throw error
    }
  }

  /**
   * 获取股票实时行情
   */
  async getStockRealtime(stockCode: string): Promise<RealtimeData | null> {
    try {
      const response = await fetch(`${this.baseUrl}/stocks/${stockCode}/realtime`)
      
      if (response.status === 404) {
        return null
      }
      
      if (!response.ok) {
        throw new Error(`获取实时数据失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error(`获取实时数据失败 ${stockCode}:`, error)
      throw error
    }
  }

  /**
   * 获取数据统计信息
   */
  async getDataStats(): Promise<DataStats> {
    try {
      const response = await fetch(`${this.baseUrl}/stats`)
      
      if (!response.ok) {
        throw new Error(`获取数据统计失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('获取数据统计失败:', error)
      throw error
    }
  }

  // ==================== 数据更新 ====================

  /**
   * 更新股票基础信息
   */
  async updateBasicInfo(): Promise<UpdateResult> {
    try {
      const response = await fetch(`${this.baseUrl}/update/basic-info`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`更新基础信息失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('更新基础信息失败:', error)
      throw error
    }
  }

  /**
   * 更新单只股票K线数据
   */
  async updateStockKline(stockCode: string, days: number = 30): Promise<UpdateResult> {
    try {
      const response = await fetch(
        `${this.baseUrl}/update/kline/${stockCode}?days=${days}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      
      if (!response.ok) {
        throw new Error(`更新K线数据失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error(`更新K线数据失败 ${stockCode}:`, error)
      throw error
    }
  }

  /**
   * 更新实时行情数据
   */
  async updateRealtimeData(stockCodes?: string): Promise<UpdateResult> {
    try {
      const queryParams = stockCodes ? `?stock_codes=${stockCodes}` : ''
      
      const response = await fetch(`${this.baseUrl}/update/realtime${queryParams}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`更新实时行情失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('更新实时行情失败:', error)
      throw error
    }
  }

  /**
   * 批量更新K线数据
   */
  async batchUpdateKline(stockCodes: string, days: number = 30): Promise<BatchUpdateResult> {
    try {
      const response = await fetch(
        `${this.baseUrl}/update/batch-kline?stock_codes=${stockCodes}&days=${days}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      
      if (!response.ok) {
        throw new Error(`批量更新K线失败: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('批量更新K线失败:', error)
      throw error
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 搜索股票
   */
  async searchStocks(keyword: string): Promise<StockBasicInfo[]> {
    try {
      const stocks = await this.getStockList({ limit: 1000 })
      
      return stocks.filter(stock =>
        stock.stock_code.includes(keyword) ||
        stock.stock_name.includes(keyword)
      )
    } catch (error) {
      console.error('搜索股票失败:', error)
      throw error
    }
  }

  /**
   * 获取交易所股票数量统计
   */
  async getExchangeStats(): Promise<Record<string, number>> {
    try {
      const stocks = await this.getStockList({ limit: 10000 })
      
      const stats: Record<string, number> = {}
      stocks.forEach(stock => {
        stats[stock.exchange] = (stats[stock.exchange] || 0) + 1
      })
      
      return stats
    } catch (error) {
      console.error('获取交易所统计失败:', error)
      throw error
    }
  }

  /**
   * 检查数据完整性
   */
  async checkDataIntegrity(): Promise<{
    stocks_without_kline: string[]
    stocks_without_realtime: string[]
    outdated_data: string[]
  }> {
    try {
      const stocks = await this.getStockList({ limit: 100 })
      const result = {
        stocks_without_kline: [] as string[],
        stocks_without_realtime: [] as string[],
        outdated_data: [] as string[]
      }

      for (const stock of stocks) {
        // 检查K线数据
        try {
          const klineData = await this.getStockKline(stock.stock_code, 5)
          if (klineData.length === 0) {
            result.stocks_without_kline.push(stock.stock_code)
          }
        } catch {
          result.stocks_without_kline.push(stock.stock_code)
        }

        // 检查实时数据
        try {
          const realtimeData = await this.getStockRealtime(stock.stock_code)
          if (!realtimeData) {
            result.stocks_without_realtime.push(stock.stock_code)
          } else {
            // 检查数据是否过期（超过1天）
            const updateTime = new Date(realtimeData.update_time)
            const now = new Date()
            const diffHours = (now.getTime() - updateTime.getTime()) / (1000 * 60 * 60)
            
            if (diffHours > 24) {
              result.outdated_data.push(stock.stock_code)
            }
          }
        } catch {
          result.stocks_without_realtime.push(stock.stock_code)
        }
      }

      return result
    } catch (error) {
      console.error('检查数据完整性失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const stockDataManagementService = new StockDataManagementService()
export default stockDataManagementService
