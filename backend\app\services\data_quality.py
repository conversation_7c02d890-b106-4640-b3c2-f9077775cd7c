"""
数据质量监控服务模块
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
import pandas as pd

from app.core.logging import get_logger
from app.models.stock import Stock, KlineData, RealtimeData
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


class DataQualityMonitor:
    """数据质量监控器"""
    
    def __init__(self):
        pass
    
    async def check_data_completeness(self, stock_code: str, start_date: date, end_date: date) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            async with DataStorageService() as storage:
                # 获取指定时间范围的K线数据
                klines = await storage.get_kline_data(
                    stock_code=stock_code,
                    period="daily",
                    start_date=start_date,
                    end_date=end_date,
                    limit=1000
                )
                
                # 计算预期的交易日数量（简化计算，不考虑节假日）
                total_days = (end_date - start_date).days + 1
                expected_trading_days = total_days * 5 // 7  # 粗略估算工作日
                
                actual_data_count = len(klines)
                completeness_rate = actual_data_count / expected_trading_days if expected_trading_days > 0 else 0
                
                # 检查数据缺失的日期
                if klines:
                    existing_dates = {kline.trade_date for kline in klines}
                    current_date = start_date
                    missing_dates = []
                    
                    while current_date <= end_date:
                        # 简单检查：跳过周末
                        if current_date.weekday() < 5 and current_date not in existing_dates:
                            missing_dates.append(current_date)
                        current_date += timedelta(days=1)
                else:
                    missing_dates = []
                
                return {
                    "stock_code": stock_code,
                    "period": f"{start_date} to {end_date}",
                    "expected_days": expected_trading_days,
                    "actual_days": actual_data_count,
                    "completeness_rate": round(completeness_rate, 4),
                    "missing_dates": [d.isoformat() for d in missing_dates[:10]],  # 最多显示10个缺失日期
                    "total_missing": len(missing_dates)
                }
                
        except Exception as e:
            logger.error(f"检查数据完整性失败: {e}")
            return {
                "stock_code": stock_code,
                "error": str(e)
            }
    
    async def check_data_consistency(self, stock_code: str, limit: int = 100) -> Dict[str, Any]:
        """检查数据一致性"""
        try:
            async with DataStorageService() as storage:
                klines = await storage.get_kline_data(
                    stock_code=stock_code,
                    period="daily",
                    limit=limit
                )
                
                if not klines:
                    return {
                        "stock_code": stock_code,
                        "message": "无数据"
                    }
                
                inconsistencies = []
                
                for i, kline in enumerate(klines):
                    # 检查价格逻辑一致性
                    if kline.high_price < max(kline.open_price, kline.close_price, kline.low_price):
                        inconsistencies.append({
                            "date": kline.trade_date.isoformat(),
                            "type": "price_logic",
                            "message": "最高价低于开盘价、收盘价或最低价"
                        })
                    
                    if kline.low_price > min(kline.open_price, kline.close_price, kline.high_price):
                        inconsistencies.append({
                            "date": kline.trade_date.isoformat(),
                            "type": "price_logic",
                            "message": "最低价高于开盘价、收盘价或最高价"
                        })
                    
                    # 检查成交量和成交额的一致性
                    if kline.volume > 0 and kline.turnover <= 0:
                        inconsistencies.append({
                            "date": kline.trade_date.isoformat(),
                            "type": "volume_turnover",
                            "message": "有成交量但成交额为0"
                        })
                    
                    # 检查涨跌幅计算是否正确（需要前一日收盘价）
                    if i > 0:
                        prev_close = float(klines[i-1].close_price)
                        current_close = float(kline.close_price)
                        calculated_change_pct = (current_close - prev_close) / prev_close * 100
                        
                        if abs(calculated_change_pct - float(kline.change_percent)) > 0.1:  # 允许0.1%的误差
                            inconsistencies.append({
                                "date": kline.trade_date.isoformat(),
                                "type": "change_percent",
                                "message": f"涨跌幅计算不一致: 记录值{kline.change_percent}%, 计算值{calculated_change_pct:.2f}%"
                            })
                
                return {
                    "stock_code": stock_code,
                    "total_records": len(klines),
                    "inconsistencies_count": len(inconsistencies),
                    "inconsistencies": inconsistencies[:20],  # 最多显示20个不一致项
                    "consistency_rate": round((len(klines) - len(inconsistencies)) / len(klines), 4) if klines else 0
                }
                
        except Exception as e:
            logger.error(f"检查数据一致性失败: {e}")
            return {
                "stock_code": stock_code,
                "error": str(e)
            }
    
    async def check_data_freshness(self) -> Dict[str, Any]:
        """检查数据新鲜度"""
        try:
            async with DataStorageService() as storage:
                # 检查最新的K线数据日期
                session = storage.session
                
                # 查询最新的K线数据日期
                latest_kline_stmt = select(func.max(KlineData.trade_date)).where(KlineData.period == "daily")
                latest_kline_result = await session.execute(latest_kline_stmt)
                latest_kline_date = latest_kline_result.scalar()
                
                # 查询最新的实时数据时间
                latest_realtime_stmt = select(func.max(RealtimeData.timestamp))
                latest_realtime_result = await session.execute(latest_realtime_stmt)
                latest_realtime_time = latest_realtime_result.scalar()
                
                # 计算数据延迟
                today = date.today()
                now = datetime.now()
                
                kline_delay_days = (today - latest_kline_date).days if latest_kline_date else None
                realtime_delay_hours = (now - latest_realtime_time).total_seconds() / 3600 if latest_realtime_time else None
                
                return {
                    "latest_kline_date": latest_kline_date.isoformat() if latest_kline_date else None,
                    "latest_realtime_time": latest_realtime_time.isoformat() if latest_realtime_time else None,
                    "kline_delay_days": kline_delay_days,
                    "realtime_delay_hours": round(realtime_delay_hours, 2) if realtime_delay_hours else None,
                    "check_time": now.isoformat()
                }
                
        except Exception as e:
            logger.error(f"检查数据新鲜度失败: {e}")
            return {
                "error": str(e)
            }
    
    async def generate_quality_report(self, stock_codes: List[str] = None, days: int = 30) -> Dict[str, Any]:
        """生成数据质量报告"""
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            if not stock_codes:
                # 如果没有指定股票代码，获取前10只股票
                async with DataStorageService() as storage:
                    stocks = await storage.get_stocks(limit=10)
                    stock_codes = [stock.stock_code for stock in stocks]
            
            if not stock_codes:
                return {
                    "message": "没有可用的股票数据",
                    "report_time": datetime.now().isoformat()
                }
            
            report = {
                "report_time": datetime.now().isoformat(),
                "analysis_period": f"{start_date} to {end_date}",
                "analyzed_stocks": len(stock_codes),
                "summary": {
                    "total_completeness_rate": 0,
                    "total_consistency_rate": 0,
                    "stocks_with_issues": 0
                },
                "stock_details": []
            }
            
            total_completeness = 0
            total_consistency = 0
            stocks_with_issues = 0
            
            for stock_code in stock_codes[:5]:  # 限制分析数量以避免超时
                try:
                    # 检查完整性
                    completeness = await self.check_data_completeness(stock_code, start_date, end_date)
                    
                    # 检查一致性
                    consistency = await self.check_data_consistency(stock_code, limit=50)
                    
                    stock_detail = {
                        "stock_code": stock_code,
                        "completeness": completeness,
                        "consistency": consistency
                    }
                    
                    report["stock_details"].append(stock_detail)
                    
                    # 累计统计
                    if "completeness_rate" in completeness:
                        total_completeness += completeness["completeness_rate"]
                    
                    if "consistency_rate" in consistency:
                        total_consistency += consistency["consistency_rate"]
                    
                    # 检查是否有问题
                    has_issues = (
                        completeness.get("completeness_rate", 1) < 0.9 or
                        consistency.get("inconsistencies_count", 0) > 0
                    )
                    
                    if has_issues:
                        stocks_with_issues += 1
                        
                except Exception as e:
                    logger.error(f"分析股票 {stock_code} 时出错: {e}")
                    continue
            
            # 计算平均值
            analyzed_count = len(report["stock_details"])
            if analyzed_count > 0:
                report["summary"]["total_completeness_rate"] = round(total_completeness / analyzed_count, 4)
                report["summary"]["total_consistency_rate"] = round(total_consistency / analyzed_count, 4)
                report["summary"]["stocks_with_issues"] = stocks_with_issues
            
            # 添加数据新鲜度检查
            freshness = await self.check_data_freshness()
            report["data_freshness"] = freshness
            
            return report
            
        except Exception as e:
            logger.error(f"生成数据质量报告失败: {e}")
            return {
                "error": str(e),
                "report_time": datetime.now().isoformat()
            }


class DataQualityService:
    """数据质量服务（整合验证、清洗和监控）"""
    
    def __init__(self):
        self.monitor = DataQualityMonitor()
    
    async def get_quality_dashboard(self) -> Dict[str, Any]:
        """获取数据质量仪表板"""
        try:
            # 生成质量报告
            report = await self.monitor.generate_quality_report()
            
            # 获取数据统计
            async with DataStorageService() as storage:
                total_stocks = await storage.get_stock_count()
                
                # 获取最近的数据统计
                session = storage.session
                
                # K线数据统计
                kline_count_stmt = select(func.count(KlineData.id))
                kline_count_result = await session.execute(kline_count_stmt)
                total_klines = kline_count_result.scalar()
                
                # 实时数据统计
                realtime_count_stmt = select(func.count(RealtimeData.id))
                realtime_count_result = await session.execute(realtime_count_stmt)
                total_realtime = realtime_count_result.scalar()
            
            dashboard = {
                "overview": {
                    "total_stocks": total_stocks,
                    "total_klines": total_klines,
                    "total_realtime_records": total_realtime,
                    "last_updated": datetime.now().isoformat()
                },
                "quality_metrics": {
                    "completeness_rate": report.get("summary", {}).get("total_completeness_rate", 0),
                    "consistency_rate": report.get("summary", {}).get("total_consistency_rate", 0),
                    "stocks_with_issues": report.get("summary", {}).get("stocks_with_issues", 0)
                },
                "data_freshness": report.get("data_freshness", {}),
                "detailed_report": report
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"获取数据质量仪表板失败: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
