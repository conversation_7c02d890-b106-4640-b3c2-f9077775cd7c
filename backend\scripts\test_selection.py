#!/usr/bin/env python3
"""
选股系统功能测试脚本
"""

import asyncio
import sys
import os
from datetime import date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.logging import setup_logging, get_logger
from app.services.stock_selection import StockScoringService, StockSelectionService

logger = get_logger(__name__)


async def test_stock_scoring():
    """测试股票评分功能"""
    logger.info("=== 测试股票评分功能 ===")
    
    test_stock = "000001"
    
    try:
        async with StockScoringService() as scoring_service:
            # 测试单只股票评分
            score_result = await scoring_service.calculate_stock_score(test_stock)
            
            if "error" not in score_result:
                logger.info("✅ 股票评分计算成功")
                logger.info(f"股票代码: {score_result['stock_code']}")
                logger.info(f"总分: {score_result['total_score']}")
                logger.info(f"评分等级: {score_result['score_level']}")
                logger.info(f"推荐等级: {score_result['recommendation']}")
                logger.info(f"置信度: {score_result['confidence']}")
                
                # 显示各维度评分
                logger.info("各维度评分:")
                logger.info(f"  技术面: {score_result['technical_score']}")
                logger.info(f"  AI预测: {score_result['ai_score']}")
                logger.info(f"  动量: {score_result['momentum_score']}")
                logger.info(f"  趋势: {score_result['trend_score']}")
                logger.info(f"  成交量: {score_result['volume_score']}")
                logger.info(f"  波动性: {score_result['volatility_score']}")
                
                # 测试保存评分
                score_id = await scoring_service.save_stock_score(score_result)
                logger.info(f"✅ 评分保存成功，ID: {score_id}")
                
            else:
                logger.warning(f"股票评分计算失败: {score_result['error']}")
                
    except Exception as e:
        logger.error(f"股票评分测试失败: {e}")


async def test_selection_strategies():
    """测试选股策略功能"""
    logger.info("=== 测试选股策略功能 ===")
    
    try:
        async with StockSelectionService() as selection_service:
            # 创建默认策略
            logger.info("创建默认选股策略...")
            await selection_service.create_default_strategies()
            logger.info("✅ 默认策略创建完成")
            
            # 测试技术面强势股策略
            logger.info("测试技术面强势股策略...")
            test_stocks = ["000001", "000002", "600000", "600036", "600519"]
            
            result = await selection_service.run_selection_strategy("技术面强势股", test_stocks)
            
            if "error" not in result:
                logger.info("✅ 技术面强势股策略执行成功")
                logger.info(f"处理股票数: {result['processed_stocks']}")
                logger.info(f"选中股票数: {len(result['selected_stocks'])}")
                logger.info(f"执行耗时: {result['execution_time']:.2f} 秒")
                
                # 显示选中的股票
                if result['selected_stocks']:
                    logger.info("选中的股票:")
                    for i, stock in enumerate(result['selected_stocks'][:3], 1):
                        logger.info(f"  {i}. {stock['stock_code']} - 评分: {stock['total_score']} ({stock['score_level']})")
                
                # 显示统计信息
                stats = result.get('statistics', {})
                logger.info(f"平均评分: {stats.get('avg_score', 'N/A')}")
                logger.info(f"评分分布: {stats.get('score_distribution', {})}")
                
            else:
                logger.warning(f"技术面强势股策略执行失败: {result['error']}")
                
    except Exception as e:
        logger.error(f"选股策略测试失败: {e}")


async def test_ai_strategy():
    """测试AI智能推荐策略"""
    logger.info("=== 测试AI智能推荐策略 ===")
    
    try:
        async with StockSelectionService() as selection_service:
            test_stocks = ["000001", "000002", "600000"]
            
            result = await selection_service.run_selection_strategy("AI智能推荐", test_stocks)
            
            if "error" not in result:
                logger.info("✅ AI智能推荐策略执行成功")
                logger.info(f"处理股票数: {result['processed_stocks']}")
                logger.info(f"选中股票数: {len(result['selected_stocks'])}")
                
                if result['selected_stocks']:
                    logger.info("AI推荐股票:")
                    for stock in result['selected_stocks']:
                        logger.info(f"  {stock['stock_code']} - 评分: {stock['total_score']} - 推荐: {stock['recommendation']}")
                        
            else:
                logger.warning(f"AI智能推荐策略执行失败: {result['error']}")
                
    except Exception as e:
        logger.error(f"AI策略测试失败: {e}")


async def test_momentum_strategy():
    """测试动量突破股策略"""
    logger.info("=== 测试动量突破股策略 ===")
    
    try:
        async with StockSelectionService() as selection_service:
            test_stocks = ["000001", "000002", "600000", "600036"]
            
            result = await selection_service.run_selection_strategy("动量突破股", test_stocks)
            
            if "error" not in result:
                logger.info("✅ 动量突破股策略执行成功")
                logger.info(f"处理股票数: {result['processed_stocks']}")
                logger.info(f"选中股票数: {len(result['selected_stocks'])}")
                
                if result['selected_stocks']:
                    logger.info("动量突破股票:")
                    for stock in result['selected_stocks']:
                        logger.info(f"  {stock['stock_code']} - 评分: {stock['total_score']} - 置信度: {stock['confidence']}")
                        
            else:
                logger.warning(f"动量突破股策略执行失败: {result['error']}")
                
    except Exception as e:
        logger.error(f"动量策略测试失败: {e}")


async def test_scoring_components():
    """测试评分组件"""
    logger.info("=== 测试评分组件 ===")
    
    try:
        async with StockScoringService() as scoring_service:
            # 测试多只股票评分
            test_stocks = ["000001", "000002", "600000"]
            
            scores = []
            for stock_code in test_stocks:
                score_result = await scoring_service.calculate_stock_score(stock_code)
                if "error" not in score_result:
                    scores.append(score_result)
                    await scoring_service.save_stock_score(score_result)
            
            if scores:
                logger.info(f"✅ 成功计算 {len(scores)} 只股票的评分")
                
                # 按总分排序
                sorted_scores = sorted(scores, key=lambda x: x["total_score"], reverse=True)
                
                logger.info("股票评分排行:")
                for i, score in enumerate(sorted_scores, 1):
                    logger.info(f"  {i}. {score['stock_code']} - {score['total_score']} ({score['score_level']}) - {score['recommendation']}")
                
                # 统计评分分布
                level_distribution = {}
                for score in scores:
                    level = score["score_level"]
                    level_distribution[level] = level_distribution.get(level, 0) + 1
                
                logger.info(f"评分等级分布: {level_distribution}")
                
            else:
                logger.warning("没有成功计算的股票评分")
                
    except Exception as e:
        logger.error(f"评分组件测试失败: {e}")


async def test_performance():
    """测试性能"""
    logger.info("=== 性能测试 ===")
    
    import time
    
    try:
        # 测试批量评分性能
        test_stocks = ["000001", "000002", "600000", "600036", "600519"]
        
        start_time = time.time()
        
        async with StockScoringService() as scoring_service:
            successful_count = 0
            for stock_code in test_stocks:
                try:
                    score_result = await scoring_service.calculate_stock_score(stock_code)
                    if "error" not in score_result:
                        successful_count += 1
                except Exception as e:
                    logger.error(f"评分股票 {stock_code} 失败: {e}")
        
        end_time = time.time()
        
        logger.info(f"✅ 批量评分性能测试完成")
        logger.info(f"处理股票数: {len(test_stocks)}")
        logger.info(f"成功评分数: {successful_count}")
        logger.info(f"总耗时: {end_time - start_time:.2f} 秒")
        logger.info(f"平均耗时: {(end_time - start_time) / len(test_stocks):.2f} 秒/股")
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")


async def test_data_requirements():
    """测试数据需求"""
    logger.info("=== 测试数据需求 ===")
    
    try:
        # 检查K线数据
        from app.services.data_storage import DataStorageService
        async with DataStorageService() as data_storage:
            klines = await data_storage.get_kline_data("000001", "daily", limit=5)
        
        if klines:
            logger.info(f"✅ 找到 {len(klines)} 条K线数据")
            latest_kline = klines[-1]
            logger.info(f"最新K线: {latest_kline.trade_date} 收盘价: {latest_kline.close_price}")
        else:
            logger.warning("❌ 没有找到K线数据，选股功能可能受限")
        
        # 检查技术指标数据
        from app.services.indicator_storage import IndicatorStorageService
        async with IndicatorStorageService() as indicator_storage:
            indicators = await indicator_storage.get_indicators("000001", "daily", limit=5)
        
        if indicators:
            logger.info(f"✅ 找到 {len(indicators)} 条技术指标数据")
            latest_indicator = indicators[-1]
            logger.info(f"最新指标: RSI={latest_indicator.rsi}, MACD={latest_indicator.macd}")
        else:
            logger.warning("❌ 没有找到技术指标数据，建议先计算技术指标")
        
        # 检查AI预测数据
        from app.services.ai_storage import AIPredictionStorageService
        async with AIPredictionStorageService() as ai_storage:
            predictions = await ai_storage.get_predictions(stock_code="000001", limit=3)
        
        if predictions:
            logger.info(f"✅ 找到 {len(predictions)} 条AI预测数据")
        else:
            logger.warning("❌ 没有找到AI预测数据，AI评分将使用默认值")
            
    except Exception as e:
        logger.error(f"数据需求测试失败: {e}")


async def main():
    """主测试函数"""
    setup_logging()
    logger.info("📊 开始选股系统功能测试")
    
    try:
        # 1. 测试数据需求
        await test_data_requirements()
        
        # 2. 测试股票评分功能
        await test_stock_scoring()
        
        # 3. 测试评分组件
        await test_scoring_components()
        
        # 4. 测试选股策略功能
        await test_selection_strategies()
        
        # 5. 测试AI智能推荐策略
        await test_ai_strategy()
        
        # 6. 测试动量突破股策略
        await test_momentum_strategy()
        
        # 7. 性能测试
        await test_performance()
        
        logger.info("✅ 所有选股系统测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
