import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Table,
  Space,
  message,
  Tag,
  Input,
  Select,
  Typography,
  Alert,
  Tabs,
  Progress,
  Modal,
  List
} from 'antd';
import {
  DatabaseOutlined,
  SyncOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { stockDataManagementService, type StockBasicInfo, type DataStats, type UpdateResult } from '../services/stockDataManagementService';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const StockDataManagement: React.FC = () => {
  const [dataStats, setDataStats] = useState<DataStats | null>(null);
  const [stockList, setStockList] = useState<StockBasicInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [selectedStocks, setSelectedStocks] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [exchangeFilter, setExchangeFilter] = useState<string>('');

  // 加载数据统计
  const loadDataStats = async () => {
    try {
      setLoading(true);
      const stats = await stockDataManagementService.getDataStats();
      setDataStats(stats);
    } catch (error) {
      message.error('加载数据统计失败');
      console.error('加载数据统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载股票列表
  const loadStockList = async () => {
    try {
      setLoading(true);
      const stocks = await stockDataManagementService.getStockList({
        limit: 100,
        offset: 0,
        exchange: exchangeFilter || undefined
      });
      setStockList(stocks);
    } catch (error) {
      message.error('加载股票列表失败');
      console.error('加载股票列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 更新基础信息
  const updateBasicInfo = async () => {
    try {
      setUpdateLoading(true);
      const result = await stockDataManagementService.updateBasicInfo();
      if (result.success) {
        message.success(result.message);
        setTimeout(() => {
          loadDataStats();
          loadStockList();
        }, 2000); // 等待后台任务执行
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('更新基础信息失败');
      console.error('更新基础信息失败:', error);
    } finally {
      setUpdateLoading(false);
    }
  };

  // 更新实时行情
  const updateRealtimeData = async () => {
    try {
      setUpdateLoading(true);
      const result = await stockDataManagementService.updateRealtimeData();
      if (result.success) {
        message.success(`实时行情更新完成: 成功 ${result.success_count}, 失败 ${result.error_count}`);
        loadDataStats();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('更新实时行情失败');
      console.error('更新实时行情失败:', error);
    } finally {
      setUpdateLoading(false);
    }
  };

  // 批量更新K线数据
  const batchUpdateKline = async () => {
    if (selectedStocks.length === 0) {
      message.warning('请先选择要更新的股票');
      return;
    }

    try {
      setUpdateLoading(true);
      const result = await stockDataManagementService.batchUpdateKline(selectedStocks.join(','), 30);
      message.success(`批量K线更新已启动: ${selectedStocks.length} 只股票`);
      setSelectedStocks([]);
    } catch (error) {
      message.error('批量更新K线数据失败');
      console.error('批量更新K线数据失败:', error);
    } finally {
      setUpdateLoading(false);
    }
  };

  useEffect(() => {
    loadDataStats();
    loadStockList();
  }, [exchangeFilter]);

  // 过滤股票列表
  const filteredStocks = stockList.filter(stock =>
    stock.stock_code.includes(searchText) ||
    stock.stock_name.includes(searchText)
  );

  // 股票表格列定义
  const stockColumns = [
    {
      title: '股票代码',
      dataIndex: 'stock_code',
      key: 'stock_code',
      width: 100,
    },
    {
      title: '股票名称',
      dataIndex: 'stock_name',
      key: 'stock_name',
      width: 120,
    },
    {
      title: '交易所',
      dataIndex: 'exchange',
      key: 'exchange',
      width: 80,
      render: (exchange: string) => (
        <Tag color={exchange === 'SH' ? 'blue' : 'green'}>{exchange}</Tag>
      ),
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
      width: 120,
      render: (industry: string | null) => industry || '--',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '停牌'}
        </Tag>
      ),
    },
    {
      title: '上市日期',
      dataIndex: 'list_date',
      key: 'list_date',
      width: 100,
      render: (date: string | null) => date || '--',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DatabaseOutlined /> 重构版股票数据管理系统
      </Title>

      <Alert
        message="系统重构说明"
        description="这是重构后的股票数据管理系统，采用新的数据架构和API设计，支持更高效的数据管理和更好的扩展性。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 数据概览 */}
      <Card title="数据概览" loading={loading} style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总股票数"
              value={dataStats?.total_stocks || 0}
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="活跃股票"
              value={dataStats?.active_stocks || 0}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="K线数据量"
              value={dataStats?.kline_data_count || 0}
              prefix={<LineChartOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="实时数据量"
              value={dataStats?.realtime_data_count || 0}
              prefix={<BarChartOutlined />}
            />
          </Col>
        </Row>
        
        {dataStats?.last_update_time && (
          <Alert
            message={`最后更新时间: ${new Date(dataStats.last_update_time).toLocaleString()}`}
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Card>

      {/* 数据更新控制 */}
      <Card title="数据更新控制" style={{ marginBottom: 24 }}>
        <Space size="large">
          <Button
            type="primary"
            icon={<SyncOutlined />}
            loading={updateLoading}
            onClick={updateBasicInfo}
          >
            更新基础信息
          </Button>
          <Button
            icon={<ReloadOutlined />}
            loading={updateLoading}
            onClick={updateRealtimeData}
          >
            更新实时行情
          </Button>
          <Button
            icon={<LineChartOutlined />}
            loading={updateLoading}
            onClick={batchUpdateKline}
            disabled={selectedStocks.length === 0}
          >
            批量更新K线 ({selectedStocks.length})
          </Button>
        </Space>
      </Card>

      {/* 股票管理 */}
      <Card title="股票管理">
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input.Search
              placeholder="搜索股票代码或名称"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
            />
            <Select
              placeholder="选择交易所"
              value={exchangeFilter}
              onChange={setExchangeFilter}
              style={{ width: 120 }}
              allowClear
            >
              <Option value="SH">上海</Option>
              <Option value="SZ">深圳</Option>
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadStockList}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={stockColumns}
          dataSource={filteredStocks}
          rowKey="stock_code"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          rowSelection={{
            selectedRowKeys: selectedStocks,
            onChange: (selectedRowKeys) => {
              setSelectedStocks(selectedRowKeys as string[]);
            },
          }}
          scroll={{ y: 400 }}
        />
      </Card>

      {/* 系统特性说明 */}
      <Card title="系统特性" style={{ marginTop: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Card size="small">
              <Title level={5}>🏗️ 重构架构</Title>
              <Text>采用新的数据模型和API设计，提供更好的性能和扩展性</Text>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Title level={5}>📊 统一数据管理</Title>
              <Text>集中管理股票基础信息、K线数据、实时行情等所有数据</Text>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Title level={5}>🔄 智能更新</Title>
              <Text>支持增量更新、批量处理和后台任务执行</Text>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default StockDataManagement;
