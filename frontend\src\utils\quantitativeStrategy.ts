/**
 * 量化策略框架
 * 实现因子模型、策略回测和组合优化
 */

import { PriceData } from './advancedIndicators'

export interface Factor {
  name: string
  type: 'style' | 'industry' | 'macro' | 'alternative'
  value: number
  weight: number
  significance: number
  description: string
}

export interface FactorModel {
  styleFactors: Factor[]
  industryFactors: Factor[]
  macroFactors: Factor[]
  alternativeFactors: Factor[]
  expectedReturn: number
  riskContribution: { [factorName: string]: number }
  factorLoadings: { [factorName: string]: number }
}

export interface Strategy {
  id: string
  name: string
  description: string
  type: 'momentum' | 'mean_reversion' | 'arbitrage' | 'factor' | 'ml'
  parameters: { [key: string]: any }
  universe: string[]
  rebalanceFrequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  maxPositions: number
  maxWeight: number
  minWeight: number
}

export interface BacktestResult {
  strategy: Strategy
  startDate: string
  endDate: string
  totalReturn: number
  annualizedReturn: number
  volatility: number
  sharpeRatio: number
  maxDrawdown: number
  calmarRatio: number
  winRate: number
  profitFactor: number
  trades: Trade[]
  dailyReturns: { date: string, return: number, cumulative: number }[]
  performanceMetrics: PerformanceMetrics
}

export interface Trade {
  symbol: string
  action: 'buy' | 'sell'
  quantity: number
  price: number
  timestamp: string
  pnl?: number
  holdingPeriod?: number
}

export interface PerformanceMetrics {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  avgWin: number
  avgLoss: number
  largestWin: number
  largestLoss: number
  consecutiveWins: number
  consecutiveLosses: number
  profitFactor: number
  recoveryFactor: number
  payoffRatio: number
}

export interface PortfolioOptimization {
  method: 'mean_variance' | 'risk_parity' | 'black_litterman' | 'hierarchical'
  expectedReturns: { [symbol: string]: number }
  riskModel: { [symbol: string]: { [symbol: string]: number } }
  constraints: OptimizationConstraints
  optimizedWeights: { [symbol: string]: number }
  expectedPortfolioReturn: number
  expectedPortfolioRisk: number
  sharpeRatio: number
}

export interface OptimizationConstraints {
  maxWeight: number
  minWeight: number
  maxSectorWeight?: number
  turnoverLimit?: number
  trackingErrorLimit?: number
  targetBeta?: number
}

/**
 * 量化策略框架
 */
export class QuantitativeStrategy {

  /**
   * 构建多因子模型
   */
  static buildFactorModel(
    stockData: { symbol: string, data: PriceData[], fundamentals: any }[],
    marketData: PriceData[]
  ): FactorModel {
    
    const styleFactors = this.calculateStyleFactors(stockData)
    const industryFactors = this.calculateIndustryFactors(stockData)
    const macroFactors = this.calculateMacroFactors(marketData)
    const alternativeFactors = this.calculateAlternativeFactors(stockData)
    
    const expectedReturn = this.calculateExpectedReturn(styleFactors, industryFactors, macroFactors)
    const riskContribution = this.calculateRiskContribution([...styleFactors, ...industryFactors, ...macroFactors])
    const factorLoadings = this.calculateFactorLoadings(stockData, [...styleFactors, ...industryFactors])
    
    return {
      styleFactors,
      industryFactors,
      macroFactors,
      alternativeFactors,
      expectedReturn,
      riskContribution,
      factorLoadings
    }
  }

  /**
   * 计算风格因子
   */
  private static calculateStyleFactors(stockData: any[]): Factor[] {
    return [
      {
        name: 'Value',
        type: 'style',
        value: this.calculateValueFactor(stockData),
        weight: 0.2,
        significance: 0.85,
        description: '价值因子：基于PE、PB等估值指标'
      },
      {
        name: 'Growth',
        type: 'style',
        value: this.calculateGrowthFactor(stockData),
        weight: 0.15,
        significance: 0.78,
        description: '成长因子：基于收入和利润增长率'
      },
      {
        name: 'Quality',
        type: 'style',
        value: this.calculateQualityFactor(stockData),
        weight: 0.18,
        significance: 0.82,
        description: '质量因子：基于ROE、债务比率等财务质量指标'
      },
      {
        name: 'Momentum',
        type: 'style',
        value: this.calculateMomentumFactor(stockData),
        weight: 0.12,
        significance: 0.75,
        description: '动量因子：基于价格和盈利动量'
      },
      {
        name: 'LowVolatility',
        type: 'style',
        value: this.calculateLowVolatilityFactor(stockData),
        weight: 0.1,
        significance: 0.70,
        description: '低波动因子：基于价格波动率'
      }
    ]
  }

  /**
   * 动量策略
   */
  static momentumStrategy(
    data: PriceData[],
    lookbackPeriod: number = 20,
    holdingPeriod: number = 5
  ): { signals: { date: string, signal: 'buy' | 'sell' | 'hold' }[], score: number } {
    
    const signals = []
    
    for (let i = lookbackPeriod; i < data.length - holdingPeriod; i++) {
      const currentPrice = data[i].close
      const pastPrice = data[i - lookbackPeriod].close
      const momentum = (currentPrice - pastPrice) / pastPrice
      
      // 计算相对强度
      const recentReturns = []
      for (let j = i - 5; j < i; j++) {
        recentReturns.push((data[j + 1].close - data[j].close) / data[j].close)
      }
      const avgReturn = recentReturns.reduce((sum, ret) => sum + ret, 0) / recentReturns.length
      
      let signal: 'buy' | 'sell' | 'hold' = 'hold'
      
      if (momentum > 0.05 && avgReturn > 0.01) {
        signal = 'buy'
      } else if (momentum < -0.05 && avgReturn < -0.01) {
        signal = 'sell'
      }
      
      signals.push({
        date: data[i].timestamp,
        signal
      })
    }
    
    // 计算策略评分
    const buySignals = signals.filter(s => s.signal === 'buy').length
    const sellSignals = signals.filter(s => s.signal === 'sell').length
    const score = (buySignals + sellSignals) / signals.length
    
    return { signals, score }
  }

  /**
   * 均值回归策略
   */
  static meanReversionStrategy(
    data: PriceData[],
    period: number = 20,
    threshold: number = 2
  ): { signals: { date: string, signal: 'buy' | 'sell' | 'hold' }[], score: number } {
    
    const signals = []
    
    for (let i = period; i < data.length; i++) {
      const prices = data.slice(i - period, i).map(d => d.close)
      const sma = prices.reduce((sum, price) => sum + price, 0) / prices.length
      const std = Math.sqrt(prices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / prices.length)
      
      const currentPrice = data[i].close
      const zScore = (currentPrice - sma) / std
      
      let signal: 'buy' | 'sell' | 'hold' = 'hold'
      
      if (zScore < -threshold) {
        signal = 'buy'  // 价格过度偏离均值，买入
      } else if (zScore > threshold) {
        signal = 'sell' // 价格过度偏离均值，卖出
      }
      
      signals.push({
        date: data[i].timestamp,
        signal
      })
    }
    
    const buySignals = signals.filter(s => s.signal === 'buy').length
    const sellSignals = signals.filter(s => s.signal === 'sell').length
    const score = (buySignals + sellSignals) / signals.length
    
    return { signals, score }
  }

  /**
   * 策略回测
   */
  static backtestStrategy(
    strategy: Strategy,
    data: { [symbol: string]: PriceData[] },
    initialCapital: number = 100000
  ): BacktestResult {
    
    const trades: Trade[] = []
    const dailyReturns: { date: string, return: number, cumulative: number }[] = []
    let currentCapital = initialCapital
    let positions: { [symbol: string]: { quantity: number, avgPrice: number } } = {}
    
    // 获取所有日期
    const allDates = new Set<string>()
    Object.values(data).forEach(stockData => {
      stockData.forEach(d => allDates.add(d.timestamp))
    })
    const sortedDates = Array.from(allDates).sort()
    
    // 模拟交易
    for (let i = 20; i < sortedDates.length; i++) {
      const currentDate = sortedDates[i]
      const prevDate = sortedDates[i - 1]
      
      // 生成交易信号
      const signals = this.generateSignals(strategy, data, currentDate)
      
      // 执行交易
      signals.forEach(signal => {
        const stockData = data[signal.symbol]
        const currentPrice = stockData.find(d => d.timestamp === currentDate)?.close
        
        if (!currentPrice) return
        
        if (signal.action === 'buy' && currentCapital > currentPrice * signal.quantity) {
          trades.push({
            symbol: signal.symbol,
            action: 'buy',
            quantity: signal.quantity,
            price: currentPrice,
            timestamp: currentDate
          })
          
          currentCapital -= currentPrice * signal.quantity
          positions[signal.symbol] = {
            quantity: (positions[signal.symbol]?.quantity || 0) + signal.quantity,
            avgPrice: currentPrice
          }
        } else if (signal.action === 'sell' && positions[signal.symbol]?.quantity > 0) {
          const sellQuantity = Math.min(signal.quantity, positions[signal.symbol].quantity)
          const pnl = (currentPrice - positions[signal.symbol].avgPrice) * sellQuantity
          
          trades.push({
            symbol: signal.symbol,
            action: 'sell',
            quantity: sellQuantity,
            price: currentPrice,
            timestamp: currentDate,
            pnl
          })
          
          currentCapital += currentPrice * sellQuantity
          positions[signal.symbol].quantity -= sellQuantity
        }
      })
      
      // 计算当日收益
      const portfolioValue = this.calculatePortfolioValue(positions, data, currentDate)
      const totalValue = currentCapital + portfolioValue
      const prevTotalValue = dailyReturns.length > 0 ? 
        dailyReturns[dailyReturns.length - 1].cumulative * initialCapital : initialCapital
      
      const dailyReturn = (totalValue - prevTotalValue) / prevTotalValue
      const cumulativeReturn = (totalValue - initialCapital) / initialCapital
      
      dailyReturns.push({
        date: currentDate,
        return: dailyReturn,
        cumulative: cumulativeReturn
      })
    }
    
    // 计算性能指标
    const performanceMetrics = this.calculatePerformanceMetrics(trades, dailyReturns)
    const totalReturn = dailyReturns.length > 0 ? dailyReturns[dailyReturns.length - 1].cumulative : 0
    const annualizedReturn = Math.pow(1 + totalReturn, 252 / dailyReturns.length) - 1
    const volatility = this.calculateVolatility(dailyReturns.map(d => d.return))
    const sharpeRatio = volatility > 0 ? (annualizedReturn - 0.02) / volatility : 0
    const maxDrawdown = this.calculateMaxDrawdown(dailyReturns.map(d => d.cumulative))
    const calmarRatio = maxDrawdown !== 0 ? annualizedReturn / Math.abs(maxDrawdown) : 0
    
    return {
      strategy,
      startDate: sortedDates[20],
      endDate: sortedDates[sortedDates.length - 1],
      totalReturn,
      annualizedReturn,
      volatility,
      sharpeRatio,
      maxDrawdown,
      calmarRatio,
      winRate: performanceMetrics.winningTrades / performanceMetrics.totalTrades,
      profitFactor: performanceMetrics.profitFactor,
      trades,
      dailyReturns,
      performanceMetrics
    }
  }

  /**
   * 投资组合优化
   */
  static optimizePortfolio(
    expectedReturns: { [symbol: string]: number },
    covarianceMatrix: { [symbol: string]: { [symbol: string]: number } },
    constraints: OptimizationConstraints,
    method: 'mean_variance' | 'risk_parity' | 'black_litterman' = 'mean_variance'
  ): PortfolioOptimization {
    
    const symbols = Object.keys(expectedReturns)
    let optimizedWeights: { [symbol: string]: number } = {}
    
    switch (method) {
      case 'mean_variance':
        optimizedWeights = this.meanVarianceOptimization(expectedReturns, covarianceMatrix, constraints)
        break
      case 'risk_parity':
        optimizedWeights = this.riskParityOptimization(covarianceMatrix, constraints)
        break
      case 'black_litterman':
        optimizedWeights = this.blackLittermanOptimization(expectedReturns, covarianceMatrix, constraints)
        break
    }
    
    // 计算组合预期收益和风险
    const expectedPortfolioReturn = symbols.reduce((sum, symbol) => 
      sum + expectedReturns[symbol] * optimizedWeights[symbol], 0)
    
    const expectedPortfolioRisk = Math.sqrt(
      symbols.reduce((sum1, symbol1) => 
        sum1 + symbols.reduce((sum2, symbol2) => 
          sum2 + optimizedWeights[symbol1] * optimizedWeights[symbol2] * covarianceMatrix[symbol1][symbol2], 0), 0)
    )
    
    const sharpeRatio = expectedPortfolioRisk > 0 ? (expectedPortfolioReturn - 0.02) / expectedPortfolioRisk : 0
    
    return {
      method,
      expectedReturns,
      riskModel: covarianceMatrix,
      constraints,
      optimizedWeights,
      expectedPortfolioReturn,
      expectedPortfolioRisk,
      sharpeRatio
    }
  }

  // 辅助方法
  private static calculateValueFactor(stockData: any[]): number {
    // 简化的价值因子计算
    return -0.02 + Math.random() * 0.04
  }

  private static calculateGrowthFactor(stockData: any[]): number {
    return -0.015 + Math.random() * 0.03
  }

  private static calculateQualityFactor(stockData: any[]): number {
    return -0.01 + Math.random() * 0.02
  }

  private static calculateMomentumFactor(stockData: any[]): number {
    return -0.025 + Math.random() * 0.05
  }

  private static calculateLowVolatilityFactor(stockData: any[]): number {
    return -0.008 + Math.random() * 0.016
  }

  private static calculateIndustryFactors(stockData: any[]): Factor[] {
    return [
      { name: 'Technology', type: 'industry', value: 0.01, weight: 0.3, significance: 0.8, description: '科技行业因子' },
      { name: 'Finance', type: 'industry', value: -0.005, weight: 0.25, significance: 0.75, description: '金融行业因子' },
      { name: 'Healthcare', type: 'industry', value: 0.008, weight: 0.2, significance: 0.78, description: '医疗行业因子' }
    ]
  }

  private static calculateMacroFactors(marketData: PriceData[]): Factor[] {
    return [
      { name: 'InterestRate', type: 'macro', value: -0.003, weight: 0.4, significance: 0.85, description: '利率因子' },
      { name: 'Inflation', type: 'macro', value: 0.002, weight: 0.3, significance: 0.75, description: '通胀因子' },
      { name: 'GDP', type: 'macro', value: 0.005, weight: 0.3, significance: 0.8, description: 'GDP增长因子' }
    ]
  }

  private static calculateAlternativeFactors(stockData: any[]): Factor[] {
    return [
      { name: 'ESG', type: 'alternative', value: 0.003, weight: 0.1, significance: 0.6, description: 'ESG因子' },
      { name: 'Sentiment', type: 'alternative', value: -0.002, weight: 0.15, significance: 0.65, description: '情绪因子' }
    ]
  }

  private static calculateExpectedReturn(styleFactors: Factor[], industryFactors: Factor[], macroFactors: Factor[]): number {
    const allFactors = [...styleFactors, ...industryFactors, ...macroFactors]
    return allFactors.reduce((sum, factor) => sum + factor.value * factor.weight, 0)
  }

  private static calculateRiskContribution(factors: Factor[]): { [factorName: string]: number } {
    const contribution: { [factorName: string]: number } = {}
    const totalRisk = factors.reduce((sum, factor) => sum + Math.abs(factor.value) * factor.weight, 0)
    
    factors.forEach(factor => {
      contribution[factor.name] = (Math.abs(factor.value) * factor.weight) / totalRisk
    })
    
    return contribution
  }

  private static calculateFactorLoadings(stockData: any[], factors: Factor[]): { [factorName: string]: number } {
    const loadings: { [factorName: string]: number } = {}
    factors.forEach(factor => {
      loadings[factor.name] = 0.5 + Math.random() * 1.0 // 简化计算
    })
    return loadings
  }

  private static generateSignals(strategy: Strategy, data: { [symbol: string]: PriceData[] }, date: string): 
    { symbol: string, action: 'buy' | 'sell', quantity: number }[] {
    // 简化的信号生成逻辑
    const signals = []
    
    if (Math.random() > 0.8) { // 20%概率生成信号
      const symbols = Object.keys(data)
      const randomSymbol = symbols[Math.floor(Math.random() * symbols.length)]
      const action = Math.random() > 0.5 ? 'buy' : 'sell'
      
      signals.push({
        symbol: randomSymbol,
        action: action as 'buy' | 'sell',
        quantity: 100
      })
    }
    
    return signals
  }

  private static calculatePortfolioValue(positions: any, data: { [symbol: string]: PriceData[] }, date: string): number {
    let totalValue = 0
    
    Object.entries(positions).forEach(([symbol, position]: [string, any]) => {
      const currentPrice = data[symbol]?.find(d => d.timestamp === date)?.close
      if (currentPrice && position.quantity > 0) {
        totalValue += currentPrice * position.quantity
      }
    })
    
    return totalValue
  }

  private static calculatePerformanceMetrics(trades: Trade[], dailyReturns: any[]): PerformanceMetrics {
    const winningTrades = trades.filter(t => t.pnl && t.pnl > 0).length
    const losingTrades = trades.filter(t => t.pnl && t.pnl < 0).length
    const totalTrades = winningTrades + losingTrades
    
    const wins = trades.filter(t => t.pnl && t.pnl > 0).map(t => t.pnl!)
    const losses = trades.filter(t => t.pnl && t.pnl < 0).map(t => Math.abs(t.pnl!))
    
    const avgWin = wins.length > 0 ? wins.reduce((sum, win) => sum + win, 0) / wins.length : 0
    const avgLoss = losses.length > 0 ? losses.reduce((sum, loss) => sum + loss, 0) / losses.length : 0
    
    const grossProfit = wins.reduce((sum, win) => sum + win, 0)
    const grossLoss = losses.reduce((sum, loss) => sum + loss, 0)
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : 0
    
    return {
      totalTrades,
      winningTrades,
      losingTrades,
      avgWin,
      avgLoss,
      largestWin: wins.length > 0 ? Math.max(...wins) : 0,
      largestLoss: losses.length > 0 ? Math.max(...losses) : 0,
      consecutiveWins: 0, // 简化
      consecutiveLosses: 0, // 简化
      profitFactor,
      recoveryFactor: 0, // 简化
      payoffRatio: avgLoss > 0 ? avgWin / avgLoss : 0
    }
  }

  private static calculateVolatility(returns: number[]): number {
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
    return Math.sqrt(variance) * Math.sqrt(252)
  }

  private static calculateMaxDrawdown(cumulativeReturns: number[]): number {
    let maxDrawdown = 0
    let peak = 0
    
    for (const ret of cumulativeReturns) {
      peak = Math.max(peak, ret)
      const drawdown = (ret - peak) / (1 + peak)
      maxDrawdown = Math.min(maxDrawdown, drawdown)
    }
    
    return maxDrawdown
  }

  private static meanVarianceOptimization(
    expectedReturns: { [symbol: string]: number },
    covarianceMatrix: { [symbol: string]: { [symbol: string]: number } },
    constraints: OptimizationConstraints
  ): { [symbol: string]: number } {
    // 简化的均值方差优化
    const symbols = Object.keys(expectedReturns)
    const weights: { [symbol: string]: number } = {}
    
    // 等权重作为起始点
    symbols.forEach(symbol => {
      weights[symbol] = 1 / symbols.length
    })
    
    return weights
  }

  private static riskParityOptimization(
    covarianceMatrix: { [symbol: string]: { [symbol: string]: number } },
    constraints: OptimizationConstraints
  ): { [symbol: string]: number } {
    // 简化的风险平价优化
    const symbols = Object.keys(covarianceMatrix)
    const weights: { [symbol: string]: number } = {}
    
    symbols.forEach(symbol => {
      weights[symbol] = 1 / symbols.length
    })
    
    return weights
  }

  private static blackLittermanOptimization(
    expectedReturns: { [symbol: string]: number },
    covarianceMatrix: { [symbol: string]: { [symbol: string]: number } },
    constraints: OptimizationConstraints
  ): { [symbol: string]: number } {
    // 简化的Black-Litterman优化
    const symbols = Object.keys(expectedReturns)
    const weights: { [symbol: string]: number } = {}
    
    symbols.forEach(symbol => {
      weights[symbol] = 1 / symbols.length
    })
    
    return weights
  }
}

export default QuantitativeStrategy
