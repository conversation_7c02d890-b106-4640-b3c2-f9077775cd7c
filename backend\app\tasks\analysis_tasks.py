"""
分析相关的Celery任务
"""

import asyncio
from typing import List, Dict, Any
from datetime import datetime, date, timedelta

from app.core.celery_app import celery_app
from app.core.logging import get_logger

logger = get_logger(__name__)


def run_async_task(coro):
    """运行异步任务的辅助函数"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def calculate_technical_indicators_task(self, stock_code: str, indicators: List[str] = None):
    """计算技术指标任务"""
    try:
        logger.info(f"开始计算股票 {stock_code} 的技术指标...")

        async def _calculate():
            from app.services.technical_indicators import IndicatorCalculationService
            from app.services.indicator_storage import IndicatorStorageService

            # 计算技术指标
            calculation_service = IndicatorCalculationService()
            indicators_data = await calculation_service.calculate_all_indicators(stock_code, "daily", 250)

            if indicators_data:
                # 保存到数据库
                async with IndicatorStorageService() as storage:
                    saved_count = await storage.save_indicators(stock_code, indicators_data)

                return {
                    "status": "success",
                    "stock_code": stock_code,
                    "indicators_count": len(indicators_data.get("indicators", {})),
                    "saved_count": saved_count,
                    "calculated_at": indicators_data.get("calculated_at")
                }
            else:
                return {
                    "status": "failed",
                    "message": "无法获取K线数据或计算失败"
                }

        result = run_async_task(_calculate())

        logger.info(f"股票 {stock_code} 技术指标计算完成")
        return result

    except Exception as exc:
        logger.error(f"技术指标计算任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=120)
def ai_prediction_task(self, stock_code: str, prediction_days: int = 5):
    """AI预测任务"""
    try:
        logger.info(f"开始对股票 {stock_code} 进行AI预测，预测天数: {prediction_days}")

        async def _predict():
            from app.services.ai_service import DeepSeekAIService
            from app.services.ai_storage import AIPredictionStorageService

            # 执行AI预测
            ai_service = DeepSeekAIService()
            prediction_result = await ai_service.predict_price(stock_code, prediction_days)

            if "error" not in prediction_result:
                # 保存预测结果
                async with AIPredictionStorageService() as storage:
                    prediction_id = await storage.save_prediction(prediction_result)

                return {
                    "status": "success",
                    "stock_code": stock_code,
                    "prediction_days": prediction_days,
                    "prediction_id": prediction_id,
                    "prediction_result": prediction_result,
                    "predicted_at": prediction_result.get("predicted_at")
                }
            else:
                return {
                    "status": "failed",
                    "message": prediction_result.get("error", "AI预测失败")
                }

        result = run_async_task(_predict())

        logger.info(f"股票 {stock_code} AI预测任务完成")
        return result

    except Exception as exc:
        logger.error(f"AI预测任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=120, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=180)
def stock_selection_task(self, target: str, task_type: str = "single_score", stock_list: List[str] = None):
    """股票选股任务"""
    try:
        logger.info(f"开始执行选股任务: {task_type}, 目标: {target}")

        async def _execute_selection():
            if task_type == "single_score":
                # 单只股票评分
                from app.services.stock_selection import StockScoringService

                async with StockScoringService() as scoring_service:
                    score_result = await scoring_service.calculate_stock_score(target)

                    if "error" not in score_result:
                        await scoring_service.save_stock_score(score_result)

                        return {
                            "status": "success",
                            "task_type": task_type,
                            "stock_code": target,
                            "score_result": score_result
                        }
                    else:
                        return {
                            "status": "failed",
                            "message": score_result.get("error", "评分计算失败")
                        }

            elif task_type == "strategy_run":
                # 执行选股策略
                from app.services.stock_selection import StockSelectionService

                async with StockSelectionService() as selection_service:
                    selection_result = await selection_service.run_selection_strategy(target, stock_list)

                    if "error" not in selection_result:
                        return {
                            "status": "success",
                            "task_type": task_type,
                            "strategy_name": target,
                            "selection_result": selection_result
                        }
                    else:
                        return {
                            "status": "failed",
                            "message": selection_result.get("error", "选股策略执行失败")
                        }

            elif task_type == "batch_score":
                # 批量股票评分
                from app.services.stock_selection import StockScoringService

                if not stock_list:
                    return {"status": "failed", "message": "批量评分需要提供股票列表"}

                results = []
                async with StockScoringService() as scoring_service:
                    for stock_code in stock_list:
                        try:
                            score_result = await scoring_service.calculate_stock_score(stock_code)
                            if "error" not in score_result:
                                await scoring_service.save_stock_score(score_result)
                                results.append({
                                    "stock_code": stock_code,
                                    "status": "success",
                                    "score": score_result["total_score"]
                                })
                            else:
                                results.append({
                                    "stock_code": stock_code,
                                    "status": "failed",
                                    "error": score_result["error"]
                                })
                        except Exception as e:
                            results.append({
                                "stock_code": stock_code,
                                "status": "failed",
                                "error": str(e)
                            })

                successful_count = len([r for r in results if r["status"] == "success"])

                return {
                    "status": "success",
                    "task_type": task_type,
                    "processed_stocks": len(stock_list),
                    "successful_count": successful_count,
                    "results": results
                }

            else:
                return {"status": "failed", "message": f"未知任务类型: {task_type}"}

        result = run_async_task(_execute_selection())

        logger.info(f"选股任务 {task_type} 完成")
        return result

    except Exception as exc:
        logger.error(f"选股任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=180, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def alert_monitoring_task(self, stock_code: str, alert_types: List[str] = None):
    """预警监控任务"""
    try:
        logger.info(f"开始监控股票 {stock_code} 的预警...")

        async def _monitor_alerts():
            from app.services.alert_service import AlertRuleEngine, AlertNotificationService

            all_events = []
            notification_count = 0

            async with AlertRuleEngine() as rule_engine:
                # 价格预警
                if not alert_types or "price" in alert_types:
                    price_events = await rule_engine.evaluate_price_alerts(stock_code)
                    all_events.extend(price_events)

                # 技术指标预警
                if not alert_types or "indicator" in alert_types:
                    indicator_events = await rule_engine.evaluate_indicator_alerts(stock_code)
                    all_events.extend(indicator_events)

                # AI信号预警
                if not alert_types or "ai" in alert_types:
                    ai_events = await rule_engine.evaluate_ai_alerts(stock_code)
                    all_events.extend(ai_events)

                # 成交量预警
                if not alert_types or "volume" in alert_types:
                    volume_events = await rule_engine.evaluate_volume_alerts(stock_code)
                    all_events.extend(volume_events)

            # 发送预警通知
            async with AlertNotificationService() as notification_service:
                for event in all_events:
                    try:
                        if await notification_service.send_alert_notification(event):
                            notification_count += 1
                    except Exception as e:
                        logger.error(f"发送预警通知失败: {e}")

            return {
                "status": "success",
                "stock_code": stock_code,
                "alert_types": alert_types or ["all"],
                "triggered_events": len(all_events),
                "notifications_sent": notification_count,
                "events_summary": [
                    {
                        "event_type": event["event_type"],
                        "alert_level": event["alert_level"],
                        "event_title": event["event_title"]
                    }
                    for event in all_events
                ],
                "monitored_at": datetime.now().isoformat()
            }

        result = run_async_task(_monitor_alerts())

        logger.info(f"股票 {stock_code} 预警监控完成，触发 {result.get('triggered_events', 0)} 个事件")
        return result

    except Exception as exc:
        logger.error(f"预警监控任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=2, default_retry_delay=180)
def market_analysis_task(self, analysis_type: str = "sector"):
    """市场分析任务"""
    try:
        logger.info(f"开始执行市场分析任务，类型: {analysis_type}")
        
        # TODO: 实现市场分析逻辑
        
        if analysis_type == "sector":
            # 板块分析
            result = {
                "analysis_type": "sector",
                "sectors": [
                    {"name": "银行", "performance": 2.5, "trend": "up"},
                    {"name": "科技", "performance": -1.2, "trend": "down"},
                    {"name": "医药", "performance": 0.8, "trend": "stable"}
                ]
            }
        elif analysis_type == "market":
            # 大盘分析
            result = {
                "analysis_type": "market",
                "indices": {
                    "shanghai": {"value": 3200, "change": 1.5},
                    "shenzhen": {"value": 2100, "change": -0.8}
                }
            }
        else:
            result = {"analysis_type": analysis_type, "data": {}}
        
        logger.info(f"市场分析任务完成，类型: {analysis_type}")
        
        return {
            "status": "success",
            "analysis": result,
            "analyzed_at": datetime.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"市场分析任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=180, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=3, default_retry_delay=90)
def stock_screening_task(self, criteria: Dict[str, Any]):
    """股票筛选任务"""
    try:
        logger.info(f"开始执行股票筛选任务，条件: {criteria}")
        
        # TODO: 实现股票筛选逻辑
        
        # 模拟筛选结果
        screened_stocks = [
            {
                "stock_code": "000001",
                "stock_name": "平安银行",
                "score": 85,
                "reason": "技术指标良好，基本面稳定"
            },
            {
                "stock_code": "600036",
                "stock_name": "招商银行",
                "score": 82,
                "reason": "盈利能力强，估值合理"
            }
        ]
        
        logger.info(f"股票筛选完成，筛选出 {len(screened_stocks)} 只股票")
        
        return {
            "status": "success",
            "criteria": criteria,
            "screened_stocks": screened_stocks,
            "total_count": len(screened_stocks),
            "screened_at": datetime.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"股票筛选任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=90, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}


@celery_app.task(bind=True, max_retries=2, default_retry_delay=300)
def portfolio_analysis_task(self, portfolio_id: int):
    """投资组合分析任务"""
    try:
        logger.info(f"开始分析投资组合 {portfolio_id}...")
        
        # TODO: 实现投资组合分析逻辑
        
        # 模拟分析结果
        analysis_result = {
            "portfolio_id": portfolio_id,
            "total_value": 100000.0,
            "total_return": 5.2,
            "risk_level": "medium",
            "diversification_score": 0.75,
            "recommendations": [
                "建议增加科技股比重",
                "考虑减少银行股配置"
            ]
        }
        
        logger.info(f"投资组合 {portfolio_id} 分析完成")
        
        return {
            "status": "success",
            "analysis": analysis_result,
            "analyzed_at": datetime.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"投资组合分析任务异常: {exc}")
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=300, exc=exc)
        else:
            return {"status": "failed", "message": str(exc)}
