"""
数据验证服务模块
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
import pandas as pd
import numpy as np
from decimal import Decimal

from app.core.logging import get_logger

logger = get_logger(__name__)


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.validation_rules = {
            'stock_code': self._validate_stock_code,
            'price': self._validate_price,
            'volume': self._validate_volume,
            'date': self._validate_date,
            'percentage': self._validate_percentage
        }
    
    def validate_stock_data(self, stock_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证股票基本信息数据"""
        errors = []
        
        # 验证股票代码
        if not self._validate_stock_code(stock_data.get('stock_code')):
            errors.append("股票代码格式无效")
        
        # 验证股票名称
        if not stock_data.get('stock_name') or len(stock_data['stock_name'].strip()) == 0:
            errors.append("股票名称不能为空")
        
        # 验证市场类型
        if stock_data.get('market') not in ['SH', 'SZ', 'BJ']:
            errors.append("市场类型必须是SH、SZ或BJ")
        
        # 验证上市日期
        if stock_data.get('list_date') and not self._validate_date(stock_data['list_date']):
            errors.append("上市日期格式无效")
        
        return len(errors) == 0, errors
    
    def validate_kline_data(self, kline_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证K线数据"""
        errors = []
        
        # 验证股票代码
        if not self._validate_stock_code(kline_data.get('stock_code')):
            errors.append("股票代码格式无效")
        
        # 验证交易日期
        if not self._validate_date(kline_data.get('trade_date')):
            errors.append("交易日期格式无效")
        
        # 验证价格数据
        prices = ['open_price', 'high_price', 'low_price', 'close_price']
        for price_field in prices:
            if not self._validate_price(kline_data.get(price_field)):
                errors.append(f"{price_field}价格数据无效")
        
        # 验证价格逻辑关系
        try:
            open_price = float(kline_data.get('open_price', 0))
            high_price = float(kline_data.get('high_price', 0))
            low_price = float(kline_data.get('low_price', 0))
            close_price = float(kline_data.get('close_price', 0))
            
            if high_price < max(open_price, close_price, low_price):
                errors.append("最高价不能低于开盘价、收盘价或最低价")
            
            if low_price > min(open_price, close_price, high_price):
                errors.append("最低价不能高于开盘价、收盘价或最高价")
                
        except (ValueError, TypeError):
            errors.append("价格数据类型错误")
        
        # 验证成交量
        if not self._validate_volume(kline_data.get('volume')):
            errors.append("成交量数据无效")
        
        # 验证成交额
        if not self._validate_turnover(kline_data.get('turnover')):
            errors.append("成交额数据无效")
        
        # 验证涨跌幅
        if kline_data.get('change_percent') is not None:
            if not self._validate_percentage(kline_data['change_percent']):
                errors.append("涨跌幅数据无效")
        
        return len(errors) == 0, errors
    
    def validate_realtime_data(self, realtime_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证实时数据"""
        errors = []
        
        # 验证股票代码
        if not self._validate_stock_code(realtime_data.get('stock_code')):
            errors.append("股票代码格式无效")
        
        # 验证当前价格
        if not self._validate_price(realtime_data.get('current_price')):
            errors.append("当前价格数据无效")
        
        # 验证成交量
        if not self._validate_volume(realtime_data.get('volume')):
            errors.append("成交量数据无效")
        
        # 验证时间戳
        timestamp = realtime_data.get('timestamp')
        if timestamp and not isinstance(timestamp, datetime):
            try:
                datetime.fromisoformat(str(timestamp))
            except (ValueError, TypeError):
                errors.append("时间戳格式无效")
        
        return len(errors) == 0, errors
    
    def _validate_stock_code(self, stock_code: Any) -> bool:
        """验证股票代码格式"""
        if not stock_code or not isinstance(stock_code, str):
            return False
        
        # A股股票代码格式：6位数字
        if len(stock_code) != 6 or not stock_code.isdigit():
            return False
        
        # 验证市场前缀
        if stock_code.startswith('6'):  # 上海主板
            return True
        elif stock_code.startswith('0'):  # 深圳主板
            return True
        elif stock_code.startswith('3'):  # 创业板
            return True
        elif stock_code.startswith('8') or stock_code.startswith('4'):  # 北交所
            return True
        
        return False
    
    def _validate_price(self, price: Any) -> bool:
        """验证价格数据"""
        if price is None:
            return False
        
        try:
            price_val = float(price)
            return price_val >= 0 and price_val <= 10000  # 合理的价格范围
        except (ValueError, TypeError):
            return False
    
    def _validate_volume(self, volume: Any) -> bool:
        """验证成交量数据"""
        if volume is None:
            return False

        try:
            volume_val = int(volume)
            return volume_val >= 0
        except (ValueError, TypeError):
            return False

    def _validate_turnover(self, turnover: Any) -> bool:
        """验证成交额数据"""
        if turnover is None:
            return False

        try:
            turnover_val = float(turnover)
            return turnover_val >= 0 and turnover_val <= 1e12  # 成交额上限1万亿
        except (ValueError, TypeError):
            return False
    
    def _validate_date(self, date_val: Any) -> bool:
        """验证日期数据"""
        if date_val is None:
            return False
        
        if isinstance(date_val, date):
            return True
        
        try:
            if isinstance(date_val, str):
                datetime.strptime(date_val, '%Y-%m-%d')
                return True
        except ValueError:
            pass
        
        return False
    
    def _validate_percentage(self, percentage: Any) -> bool:
        """验证百分比数据"""
        if percentage is None:
            return True  # 百分比可以为空
        
        try:
            pct_val = float(percentage)
            return -100 <= pct_val <= 100  # 涨跌幅限制在±100%
        except (ValueError, TypeError):
            return False


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.validator = DataValidator()
    
    def clean_stock_data(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗股票基本信息数据"""
        cleaned_data = stock_data.copy()
        
        # 清洗股票代码
        if 'stock_code' in cleaned_data:
            cleaned_data['stock_code'] = str(cleaned_data['stock_code']).strip().zfill(6)
        
        # 清洗股票名称
        if 'stock_name' in cleaned_data:
            cleaned_data['stock_name'] = str(cleaned_data['stock_name']).strip()
        
        # 清洗市场类型
        if 'market' in cleaned_data:
            market = str(cleaned_data['market']).upper().strip()
            cleaned_data['market'] = market
        
        # 清洗行业信息
        if 'industry' in cleaned_data and cleaned_data['industry']:
            cleaned_data['industry'] = str(cleaned_data['industry']).strip()
        
        return cleaned_data
    
    def clean_kline_data(self, kline_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗K线数据"""
        cleaned_data = kline_data.copy()
        
        # 清洗股票代码
        if 'stock_code' in cleaned_data:
            cleaned_data['stock_code'] = str(cleaned_data['stock_code']).strip().zfill(6)
        
        # 清洗价格数据
        price_fields = ['open_price', 'high_price', 'low_price', 'close_price', 'turnover']
        for field in price_fields:
            if field in cleaned_data:
                try:
                    cleaned_data[field] = round(float(cleaned_data[field]), 3)
                except (ValueError, TypeError):
                    cleaned_data[field] = 0.0
        
        # 清洗成交量
        if 'volume' in cleaned_data:
            try:
                cleaned_data['volume'] = int(float(cleaned_data['volume']))
            except (ValueError, TypeError):
                cleaned_data['volume'] = 0
        
        # 清洗涨跌幅
        if 'change_percent' in cleaned_data:
            try:
                cleaned_data['change_percent'] = round(float(cleaned_data['change_percent']), 4)
            except (ValueError, TypeError):
                cleaned_data['change_percent'] = 0.0
        
        # 清洗涨跌额
        if 'change' in cleaned_data:
            try:
                cleaned_data['change'] = round(float(cleaned_data['change']), 3)
            except (ValueError, TypeError):
                cleaned_data['change'] = 0.0
        
        return cleaned_data
    
    def clean_realtime_data(self, realtime_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗实时数据"""
        cleaned_data = realtime_data.copy()
        
        # 清洗股票代码
        if 'stock_code' in cleaned_data:
            cleaned_data['stock_code'] = str(cleaned_data['stock_code']).strip().zfill(6)
        
        # 清洗价格数据
        price_fields = ['current_price', 'bid_price', 'ask_price', 'turnover']
        for field in price_fields:
            if field in cleaned_data:
                try:
                    cleaned_data[field] = round(float(cleaned_data[field]), 3)
                except (ValueError, TypeError):
                    cleaned_data[field] = 0.0
        
        # 清洗成交量
        volume_fields = ['volume', 'bid_volume', 'ask_volume']
        for field in volume_fields:
            if field in cleaned_data:
                try:
                    cleaned_data[field] = int(float(cleaned_data[field]))
                except (ValueError, TypeError):
                    cleaned_data[field] = 0
        
        # 清洗涨跌数据
        if 'change_percent' in cleaned_data:
            try:
                cleaned_data['change_percent'] = round(float(cleaned_data['change_percent']), 4)
            except (ValueError, TypeError):
                cleaned_data['change_percent'] = 0.0
        
        if 'change' in cleaned_data:
            try:
                cleaned_data['change'] = round(float(cleaned_data['change']), 3)
            except (ValueError, TypeError):
                cleaned_data['change'] = 0.0
        
        return cleaned_data
    
    def detect_outliers_kline(self, kline_data_list: List[Dict[str, Any]]) -> List[int]:
        """检测K线数据中的异常值"""
        if len(kline_data_list) < 10:  # 数据量太少，无法检测异常值
            return []
        
        outlier_indices = []
        
        try:
            # 转换为DataFrame进行分析
            df = pd.DataFrame(kline_data_list)
            
            # 检测价格异常值（使用IQR方法）
            price_columns = ['open_price', 'high_price', 'low_price', 'close_price']
            for col in price_columns:
                if col in df.columns:
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)].index.tolist()
                    outlier_indices.extend(outliers)
            
            # 检测成交量异常值
            if 'volume' in df.columns:
                volume_median = df['volume'].median()
                volume_std = df['volume'].std()
                
                # 成交量异常：超过中位数的10倍或为0
                volume_outliers = df[
                    (df['volume'] > volume_median * 10) | (df['volume'] == 0)
                ].index.tolist()
                outlier_indices.extend(volume_outliers)
            
            # 去重并排序
            outlier_indices = sorted(list(set(outlier_indices)))
            
        except Exception as e:
            logger.error(f"检测异常值时出错: {e}")
            return []
        
        return outlier_indices
    
    def remove_duplicates(self, data_list: List[Dict[str, Any]], key_fields: List[str]) -> List[Dict[str, Any]]:
        """移除重复数据"""
        seen = set()
        unique_data = []
        
        for data in data_list:
            # 创建唯一键
            key_values = tuple(data.get(field) for field in key_fields)
            
            if key_values not in seen:
                seen.add(key_values)
                unique_data.append(data)
        
        logger.info(f"原始数据: {len(data_list)} 条，去重后: {len(unique_data)} 条")
        return unique_data
