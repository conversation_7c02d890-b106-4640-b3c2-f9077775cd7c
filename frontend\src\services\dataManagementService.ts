// 数据管理服务 - 处理本地数据库数据操作
export interface APIConfig {
  id: string
  name: string
  type: 'akshare' | 'tushare' | 'sina'
  enabled: boolean
  api_key?: string
  endpoint?: string
  rate_limit: number
  timeout: number
  priority: number
}

export interface DatabaseStats {
  total_stocks: number
  today_updates: number
  last_update_time: string
  data_size: string
  news_count: number
  news_updated_today: number
  table_stats: {
    name: string
    count: number
    size: string
  }[]
}

export interface UpdateTask {
  id: string
  name: string
  type: 'stock_basic' | 'stock_price' | 'financial_news' | 'technical_indicators'
  status: 'running' | 'completed' | 'failed' | 'pending'
  progress: number
  start_time?: string
  end_time?: string
  records_processed?: number
  total_records?: number
  error_message?: string
}

export interface TaskLog {
  timestamp: string
  level: 'INFO' | 'WARNING' | 'ERROR'
  message: string
  data?: Record<string, any>
}

export interface TaskUpdateDetails {
  total_stocks?: number
  updated_today?: number
  today_records?: number
  stocks_count?: number
  today_news?: number
  today_indicators?: number
  update_type: string
  data_summary: string
  error?: string
}

export interface TaskExecutionInfo {
  created_at: string
  updated_at: string
  duration: string
  data_source: {
    source: string
    api: string
    description: string
    update_frequency: string
    data_fields: string[]
  }
}

export interface TaskDetails {
  task: UpdateTask
  logs: TaskLog[]
  update_details: TaskUpdateDetails
  execution_info: TaskExecutionInfo
}

export interface AutoUpdateSettings {
  enabled: boolean
  stockDataInterval: number
  newsInterval: number
  weekendUpdate: boolean
  updateTime: string
  retryCount: number
  enableNotification: boolean
}

class DataManagementService {
  private baseUrl = '/api/v1/data-management'

  // API配置管理
  async getAPIConfigs(): Promise<APIConfig[]> {
    const response = await fetch(`${this.baseUrl}/api-configs`)
    if (!response.ok) {
      throw new Error(`获取API配置失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  async updateAPIConfig(config: APIConfig): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/api-configs/${config.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    })
    if (!response.ok) {
      throw new Error(`更新API配置失败: ${response.status} ${response.statusText}`)
    }
    return true
  }

  async testAPIConnection(apiId: string): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/api-configs/${apiId}/test`, {
      method: 'POST',
    })
    if (!response.ok) {
      throw new Error(`测试API连接失败: ${response.status} ${response.statusText}`)
    }
    const result = await response.json()
    return result.success
  }

  // 数据库状态管理
  async getDatabaseStats(): Promise<DatabaseStats> {
    const response = await fetch(`${this.baseUrl}/database/stats`)
    if (!response.ok) {
      throw new Error(`获取数据库状态失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  // 更新任务管理
  async getUpdateTasks(): Promise<UpdateTask[]> {
    const response = await fetch(`${this.baseUrl}/update-tasks`)
    if (!response.ok) {
      throw new Error(`获取更新任务失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  async startUpdateTask(taskType: string): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/update-tasks/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ type: taskType }),
    })
    if (!response.ok) {
      throw new Error(`启动更新任务失败: ${response.status} ${response.statusText}`)
    }
    return true
  }

  async stopUpdateTask(taskId: string): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/update-tasks/${taskId}/stop`, {
      method: 'POST',
    })
    if (!response.ok) {
      throw new Error(`停止更新任务失败: ${response.status} ${response.statusText}`)
    }
    return true
  }

  async getTaskDetails(taskId: string): Promise<TaskDetails> {
    const response = await fetch(`${this.baseUrl}/update-tasks/${taskId}/details`)
    if (!response.ok) {
      throw new Error(`获取任务详情失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  // 自动更新设置
  async getAutoUpdateSettings(): Promise<AutoUpdateSettings> {
    const response = await fetch(`${this.baseUrl}/auto-update/settings`)
    if (!response.ok) {
      throw new Error(`获取自动更新设置失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  async updateAutoUpdateSettings(settings: AutoUpdateSettings): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/auto-update/settings`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
    })
    if (!response.ok) {
      throw new Error(`更新自动更新设置失败: ${response.status} ${response.statusText}`)
    }
    return true
  }

  // 数据清理和优化
  async cleanExpiredData(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/database/clean-expired`, {
        method: 'POST',
      })
      return response.ok
    } catch (error) {
      console.error('清理过期数据失败:', error)
      return false
    }
  }

  async optimizeDatabase(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/database/optimize`, {
        method: 'POST',
      })
      return response.ok
    } catch (error) {
      console.error('优化数据库失败:', error)
      return false
    }
  }

  // 手动更新操作
  async updateStockBasicInfo(): Promise<boolean> {
    return this.startUpdateTask('stock_basic')
  }

  async updateStockPrices(): Promise<boolean> {
    return this.startUpdateTask('stock_price')
  }

  async updateFinancialNews(): Promise<boolean> {
    return this.startUpdateTask('financial_news')
  }

  async calculateTechnicalIndicators(): Promise<boolean> {
    return this.startUpdateTask('technical_indicators')
  }

  async fullDataUpdate(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/update-tasks/full-update`, {
        method: 'POST',
      })
      return response.ok
    } catch (error) {
      console.error('全量数据更新失败:', error)
      return false
    }
  }
}

export const dataManagementService = new DataManagementService()
export { DataManagementService }
