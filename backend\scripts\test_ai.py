#!/usr/bin/env python3
"""
AI功能测试脚本
"""

import asyncio
import sys
import os
import json
from datetime import date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.logging import setup_logging, get_logger
from app.services.ai_service import DeepSeekAIService, AIAnalysisService
from app.services.ai_storage import AIPredictionStorageService
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


async def test_deepseek_service():
    """测试DeepSeek AI服务"""
    logger.info("=== 测试DeepSeek AI服务 ===")
    
    ai_service = DeepSeekAIService()
    test_stock = "000001"
    
    try:
        # 测试价格预测
        logger.info("测试价格预测...")
        prediction_result = await ai_service.predict_price(test_stock, 5)
        
        if "error" not in prediction_result:
            logger.info("✅ 价格预测成功")
            logger.info(f"预测方向: {prediction_result.get('prediction', 'N/A')}")
            logger.info(f"置信度: {prediction_result.get('confidence', 'N/A')}")
            logger.info(f"目标价格: {prediction_result.get('target_price', 'N/A')}")
            logger.info(f"分析理由: {prediction_result.get('reasoning', 'N/A')[:100]}...")
        else:
            logger.warning(f"价格预测失败: {prediction_result['error']}")
        
        # 测试趋势分析
        logger.info("测试趋势分析...")
        trend_result = await ai_service.analyze_trend(test_stock)
        
        if "error" not in trend_result:
            logger.info("✅ 趋势分析成功")
            logger.info(f"主要趋势: {trend_result.get('trend', 'N/A')}")
            logger.info(f"趋势强度: {trend_result.get('strength', 'N/A')}")
            logger.info(f"预期持续时间: {trend_result.get('duration', 'N/A')}")
        else:
            logger.warning(f"趋势分析失败: {trend_result['error']}")
        
        # 测试形态识别
        logger.info("测试形态识别...")
        pattern_result = await ai_service.recognize_patterns(test_stock)
        
        if "error" not in pattern_result:
            logger.info("✅ 形态识别成功")
            patterns = pattern_result.get('patterns', [])
            logger.info(f"识别到 {len(patterns)} 个形态")
            for pattern in patterns[:2]:  # 显示前2个形态
                if isinstance(pattern, dict):
                    logger.info(f"  - {pattern.get('name', 'N/A')}: 置信度 {pattern.get('confidence', 'N/A')}")
        else:
            logger.warning(f"形态识别失败: {pattern_result['error']}")
            
    except Exception as e:
        logger.error(f"DeepSeek AI服务测试失败: {e}")


async def test_ai_analysis_service():
    """测试AI分析服务"""
    logger.info("=== 测试AI分析服务 ===")
    
    ai_service = AIAnalysisService()
    test_stock = "000001"
    
    try:
        # 测试综合分析
        logger.info("测试综合分析...")
        analysis_result = await ai_service.comprehensive_analysis(test_stock)
        
        if "error" not in analysis_result:
            logger.info("✅ 综合分析成功")
            
            # 显示各项分析结果
            price_pred = analysis_result.get("price_prediction", {})
            if "error" not in price_pred:
                logger.info(f"价格预测: {price_pred.get('prediction', 'N/A')} (置信度: {price_pred.get('confidence', 'N/A')})")
            
            trend_analysis = analysis_result.get("trend_analysis", {})
            if "error" not in trend_analysis:
                logger.info(f"趋势分析: {trend_analysis.get('trend', 'N/A')} ({trend_analysis.get('strength', 'N/A')})")
            
            pattern_recog = analysis_result.get("pattern_recognition", {})
            if "error" not in pattern_recog:
                patterns = pattern_recog.get("patterns", [])
                logger.info(f"形态识别: 发现 {len(patterns)} 个形态")
            
            # 显示综合评分
            overall_score = analysis_result.get("overall_score", {})
            logger.info(f"综合评分: {overall_score.get('score', 'N/A')} ({overall_score.get('level', 'N/A')})")
            
        else:
            logger.warning(f"综合分析失败: {analysis_result['error']}")
            
    except Exception as e:
        logger.error(f"AI分析服务测试失败: {e}")


async def test_ai_storage():
    """测试AI存储服务"""
    logger.info("=== 测试AI存储服务 ===")
    
    test_stock = "000001"
    
    try:
        # 先生成一些测试数据
        ai_service = DeepSeekAIService()
        prediction_result = await ai_service.predict_price(test_stock, 5)
        pattern_result = await ai_service.recognize_patterns(test_stock)
        
        async with AIPredictionStorageService() as storage:
            # 测试保存预测结果
            if "error" not in prediction_result:
                prediction_id = await storage.save_prediction(prediction_result)
                logger.info(f"✅ 预测结果保存成功，ID: {prediction_id}")
            
            # 测试保存形态识别结果
            if "error" not in pattern_result:
                pattern_count = await storage.save_pattern_recognition(pattern_result)
                logger.info(f"✅ 形态识别结果保存成功，数量: {pattern_count}")
            
            # 测试查询预测结果
            predictions = await storage.get_predictions(stock_code=test_stock, limit=5)
            logger.info(f"✅ 查询到 {len(predictions)} 条预测记录")
            
            if predictions:
                latest = predictions[0]
                logger.info(f"最新预测: {latest.prediction_date} -> {latest.target_date}")
                logger.info(f"  预测价格: {latest.predicted_price}")
                logger.info(f"  置信度: {latest.confidence_score}")
                logger.info(f"  趋势方向: {latest.trend_direction}")
            
            # 测试查询形态识别结果
            patterns = await storage.get_pattern_recognitions(stock_code=test_stock, limit=5)
            logger.info(f"✅ 查询到 {len(patterns)} 条形态记录")
            
            if patterns:
                latest_pattern = patterns[0]
                logger.info(f"最新形态: {latest_pattern.pattern_name}")
                logger.info(f"  形态类型: {latest_pattern.pattern_type}")
                logger.info(f"  置信度: {latest_pattern.confidence}")
            
            # 测试统计信息
            stats = await storage.get_prediction_statistics()
            logger.info(f"✅ 预测统计信息: {stats}")
            
            accuracy = await storage.get_prediction_accuracy(30)
            logger.info(f"✅ 预测准确率: {accuracy}")
            
    except Exception as e:
        logger.error(f"AI存储服务测试失败: {e}")


async def test_mock_responses():
    """测试模拟响应"""
    logger.info("=== 测试模拟响应 ===")
    
    ai_service = DeepSeekAIService()
    
    # 测试不同类型的模拟响应
    test_messages = [
        [{"role": "user", "content": "请进行价格预测分析"}],
        [{"role": "user", "content": "请进行趋势分析"}],
        [{"role": "user", "content": "请进行形态识别"}],
        [{"role": "user", "content": "请进行综合分析"}]
    ]
    
    for i, messages in enumerate(test_messages):
        try:
            response = ai_service._get_mock_response(messages)
            parsed_response = json.loads(response)
            logger.info(f"✅ 模拟响应 {i+1}: {list(parsed_response.keys())}")
        except Exception as e:
            logger.error(f"模拟响应 {i+1} 测试失败: {e}")


async def test_data_preparation():
    """测试数据准备"""
    logger.info("=== 测试数据准备 ===")
    
    test_stock = "000001"
    
    try:
        # 检查是否有K线数据
        async with DataStorageService() as storage:
            klines = await storage.get_kline_data(test_stock, "daily", limit=10)
        
        if klines:
            logger.info(f"✅ 找到 {len(klines)} 条K线数据")
            latest_kline = klines[-1]
            logger.info(f"最新K线: {latest_kline.trade_date} 收盘价: {latest_kline.close_price}")
            
            # 准备价格数据
            prices = [float(kline.close_price) for kline in klines]
            logger.info(f"价格数据: {prices[-5:]}")  # 显示最近5天价格
            
        else:
            logger.warning("没有找到K线数据，AI分析可能受限")
            
        # 检查技术指标数据
        from app.services.indicator_storage import IndicatorStorageService
        async with IndicatorStorageService() as indicator_storage:
            indicators = await indicator_storage.get_indicators(test_stock, "daily", limit=5)
        
        if indicators:
            logger.info(f"✅ 找到 {len(indicators)} 条技术指标数据")
            latest_indicator = indicators[-1]
            logger.info(f"最新指标: RSI={latest_indicator.rsi}, MACD={latest_indicator.macd}")
        else:
            logger.warning("没有找到技术指标数据，建议先计算技术指标")
            
    except Exception as e:
        logger.error(f"数据准备测试失败: {e}")


async def main():
    """主测试函数"""
    setup_logging()
    logger.info("🤖 开始AI功能测试")
    
    try:
        # 1. 测试数据准备
        await test_data_preparation()
        
        # 2. 测试模拟响应
        await test_mock_responses()
        
        # 3. 测试DeepSeek AI服务
        await test_deepseek_service()
        
        # 4. 测试AI分析服务
        await test_ai_analysis_service()
        
        # 5. 测试AI存储服务
        await test_ai_storage()
        
        logger.info("✅ 所有AI功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
