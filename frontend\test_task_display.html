<!DOCTYPE html>
<html>
<head>
    <title>任务管理测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .task-item { 
            border: 1px solid #ddd; 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 4px; 
            background: #f9f9f9;
        }
        .task-header { 
            font-weight: bold; 
            margin-bottom: 10px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center;
        }
        .task-status {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
        }
        .status-running { background-color: #1890ff; }
        .status-completed { background-color: #52c41a; }
        .status-failed { background-color: #ff4d4f; }
        .status-pending { background-color: #d9d9d9; color: #666; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #1890ff;
            transition: width 0.3s ease;
        }
        .task-details { font-size: 14px; color: #666; }
        button { margin: 5px; padding: 10px 15px; }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
    </style>
</head>
<body>
    <h1>任务管理功能测试</h1>
    
    <div>
        <button onclick="loadTasks()">刷新任务列表</button>
        <button onclick="startTask('stock_basic')">启动股票基础信息更新</button>
        <button onclick="startTask('stock_price')">启动股票价格更新</button>
        <button onclick="startTask('financial_news')">启动财经新闻更新</button>
    </div>
    
    <div id="status"></div>
    <div id="tasks"></div>

    <script>
        const API_BASE = 'http://localhost:8001/api/v1/data-management';
        
        function showStatus(message, isSuccess = true) {
            const div = document.getElementById('status');
            div.innerHTML = `<p class="${isSuccess ? 'success' : 'error'}">${message}</p>`;
        }
        
        function formatTime(timeStr) {
            if (!timeStr) return '未开始';
            return timeStr.replace('T', ' ').substring(0, 19);
        }
        
        function getStatusClass(status) {
            return `status-${status}`;
        }
        
        function getStatusText(status) {
            const statusMap = {
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败',
                'pending': '等待中'
            };
            return statusMap[status] || status;
        }
        
        function renderTasks(tasks) {
            const container = document.getElementById('tasks');
            
            if (tasks.length === 0) {
                container.innerHTML = '<p>暂无任务</p>';
                return;
            }
            
            const html = tasks.map(task => `
                <div class="task-item">
                    <div class="task-header">
                        <span>${task.name}</span>
                        <span class="task-status ${getStatusClass(task.status)}">
                            ${getStatusText(task.status)}
                        </span>
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${task.progress}%"></div>
                    </div>
                    
                    <div class="task-details">
                        <div>任务ID: ${task.id}</div>
                        <div>类型: ${task.type}</div>
                        <div>进度: ${task.progress}%</div>
                        ${task.start_time ? `<div>开始时间: ${formatTime(task.start_time)}</div>` : ''}
                        ${task.end_time ? `<div>结束时间: ${formatTime(task.end_time)}</div>` : ''}
                        ${task.records_processed && task.total_records ?
                            `<div>处理进度: ${task.records_processed}/${task.total_records}</div>` : ''}
                        ${task.error_message ? `<div style="color: #ff4d4f;">错误信息: ${task.error_message}</div>` : ''}
                        <div style="margin-top: 10px;">
                            <button onclick="viewTaskDetails('${task.id}')" style="padding: 4px 8px; font-size: 12px;">查看详情</button>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        async function loadTasks() {
            try {
                showStatus('正在加载任务列表...');
                const response = await fetch(`${API_BASE}/update-tasks`);
                
                if (response.ok) {
                    const tasks = await response.json();
                    showStatus(`✅ 加载成功，共${tasks.length}个任务`);
                    renderTasks(tasks);
                } else {
                    showStatus(`❌ 加载失败: ${response.status}`, false);
                }
            } catch (error) {
                showStatus(`❌ 加载错误: ${error.message}`, false);
            }
        }
        
        async function startTask(taskType) {
            try {
                showStatus(`正在启动${taskType}任务...`);
                const response = await fetch(`${API_BASE}/update-tasks/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ type: taskType })
                });

                if (response.ok) {
                    const result = await response.json();
                    showStatus(`✅ 任务启动成功: ${result.message}`);
                    // 延迟刷新任务列表
                    setTimeout(loadTasks, 1000);
                } else {
                    showStatus(`❌ 启动失败: ${response.status}`, false);
                }
            } catch (error) {
                showStatus(`❌ 启动错误: ${error.message}`, false);
            }
        }

        async function viewTaskDetails(taskId) {
            try {
                showStatus(`正在获取任务详情...`);
                const response = await fetch(`${API_BASE}/update-tasks/${taskId}/details`);

                if (response.ok) {
                    const details = await response.json();
                    showTaskDetails(details);
                } else {
                    showStatus(`❌ 获取详情失败: ${response.status}`, false);
                }
            } catch (error) {
                showStatus(`❌ 获取详情错误: ${error.message}`, false);
            }
        }

        function showTaskDetails(details) {
            const task = details.task;
            const updateDetails = details.update_details;
            const dataSource = details.execution_info.data_source;
            const logs = details.logs;

            let detailsHtml = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;" onclick="closeTaskDetails()">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; max-width: 600px; max-height: 80%; overflow-y: auto;" onclick="event.stopPropagation()">
                        <h3>任务详情 - ${task.name}</h3>

                        <div style="margin-bottom: 15px;">
                            <h4>基本信息</h4>
                            <p><strong>任务ID:</strong> ${task.id}</p>
                            <p><strong>状态:</strong> <span class="task-status ${getStatusClass(task.status)}">${getStatusText(task.status)}</span></p>
                            <p><strong>进度:</strong> ${task.progress}%</p>
                            <p><strong>开始时间:</strong> ${formatTime(task.start_time)}</p>
                            <p><strong>结束时间:</strong> ${formatTime(task.end_time)}</p>
                            <p><strong>执行时长:</strong> ${details.execution_info.duration}</p>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <h4>数据源信息</h4>
                            <p><strong>数据源:</strong> ${dataSource.source}</p>
                            <p><strong>API接口:</strong> ${dataSource.api}</p>
                            <p><strong>更新频率:</strong> ${dataSource.update_frequency}</p>
                            <p><strong>描述:</strong> ${dataSource.description}</p>
                            <p><strong>数据字段:</strong> ${dataSource.data_fields.join(', ')}</p>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <h4>更新详情</h4>
                            <p><strong>更新类型:</strong> ${updateDetails.update_type}</p>
                            <p><strong>数据摘要:</strong> ${updateDetails.data_summary}</p>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <h4>执行日志 (${logs.length}条)</h4>
                            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
                                ${logs.map(log => `
                                    <div style="margin-bottom: 5px; font-size: 12px;">
                                        <span style="color: #666;">[${log.timestamp}]</span>
                                        <span style="color: ${log.level === 'ERROR' ? '#ff4d4f' : log.level === 'WARNING' ? '#faad14' : '#1890ff'};">${log.level}:</span>
                                        ${log.message}
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <button onclick="closeTaskDetails()" style="padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', detailsHtml);
        }

        function closeTaskDetails() {
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) {
                modal.remove();
            }
        }
        
        // 页面加载时自动加载任务列表
        window.onload = function() {
            loadTasks();
            // 每5秒自动刷新任务列表
            setInterval(loadTasks, 5000);
        };
    </script>
</body>
</html>
