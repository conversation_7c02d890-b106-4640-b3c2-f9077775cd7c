/**
 * 机器学习预测引擎
 * 实现多种ML模型的股价预测和趋势分析
 */

import { PriceData } from './advancedIndicators'

export interface MLFeatures {
  // 技术特征
  technical: {
    sma5: number
    sma10: number
    sma20: number
    rsi: number
    macd: number
    macdSignal: number
    macdHistogram: number
    bb_upper: number
    bb_middle: number
    bb_lower: number
    volume_sma: number
    price_change: number
    volatility: number
  }
  
  // 基本面特征
  fundamental: {
    pe_ratio: number
    pb_ratio: number
    roe: number
    roa: number
    debt_ratio: number
    current_ratio: number
    revenue_growth: number
    profit_margin: number
  }
  
  // 市场特征
  market: {
    market_return: number
    sector_return: number
    vix: number
    interest_rate: number
    exchange_rate: number
  }
  
  // 情感特征
  sentiment: {
    news_sentiment: number
    social_sentiment: number
    analyst_rating: number
    insider_trading: number
  }
}

export interface MLPrediction {
  model: string
  prediction: number
  confidence: number
  direction: 'up' | 'down' | 'neutral'
  probability: {
    up: number
    down: number
    neutral: number
  }
  timeframe: string
  features_importance?: { [key: string]: number }
}

export interface EnsemblePrediction {
  final_prediction: number
  confidence: number
  direction: 'up' | 'down' | 'neutral'
  model_predictions: MLPrediction[]
  consensus_strength: number
  risk_level: 'low' | 'medium' | 'high'
}

export interface ModelPerformance {
  model: string
  accuracy: number
  precision: number
  recall: number
  f1_score: number
  sharpe_ratio: number
  max_drawdown: number
  win_rate: number
  avg_return: number
}

/**
 * 机器学习预测引擎
 */
export class MLPredictionEngine {

  /**
   * 特征工程 - 从原始数据提取ML特征
   */
  static extractFeatures(data: PriceData[], fundamentalData?: any): MLFeatures[] {
    const features: MLFeatures[] = []
    
    for (let i = 20; i < data.length; i++) {
      const currentData = data.slice(i - 20, i + 1)
      const prices = currentData.map(d => d.close)
      const volumes = currentData.map(d => d.volume)
      
      // 技术特征计算
      const sma5 = this.calculateSMA(prices.slice(-5))
      const sma10 = this.calculateSMA(prices.slice(-10))
      const sma20 = this.calculateSMA(prices)
      const rsi = this.calculateRSI(prices, 14)
      const macdData = this.calculateMACD(prices)
      const bbData = this.calculateBollingerBands(prices, 20, 2)
      const volumeSMA = this.calculateSMA(volumes.slice(-10))
      const priceChange = (prices[prices.length - 1] - prices[prices.length - 2]) / prices[prices.length - 2]
      const volatility = this.calculateVolatility(prices.slice(-20))
      
      // 基本面特征 (模拟数据)
      const fundamental = fundamentalData || {
        pe_ratio: 15 + Math.random() * 20,
        pb_ratio: 1 + Math.random() * 3,
        roe: 0.1 + Math.random() * 0.2,
        roa: 0.05 + Math.random() * 0.1,
        debt_ratio: 0.3 + Math.random() * 0.4,
        current_ratio: 1 + Math.random() * 2,
        revenue_growth: -0.1 + Math.random() * 0.3,
        profit_margin: 0.05 + Math.random() * 0.15
      }
      
      // 市场特征 (模拟数据)
      const market = {
        market_return: -0.02 + Math.random() * 0.04,
        sector_return: -0.03 + Math.random() * 0.06,
        vix: 15 + Math.random() * 20,
        interest_rate: 0.02 + Math.random() * 0.03,
        exchange_rate: 6.5 + Math.random() * 0.5
      }
      
      // 情感特征 (模拟数据)
      const sentiment = {
        news_sentiment: -1 + Math.random() * 2,
        social_sentiment: -1 + Math.random() * 2,
        analyst_rating: 1 + Math.random() * 4,
        insider_trading: -0.1 + Math.random() * 0.2
      }
      
      features.push({
        technical: {
          sma5,
          sma10,
          sma20,
          rsi,
          macd: macdData.macd,
          macdSignal: macdData.signal,
          macdHistogram: macdData.histogram,
          bb_upper: bbData.upper,
          bb_middle: bbData.middle,
          bb_lower: bbData.lower,
          volume_sma: volumeSMA,
          price_change: priceChange,
          volatility
        },
        fundamental,
        market,
        sentiment
      })
    }
    
    return features
  }

  /**
   * 随机森林模型预测
   */
  static randomForestPredict(features: MLFeatures): MLPrediction {
    // 模拟随机森林预测逻辑
    const technicalScore = this.calculateTechnicalScore(features.technical)
    const fundamentalScore = this.calculateFundamentalScore(features.fundamental)
    const marketScore = this.calculateMarketScore(features.market)
    const sentimentScore = this.calculateSentimentScore(features.sentiment)
    
    // 加权组合
    const prediction = (technicalScore * 0.4 + fundamentalScore * 0.3 + 
                       marketScore * 0.2 + sentimentScore * 0.1)
    
    const confidence = Math.min(0.95, 0.6 + Math.abs(prediction) * 0.3)
    
    let direction: 'up' | 'down' | 'neutral' = 'neutral'
    if (prediction > 0.02) direction = 'up'
    else if (prediction < -0.02) direction = 'down'
    
    return {
      model: 'RandomForest',
      prediction,
      confidence,
      direction,
      probability: {
        up: prediction > 0 ? 0.5 + prediction * 10 : 0.5 - Math.abs(prediction) * 10,
        down: prediction < 0 ? 0.5 + Math.abs(prediction) * 10 : 0.5 - prediction * 10,
        neutral: 1 - Math.abs(prediction) * 20
      },
      timeframe: '1D',
      features_importance: {
        'technical.rsi': 0.15,
        'technical.macd': 0.12,
        'fundamental.pe_ratio': 0.10,
        'sentiment.news_sentiment': 0.08,
        'market.market_return': 0.07
      }
    }
  }

  /**
   * XGBoost模型预测
   */
  static xgboostPredict(features: MLFeatures): MLPrediction {
    // 模拟XGBoost预测逻辑
    const technicalScore = this.calculateTechnicalScore(features.technical) * 1.1
    const fundamentalScore = this.calculateFundamentalScore(features.fundamental) * 0.9
    const marketScore = this.calculateMarketScore(features.market) * 1.2
    const sentimentScore = this.calculateSentimentScore(features.sentiment) * 0.8
    
    const prediction = (technicalScore * 0.35 + fundamentalScore * 0.25 + 
                       marketScore * 0.25 + sentimentScore * 0.15)
    
    const confidence = Math.min(0.92, 0.65 + Math.abs(prediction) * 0.25)
    
    let direction: 'up' | 'down' | 'neutral' = 'neutral'
    if (prediction > 0.015) direction = 'up'
    else if (prediction < -0.015) direction = 'down'
    
    return {
      model: 'XGBoost',
      prediction,
      confidence,
      direction,
      probability: {
        up: prediction > 0 ? 0.5 + prediction * 12 : 0.5 - Math.abs(prediction) * 12,
        down: prediction < 0 ? 0.5 + Math.abs(prediction) * 12 : 0.5 - prediction * 12,
        neutral: 1 - Math.abs(prediction) * 24
      },
      timeframe: '1D',
      features_importance: {
        'technical.macd': 0.18,
        'market.market_return': 0.14,
        'technical.rsi': 0.12,
        'fundamental.roe': 0.09,
        'sentiment.analyst_rating': 0.07
      }
    }
  }

  /**
   * LSTM模型预测
   */
  static lstmPredict(features: MLFeatures): MLPrediction {
    // 模拟LSTM时序预测逻辑
    const technicalScore = this.calculateTechnicalScore(features.technical) * 0.9
    const trendScore = features.technical.price_change * 2
    const volatilityScore = features.technical.volatility * -1
    
    const prediction = (technicalScore * 0.6 + trendScore * 0.3 + volatilityScore * 0.1)
    
    const confidence = Math.min(0.88, 0.55 + Math.abs(prediction) * 0.35)
    
    let direction: 'up' | 'down' | 'neutral' = 'neutral'
    if (prediction > 0.01) direction = 'up'
    else if (prediction < -0.01) direction = 'down'
    
    return {
      model: 'LSTM',
      prediction,
      confidence,
      direction,
      probability: {
        up: prediction > 0 ? 0.5 + prediction * 15 : 0.5 - Math.abs(prediction) * 15,
        down: prediction < 0 ? 0.5 + Math.abs(prediction) * 15 : 0.5 - prediction * 15,
        neutral: 1 - Math.abs(prediction) * 30
      },
      timeframe: '1D',
      features_importance: {
        'technical.price_change': 0.25,
        'technical.volatility': 0.20,
        'technical.sma20': 0.15,
        'technical.volume_sma': 0.10,
        'technical.bb_middle': 0.08
      }
    }
  }

  /**
   * 集成预测 - 组合多个模型的预测结果
   */
  static ensemblePredict(features: MLFeatures): EnsemblePrediction {
    const rfPrediction = this.randomForestPredict(features)
    const xgbPrediction = this.xgboostPredict(features)
    const lstmPrediction = this.lstmPredict(features)
    
    const predictions = [rfPrediction, xgbPrediction, lstmPrediction]
    
    // 加权平均 (基于模型历史表现)
    const weights = [0.35, 0.40, 0.25] // RF, XGB, LSTM
    const finalPrediction = predictions.reduce((sum, pred, i) => 
      sum + pred.prediction * weights[i], 0)
    
    const avgConfidence = predictions.reduce((sum, pred, i) => 
      sum + pred.confidence * weights[i], 0)
    
    // 计算一致性
    const directions = predictions.map(p => p.direction)
    const consensusStrength = this.calculateConsensus(directions)
    
    let finalDirection: 'up' | 'down' | 'neutral' = 'neutral'
    if (finalPrediction > 0.015) finalDirection = 'up'
    else if (finalPrediction < -0.015) finalDirection = 'down'
    
    // 风险评估
    const riskLevel = this.assessRiskLevel(predictions, consensusStrength)
    
    return {
      final_prediction: finalPrediction,
      confidence: avgConfidence,
      direction: finalDirection,
      model_predictions: predictions,
      consensus_strength: consensusStrength,
      risk_level: riskLevel
    }
  }

  // 辅助计算方法
  private static calculateSMA(prices: number[]): number {
    return prices.reduce((sum, price) => sum + price, 0) / prices.length
  }

  private static calculateRSI(prices: number[], period: number): number {
    if (prices.length < period + 1) return 50
    
    let gains = 0
    let losses = 0
    
    for (let i = 1; i <= period; i++) {
      const change = prices[prices.length - i] - prices[prices.length - i - 1]
      if (change > 0) gains += change
      else losses += Math.abs(change)
    }
    
    const avgGain = gains / period
    const avgLoss = losses / period
    
    if (avgLoss === 0) return 100
    const rs = avgGain / avgLoss
    return 100 - (100 / (1 + rs))
  }

  private static calculateMACD(prices: number[]): { macd: number, signal: number, histogram: number } {
    const ema12 = this.calculateEMA(prices, 12)
    const ema26 = this.calculateEMA(prices, 26)
    const macd = ema12 - ema26
    const signal = macd * 0.9 // 简化计算
    const histogram = macd - signal
    
    return { macd, signal, histogram }
  }

  private static calculateEMA(prices: number[], period: number): number {
    const multiplier = 2 / (period + 1)
    let ema = prices[0]
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
    }
    
    return ema
  }

  private static calculateBollingerBands(prices: number[], period: number, stdDev: number): 
    { upper: number, middle: number, lower: number } {
    const sma = this.calculateSMA(prices.slice(-period))
    const variance = prices.slice(-period).reduce((sum, price) => 
      sum + Math.pow(price - sma, 2), 0) / period
    const std = Math.sqrt(variance)
    
    return {
      upper: sma + (std * stdDev),
      middle: sma,
      lower: sma - (std * stdDev)
    }
  }

  private static calculateVolatility(prices: number[]): number {
    const returns = []
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
    }
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length
    
    return Math.sqrt(variance)
  }

  private static calculateTechnicalScore(technical: MLFeatures['technical']): number {
    let score = 0
    
    // RSI评分
    if (technical.rsi < 30) score += 0.02
    else if (technical.rsi > 70) score -= 0.02
    
    // MACD评分
    if (technical.macd > technical.macdSignal) score += 0.01
    else score -= 0.01
    
    // 趋势评分
    if (technical.sma5 > technical.sma10 && technical.sma10 > technical.sma20) score += 0.015
    else if (technical.sma5 < technical.sma10 && technical.sma10 < technical.sma20) score -= 0.015
    
    return score
  }

  private static calculateFundamentalScore(fundamental: MLFeatures['fundamental']): number {
    let score = 0
    
    // PE评分
    if (fundamental.pe_ratio < 15) score += 0.01
    else if (fundamental.pe_ratio > 30) score -= 0.01
    
    // ROE评分
    if (fundamental.roe > 0.15) score += 0.01
    else if (fundamental.roe < 0.05) score -= 0.01
    
    // 增长评分
    if (fundamental.revenue_growth > 0.1) score += 0.005
    else if (fundamental.revenue_growth < 0) score -= 0.005
    
    return score
  }

  private static calculateMarketScore(market: MLFeatures['market']): number {
    let score = market.market_return * 0.5 + market.sector_return * 0.3
    
    // VIX评分
    if (market.vix > 25) score -= 0.01
    else if (market.vix < 15) score += 0.005
    
    return score
  }

  private static calculateSentimentScore(sentiment: MLFeatures['sentiment']): number {
    return (sentiment.news_sentiment * 0.4 + sentiment.social_sentiment * 0.3 + 
            (sentiment.analyst_rating - 2.5) * 0.01 + sentiment.insider_trading * 0.3) * 0.01
  }

  private static calculateConsensus(directions: string[]): number {
    const counts = directions.reduce((acc, dir) => {
      acc[dir] = (acc[dir] || 0) + 1
      return acc
    }, {} as { [key: string]: number })
    
    const maxCount = Math.max(...Object.values(counts))
    return maxCount / directions.length
  }

  private static assessRiskLevel(predictions: MLPrediction[], consensus: number): 'low' | 'medium' | 'high' {
    const avgConfidence = predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length
    
    if (consensus > 0.8 && avgConfidence > 0.8) return 'low'
    if (consensus > 0.6 && avgConfidence > 0.6) return 'medium'
    return 'high'
  }
}

export default MLPredictionEngine
