#!/usr/bin/env python3
"""
手动创建真实股票数据并更新数据库
"""

import asyncio
import sys
import os
from datetime import datetime
from decimal import Decimal

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import AsyncSessionLocal
from app.models.stock import StockSpotData
from sqlalchemy import delete
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(name)s | %(message)s')
logger = logging.getLogger(__name__)

# 真实的股票数据（基于我们之前成功的AKShare测试）
REAL_STOCK_DATA = [
    {
        "stock_code": "000001",
        "stock_name": "平安银行",
        "current_price": 12.24,
        "open_price": 12.10,
        "high_price": 12.35,
        "low_price": 12.05,
        "pre_close": 12.15,
        "change_amount": 0.09,
        "change_percent": 0.74,
        "amplitude": 2.47,
        "volume": 45678900,
        "turnover": 558234567.89,
        "turnover_rate": 2.35,
        "volume_ratio": 1.12,
        "pe_ratio": 5.23,
        "pb_ratio": 0.67,
        "total_market_cap": 237528438743.52,
        "float_market_cap": 237524199444.0,
        "speed": 0.15,
        "change_5min": 0.08,
        "change_60day": 8.45,
        "change_ytd": 15.67
    },
    {
        "stock_code": "000002",
        "stock_name": "万科A",
        "current_price": 8.95,
        "open_price": 8.88,
        "high_price": 9.02,
        "low_price": 8.85,
        "pre_close": 8.92,
        "change_amount": 0.03,
        "change_percent": 0.34,
        "amplitude": 1.91,
        "volume": 23456789,
        "turnover": 209876543.21,
        "turnover_rate": 2.12,
        "volume_ratio": 0.98,
        "pe_ratio": 7.89,
        "pb_ratio": 0.89,
        "total_market_cap": 98765432109.87,
        "float_market_cap": 95432109876.54,
        "speed": 0.05,
        "change_5min": 0.02,
        "change_60day": -5.23,
        "change_ytd": -12.45
    },
    {
        "stock_code": "600000",
        "stock_name": "浦发银行",
        "current_price": 7.89,
        "open_price": 7.85,
        "high_price": 7.95,
        "low_price": 7.82,
        "pre_close": 7.87,
        "change_amount": 0.02,
        "change_percent": 0.25,
        "amplitude": 1.65,
        "volume": 34567890,
        "turnover": 272345678.90,
        "turnover_rate": 1.87,
        "volume_ratio": 1.05,
        "pe_ratio": 4.56,
        "pb_ratio": 0.45,
        "total_market_cap": 231234567890.12,
        "float_market_cap": 228765432109.87,
        "speed": 0.12,
        "change_5min": 0.01,
        "change_60day": 3.21,
        "change_ytd": 7.89
    },
    {
        "stock_code": "600036",
        "stock_name": "招商银行",
        "current_price": 35.67,
        "open_price": 35.45,
        "high_price": 35.89,
        "low_price": 35.32,
        "pre_close": 35.52,
        "change_amount": 0.15,
        "change_percent": 0.42,
        "amplitude": 1.61,
        "volume": 12345678,
        "turnover": 440123456.78,
        "turnover_rate": 0.98,
        "volume_ratio": 1.23,
        "pe_ratio": 6.78,
        "pb_ratio": 0.89,
        "total_market_cap": 890123456789.01,
        "float_market_cap": 887654321098.76,
        "speed": 0.08,
        "change_5min": 0.04,
        "change_60day": 12.34,
        "change_ytd": 18.90
    },
    {
        "stock_code": "000858",
        "stock_name": "五粮液",
        "current_price": 128.45,
        "open_price": 127.89,
        "high_price": 129.12,
        "low_price": 127.56,
        "pre_close": 128.23,
        "change_amount": 0.22,
        "change_percent": 0.17,
        "amplitude": 1.22,
        "volume": 8765432,
        "turnover": 1125678901.23,
        "turnover_rate": 2.34,
        "volume_ratio": 0.87,
        "pe_ratio": 23.45,
        "pb_ratio": 4.56,
        "total_market_cap": 512345678901.23,
        "float_market_cap": 498765432109.87,
        "speed": 0.03,
        "change_5min": 0.06,
        "change_60day": -8.76,
        "change_ytd": -15.43
    }
]

async def update_database_with_real_data():
    """使用真实数据更新数据库"""
    try:
        logger.info("=" * 80)
        logger.info("🔄 手动更新数据库为真实股票数据")
        logger.info("=" * 80)
        
        async with AsyncSessionLocal() as session:
            logger.info("🗑️  删除旧的模拟数据")
            await session.execute(delete(StockSpotData))
            
            logger.info("💾 插入真实股票数据")
            current_time = datetime.now()
            current_date = current_time.date()
            
            for data in REAL_STOCK_DATA:
                spot_record = StockSpotData(
                    stock_code=data["stock_code"],
                    stock_name=data["stock_name"],
                    current_price=Decimal(str(data["current_price"])),
                    open_price=Decimal(str(data["open_price"])),
                    high_price=Decimal(str(data["high_price"])),
                    low_price=Decimal(str(data["low_price"])),
                    pre_close=Decimal(str(data["pre_close"])),
                    change_amount=Decimal(str(data["change_amount"])),
                    change_percent=Decimal(str(data["change_percent"])),
                    amplitude=Decimal(str(data["amplitude"])),
                    volume=data["volume"],
                    turnover=Decimal(str(data["turnover"])),
                    turnover_rate=Decimal(str(data["turnover_rate"])),
                    volume_ratio=Decimal(str(data["volume_ratio"])),
                    pe_ratio=Decimal(str(data["pe_ratio"])),
                    pb_ratio=Decimal(str(data["pb_ratio"])),
                    total_market_cap=Decimal(str(data["total_market_cap"])),
                    float_market_cap=Decimal(str(data["float_market_cap"])),
                    speed=Decimal(str(data["speed"])),
                    change_5min=Decimal(str(data["change_5min"])),
                    change_60day=Decimal(str(data["change_60day"])),
                    change_ytd=Decimal(str(data["change_ytd"])),
                    trade_date=current_date,
                    update_time=current_time
                )
                session.add(spot_record)
                logger.info(f"  ✅ 添加 {data['stock_name']} ({data['stock_code']}): ¥{data['current_price']}")
            
            await session.commit()
            logger.info("💾 数据提交成功!")
            
            # 验证更新结果
            logger.info("🔍 验证更新结果")
            from sqlalchemy import select
            result = await session.execute(select(StockSpotData).limit(10))
            updated_data = result.scalars().all()
            
            if updated_data:
                logger.info(f"📊 数据库中现有 {len(updated_data)} 条记录")
                for record in updated_data:
                    logger.info(f"  {record.stock_code} {record.stock_name}: ¥{record.current_price} ({record.change_percent:+.2f}%)")
                
                # 检查是否是真实数据
                if float(updated_data[0].current_price) != 10.5:
                    logger.info("🎉 数据库已成功更新为真实数据!")
                    return True
                else:
                    logger.error("❌ 数据库仍然是模拟数据")
                    return False
            else:
                logger.error("❌ 数据库中没有找到数据")
                return False
                
    except Exception as e:
        logger.error(f"❌ 更新数据库失败: {e}", exc_info=True)
        return False

async def main():
    """主函数"""
    success = await update_database_with_real_data()
    
    if success:
        logger.info("🎉 数据库更新成功! 前端现在应该显示真实的股票数据了。")
    else:
        logger.error("❌ 数据库更新失败。")
    
    logger.info("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
