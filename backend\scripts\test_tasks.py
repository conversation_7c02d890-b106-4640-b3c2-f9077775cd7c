#!/usr/bin/env python3
"""
任务系统测试脚本
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from app.core.logging import setup_logging, get_logger
from app.core.celery_app import celery_app
from app.tasks import data_tasks, analysis_tasks, notification_tasks

setup_logging()
logger = get_logger(__name__)


def test_celery_connection():
    """测试Celery连接"""
    logger.info("=== 测试Celery连接 ===")
    
    try:
        # 检查Celery状态
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            logger.info("✅ Celery连接成功")
            for worker, stat in stats.items():
                logger.info(f"Worker: {worker}")
                logger.info(f"  - 进程数: {stat.get('pool', {}).get('max-concurrency', 'N/A')}")
                logger.info(f"  - 任务总数: {stat.get('total', 'N/A')}")
            return True
        else:
            logger.error("❌ 无法连接到Celery worker")
            return False
            
    except Exception as e:
        logger.error(f"❌ Celery连接测试失败: {e}")
        return False


def test_simple_task():
    """测试简单任务"""
    logger.info("=== 测试简单任务 ===")
    
    try:
        # 发送一个简单的测试任务
        logger.info("发送测试任务...")
        
        # 测试数据同步任务
        task = data_tasks.sync_stock_list_task.delay()
        logger.info(f"任务ID: {task.id}")
        
        # 等待任务完成（最多等待30秒）
        logger.info("等待任务完成...")
        result = task.get(timeout=30)
        
        logger.info(f"✅ 任务完成: {result}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 简单任务测试失败: {e}")
        return False


def test_task_with_params():
    """测试带参数的任务"""
    logger.info("=== 测试带参数的任务 ===")
    
    try:
        # 测试技术指标计算任务
        stock_code = "000001"
        indicators = ["MA", "RSI"]
        
        logger.info(f"发送技术指标计算任务: {stock_code}, {indicators}")
        task = analysis_tasks.calculate_technical_indicators_task.delay(stock_code, indicators)
        
        logger.info(f"任务ID: {task.id}")
        
        # 等待任务完成
        result = task.get(timeout=20)
        
        logger.info(f"✅ 技术指标计算完成: {result['status']}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 带参数任务测试失败: {e}")
        return False


def test_notification_task():
    """测试通知任务"""
    logger.info("=== 测试通知任务 ===")
    
    try:
        # 测试预警通知任务
        alert_data = {
            "alert_id": "test_001",
            "stock_code": "000001",
            "type": "price_alert",
            "title": "价格预警",
            "message": "股票价格突破阻力位"
        }
        
        logger.info("发送预警通知任务...")
        task = notification_tasks.send_alert_notification_task.delay(alert_data)
        
        logger.info(f"任务ID: {task.id}")
        
        # 等待任务完成
        result = task.get(timeout=15)
        
        logger.info(f"✅ 通知发送完成: {result['status']}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 通知任务测试失败: {e}")
        return False


def test_task_status():
    """测试任务状态查询"""
    logger.info("=== 测试任务状态查询 ===")
    
    try:
        # 发送一个任务
        task = analysis_tasks.market_analysis_task.delay("sector")
        task_id = task.id
        
        logger.info(f"任务ID: {task_id}")
        
        # 查询任务状态
        logger.info("查询任务状态...")
        
        # 等待一会儿让任务开始执行
        time.sleep(2)
        
        # 检查任务状态
        result = celery_app.AsyncResult(task_id)
        logger.info(f"任务状态: {result.status}")
        
        if result.ready():
            logger.info(f"任务结果: {result.result}")
        
        logger.info("✅ 任务状态查询成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 任务状态查询失败: {e}")
        return False


def test_scheduled_tasks():
    """测试定时任务配置"""
    logger.info("=== 测试定时任务配置 ===")
    
    try:
        # 获取定时任务配置
        beat_schedule = celery_app.conf.beat_schedule
        
        logger.info("已配置的定时任务:")
        for task_name, config in beat_schedule.items():
            logger.info(f"  - {task_name}: {config['task']}")
            logger.info(f"    调度: {config['schedule']}")
        
        logger.info("✅ 定时任务配置正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ 定时任务配置测试失败: {e}")
        return False


def test_task_queues():
    """测试任务队列"""
    logger.info("=== 测试任务队列 ===")
    
    try:
        # 向不同队列发送任务
        queues_to_test = ["data", "analysis", "notifications"]
        
        for queue in queues_to_test:
            logger.info(f"测试队列: {queue}")
            
            if queue == "data":
                task = data_tasks.sync_realtime_data_task.apply_async(
                    args=[["000001"]], 
                    queue=queue
                )
            elif queue == "analysis":
                task = analysis_tasks.ai_prediction_task.apply_async(
                    args=["000001", 3], 
                    queue=queue
                )
            elif queue == "notifications":
                task = notification_tasks.send_system_notification_task.apply_async(
                    args=["test", "测试消息", ["admin"]], 
                    queue=queue
                )
            
            logger.info(f"  任务ID: {task.id}")
            
            # 等待任务完成
            try:
                result = task.get(timeout=15)
                logger.info(f"  ✅ 队列 {queue} 测试成功")
            except Exception as e:
                logger.warning(f"  ⚠️ 队列 {queue} 测试超时或失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 任务队列测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始Celery任务系统测试")
    
    tests = [
        ("Celery连接", test_celery_connection),
        ("简单任务", test_simple_task),
        ("带参数任务", test_task_with_params),
        ("通知任务", test_notification_task),
        ("任务状态查询", test_task_status),
        ("定时任务配置", test_scheduled_tasks),
        ("任务队列", test_task_queues),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"测试失败: {test_name}")
        except Exception as e:
            logger.error(f"测试异常: {test_name} - {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("✅ 所有测试通过！")
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
