"""
专业级股票数据库管理API
提供完整的数据库管理功能，类似专业数据库管理工具
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text, and_, or_, desc, asc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any, Union
from datetime import date, datetime
import json
import pandas as pd
import io
from loguru import logger

from app.core.database import get_db
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 创建专业数据库连接
professional_engine = create_async_engine(
    "sqlite+aiosqlite:///./professional_stock_data.db",
    echo=False
)

ProfessionalAsyncSessionLocal = sessionmaker(
    professional_engine, class_=AsyncSession, expire_on_commit=False
)

async def get_professional_db():
    """获取专业数据库会话"""
    async with ProfessionalAsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

from app.models.professional_stock_db import (
    StockBasicInfo, DataUpdateLog, StockKlineData, StockRealtimeData,
    StockMoneyFlow, NorthboundCapital, SectorInfo, StockSectorMapping,
    StockFinancialData, CompanyInfo, CustomIndicator, StockPattern, BacktestStrategy
)

router = APIRouter()

# ================================
# 1. 数据库概览和统计
# ================================

@router.get("/overview")
async def get_database_overview(db: AsyncSession = Depends(get_professional_db)):
    """获取数据库概览统计"""
    try:
        # 获取各表的记录数
        tables_info = []
        
        # 定义表信息
        table_configs = [
            {"model": StockBasicInfo, "name": "股票基础信息", "category": "基础数据"},
            {"model": StockKlineData, "name": "K线数据", "category": "交易数据"},
            {"model": StockRealtimeData, "name": "实时行情", "category": "交易数据"},
            {"model": StockMoneyFlow, "name": "资金流向", "category": "情绪数据"},
            {"model": NorthboundCapital, "name": "北向资金", "category": "情绪数据"},
            {"model": SectorInfo, "name": "板块信息", "category": "板块数据"},
            {"model": StockSectorMapping, "name": "股票板块关联", "category": "板块数据"},
            {"model": StockFinancialData, "name": "财务数据", "category": "基本面数据"},
            {"model": CompanyInfo, "name": "公司信息", "category": "基本面数据"},
            {"model": CustomIndicator, "name": "自定义指标", "category": "自定义功能"},
            {"model": StockPattern, "name": "股票形态", "category": "自定义功能"},
            {"model": BacktestStrategy, "name": "回测策略", "category": "自定义功能"},
            {"model": DataUpdateLog, "name": "更新日志", "category": "系统数据"},
        ]
        
        for config in table_configs:
            result = await db.execute(select(func.count()).select_from(config["model"]))
            count = result.scalar()
            
            # 获取最新更新时间
            if hasattr(config["model"], 'updated_at'):
                latest_result = await db.execute(
                    select(func.max(config["model"].updated_at)).select_from(config["model"])
                )
                latest_update = latest_result.scalar()
            elif hasattr(config["model"], 'created_at'):
                latest_result = await db.execute(
                    select(func.max(config["model"].created_at)).select_from(config["model"])
                )
                latest_update = latest_result.scalar()
            else:
                latest_update = None
            
            tables_info.append({
                "table_name": config["model"].__tablename__,
                "display_name": config["name"],
                "category": config["category"],
                "record_count": count,
                "latest_update": latest_update.isoformat() if latest_update else None
            })
        
        # 按分类统计
        category_stats = {}
        for table in tables_info:
            category = table["category"]
            if category not in category_stats:
                category_stats[category] = {"tables": 0, "records": 0}
            category_stats[category]["tables"] += 1
            category_stats[category]["records"] += table["record_count"]
        
        # 获取活跃股票数
        active_stocks_result = await db.execute(
            select(func.count()).select_from(StockBasicInfo).where(StockBasicInfo.status == "active")
        )
        active_stocks = active_stocks_result.scalar()
        
        # 获取最近更新日志
        recent_logs_result = await db.execute(
            select(DataUpdateLog)
            .order_by(desc(DataUpdateLog.update_time))
            .limit(10)
        )
        recent_logs = recent_logs_result.scalars().all()
        
        return {
            "database_info": {
                "total_tables": len(tables_info),
                "total_records": sum(table["record_count"] for table in tables_info),
                "active_stocks": active_stocks,
                "last_update": max(
                    (table["latest_update"] for table in tables_info if table["latest_update"]),
                    default=None
                )
            },
            "tables_info": tables_info,
            "category_stats": category_stats,
            "recent_updates": [
                {
                    "id": log.id,
                    "data_type": log.data_type,
                    "stock_code": log.stock_code,
                    "status": log.status,
                    "records_count": log.records_count,
                    "update_time": log.update_time.isoformat(),
                    "duration": log.duration
                }
                for log in recent_logs
            ]
        }
        
    except Exception as e:
        logger.error(f"获取数据库概览失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据库概览失败")

# ================================
# 2. 表结构管理
# ================================

@router.get("/tables")
async def get_tables_info():
    """获取所有表的结构信息"""
    try:
        tables_structure = []
        
        # 获取所有模型类
        models = [
            StockBasicInfo, DataUpdateLog, StockKlineData, StockRealtimeData,
            StockMoneyFlow, NorthboundCapital, SectorInfo, StockSectorMapping,
            StockFinancialData, CompanyInfo, CustomIndicator, StockPattern, BacktestStrategy
        ]
        
        for model in models:
            table_info = {
                "table_name": model.__tablename__,
                "model_name": model.__name__,
                "columns": [],
                "indexes": [],
                "relationships": []
            }
            
            # 获取列信息
            for column in model.__table__.columns:
                column_info = {
                    "name": column.name,
                    "type": str(column.type),
                    "nullable": column.nullable,
                    "primary_key": column.primary_key,
                    "foreign_key": bool(column.foreign_keys),
                    "unique": column.unique,
                    "default": str(column.default) if column.default else None,
                    "comment": column.comment
                }
                table_info["columns"].append(column_info)
            
            # 获取索引信息
            for index in model.__table__.indexes:
                index_info = {
                    "name": index.name,
                    "columns": [col.name for col in index.columns],
                    "unique": index.unique
                }
                table_info["indexes"].append(index_info)
            
            tables_structure.append(table_info)
        
        return {
            "tables": tables_structure,
            "total_tables": len(tables_structure)
        }
        
    except Exception as e:
        logger.error(f"获取表结构信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取表结构信息失败")

@router.get("/tables/{table_name}/structure")
async def get_table_structure(table_name: str):
    """获取指定表的详细结构"""
    try:
        # 根据表名找到对应的模型
        model_map = {
            "stock_basic_info": StockBasicInfo,
            "data_update_logs": DataUpdateLog,
            "stock_kline_data": StockKlineData,
            "stock_realtime_data": StockRealtimeData,
            "stock_money_flow": StockMoneyFlow,
            "northbound_capital": NorthboundCapital,
            "sector_info": SectorInfo,
            "stock_sector_mapping": StockSectorMapping,
            "stock_financial_data": StockFinancialData,
            "company_info": CompanyInfo,
            "custom_indicators": CustomIndicator,
            "stock_patterns": StockPattern,
            "backtest_strategies": BacktestStrategy,
        }
        
        if table_name not in model_map:
            raise HTTPException(status_code=404, detail="表不存在")
        
        model = model_map[table_name]
        
        # 详细的表结构信息
        structure = {
            "table_name": model.__tablename__,
            "model_name": model.__name__,
            "columns": [],
            "indexes": [],
            "foreign_keys": [],
            "constraints": []
        }
        
        # 详细列信息
        for column in model.__table__.columns:
            column_info = {
                "name": column.name,
                "type": str(column.type),
                "python_type": column.type.python_type.__name__ if hasattr(column.type, 'python_type') else None,
                "nullable": column.nullable,
                "primary_key": column.primary_key,
                "autoincrement": column.autoincrement,
                "unique": column.unique,
                "index": column.index,
                "default": str(column.default) if column.default else None,
                "comment": column.comment,
                "foreign_keys": [
                    {
                        "column": fk.column.name,
                        "table": fk.column.table.name
                    }
                    for fk in column.foreign_keys
                ]
            }
            structure["columns"].append(column_info)
        
        # 索引信息
        for index in model.__table__.indexes:
            index_info = {
                "name": index.name,
                "columns": [col.name for col in index.columns],
                "unique": index.unique,
                "expressions": [str(expr) for expr in index.expressions]
            }
            structure["indexes"].append(index_info)
        
        return structure
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        raise HTTPException(status_code=500, detail="获取表结构失败")

# ================================
# 3. 数据浏览和查询
# ================================

@router.get("/tables/{table_name}/data")
async def browse_table_data(
    table_name: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=1000, description="每页记录数"),
    sort_by: Optional[str] = Query(None, description="排序字段"),
    sort_order: str = Query("asc", regex="^(asc|desc)$", description="排序方向"),
    filters: Optional[str] = Query(None, description="过滤条件(JSON格式)"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_professional_db)
):
    """浏览表数据，支持分页、排序、过滤、搜索"""
    try:
        # 模型映射
        model_map = {
            "stock_basic_info": StockBasicInfo,
            "data_update_logs": DataUpdateLog,
            "stock_kline_data": StockKlineData,
            "stock_realtime_data": StockRealtimeData,
            "stock_money_flow": StockMoneyFlow,
            "northbound_capital": NorthboundCapital,
            "sector_info": SectorInfo,
            "stock_sector_mapping": StockSectorMapping,
            "stock_financial_data": StockFinancialData,
            "company_info": CompanyInfo,
            "custom_indicators": CustomIndicator,
            "stock_patterns": StockPattern,
            "backtest_strategies": BacktestStrategy,
        }
        
        if table_name not in model_map:
            raise HTTPException(status_code=404, detail="表不存在")
        
        model = model_map[table_name]
        
        # 构建查询
        query = select(model)
        
        # 应用搜索
        if search:
            # 根据表的特点应用搜索条件
            if table_name == "stock_basic_info":
                query = query.where(
                    or_(
                        model.stock_code.contains(search),
                        model.stock_name.contains(search),
                        model.industry.contains(search)
                    )
                )
            elif table_name == "company_info":
                query = query.where(
                    or_(
                        model.stock_code.contains(search),
                        model.company_name.contains(search)
                    )
                )
        
        # 应用过滤条件
        if filters:
            try:
                filter_conditions = json.loads(filters)
                for field, value in filter_conditions.items():
                    if hasattr(model, field):
                        column = getattr(model, field)
                        if isinstance(value, dict):
                            # 支持范围查询等复杂条件
                            if "gte" in value:
                                query = query.where(column >= value["gte"])
                            if "lte" in value:
                                query = query.where(column <= value["lte"])
                            if "eq" in value:
                                query = query.where(column == value["eq"])
                            if "in" in value:
                                query = query.where(column.in_(value["in"]))
                        else:
                            query = query.where(column == value)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="过滤条件格式错误")
        
        # 应用排序
        if sort_by and hasattr(model, sort_by):
            column = getattr(model, sort_by)
            if sort_order == "desc":
                query = query.order_by(desc(column))
            else:
                query = query.order_by(asc(column))
        else:
            # 默认排序
            if hasattr(model, 'id'):
                query = query.order_by(desc(model.id))
        
        # 获取总记录数
        count_query = select(func.count()).select_from(model)
        if search or filters:
            # 应用相同的过滤条件到计数查询
            count_query = query.with_only_columns(func.count()).order_by(None)
        
        total_result = await db.execute(count_query)
        total_records = total_result.scalar()
        
        # 应用分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        # 执行查询
        result = await db.execute(query)
        records = result.scalars().all()
        
        # 转换为字典格式
        data = []
        for record in records:
            record_dict = {}
            for column in model.__table__.columns:
                value = getattr(record, column.name)
                if isinstance(value, (date, datetime)):
                    value = value.isoformat()
                elif value is None:
                    value = None
                else:
                    value = str(value)
                record_dict[column.name] = value
            data.append(record_dict)
        
        return {
            "table_name": table_name,
            "data": data,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_records": total_records,
                "total_pages": (total_records + page_size - 1) // page_size
            },
            "query_info": {
                "sort_by": sort_by,
                "sort_order": sort_order,
                "filters": filters,
                "search": search
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"浏览表数据失败: {e}")
        raise HTTPException(status_code=500, detail="浏览表数据失败")

# ================================
# 4. 数据编辑和管理
# ================================

@router.get("/tables/{table_name}/record/{record_id}")
async def get_record_detail(
    table_name: str,
    record_id: int,
    db: AsyncSession = Depends(get_professional_db)
):
    """获取单条记录的详细信息"""
    try:
        model_map = {
            "stock_basic_info": StockBasicInfo,
            "data_update_logs": DataUpdateLog,
            "stock_kline_data": StockKlineData,
            "stock_realtime_data": StockRealtimeData,
            "stock_money_flow": StockMoneyFlow,
            "northbound_capital": NorthboundCapital,
            "sector_info": SectorInfo,
            "stock_sector_mapping": StockSectorMapping,
            "stock_financial_data": StockFinancialData,
            "company_info": CompanyInfo,
            "custom_indicators": CustomIndicator,
            "stock_patterns": StockPattern,
            "backtest_strategies": BacktestStrategy,
        }

        if table_name not in model_map:
            raise HTTPException(status_code=404, detail="表不存在")

        model = model_map[table_name]

        # 查询记录
        result = await db.execute(select(model).where(model.id == record_id))
        record = result.scalar_one_or_none()

        if not record:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 转换为字典格式
        record_dict = {}
        for column in model.__table__.columns:
            value = getattr(record, column.name)
            if isinstance(value, (date, datetime)):
                value = value.isoformat()
            record_dict[column.name] = value

        # 获取字段元数据
        fields_metadata = []
        for column in model.__table__.columns:
            field_info = {
                "name": column.name,
                "type": str(column.type),
                "nullable": column.nullable,
                "primary_key": column.primary_key,
                "foreign_key": bool(column.foreign_keys),
                "comment": column.comment,
                "editable": not column.primary_key and column.name not in ['created_at', 'updated_at']
            }
            fields_metadata.append(field_info)

        return {
            "table_name": table_name,
            "record_id": record_id,
            "data": record_dict,
            "fields_metadata": fields_metadata
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取记录详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取记录详情失败")

@router.put("/tables/{table_name}/record/{record_id}")
async def update_record(
    table_name: str,
    record_id: int,
    update_data: Dict[str, Any],
    db: AsyncSession = Depends(get_professional_db)
):
    """更新单条记录"""
    try:
        model_map = {
            "stock_basic_info": StockBasicInfo,
            "data_update_logs": DataUpdateLog,
            "stock_kline_data": StockKlineData,
            "stock_realtime_data": StockRealtimeData,
            "stock_money_flow": StockMoneyFlow,
            "northbound_capital": NorthboundCapital,
            "sector_info": SectorInfo,
            "stock_sector_mapping": StockSectorMapping,
            "stock_financial_data": StockFinancialData,
            "company_info": CompanyInfo,
            "custom_indicators": CustomIndicator,
            "stock_patterns": StockPattern,
            "backtest_strategies": BacktestStrategy,
        }

        if table_name not in model_map:
            raise HTTPException(status_code=404, detail="表不存在")

        model = model_map[table_name]

        # 查询记录
        result = await db.execute(select(model).where(model.id == record_id))
        record = result.scalar_one_or_none()

        if not record:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 更新字段
        updated_fields = []
        for field, value in update_data.items():
            if hasattr(record, field) and field not in ['id', 'created_at']:
                # 类型转换
                column = getattr(model.__table__.c, field)
                if value is not None:
                    if 'date' in str(column.type).lower():
                        if isinstance(value, str):
                            value = datetime.fromisoformat(value.replace('Z', '+00:00')).date()
                    elif 'datetime' in str(column.type).lower():
                        if isinstance(value, str):
                            value = datetime.fromisoformat(value.replace('Z', '+00:00'))

                setattr(record, field, value)
                updated_fields.append(field)

        # 更新 updated_at 字段
        if hasattr(record, 'updated_at'):
            record.updated_at = datetime.now()

        await db.commit()
        await db.refresh(record)

        # 记录操作日志
        log_entry = DataUpdateLog(
            data_type=f"manual_update_{table_name}",
            stock_code=getattr(record, 'stock_code', None),
            status="success",
            records_count=1,
            source="manual_edit",
            duration=0.0
        )
        db.add(log_entry)
        await db.commit()

        return {
            "message": "记录更新成功",
            "table_name": table_name,
            "record_id": record_id,
            "updated_fields": updated_fields
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新记录失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新记录失败: {str(e)}")

@router.delete("/tables/{table_name}/record/{record_id}")
async def delete_record(
    table_name: str,
    record_id: int,
    db: AsyncSession = Depends(get_professional_db)
):
    """删除单条记录"""
    try:
        model_map = {
            "stock_basic_info": StockBasicInfo,
            "data_update_logs": DataUpdateLog,
            "stock_kline_data": StockKlineData,
            "stock_realtime_data": StockRealtimeData,
            "stock_money_flow": StockMoneyFlow,
            "northbound_capital": NorthboundCapital,
            "sector_info": SectorInfo,
            "stock_sector_mapping": StockSectorMapping,
            "stock_financial_data": StockFinancialData,
            "company_info": CompanyInfo,
            "custom_indicators": CustomIndicator,
            "stock_patterns": StockPattern,
            "backtest_strategies": BacktestStrategy,
        }

        if table_name not in model_map:
            raise HTTPException(status_code=404, detail="表不存在")

        model = model_map[table_name]

        # 查询记录
        result = await db.execute(select(model).where(model.id == record_id))
        record = result.scalar_one_or_none()

        if not record:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 删除记录
        await db.delete(record)
        await db.commit()

        # 记录操作日志
        log_entry = DataUpdateLog(
            data_type=f"manual_delete_{table_name}",
            stock_code=getattr(record, 'stock_code', None),
            status="success",
            records_count=1,
            source="manual_delete",
            duration=0.0
        )
        db.add(log_entry)
        await db.commit()

        return {
            "message": "记录删除成功",
            "table_name": table_name,
            "record_id": record_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除记录失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除记录失败: {str(e)}")

# ================================
# 5. 批量操作
# ================================

@router.post("/tables/{table_name}/batch-delete")
async def batch_delete_records(
    table_name: str,
    record_ids: List[int],
    db: AsyncSession = Depends(get_professional_db)
):
    """批量删除记录"""
    try:
        model_map = {
            "stock_basic_info": StockBasicInfo,
            "data_update_logs": DataUpdateLog,
            "stock_kline_data": StockKlineData,
            "stock_realtime_data": StockRealtimeData,
            "stock_money_flow": StockMoneyFlow,
            "northbound_capital": NorthboundCapital,
            "sector_info": SectorInfo,
            "stock_sector_mapping": StockSectorMapping,
            "stock_financial_data": StockFinancialData,
            "company_info": CompanyInfo,
            "custom_indicators": CustomIndicator,
            "stock_patterns": StockPattern,
            "backtest_strategies": BacktestStrategy,
        }

        if table_name not in model_map:
            raise HTTPException(status_code=404, detail="表不存在")

        model = model_map[table_name]

        # 查询要删除的记录
        result = await db.execute(select(model).where(model.id.in_(record_ids)))
        records = result.scalars().all()

        if not records:
            raise HTTPException(status_code=404, detail="没有找到要删除的记录")

        # 批量删除
        deleted_count = 0
        for record in records:
            await db.delete(record)
            deleted_count += 1

        await db.commit()

        # 记录操作日志
        log_entry = DataUpdateLog(
            data_type=f"batch_delete_{table_name}",
            status="success",
            records_count=deleted_count,
            source="batch_delete",
            duration=0.0
        )
        db.add(log_entry)
        await db.commit()

        return {
            "message": f"成功删除 {deleted_count} 条记录",
            "table_name": table_name,
            "deleted_count": deleted_count,
            "deleted_ids": record_ids
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")
