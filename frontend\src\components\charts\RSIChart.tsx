/**
 * RSI相对强弱指标图表组件
 */

import React, { useMemo } from 'react'
import ReactECharts from 'echarts-for-react'

interface PriceData {
  timestamp: string
  close: number
}

interface RSIChartProps {
  data: PriceData[]
  height?: number
  timeFrame?: string
}

// 计算RSI
const calculateRSI = (data: number[], period: number): number[] => {
  const rsi: number[] = []
  
  if (data.length < period + 1) {
    return new Array(data.length).fill(50)
  }

  for (let i = 0; i < period; i++) {
    rsi.push(50) // 前period个值设为50
  }

  for (let i = period; i < data.length; i++) {
    let gains = 0
    let losses = 0
    
    for (let j = i - period + 1; j <= i; j++) {
      const change = data[j] - data[j - 1]
      if (change > 0) {
        gains += change
      } else {
        losses += Math.abs(change)
      }
    }
    
    const avgGain = gains / period
    const avgLoss = losses / period
    
    if (avgLoss === 0) {
      rsi.push(100)
    } else {
      const rs = avgGain / avgLoss
      rsi.push(100 - (100 / (1 + rs)))
    }
  }
  
  return rsi
}

const RSIChart: React.FC<RSIChartProps> = ({
  data,
  height = 250,
  timeFrame = '1D'
}) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null

    const closes = data.map(item => item.close)
    
    // 计算不同周期的RSI
    const rsi6 = calculateRSI(closes, 6)
    const rsi12 = calculateRSI(closes, 12)
    const rsi24 = calculateRSI(closes, 24)

    // 准备时间轴
    const timeAxis = data.map(item => {
      const date = new Date(item.timestamp)
      if (timeFrame === '1m' || timeFrame === '5m' || timeFrame === '15m' || timeFrame === '30m' || timeFrame === '1h') {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    })

    return {
      timeAxis,
      rsi6,
      rsi12,
      rsi24
    }
  }, [data, timeFrame])

  const option = useMemo(() => {
    if (!chartData) return {}

    return {
      backgroundColor: '#ffffff',
      grid: {
        left: '8%',
        right: '8%',
        top: '15%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.timeAxis,
        axisLabel: {
          fontSize: 10
        },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          fontSize: 10
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        textStyle: {
          color: '#000',
          fontSize: 12
        }
      },
      legend: {
        data: ['RSI(6)', 'RSI(12)', 'RSI(24)'],
        top: '5%',
        textStyle: {
          fontSize: 10
        }
      },
      series: [
        {
          name: 'RSI(6)',
          type: 'line',
          data: chartData.rsi6,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          symbol: 'none'
        },
        {
          name: 'RSI(12)',
          type: 'line',
          data: chartData.rsi12,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 2
          },
          symbol: 'none'
        },
        {
          name: 'RSI(24)',
          type: 'line',
          data: chartData.rsi24,
          smooth: true,
          lineStyle: {
            color: '#faad14',
            width: 2
          },
          symbol: 'none'
        }
      ],
      // 添加超买超卖线
      markLine: {
        data: [
          { yAxis: 70, lineStyle: { color: '#ff4d4f', type: 'dashed' } },
          { yAxis: 30, lineStyle: { color: '#52c41a', type: 'dashed' } }
        ],
        label: {
          show: true,
          position: 'end',
          fontSize: 10
        }
      }
    }
  }, [chartData])

  if (!chartData) {
    return (
      <div style={{
        height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>暂无数据</div>
      </div>
    )
  }

  return (
    <ReactECharts
      option={option}
      style={{ height, width: '100%' }}
      opts={{ renderer: 'canvas' }}
    />
  )
}

export default RSIChart
