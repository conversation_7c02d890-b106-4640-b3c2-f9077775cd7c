/**
 * 高级技术指标计算类
 * 实现专业级别的技术分析指标
 */

export interface PriceData {
  open: number
  high: number
  low: number
  close: number
  volume: number
  timestamp: string
  date: string
  adj_close: number
}

export interface IndicatorResult {
  value: number
  timestamp: string
  signal?: 'buy' | 'sell' | 'neutral'
  strength?: 'strong' | 'medium' | 'weak'
}

export interface IndicatorInfo {
  name: string
  description: string
  interpretation: string
  safeRange: { min: number, max: number }
  extremeRange: { min: number, max: number }
  buySignal: string
  sellSignal: string
  neutralRange: { min: number, max: number }
  usage: string
  timeframe: string
  reliability: 'high' | 'medium' | 'low'
}

export interface StochRSI {
  k: number
  d: number
  rsi: number
  timestamp: string
}

export interface KellerChannel {
  upper: number
  middle: number
  lower: number
  timestamp: string
  signal?: 'buy' | 'sell' | 'neutral'
  signalStrength?: 'strong' | 'medium' | 'weak'
  signalDescription?: string
}

export interface CandlestickData {
  timestamp: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface TradingSignal {
  timestamp: string
  type: 'buy' | 'sell'
  price: number
  strength: 'strong' | 'medium' | 'weak'
  reason: string
  confidence: number
}

export interface VolumeNode {
  price: number
  volume: number
  percentage: number
}

export interface VolumeProfile {
  valueAreaHigh: number
  valueAreaLow: number
  pointOfControl: number
  volumeNodes: VolumeNode[]
}

/**
 * 高级技术指标计算器
 */
export class AdvancedTechnicalIndicators {

  /**
   * 获取指标信息和使用说明
   */
  static getIndicatorInfo(): { [key: string]: IndicatorInfo } {
    return {
      kaufmanAMA: {
        name: 'Kaufman自适应移动平均线',
        description: '根据市场效率自动调整平滑因子的移动平均线，在趋势市场中快速响应，在震荡市场中减少噪音。',
        interpretation: '当价格在AMA上方时看多，下方时看空。AMA斜率变化反映趋势强度。',
        safeRange: { min: -0.02, max: 0.02 },
        extremeRange: { min: -0.05, max: 0.05 },
        buySignal: '价格突破AMA向上，且AMA斜率转正',
        sellSignal: '价格跌破AMA向下，且AMA斜率转负',
        neutralRange: { min: -0.01, max: 0.01 },
        usage: '适用于所有市场环境，特别是震荡和趋势转换期',
        timeframe: '中短期(5-20日)',
        reliability: 'high'
      },
      hullMA: {
        name: 'Hull移动平均线',
        description: '通过加权移动平均的组合计算，显著减少滞后性，更快响应价格变化的移动平均线。',
        interpretation: '价格在Hull MA上方为多头市场，下方为空头市场。Hull MA方向变化是趋势转换的早期信号。',
        safeRange: { min: -0.015, max: 0.015 },
        extremeRange: { min: -0.04, max: 0.04 },
        buySignal: '价格站上Hull MA且Hull MA向上倾斜',
        sellSignal: '价格跌破Hull MA且Hull MA向下倾斜',
        neutralRange: { min: -0.008, max: 0.008 },
        usage: '适合短期交易和趋势跟踪',
        timeframe: '短期(5-14日)',
        reliability: 'high'
      },
      stochasticRSI: {
        name: '随机RSI',
        description: '将随机指标应用于RSI值，结合两种指标的优势，提供更敏感的超买超卖信号。',
        interpretation: 'K值和D值在80以上为超买，20以下为超卖。K线上穿D线为买入信号，下穿为卖出信号。',
        safeRange: { min: 20, max: 80 },
        extremeRange: { min: 10, max: 90 },
        buySignal: 'K值从20以下上穿D值，或K、D值同时从超卖区域向上',
        sellSignal: 'K值从80以上下穿D值，或K、D值同时从超买区域向下',
        neutralRange: { min: 30, max: 70 },
        usage: '识别短期超买超卖，确认趋势转折点',
        timeframe: '短期(5-14日)',
        reliability: 'medium'
      },
      williamsVIX: {
        name: '威廉波动率指数',
        description: '衡量价格波动强度的指标，数值越高表示市场波动越剧烈，通常伴随着风险的增加。',
        interpretation: '低波动率(VIX<15)表示市场平静，高波动率(VIX>25)表示市场恐慌或不确定性增加。',
        safeRange: { min: 10, max: 20 },
        extremeRange: { min: 5, max: 40 },
        buySignal: 'VIX从高位回落至20以下，市场恐慌情绪缓解',
        sellSignal: 'VIX快速上升至25以上，市场风险增加',
        neutralRange: { min: 15, max: 25 },
        usage: '风险评估和市场情绪判断',
        timeframe: '中期(10-20日)',
        reliability: 'medium'
      },
      kellerChannels: {
        name: 'Keltner通道',
        description: '基于指数移动平均线和平均真实波动范围(ATR)构建的动态通道，用于识别趋势和支撑阻力。',
        interpretation: '价格在通道上轨附近为超买，下轨附近为超卖。突破通道表示强势趋势。',
        safeRange: { min: -0.02, max: 0.02 },
        extremeRange: { min: -0.05, max: 0.05 },
        buySignal: '价格从下轨反弹或突破上轨',
        sellSignal: '价格从上轨回落或跌破下轨',
        neutralRange: { min: -0.01, max: 0.01 },
        usage: '趋势跟踪和支撑阻力识别',
        timeframe: '中期(20日)',
        reliability: 'high'
      },
      atr: {
        name: '平均真实波动范围',
        description: '衡量价格波动性的指标，反映市场的活跃程度和风险水平。ATR值越高，市场波动越大。',
        interpretation: 'ATR上升表示波动性增加，下降表示波动性减少。用于设置止损位和仓位管理。',
        safeRange: { min: 0.5, max: 2.0 },
        extremeRange: { min: 0.2, max: 5.0 },
        buySignal: 'ATR处于低位时买入，风险相对较小',
        sellSignal: 'ATR快速上升时减仓，控制风险',
        neutralRange: { min: 1.0, max: 1.5 },
        usage: '风险管理和止损设置',
        timeframe: '中期(14日)',
        reliability: 'high'
      }
    }
  }

  /**
   * 分析指标信号强度
   */
  static analyzeIndicatorSignal(indicatorName: string, value: number): {
    signal: 'buy' | 'sell' | 'neutral'
    strength: 'strong' | 'medium' | 'weak'
    description: string
  } {
    const info = this.getIndicatorInfo()[indicatorName]
    if (!info) {
      return { signal: 'neutral', strength: 'weak', description: '未知指标' }
    }

    // 检查value是否有效
    if (value === undefined || value === null || isNaN(value)) {
      return { signal: 'neutral', strength: 'weak', description: '数据不足，无法分析' }
    }

    let signal: 'buy' | 'sell' | 'neutral' = 'neutral'
    let strength: 'strong' | 'medium' | 'weak' = 'weak'
    let description = ''

    switch (indicatorName) {
      case 'stochasticRSI':
        if (value < 20) {
          signal = 'buy'
          strength = value < 10 ? 'strong' : 'medium'
          description = `超卖区域(${value.toFixed(1)})，关注反弹机会`
        } else if (value > 80) {
          signal = 'sell'
          strength = value > 90 ? 'strong' : 'medium'
          description = `超买区域(${value.toFixed(1)})，注意回调风险`
        } else {
          signal = 'neutral'
          strength = 'weak'
          description = `正常区域(${value.toFixed(1)})，等待明确信号`
        }
        break

      case 'williamsVIX':
        if (value < 15) {
          signal = 'buy'
          strength = value < 10 ? 'strong' : 'medium'
          description = `低波动率(${value.toFixed(1)})，市场相对平静`
        } else if (value > 25) {
          signal = 'sell'
          strength = value > 35 ? 'strong' : 'medium'
          description = `高波动率(${value.toFixed(1)})，市场风险增加`
        } else {
          signal = 'neutral'
          strength = 'weak'
          description = `正常波动率(${value.toFixed(1)})，保持观望`
        }
        break

      case 'atr':
        if (value < info.safeRange.min) {
          signal = 'buy'
          strength = 'medium'
          description = `低波动性(${value.toFixed(2)})，适合建仓`
        } else if (value > info.safeRange.max) {
          signal = 'sell'
          strength = value > info.extremeRange.max ? 'strong' : 'medium'
          description = `高波动性(${value.toFixed(2)})，注意风险控制`
        } else {
          signal = 'neutral'
          strength = 'weak'
          description = `正常波动性(${value.toFixed(2)})，保持现有策略`
        }
        break

      default:
        // 对于价格类指标 (kaufmanAMA, hullMA, kellerChannels)
        if (value > info.safeRange.max) {
          signal = 'buy'
          strength = value > info.extremeRange.max ? 'strong' : 'medium'
          description = `价格强势(${(value * 100).toFixed(2)}%)，趋势向上`
        } else if (value < info.safeRange.min) {
          signal = 'sell'
          strength = value < info.extremeRange.min ? 'strong' : 'medium'
          description = `价格弱势(${(value * 100).toFixed(2)}%)，趋势向下`
        } else {
          signal = 'neutral'
          strength = 'weak'
          description = `价格平衡(${(value * 100).toFixed(2)}%)，等待方向选择`
        }
    }

    return { signal, strength, description }
  }
  
  /**
   * 自适应移动平均线 (Adaptive Moving Average)
   * 根据市场波动性自动调整平滑因子
   */
  static kaufmanAMA(data: PriceData[], period: number = 14, fastSC: number = 2, slowSC: number = 30): IndicatorResult[] {
    if (data.length < period + 1) return []
    
    const results: IndicatorResult[] = []
    const fastSCF = 2 / (fastSC + 1)
    const slowSCF = 2 / (slowSC + 1)
    
    for (let i = period; i < data.length; i++) {
      // 计算效率比率 (Efficiency Ratio)
      const change = Math.abs(data[i].close - data[i - period].close)
      let volatility = 0
      
      for (let j = i - period + 1; j <= i; j++) {
        volatility += Math.abs(data[j].close - data[j - 1].close)
      }
      
      const efficiencyRatio = volatility === 0 ? 0 : change / volatility
      
      // 计算平滑常数
      const smoothingConstant = Math.pow(efficiencyRatio * (fastSCF - slowSCF) + slowSCF, 2)
      
      // 计算AMA
      const prevAMA = i === period ? data[i - 1].close : results[results.length - 1].value
      const currentAMA = prevAMA + smoothingConstant * (data[i].close - prevAMA)
      
      results.push({
        value: currentAMA,
        timestamp: data[i].timestamp
      })
    }
    
    return results
  }

  /**
   * Hull移动平均线 (Hull Moving Average)
   * 减少滞后性的移动平均线
   */
  static hullMA(data: PriceData[], period: number = 14): IndicatorResult[] {
    if (data.length < period) return []
    
    const halfPeriod = Math.floor(period / 2)
    const sqrtPeriod = Math.floor(Math.sqrt(period))
    
    // 计算WMA(period/2) * 2
    const wma1 = this.weightedMA(data, halfPeriod).map(item => ({
      ...item,
      value: item.value * 2
    }))
    
    // 计算WMA(period)
    const wma2 = this.weightedMA(data, period)
    
    // 计算差值序列
    const diffData: PriceData[] = []
    const minLength = Math.min(wma1.length, wma2.length)
    
    for (let i = 0; i < minLength; i++) {
      const diff = wma1[wma1.length - minLength + i].value - wma2[wma2.length - minLength + i].value
      diffData.push({
        ...data[data.length - minLength + i],
        close: diff
      })
    }
    
    // 对差值序列计算WMA(sqrt(period))
    return this.weightedMA(diffData, sqrtPeriod)
  }

  /**
   * 加权移动平均线
   */
  private static weightedMA(data: PriceData[], period: number): IndicatorResult[] {
    if (data.length < period) return []
    
    const results: IndicatorResult[] = []
    const weights = Array.from({ length: period }, (_, i) => i + 1)
    const weightSum = weights.reduce((sum, weight) => sum + weight, 0)
    
    for (let i = period - 1; i < data.length; i++) {
      let weightedSum = 0
      
      for (let j = 0; j < period; j++) {
        weightedSum += data[i - period + 1 + j].close * weights[j]
      }
      
      results.push({
        value: weightedSum / weightSum,
        timestamp: data[i].timestamp
      })
    }
    
    return results
  }

  /**
   * 随机RSI (Stochastic RSI)
   * 结合RSI和随机指标的优势
   */
  static stochasticRSI(data: PriceData[], rsiPeriod: number = 14, stochPeriod: number = 14): StochRSI[] {
    if (data.length < rsiPeriod + stochPeriod) return []
    
    // 先计算RSI
    const rsiValues = this.calculateRSI(data, rsiPeriod)
    
    const results: StochRSI[] = []
    
    for (let i = stochPeriod - 1; i < rsiValues.length; i++) {
      const rsiSlice = rsiValues.slice(i - stochPeriod + 1, i + 1)
      const minRSI = Math.min(...rsiSlice.map(r => r.value))
      const maxRSI = Math.max(...rsiSlice.map(r => r.value))
      const currentRSI = rsiValues[i].value
      
      const stochRSI = maxRSI === minRSI ? 0 : (currentRSI - minRSI) / (maxRSI - minRSI) * 100
      
      // 计算%K和%D
      const k = stochRSI
      let d = 0
      
      if (results.length >= 2) {
        d = (results[results.length - 1].d * 2 + k) / 3
      } else {
        d = k
      }
      
      results.push({
        k,
        d,
        rsi: currentRSI,
        timestamp: rsiValues[i].timestamp
      })
    }
    
    return results
  }

  /**
   * RSI计算
   */
  private static calculateRSI(data: PriceData[], period: number): IndicatorResult[] {
    if (data.length < period + 1) return []
    
    const results: IndicatorResult[] = []
    let avgGain = 0
    let avgLoss = 0
    
    // 计算初始平均收益和损失
    for (let i = 1; i <= period; i++) {
      const change = data[i].close - data[i - 1].close
      if (change > 0) {
        avgGain += change
      } else {
        avgLoss += Math.abs(change)
      }
    }
    
    avgGain /= period
    avgLoss /= period
    
    // 计算RSI
    for (let i = period; i < data.length; i++) {
      const change = data[i].close - data[i - 1].close
      
      if (change > 0) {
        avgGain = (avgGain * (period - 1) + change) / period
        avgLoss = (avgLoss * (period - 1)) / period
      } else {
        avgGain = (avgGain * (period - 1)) / period
        avgLoss = (avgLoss * (period - 1) + Math.abs(change)) / period
      }
      
      const rs = avgLoss === 0 ? 100 : avgGain / avgLoss
      const rsi = 100 - (100 / (1 + rs))
      
      results.push({
        value: rsi,
        timestamp: data[i].timestamp
      })
    }
    
    return results
  }

  /**
   * 威廉波动率指数 (Williams VIX)
   */
  static williamsVIX(data: PriceData[], period: number = 14): IndicatorResult[] {
    if (data.length < period) return []
    
    const results: IndicatorResult[] = []
    
    for (let i = period - 1; i < data.length; i++) {
      const slice = data.slice(i - period + 1, i + 1)
      const trueRanges = []
      
      for (let j = 1; j < slice.length; j++) {
        const high = slice[j].high
        const low = slice[j].low
        const prevClose = slice[j - 1].close
        
        const tr = Math.max(
          high - low,
          Math.abs(high - prevClose),
          Math.abs(low - prevClose)
        )
        trueRanges.push(tr)
      }
      
      const avgTrueRange = trueRanges.reduce((sum, tr) => sum + tr, 0) / trueRanges.length
      const currentPrice = data[i].close
      const vix = (avgTrueRange / currentPrice) * 100
      
      results.push({
        value: vix,
        timestamp: data[i].timestamp
      })
    }
    
    return results
  }

  /**
   * Keltner通道 (增强版，包含信号分析)
   */
  static kellerChannels(data: PriceData[], period: number = 20, atrMultiplier: number = 2): KellerChannel[] {
    if (data.length < period + 1) return []

    const results: KellerChannel[] = []

    // 计算EMA和ATR
    for (let i = period; i < data.length; i++) {
      // 计算EMA（使用典型价格：(H+L+C)/3）
      let ema = 0
      const multiplier = 2 / (period + 1)

      if (i === period) {
        // 初始EMA使用简单移动平均
        let sum = 0
        for (let j = i - period; j <= i; j++) {
          const typicalPrice = (data[j].high + data[j].low + data[j].close) / 3
          sum += typicalPrice
        }
        ema = sum / (period + 1)
      } else {
        // 使用前一个EMA值计算新的EMA
        const prevEMA = results[results.length - 1].middle
        const typicalPrice = (data[i].high + data[i].low + data[i].close) / 3
        ema = (typicalPrice * multiplier) + (prevEMA * (1 - multiplier))
      }

      // 计算ATR
      let atr = 0
      if (i >= period) {
        const trueRanges = []
        for (let j = i - period + 1; j <= i; j++) {
          let tr = data[j].high - data[j].low
          if (j > 0) {
            const prevClose = data[j - 1].close
            tr = Math.max(
              data[j].high - data[j].low,
              Math.abs(data[j].high - prevClose),
              Math.abs(data[j].low - prevClose)
            )
          }
          trueRanges.push(tr)
        }
        atr = trueRanges.reduce((sum, tr) => sum + tr, 0) / trueRanges.length
      }

      const upper = ema + (atr * atrMultiplier)
      const middle = ema
      const lower = ema - (atr * atrMultiplier)

      // 使用收盘价进行信号分析
      const currentPrice = data[i].close
      const signalAnalysis = this.analyzeKeltnerSignal(currentPrice, upper, middle, lower, data, i)

      results.push({
        upper,
        middle,
        lower,
        timestamp: data[i].timestamp,
        signal: signalAnalysis.signal,
        signalStrength: signalAnalysis.strength,
        signalDescription: signalAnalysis.description
      })
    }

    return results
  }

  /**
   * 分析Keltner通道信号
   */
  private static analyzeKeltnerSignal(
    price: number,
    upper: number,
    middle: number,
    lower: number,
    data: PriceData[],
    index: number
  ): { signal: 'buy' | 'sell' | 'neutral', strength: 'strong' | 'medium' | 'weak', description: string } {

    const channelWidth = upper - lower
    const pricePosition = (price - lower) / channelWidth // 0-1之间，0.5为中轨

    // 获取前一个价格用于趋势判断
    const prevPrice = index > 0 ? data[index - 1].close : price
    const priceChange = price - prevPrice

    // 获取成交量信息
    const currentVolume = data[index].volume
    const avgVolume = index >= 5 ?
      data.slice(index - 4, index + 1).reduce((sum, d) => sum + d.volume, 0) / 5 : currentVolume
    const volumeRatio = currentVolume / avgVolume

    let signal: 'buy' | 'sell' | 'neutral' = 'neutral'
    let strength: 'strong' | 'medium' | 'weak' = 'weak'
    let description = ''

    // 突破上轨
    if (price > upper) {
      signal = 'buy'
      if (priceChange > 0 && volumeRatio > 1.2) {
        strength = 'strong'
        description = `强势突破上轨(${price.toFixed(2)})，成交量放大${(volumeRatio * 100).toFixed(0)}%`
      } else if (priceChange > 0) {
        strength = 'medium'
        description = `突破上轨(${price.toFixed(2)})，关注后续确认`
      } else {
        strength = 'weak'
        description = `价格在上轨上方(${price.toFixed(2)})，但动能不足`
      }
    }
    // 跌破下轨
    else if (price < lower) {
      signal = 'sell'
      if (priceChange < 0 && volumeRatio > 1.2) {
        strength = 'strong'
        description = `跌破下轨(${price.toFixed(2)})，成交量放大，趋势转弱`
      } else if (priceChange < 0) {
        strength = 'medium'
        description = `跌破下轨(${price.toFixed(2)})，关注支撑情况`
      } else {
        strength = 'weak'
        description = `价格在下轨下方(${price.toFixed(2)})，可能超卖反弹`
      }
    }
    // 接近上轨
    else if (pricePosition > 0.8) {
      signal = 'sell'
      strength = pricePosition > 0.9 ? 'medium' : 'weak'
      description = `接近上轨阻力(${price.toFixed(2)})，注意回调风险`
    }
    // 接近下轨
    else if (pricePosition < 0.2) {
      signal = 'buy'
      strength = pricePosition < 0.1 ? 'medium' : 'weak'
      description = `接近下轨支撑(${price.toFixed(2)})，关注反弹机会`
    }
    // 中轨附近
    else if (Math.abs(price - middle) / middle < 0.01) {
      signal = 'neutral'
      strength = 'weak'
      if (priceChange > 0) {
        description = `价格在中轨附近(${price.toFixed(2)})，略偏多头`
      } else if (priceChange < 0) {
        description = `价格在中轨附近(${price.toFixed(2)})，略偏空头`
      } else {
        description = `价格在中轨附近(${price.toFixed(2)})，等待方向选择`
      }
    }
    // 通道内正常波动
    else {
      signal = 'neutral'
      strength = 'weak'
      description = `价格在通道内正常波动(${price.toFixed(2)})，保持观望`
    }

    return { signal, strength, description }
  }

  /**
   * 生成Keltner通道交易信号
   */
  static generateKeltnerTradingSignals(data: PriceData[], keltnerData: KellerChannel[]): TradingSignal[] {
    const signals: TradingSignal[] = []

    for (let i = 1; i < keltnerData.length; i++) {
      const current = keltnerData[i]
      const prev = keltnerData[i - 1]
      const currentPrice = data[data.length - keltnerData.length + i].close
      const prevPrice = data[data.length - keltnerData.length + i - 1].close

      // 突破信号
      if (prevPrice <= prev.upper && currentPrice > current.upper) {
        signals.push({
          timestamp: current.timestamp,
          type: 'buy',
          price: currentPrice,
          strength: current.signalStrength || 'medium',
          reason: '突破Keltner上轨',
          confidence: current.signalStrength === 'strong' ? 0.8 : 0.6
        })
      }

      // 跌破信号
      if (prevPrice >= prev.lower && currentPrice < current.lower) {
        signals.push({
          timestamp: current.timestamp,
          type: 'sell',
          price: currentPrice,
          strength: current.signalStrength || 'medium',
          reason: '跌破Keltner下轨',
          confidence: current.signalStrength === 'strong' ? 0.8 : 0.6
        })
      }
    }

    return signals
  }

  /**
   * 指数移动平均线
   */
  static exponentialMA(data: PriceData[], period: number): IndicatorResult[] {
    if (data.length < period) return []
    
    const results: IndicatorResult[] = []
    const multiplier = 2 / (period + 1)
    
    // 初始值使用简单移动平均
    let ema = data.slice(0, period).reduce((sum, item) => sum + item.close, 0) / period
    
    for (let i = period - 1; i < data.length; i++) {
      if (i === period - 1) {
        results.push({
          value: ema,
          timestamp: data[i].timestamp
        })
      } else {
        ema = (data[i].close * multiplier) + (ema * (1 - multiplier))
        results.push({
          value: ema,
          timestamp: data[i].timestamp
        })
      }
    }
    
    return results
  }

  /**
   * 平均真实波动范围 (Average True Range)
   */
  static averageTrueRange(data: PriceData[], period: number = 14): IndicatorResult[] {
    if (data.length < period + 1) return []
    
    const trueRanges: number[] = []
    
    // 计算真实波动范围
    for (let i = 1; i < data.length; i++) {
      const high = data[i].high
      const low = data[i].low
      const prevClose = data[i - 1].close
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      )
      trueRanges.push(tr)
    }
    
    const results: IndicatorResult[] = []
    
    // 计算ATR
    for (let i = period - 1; i < trueRanges.length; i++) {
      let atr: number
      
      if (i === period - 1) {
        // 初始ATR使用简单平均
        atr = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period
      } else {
        // 后续使用指数平滑
        const prevATR = results[results.length - 1].value
        atr = (prevATR * (period - 1) + trueRanges[i]) / period
      }
      
      results.push({
        value: atr,
        timestamp: data[i + 1].timestamp
      })
    }
    
    return results
  }

  /**
   * 成交量分布分析
   */
  static volumeProfile(data: PriceData[], bins: number = 50): VolumeProfile {
    if (data.length === 0) {
      return {
        valueAreaHigh: 0,
        valueAreaLow: 0,
        pointOfControl: 0,
        volumeNodes: []
      }
    }
    
    const minPrice = Math.min(...data.map(d => d.low))
    const maxPrice = Math.max(...data.map(d => d.high))
    const priceStep = (maxPrice - minPrice) / bins
    
    const volumeNodes: VolumeNode[] = []
    const totalVolume = data.reduce((sum, d) => sum + d.volume, 0)
    
    // 创建价格区间并计算每个区间的成交量
    for (let i = 0; i < bins; i++) {
      const priceLevel = minPrice + (i * priceStep)
      let volumeAtLevel = 0
      
      data.forEach(candle => {
        // 如果价格区间与K线重叠，按比例分配成交量
        const candleLow = candle.low
        const candleHigh = candle.high
        const levelLow = priceLevel
        const levelHigh = priceLevel + priceStep
        
        if (levelHigh >= candleLow && levelLow <= candleHigh) {
          const overlapRatio = Math.min(levelHigh, candleHigh) - Math.max(levelLow, candleLow)
          const candleRange = candleHigh - candleLow
          const volumeRatio = candleRange > 0 ? overlapRatio / candleRange : 1
          volumeAtLevel += candle.volume * volumeRatio
        }
      })
      
      if (volumeAtLevel > 0) {
        volumeNodes.push({
          price: priceLevel + priceStep / 2,
          volume: volumeAtLevel,
          percentage: (volumeAtLevel / totalVolume) * 100
        })
      }
    }
    
    // 排序并找到POC
    volumeNodes.sort((a, b) => b.volume - a.volume)
    const pointOfControl = volumeNodes.length > 0 ? volumeNodes[0].price : 0
    
    // 计算价值区域 (70%的成交量)
    let cumulativeVolume = 0
    const valueAreaVolume = totalVolume * 0.7
    const valueAreaNodes: VolumeNode[] = []
    
    for (const node of volumeNodes) {
      if (cumulativeVolume < valueAreaVolume) {
        valueAreaNodes.push(node)
        cumulativeVolume += node.volume
      } else {
        break
      }
    }
    
    const valueAreaPrices = valueAreaNodes.map(n => n.price).sort((a, b) => a - b)
    const valueAreaHigh = valueAreaPrices.length > 0 ? Math.max(...valueAreaPrices) : 0
    const valueAreaLow = valueAreaPrices.length > 0 ? Math.min(...valueAreaPrices) : 0
    
    return {
      valueAreaHigh,
      valueAreaLow,
      pointOfControl,
      volumeNodes: volumeNodes.sort((a, b) => a.price - b.price)
    }
  }
}

export default AdvancedTechnicalIndicators
