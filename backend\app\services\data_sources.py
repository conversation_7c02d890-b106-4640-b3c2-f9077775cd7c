"""
数据源服务 - 对接多个数据接口
支持: AKShare、<PERSON><PERSON><PERSON>、新浪财经API
"""

import asyncio
import aiohttp
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import re
from app.core.config import settings

logger = logging.getLogger(__name__)

class BaseDataSource:
    """数据源基类"""
    
    def __init__(self):
        self.session = None
        self.rate_limit = 100  # 每分钟请求限制
        self.timeout = 30
    
    async def get_session(self):
        """获取HTTP会话"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 子类实现具体的连接测试
            return await self._test_connection_impl()
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
    
    async def _test_connection_impl(self) -> bool:
        """连接测试实现 - 子类重写"""
        return True

class AKShareService(BaseDataSource):
    """AKShare数据源服务"""

    def __init__(self):
        super().__init__()
        self.rate_limit = 100

    async def _test_connection_impl(self) -> bool:
        """测试AKShare连接"""
        try:
            # AKShare是本地Python库，测试导入是否成功
            import akshare as ak
            # 验证版本信息，这是最快的测试方法
            version = getattr(ak, '__version__', None)
            if version:
                logger.info(f"AKShare库可用，版本: {version}")
                return True
            else:
                logger.warning("AKShare库版本信息不可用")
                return True  # 即使没有版本信息，导入成功就认为可用
        except ImportError:
            logger.error("AKShare库未安装")
            return False
        except Exception as e:
            logger.error(f"AKShare连接测试失败: {e}")
            return False
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            session = await self.get_session()
            async with session.get(f"{self.base_url}/stock_info_a_code_name") as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_stock_list(data)
                return []
        except Exception as e:
            logger.error(f"AKShare获取股票列表失败: {e}")
            return []
    
    async def get_stock_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取股票实时价格"""
        try:
            session = await self.get_session()
            # 使用AKShare的实时行情接口
            params = {"symbol": symbol}
            async with session.get(f"{self.base_url}/stock_zh_a_spot_em", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_stock_price(data, symbol)
                return None
        except Exception as e:
            logger.error(f"AKShare获取股票 {symbol} 价格失败: {e}")
            return None
    
    async def get_financial_news(self) -> List[Dict[str, Any]]:
        """获取财经新闻"""
        try:
            session = await self.get_session()
            async with session.get(f"{self.base_url}/stock_news_em") as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_financial_news(data)
                return []
        except Exception as e:
            logger.error(f"AKShare获取财经新闻失败: {e}")
            return []
    
    def _format_stock_list(self, data: List[Dict]) -> List[Dict[str, Any]]:
        """格式化股票列表数据"""
        formatted_data = []
        for item in data:
            formatted_data.append({
                "symbol": item.get("代码"),
                "name": item.get("名称"),
                "market": "A股",
                "source": "akshare"
            })
        return formatted_data
    
    def _format_stock_price(self, data: Dict, symbol: str) -> Dict[str, Any]:
        """格式化股票价格数据"""
        if not data:
            return {}
        
        return {
            "symbol": symbol,
            "current_price": float(data.get("最新价", 0)),
            "open_price": float(data.get("今开", 0)),
            "high_price": float(data.get("最高", 0)),
            "low_price": float(data.get("最低", 0)),
            "volume": int(data.get("成交量", 0)),
            "turnover": float(data.get("成交额", 0)),
            "change_percent": float(data.get("涨跌幅", 0)),
            "timestamp": datetime.now(),
            "source": "akshare"
        }
    
    def _format_financial_news(self, data: List[Dict]) -> List[Dict[str, Any]]:
        """格式化财经新闻数据"""
        formatted_news = []
        for item in data:
            formatted_news.append({
                "title": item.get("新闻标题"),
                "content": item.get("新闻内容", ""),
                "source": "东方财富",
                "url": item.get("新闻链接"),
                "publish_time": self._parse_datetime(item.get("发布时间")),
                "category": "财经新闻"
            })
        return formatted_news

class TushareService(BaseDataSource):
    """Tushare数据源服务"""
    
    def __init__(self):
        super().__init__()
        self.api_key = settings.TUSHARE_API_KEY
        self.base_url = "http://api.tushare.pro"
        self.rate_limit = 200
    
    async def _test_connection_impl(self) -> bool:
        """测试Tushare连接"""
        try:
            # 测试获取股票列表
            result = await self._call_api("stock_basic", {})
            return result is not None
        except Exception:
            return False
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            data = await self._call_api("stock_basic", {
                "exchange": "",
                "list_status": "L",
                "fields": "ts_code,symbol,name,area,industry,list_date"
            })
            
            if data and "data" in data:
                return self._format_tushare_stock_list(data["data"]["items"])
            return []
        except Exception as e:
            logger.error(f"Tushare获取股票列表失败: {e}")
            return []
    
    async def get_stock_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取股票实时价格"""
        try:
            # Tushare的实时数据需要高级权限，这里使用日线数据的最新一条
            ts_code = self._convert_symbol_to_tushare(symbol)
            data = await self._call_api("daily", {
                "ts_code": ts_code,
                "trade_date": datetime.now().strftime("%Y%m%d")
            })
            
            if data and "data" in data and data["data"]["items"]:
                return self._format_tushare_price(data["data"]["items"][0], symbol)
            return None
        except Exception as e:
            logger.error(f"Tushare获取股票 {symbol} 价格失败: {e}")
            return None
    
    async def _call_api(self, api_name: str, params: Dict) -> Optional[Dict]:
        """调用Tushare API"""
        try:
            session = await self.get_session()
            
            request_data = {
                "api_name": api_name,
                "token": self.api_key,
                "params": params,
                "fields": params.get("fields", "")
            }
            
            async with session.post(self.base_url, json=request_data) as response:
                if response.status == 200:
                    return await response.json()
                return None
        except Exception as e:
            logger.error(f"Tushare API调用失败: {e}")
            return None
    
    def _convert_symbol_to_tushare(self, symbol: str) -> str:
        """转换股票代码为Tushare格式"""
        if symbol.startswith("6"):
            return f"{symbol}.SH"
        elif symbol.startswith(("0", "3")):
            return f"{symbol}.SZ"
        return symbol
    
    def _format_tushare_stock_list(self, data: List[List]) -> List[Dict[str, Any]]:
        """格式化Tushare股票列表"""
        formatted_data = []
        for item in data:
            if len(item) >= 6:
                formatted_data.append({
                    "symbol": item[1],  # symbol
                    "ts_code": item[0],  # ts_code
                    "name": item[2],     # name
                    "area": item[3],     # area
                    "industry": item[4], # industry
                    "list_date": item[5], # list_date
                    "market": "A股",
                    "source": "tushare"
                })
        return formatted_data
    
    def _format_tushare_price(self, data: List, symbol: str) -> Dict[str, Any]:
        """格式化Tushare价格数据"""
        if len(data) < 9:
            return {}
        
        return {
            "symbol": symbol,
            "current_price": float(data[2]),  # close
            "open_price": float(data[1]),     # open
            "high_price": float(data[3]),     # high
            "low_price": float(data[4]),      # low
            "volume": int(data[9]) if len(data) > 9 else 0,  # vol
            "change_percent": float(data[8]) if len(data) > 8 else 0,  # pct_chg
            "timestamp": datetime.now(),
            "source": "tushare"
        }

class SinaService(BaseDataSource):
    """新浪财经数据源服务"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "http://hq.sinajs.cn"
        self.rate_limit = 500
    
    async def _test_connection_impl(self) -> bool:
        """测试新浪财经连接"""
        try:
            session = await self.get_session()
            # 设置合适的请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'http://finance.sina.com.cn/'
            }
            # 测试获取一只股票的数据
            async with session.get(f"{self.base_url}/list=sh000001", headers=headers) as response:
                if response.status == 200:
                    text = await response.text()
                    # 检查返回的数据是否包含股票信息
                    return 'var hq_str_sh000001=' in text
                return False
        except Exception as e:
            logger.error(f"新浪财经连接测试失败: {e}")
            return False
    
    async def get_stock_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取股票实时价格"""
        try:
            session = await self.get_session()
            sina_symbol = self._convert_symbol_to_sina(symbol)
            
            async with session.get(f"{self.base_url}/list={sina_symbol}") as response:
                if response.status == 200:
                    text = await response.text()
                    return self._parse_sina_price(text, symbol)
                return None
        except Exception as e:
            logger.error(f"新浪财经获取股票 {symbol} 价格失败: {e}")
            return None
    
    async def get_financial_news(self) -> List[Dict[str, Any]]:
        """获取财经新闻"""
        try:
            # 新浪财经新闻API（简化版本）
            session = await self.get_session()
            news_url = "https://feed.mix.sina.com.cn/api/roll/get"
            params = {
                "pageid": "153",
                "lid": "1686",
                "num": "50",
                "versionNumber": "1.2.8"
            }
            
            async with session.get(news_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_sina_news(data)
                return []
        except Exception as e:
            logger.error(f"新浪财经获取新闻失败: {e}")
            return []
    
    def _convert_symbol_to_sina(self, symbol: str) -> str:
        """转换股票代码为新浪格式"""
        if symbol.startswith("6"):
            return f"sh{symbol}"
        elif symbol.startswith(("0", "3")):
            return f"sz{symbol}"
        return symbol
    
    def _parse_sina_price(self, text: str, symbol: str) -> Dict[str, Any]:
        """解析新浪股价数据"""
        try:
            # 新浪返回格式: var hq_str_sh000001="上证指数,3000.00,2990.00,..."
            match = re.search(r'"([^"]*)"', text)
            if not match:
                return {}
            
            data_str = match.group(1)
            data_parts = data_str.split(",")
            
            if len(data_parts) < 32:
                return {}
            
            return {
                "symbol": symbol,
                "name": data_parts[0],
                "open_price": float(data_parts[1]) if data_parts[1] else 0,
                "close_price": float(data_parts[2]) if data_parts[2] else 0,
                "current_price": float(data_parts[3]) if data_parts[3] else 0,
                "high_price": float(data_parts[4]) if data_parts[4] else 0,
                "low_price": float(data_parts[5]) if data_parts[5] else 0,
                "volume": int(data_parts[8]) if data_parts[8] else 0,
                "turnover": float(data_parts[9]) if data_parts[9] else 0,
                "timestamp": datetime.now(),
                "source": "sina"
            }
        except Exception as e:
            logger.error(f"解析新浪股价数据失败: {e}")
            return {}
    
    def _format_sina_news(self, data: Dict) -> List[Dict[str, Any]]:
        """格式化新浪新闻数据"""
        formatted_news = []
        
        if "result" in data and "data" in data["result"]:
            for item in data["result"]["data"]:
                formatted_news.append({
                    "title": item.get("title"),
                    "content": item.get("intro", ""),
                    "source": "新浪财经",
                    "url": item.get("url"),
                    "publish_time": self._parse_datetime(item.get("ctime")),
                    "category": "财经新闻"
                })
        
        return formatted_news
    
    def _parse_datetime(self, time_str: str) -> datetime:
        """解析时间字符串"""
        try:
            if isinstance(time_str, str):
                # 尝试多种时间格式
                formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d %H:%M",
                    "%Y-%m-%d",
                    "%m-%d %H:%M"
                ]
                
                for fmt in formats:
                    try:
                        return datetime.strptime(time_str, fmt)
                    except ValueError:
                        continue
            
            # 如果是时间戳
            if isinstance(time_str, (int, float)):
                return datetime.fromtimestamp(time_str)
            
            return datetime.now()
        except Exception:
            return datetime.now()
