import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  Table,
  Tag,
  message,
  Row,
  Col,
  Typography,
  Divider,
  Steps,
} from 'antd'
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  SearchOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

interface StockPosition {
  code: string
  name: string
  weight: number
  shares?: number
  price?: number
  value?: number
}

interface PortfolioConfig {
  name: string
  description: string
  strategy: string
  riskLevel: string
  initialCapital: number
  positions: StockPosition[]
}

const PortfolioCreate: React.FC = () => {
  const [form] = Form.useForm()
  const [currentStep, setCurrentStep] = useState(0)
  const [portfolioConfig, setPortfolioConfig] = useState<PortfolioConfig>({
    name: '',
    description: '',
    strategy: '',
    riskLevel: '',
    initialCapital: 100000,
    positions: [],
  })
  const [searchValue, setSearchValue] = useState('')
  const [loading, setLoading] = useState(false)

  // 模拟股票搜索数据
  const mockStocks = [
    { code: '000001', name: '平安银行', price: 12.45 },
    { code: '000002', name: '万科A', price: 23.67 },
    { code: '600000', name: '浦发银行', price: 8.90 },
    { code: '600036', name: '招商银行', price: 45.23 },
    { code: '000858', name: '五粮液', price: 156.78 },
    { code: '600519', name: '贵州茅台', price: 1678.90 },
    { code: '300750', name: '宁德时代', price: 234.56 },
    { code: '002594', name: '比亚迪', price: 189.34 },
  ]

  const addPosition = (stock: any) => {
    const existingPosition = portfolioConfig.positions.find(p => p.code === stock.code)
    if (existingPosition) {
      message.warning('该股票已在组合中')
      return
    }

    const newPosition: StockPosition = {
      code: stock.code,
      name: stock.name,
      weight: 0,
      price: stock.price,
      shares: 0,
      value: 0,
    }

    setPortfolioConfig(prev => ({
      ...prev,
      positions: [...prev.positions, newPosition],
    }))
    message.success(`已添加 ${stock.name}`)
  }

  const removePosition = (code: string) => {
    setPortfolioConfig(prev => ({
      ...prev,
      positions: prev.positions.filter(p => p.code !== code),
    }))
  }

  const updatePosition = (code: string, field: keyof StockPosition, value: any) => {
    setPortfolioConfig(prev => ({
      ...prev,
      positions: prev.positions.map(p => {
        if (p.code === code) {
          const updated = { ...p, [field]: value }
          
          // 自动计算相关字段
          if (field === 'weight' && updated.price) {
            updated.value = (portfolioConfig.initialCapital * value / 100)
            updated.shares = Math.floor(updated.value / updated.price)
          } else if (field === 'shares' && updated.price) {
            updated.value = value * updated.price
            updated.weight = (updated.value / portfolioConfig.initialCapital) * 100
          }
          
          return updated
        }
        return p
      }),
    }))
  }

  const handleNext = () => {
    if (currentStep === 0) {
      form.validateFields(['name', 'strategy', 'riskLevel', 'initialCapital'])
        .then(values => {
          setPortfolioConfig(prev => ({ ...prev, ...values }))
          setCurrentStep(1)
        })
        .catch(() => {
          message.error('请完善基本信息')
        })
    } else if (currentStep === 1) {
      if (portfolioConfig.positions.length === 0) {
        message.error('请至少添加一只股票')
        return
      }
      const totalWeight = portfolioConfig.positions.reduce((sum, p) => sum + p.weight, 0)
      if (Math.abs(totalWeight - 100) > 1) {
        message.error('权重总和应为100%')
        return
      }
      setCurrentStep(2)
    }
  }

  const handlePrev = () => {
    setCurrentStep(currentStep - 1)
  }

  const handleSubmit = async () => {
    setLoading(true)
    try {
      // 模拟保存
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('组合创建成功！')
      // 这里可以导航到组合详情页
    } catch (error) {
      message.error('创建失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const positionColumns = [
    {
      title: '股票代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '当前价格',
      dataIndex: 'price',
      key: 'price',
      render: (value: number) => `¥${value?.toFixed(2)}`,
    },
    {
      title: '权重(%)',
      dataIndex: 'weight',
      key: 'weight',
      render: (value: number, record: StockPosition) => (
        <InputNumber
          value={value}
          onChange={(val) => updatePosition(record.code, 'weight', val || 0)}
          min={0}
          max={100}
          precision={2}
          size="small"
          style={{ width: 80 }}
        />
      ),
    },
    {
      title: '股数',
      dataIndex: 'shares',
      key: 'shares',
      render: (value: number, record: StockPosition) => (
        <InputNumber
          value={value}
          onChange={(val) => updatePosition(record.code, 'shares', val || 0)}
          min={0}
          precision={0}
          size="small"
          style={{ width: 80 }}
        />
      ),
    },
    {
      title: '市值',
      dataIndex: 'value',
      key: 'value',
      render: (value: number) => `¥${value?.toFixed(2)}`,
    },
    {
      title: '操作',
      key: 'action',
      render: (record: StockPosition) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removePosition(record.code)}
          size="small"
        >
          删除
        </Button>
      ),
    },
  ]

  const filteredStocks = mockStocks.filter(stock =>
    stock.code.includes(searchValue) || stock.name.includes(searchValue)
  )

  const totalWeight = portfolioConfig.positions.reduce((sum, p) => sum + p.weight, 0)
  const totalValue = portfolioConfig.positions.reduce((sum, p) => sum + (p.value || 0), 0)

  const steps = [
    {
      title: '基本信息',
      description: '设置组合基本信息',
    },
    {
      title: '股票配置',
      description: '添加股票和设置权重',
    },
    {
      title: '确认创建',
      description: '确认信息并创建组合',
    },
  ]

  return (
    <div>
      <Title level={2}>创建投资组合</Title>
      
      <Card>
        <Steps current={currentStep} items={steps} style={{ marginBottom: 32 }} />

        {currentStep === 0 && (
          <Form form={form} layout="vertical" initialValues={portfolioConfig}>
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="组合名称"
                  name="name"
                  rules={[{ required: true, message: '请输入组合名称' }]}
                >
                  <Input placeholder="请输入组合名称" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="投资策略"
                  name="strategy"
                  rules={[{ required: true, message: '请选择投资策略' }]}
                >
                  <Select placeholder="请选择投资策略">
                    <Option value="growth">成长型</Option>
                    <Option value="value">价值型</Option>
                    <Option value="balanced">平衡型</Option>
                    <Option value="dividend">股息型</Option>
                    <Option value="momentum">动量型</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="风险等级"
                  name="riskLevel"
                  rules={[{ required: true, message: '请选择风险等级' }]}
                >
                  <Select placeholder="请选择风险等级">
                    <Option value="conservative">保守型</Option>
                    <Option value="moderate">稳健型</Option>
                    <Option value="aggressive">积极型</Option>
                    <Option value="speculative">投机型</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="初始资金"
                  name="initialCapital"
                  rules={[{ required: true, message: '请输入初始资金' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入初始资金"
                    min={1000}
                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/¥\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item label="组合描述" name="description">
                  <TextArea rows={4} placeholder="请输入组合描述（可选）" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        )}

        {currentStep === 1 && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col flex="auto">
                  <Input
                    placeholder="搜索股票代码或名称"
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    prefix={<SearchOutlined />}
                  />
                </Col>
              </Row>
              
              {searchValue && (
                <div style={{ marginTop: 16 }}>
                  <Text strong>搜索结果：</Text>
                  <div style={{ marginTop: 8 }}>
                    {filteredStocks.map(stock => (
                      <Tag
                        key={stock.code}
                        style={{ margin: '4px', cursor: 'pointer' }}
                        onClick={() => addPosition(stock)}
                      >
                        <PlusOutlined /> {stock.code} {stock.name}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            <Card
              title={
                <Space>
                  <span>股票配置</span>
                  <Tag color={Math.abs(totalWeight - 100) <= 1 ? 'green' : 'red'}>
                    权重总和: {totalWeight.toFixed(2)}%
                  </Tag>
                  <Tag>总市值: ¥{totalValue.toFixed(2)}</Tag>
                </Space>
              }
            >
              <Table
                dataSource={portfolioConfig.positions}
                columns={positionColumns}
                rowKey="code"
                pagination={false}
                size="small"
              />
            </Card>
          </div>
        )}

        {currentStep === 2 && (
          <div>
            <Card title="组合信息确认">
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Text strong>组合名称：</Text>
                  <Text>{portfolioConfig.name}</Text>
                </Col>
                <Col xs={24} md={12}>
                  <Text strong>投资策略：</Text>
                  <Text>{portfolioConfig.strategy}</Text>
                </Col>
                <Col xs={24} md={12}>
                  <Text strong>风险等级：</Text>
                  <Text>{portfolioConfig.riskLevel}</Text>
                </Col>
                <Col xs={24} md={12}>
                  <Text strong>初始资金：</Text>
                  <Text>¥{portfolioConfig.initialCapital.toLocaleString()}</Text>
                </Col>
                <Col xs={24}>
                  <Text strong>组合描述：</Text>
                  <Text>{portfolioConfig.description || '无'}</Text>
                </Col>
              </Row>
              
              <Divider />
              
              <Title level={4}>股票配置</Title>
              <Table
                dataSource={portfolioConfig.positions}
                columns={positionColumns.filter(col => col.key !== 'action')}
                rowKey="code"
                pagination={false}
                size="small"
                summary={() => (
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={3}>
                      <Text strong>总计</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>
                      <Text strong>{totalWeight.toFixed(2)}%</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4}>
                      <Text strong>
                        {portfolioConfig.positions.reduce((sum, p) => sum + (p.shares || 0), 0)}
                      </Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5}>
                      <Text strong>¥{totalValue.toFixed(2)}</Text>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                )}
              />
            </Card>
          </div>
        )}

        <div style={{ marginTop: 24, textAlign: 'right' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {currentStep < steps.length - 1 && (
              <Button type="primary" onClick={handleNext}>
                下一步
              </Button>
            )}
            {currentStep === steps.length - 1 && (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleSubmit}
              >
                创建组合
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default PortfolioCreate
