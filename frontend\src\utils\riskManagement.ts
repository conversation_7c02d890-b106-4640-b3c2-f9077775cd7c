/**
 * 风险管理系统
 * 实现VaR、压力测试、风险度量和动态止损
 */

import { PriceData } from './advancedIndicators'

export interface VaRResult {
  var95: number
  var99: number
  expectedShortfall95: number
  expectedShortfall99: number
  confidence: number
  timeHorizon: number
  method: 'historical' | 'parametric' | 'montecarlo'
}

export interface StressTestScenario {
  name: string
  description: string
  marketShock: number
  sectorShock: number
  liquidityShock: number
  volatilityMultiplier: number
  correlationBreakdown: boolean
}

export interface StressTestResult {
  scenario: StressTestScenario
  portfolioLoss: number
  maxDrawdown: number
  recoveryTime: number
  survivabilityScore: number
  criticalPositions: string[]
}

export interface RiskMetrics {
  volatility: number
  sharpeRatio: number
  sortinoRatio: number
  maxDrawdown: number
  calmarRatio: number
  beta: number
  alpha: number
  trackingError: number
  informationRatio: number
  downside_deviation: number
}

export interface DynamicStopLoss {
  currentStopPrice: number
  trailingStopPrice: number
  atrStopPrice: number
  volatilityStopPrice: number
  recommendedStop: number
  stopType: 'fixed' | 'trailing' | 'atr' | 'volatility'
  riskAmount: number
  riskPercentage: number
}

export interface PortfolioRisk {
  totalRisk: number
  diversificationBenefit: number
  concentrationRisk: number
  liquidityRisk: number
  marketRisk: number
  specificRisk: number
  riskContribution: { [symbol: string]: number }
}

export interface RiskAlert {
  type: 'var_breach' | 'drawdown_limit' | 'concentration' | 'liquidity' | 'correlation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  recommendation: string
  timestamp: string
}

/**
 * 风险管理计算器
 */
export class RiskManagement {

  /**
   * 计算风险价值 (Value at Risk)
   */
  static calculateVaR(
    returns: number[], 
    confidence: number = 0.95, 
    timeHorizon: number = 1,
    method: 'historical' | 'parametric' | 'montecarlo' = 'historical'
  ): VaRResult {
    
    switch (method) {
      case 'historical':
        return this.historicalVaR(returns, confidence, timeHorizon)
      case 'parametric':
        return this.parametricVaR(returns, confidence, timeHorizon)
      case 'montecarlo':
        return this.monteCarloVaR(returns, confidence, timeHorizon)
      default:
        return this.historicalVaR(returns, confidence, timeHorizon)
    }
  }

  /**
   * 历史模拟法VaR
   */
  private static historicalVaR(returns: number[], confidence: number, timeHorizon: number): VaRResult {
    const sortedReturns = [...returns].sort((a, b) => a - b)
    const n = sortedReturns.length
    
    const var95Index = Math.floor((1 - 0.95) * n)
    const var99Index = Math.floor((1 - 0.99) * n)
    
    const var95 = -sortedReturns[var95Index] * Math.sqrt(timeHorizon)
    const var99 = -sortedReturns[var99Index] * Math.sqrt(timeHorizon)
    
    // 期望损失 (Expected Shortfall)
    const es95 = -sortedReturns.slice(0, var95Index + 1).reduce((sum, ret) => sum + ret, 0) / (var95Index + 1) * Math.sqrt(timeHorizon)
    const es99 = -sortedReturns.slice(0, var99Index + 1).reduce((sum, ret) => sum + ret, 0) / (var99Index + 1) * Math.sqrt(timeHorizon)
    
    return {
      var95,
      var99,
      expectedShortfall95: es95,
      expectedShortfall99: es99,
      confidence: 0.9,
      timeHorizon,
      method: 'historical'
    }
  }

  /**
   * 参数法VaR
   */
  private static parametricVaR(returns: number[], confidence: number, timeHorizon: number): VaRResult {
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1)
    const std = Math.sqrt(variance)
    
    // 正态分布分位数
    const z95 = 1.645  // 95%置信度
    const z99 = 2.326  // 99%置信度
    
    const var95 = -(mean - z95 * std) * Math.sqrt(timeHorizon)
    const var99 = -(mean - z99 * std) * Math.sqrt(timeHorizon)
    
    // 正态分布下的期望损失
    const es95 = std * Math.exp(-0.5 * z95 * z95) / (Math.sqrt(2 * Math.PI) * (1 - 0.95)) * Math.sqrt(timeHorizon)
    const es99 = std * Math.exp(-0.5 * z99 * z99) / (Math.sqrt(2 * Math.PI) * (1 - 0.99)) * Math.sqrt(timeHorizon)
    
    return {
      var95,
      var99,
      expectedShortfall95: es95,
      expectedShortfall99: es99,
      confidence: 0.95,
      timeHorizon,
      method: 'parametric'
    }
  }

  /**
   * 蒙特卡洛模拟VaR
   */
  private static monteCarloVaR(returns: number[], confidence: number, timeHorizon: number): VaRResult {
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const std = Math.sqrt(returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1))
    
    const simulations = 10000
    const simulatedReturns = []
    
    // 蒙特卡洛模拟
    for (let i = 0; i < simulations; i++) {
      let cumulativeReturn = 0
      for (let j = 0; j < timeHorizon; j++) {
        const randomReturn = this.normalRandom() * std + mean
        cumulativeReturn += randomReturn
      }
      simulatedReturns.push(cumulativeReturn)
    }
    
    simulatedReturns.sort((a, b) => a - b)
    
    const var95Index = Math.floor((1 - 0.95) * simulations)
    const var99Index = Math.floor((1 - 0.99) * simulations)
    
    const var95 = -simulatedReturns[var95Index]
    const var99 = -simulatedReturns[var99Index]
    
    const es95 = -simulatedReturns.slice(0, var95Index + 1).reduce((sum, ret) => sum + ret, 0) / (var95Index + 1)
    const es99 = -simulatedReturns.slice(0, var99Index + 1).reduce((sum, ret) => sum + ret, 0) / (var99Index + 1)
    
    return {
      var95,
      var99,
      expectedShortfall95: es95,
      expectedShortfall99: es99,
      confidence: 0.85,
      timeHorizon,
      method: 'montecarlo'
    }
  }

  /**
   * 压力测试
   */
  static performStressTest(
    portfolioData: { symbol: string, weight: number, returns: number[] }[],
    scenarios: StressTestScenario[]
  ): StressTestResult[] {
    
    return scenarios.map(scenario => {
      let totalLoss = 0
      const positionLosses: { symbol: string, loss: number }[] = []
      
      portfolioData.forEach(position => {
        // 应用压力情景
        const stressedReturns = position.returns.map(ret => {
          let stressedReturn = ret
          
          // 市场冲击
          stressedReturn += scenario.marketShock
          
          // 板块冲击
          stressedReturn += scenario.sectorShock * 0.5 // 假设50%相关性
          
          // 波动率冲击
          stressedReturn *= scenario.volatilityMultiplier
          
          // 流动性冲击
          if (scenario.liquidityShock !== 0) {
            stressedReturn += scenario.liquidityShock * 0.1
          }
          
          return stressedReturn
        })
        
        const positionLoss = Math.min(...stressedReturns) * position.weight
        positionLosses.push({ symbol: position.symbol, loss: positionLoss })
        totalLoss += positionLoss
      })
      
      // 计算最大回撤
      const maxDrawdown = Math.abs(totalLoss)
      
      // 估算恢复时间 (简化)
      const recoveryTime = Math.max(30, maxDrawdown * 365)
      
      // 生存能力评分
      const survivabilityScore = Math.max(0, 100 - maxDrawdown * 100)
      
      // 识别关键风险头寸
      const criticalPositions = positionLosses
        .filter(p => Math.abs(p.loss) > 0.05)
        .map(p => p.symbol)
      
      return {
        scenario,
        portfolioLoss: totalLoss,
        maxDrawdown,
        recoveryTime,
        survivabilityScore,
        criticalPositions
      }
    })
  }

  /**
   * 计算风险指标
   */
  static calculateRiskMetrics(returns: number[], benchmarkReturns?: number[]): RiskMetrics {
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1)
    const volatility = Math.sqrt(variance) * Math.sqrt(252) // 年化波动率
    
    // 夏普比率 (假设无风险利率为2%)
    const riskFreeRate = 0.02
    const sharpeRatio = (mean * 252 - riskFreeRate) / volatility
    
    // 索提诺比率
    const downsideReturns = returns.filter(ret => ret < mean)
    const downsideDeviation = downsideReturns.length > 0 
      ? Math.sqrt(downsideReturns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / downsideReturns.length) * Math.sqrt(252)
      : 0
    const sortinoRatio = downsideDeviation > 0 ? (mean * 252 - riskFreeRate) / downsideDeviation : 0
    
    // 最大回撤
    const maxDrawdown = this.calculateMaxDrawdown(returns)
    
    // 卡尔玛比率
    const calmarRatio = maxDrawdown !== 0 ? (mean * 252) / Math.abs(maxDrawdown) : 0
    
    // Beta和Alpha (如果有基准)
    let beta = 1
    let alpha = 0
    let trackingError = 0
    let informationRatio = 0
    
    if (benchmarkReturns && benchmarkReturns.length === returns.length) {
      const benchmarkMean = benchmarkReturns.reduce((sum, ret) => sum + ret, 0) / benchmarkReturns.length
      const covariance = returns.reduce((sum, ret, i) => 
        sum + (ret - mean) * (benchmarkReturns[i] - benchmarkMean), 0) / (returns.length - 1)
      const benchmarkVariance = benchmarkReturns.reduce((sum, ret) => 
        sum + Math.pow(ret - benchmarkMean, 2), 0) / (benchmarkReturns.length - 1)
      
      beta = covariance / benchmarkVariance
      alpha = (mean - riskFreeRate / 252) - beta * (benchmarkMean - riskFreeRate / 252)
      
      const excessReturns = returns.map((ret, i) => ret - benchmarkReturns[i])
      const trackingErrorVariance = excessReturns.reduce((sum, ret) => 
        sum + Math.pow(ret, 2), 0) / (excessReturns.length - 1)
      trackingError = Math.sqrt(trackingErrorVariance) * Math.sqrt(252)
      
      const excessMean = excessReturns.reduce((sum, ret) => sum + ret, 0) / excessReturns.length
      informationRatio = trackingError !== 0 ? (excessMean * 252) / trackingError : 0
    }
    
    return {
      volatility,
      sharpeRatio,
      sortinoRatio,
      maxDrawdown,
      calmarRatio,
      beta,
      alpha: alpha * 252,
      trackingError,
      informationRatio,
      downside_deviation: downsideDeviation
    }
  }

  /**
   * 动态止损计算
   */
  static calculateDynamicStopLoss(
    data: PriceData[],
    entryPrice: number,
    positionSize: number,
    riskTolerance: number = 0.02
  ): DynamicStopLoss {
    
    const currentPrice = data[data.length - 1].close
    const atr = this.calculateATR(data, 14)
    const volatility = this.calculateVolatility(data.slice(-20))
    
    // 固定止损 (基于风险容忍度)
    const fixedStopPrice = entryPrice * (1 - riskTolerance)
    
    // 追踪止损
    const trailingStopPrice = currentPrice * (1 - riskTolerance)
    
    // ATR止损
    const atrMultiplier = 2
    const atrStopPrice = currentPrice - (atr * atrMultiplier)
    
    // 波动率止损
    const volatilityMultiplier = 2
    const volatilityStopPrice = currentPrice - (currentPrice * volatility * volatilityMultiplier)
    
    // 推荐止损价格 (选择最保守的)
    const recommendedStop = Math.max(fixedStopPrice, atrStopPrice, volatilityStopPrice)
    
    // 风险金额和百分比
    const riskAmount = (entryPrice - recommendedStop) * positionSize
    const riskPercentage = (entryPrice - recommendedStop) / entryPrice
    
    // 确定止损类型
    let stopType: 'fixed' | 'trailing' | 'atr' | 'volatility' = 'fixed'
    if (recommendedStop === atrStopPrice) stopType = 'atr'
    else if (recommendedStop === volatilityStopPrice) stopType = 'volatility'
    else if (recommendedStop === trailingStopPrice) stopType = 'trailing'
    
    return {
      currentStopPrice: fixedStopPrice,
      trailingStopPrice,
      atrStopPrice,
      volatilityStopPrice,
      recommendedStop,
      stopType,
      riskAmount,
      riskPercentage
    }
  }

  /**
   * 生成风险预警
   */
  static generateRiskAlerts(
    varResult: VaRResult,
    riskMetrics: RiskMetrics,
    portfolioRisk: PortfolioRisk
  ): RiskAlert[] {
    
    const alerts: RiskAlert[] = []
    const now = new Date().toISOString()
    
    // VaR突破预警
    if (varResult.var95 > 0.05) {
      alerts.push({
        type: 'var_breach',
        severity: varResult.var95 > 0.1 ? 'critical' : 'high',
        message: `95% VaR超过${(varResult.var95 * 100).toFixed(2)}%`,
        recommendation: '考虑减少风险敞口或增加对冲',
        timestamp: now
      })
    }
    
    // 最大回撤预警
    if (Math.abs(riskMetrics.maxDrawdown) > 0.15) {
      alerts.push({
        type: 'drawdown_limit',
        severity: Math.abs(riskMetrics.maxDrawdown) > 0.25 ? 'critical' : 'high',
        message: `最大回撤达到${(Math.abs(riskMetrics.maxDrawdown) * 100).toFixed(2)}%`,
        recommendation: '检查投资组合配置，考虑止损',
        timestamp: now
      })
    }
    
    // 集中度风险预警
    if (portfolioRisk.concentrationRisk > 0.3) {
      alerts.push({
        type: 'concentration',
        severity: portfolioRisk.concentrationRisk > 0.5 ? 'high' : 'medium',
        message: '投资组合集中度过高',
        recommendation: '增加分散化投资',
        timestamp: now
      })
    }
    
    return alerts
  }

  // 辅助方法
  private static calculateMaxDrawdown(returns: number[]): number {
    let maxDrawdown = 0
    let peak = 0
    let cumulativeReturn = 0
    
    for (const ret of returns) {
      cumulativeReturn += ret
      peak = Math.max(peak, cumulativeReturn)
      const drawdown = (cumulativeReturn - peak) / (1 + peak)
      maxDrawdown = Math.min(maxDrawdown, drawdown)
    }
    
    return maxDrawdown
  }

  private static calculateATR(data: PriceData[], period: number): number {
    if (data.length < period + 1) return 0
    
    const trueRanges = []
    for (let i = 1; i < data.length; i++) {
      const high = data[i].high
      const low = data[i].low
      const prevClose = data[i - 1].close
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      )
      trueRanges.push(tr)
    }
    
    return trueRanges.slice(-period).reduce((sum, tr) => sum + tr, 0) / period
  }

  private static calculateVolatility(data: PriceData[]): number {
    const returns = []
    for (let i = 1; i < data.length; i++) {
      returns.push((data[i].close - data[i - 1].close) / data[i - 1].close)
    }
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
    
    return Math.sqrt(variance)
  }

  private static normalRandom(): number {
    // Box-Muller变换生成正态分布随机数
    let u = 0, v = 0
    while (u === 0) u = Math.random()
    while (v === 0) v = Math.random()
    return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v)
  }
}

export default RiskManagement
