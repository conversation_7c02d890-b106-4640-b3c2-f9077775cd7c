import React, { useState, useEffect, useCallback } from 'react'
import { Card, Row, Col, Statistic, Progress, Tag, Space, Button, Alert } from 'antd'
import { 
  DashboardOutlined, 
  ClockCircleOutlined, 
  DatabaseOutlined,
  WifiOutlined,
  ReloadOutlined,
  WarningOutlined,
} from '@ant-design/icons'

interface PerformanceMetrics {
  apiResponseTime: number
  memoryUsage: number
  cacheHitRate: number
  activeConnections: number
  requestsPerSecond: number
  errorRate: number
  lastUpdate: Date
}

interface PerformanceMonitorProps {
  autoRefresh?: boolean
  refreshInterval?: number
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  autoRefresh = true,
  refreshInterval = 5000,
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    apiResponseTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    activeConnections: 0,
    requestsPerSecond: 0,
    errorRate: 0,
    lastUpdate: new Date(),
  })
  
  const [loading, setLoading] = useState(false)
  const [isHealthy, setIsHealthy] = useState(true)

  // 模拟性能数据获取
  const fetchMetrics = useCallback(async () => {
    setLoading(true)
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // 生成模拟性能数据
      const newMetrics: PerformanceMetrics = {
        apiResponseTime: Math.random() * 200 + 50, // 50-250ms
        memoryUsage: Math.random() * 40 + 30, // 30-70%
        cacheHitRate: Math.random() * 20 + 80, // 80-100%
        activeConnections: Math.floor(Math.random() * 100 + 10), // 10-110
        requestsPerSecond: Math.random() * 50 + 10, // 10-60 RPS
        errorRate: Math.random() * 2, // 0-2%
        lastUpdate: new Date(),
      }
      
      setMetrics(newMetrics)
      
      // 健康状态检查
      const healthy = 
        newMetrics.apiResponseTime < 500 &&
        newMetrics.memoryUsage < 80 &&
        newMetrics.errorRate < 5
      
      setIsHealthy(healthy)
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // 自动刷新
  useEffect(() => {
    fetchMetrics()
    
    if (!autoRefresh) return

    const interval = setInterval(fetchMetrics, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchMetrics])

  // 获取性能等级颜色
  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return '#52c41a'
    if (value <= thresholds.warning) return '#faad14'
    return '#ff4d4f'
  }

  // 获取进度条颜色
  const getProgressColor = (value: number, reverse = false) => {
    if (reverse) {
      if (value >= 90) return '#52c41a'
      if (value >= 70) return '#faad14'
      return '#ff4d4f'
    } else {
      if (value <= 30) return '#52c41a'
      if (value <= 70) return '#faad14'
      return '#ff4d4f'
    }
  }

  return (
    <Card
      title={
        <Space>
          <DashboardOutlined />
          系统性能监控
          <Tag color={isHealthy ? 'green' : 'red'}>
            {isHealthy ? '健康' : '警告'}
          </Tag>
        </Space>
      }
      extra={
        <Space>
          <Tag icon={<ClockCircleOutlined />}>
            {metrics.lastUpdate.toLocaleTimeString()}
          </Tag>
          <Button 
            size="small" 
            icon={<ReloadOutlined />} 
            loading={loading}
            onClick={fetchMetrics}
          >
            刷新
          </Button>
        </Space>
      }
      size="small"
    >
      {!isHealthy && (
        <Alert
          message="系统性能警告"
          description="检测到性能指标异常，请检查系统状态"
          type="warning"
          icon={<WarningOutlined />}
          style={{ marginBottom: 16 }}
          showIcon
        />
      )}

      <Row gutter={[16, 16]}>
        {/* API响应时间 */}
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="API响应时间"
              value={metrics.apiResponseTime}
              precision={0}
              suffix="ms"
              valueStyle={{
                color: getPerformanceColor(metrics.apiResponseTime, { good: 100, warning: 300 })
              }}
            />
            <Progress
              percent={Math.min((metrics.apiResponseTime / 500) * 100, 100)}
              strokeColor={getPerformanceColor(metrics.apiResponseTime, { good: 100, warning: 300 })}
              size="small"
              showInfo={false}
            />
          </Card>
        </Col>

        {/* 内存使用率 */}
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="内存使用率"
              value={metrics.memoryUsage}
              precision={1}
              suffix="%"
              valueStyle={{
                color: getProgressColor(metrics.memoryUsage)
              }}
            />
            <Progress
              percent={metrics.memoryUsage}
              strokeColor={getProgressColor(metrics.memoryUsage)}
              size="small"
              showInfo={false}
            />
          </Card>
        </Col>

        {/* 缓存命中率 */}
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="缓存命中率"
              value={metrics.cacheHitRate}
              precision={1}
              suffix="%"
              valueStyle={{
                color: getProgressColor(metrics.cacheHitRate, true)
              }}
            />
            <Progress
              percent={metrics.cacheHitRate}
              strokeColor={getProgressColor(metrics.cacheHitRate, true)}
              size="small"
              showInfo={false}
            />
          </Card>
        </Col>

        {/* 错误率 */}
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="错误率"
              value={metrics.errorRate}
              precision={2}
              suffix="%"
              valueStyle={{
                color: getPerformanceColor(metrics.errorRate, { good: 1, warning: 3 })
              }}
            />
            <Progress
              percent={Math.min(metrics.errorRate * 20, 100)} // 5%错误率对应100%进度
              strokeColor={getPerformanceColor(metrics.errorRate, { good: 1, warning: 3 })}
              size="small"
              showInfo={false}
            />
          </Card>
        </Col>

        {/* 活跃连接数 */}
        <Col span={12}>
          <Card size="small" title="活跃连接数">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="当前连接"
                  value={metrics.activeConnections}
                  prefix={<WifiOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="请求/秒"
                  value={metrics.requestsPerSecond}
                  precision={1}
                  suffix="RPS"
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 系统状态总览 */}
        <Col span={12}>
          <Card size="small" title="系统状态">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>前端服务:</span>
                <Tag color="green">运行中</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>后端API:</span>
                <Tag color="green">运行中</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>数据库:</span>
                <Tag color="green">连接正常</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>实时数据:</span>
                <Tag color="blue">模拟模式</Tag>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 性能建议 */}
      <Card size="small" title="性能建议" style={{ marginTop: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {metrics.apiResponseTime > 300 && (
            <Alert
              message="API响应时间较慢"
              description="建议检查网络连接或优化后端查询"
              type="warning"
              size="small"
            />
          )}
          {metrics.memoryUsage > 80 && (
            <Alert
              message="内存使用率过高"
              description="建议清理缓存或重启服务"
              type="error"
              size="small"
            />
          )}
          {metrics.cacheHitRate < 70 && (
            <Alert
              message="缓存命中率较低"
              description="建议优化缓存策略或增加缓存时间"
              type="info"
              size="small"
            />
          )}
          {metrics.errorRate > 2 && (
            <Alert
              message="错误率偏高"
              description="建议检查系统日志和错误处理"
              type="error"
              size="small"
            />
          )}
        </Space>
      </Card>
    </Card>
  )
}

export default PerformanceMonitor
