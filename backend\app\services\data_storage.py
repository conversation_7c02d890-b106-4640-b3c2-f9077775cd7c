"""
数据存储服务模块
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, date
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.dialects.sqlite import insert

from app.core.logging import get_logger
from app.models.stock import Stock, KlineData, RealtimeData
from app.core.database import AsyncSessionLocal

logger = get_logger(__name__)


class DataStorageService:
    """数据存储服务"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            if exc_type:
                await self.session.rollback()
            else:
                await self.session.commit()
            await self.session.close()
    
    async def save_stocks(self, stocks_data: List[Dict[str, Any]]) -> int:
        """保存股票基本信息"""
        try:
            logger.info(f"开始保存 {len(stocks_data)} 条股票信息...")
            
            saved_count = 0
            for stock_data in stocks_data:
                try:
                    # 检查股票是否已存在
                    stmt = select(Stock).where(Stock.stock_code == stock_data['stock_code'])
                    result = await self.session.execute(stmt)
                    existing_stock = result.scalar_one_or_none()
                    
                    if existing_stock:
                        # 更新现有股票信息
                        existing_stock.stock_name = stock_data.get('stock_name', existing_stock.stock_name)
                        existing_stock.market = stock_data.get('market', existing_stock.market)
                        existing_stock.industry = stock_data.get('industry', existing_stock.industry)
                        existing_stock.concept = stock_data.get('concept', existing_stock.concept)
                        existing_stock.updated_at = datetime.utcnow()
                    else:
                        # 创建新股票记录
                        new_stock = Stock(
                            stock_code=stock_data['stock_code'],
                            stock_name=stock_data['stock_name'],
                            market=stock_data['market'],
                            industry=stock_data.get('industry'),
                            concept=stock_data.get('concept'),
                            list_date=stock_data.get('list_date'),
                            is_active=True
                        )
                        self.session.add(new_stock)
                    
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"保存股票 {stock_data.get('stock_code')} 失败: {e}")
                    continue
            
            await self.session.commit()
            logger.info(f"成功保存 {saved_count} 条股票信息")
            return saved_count
            
        except Exception as e:
            logger.error(f"批量保存股票信息失败: {e}")
            await self.session.rollback()
            return 0
    
    async def save_kline_data(self, kline_data: List[Dict[str, Any]]) -> int:
        """保存K线数据"""
        try:
            logger.info(f"开始保存 {len(kline_data)} 条K线数据...")
            
            saved_count = 0
            for data in kline_data:
                try:
                    # 检查K线数据是否已存在
                    stmt = select(KlineData).where(
                        and_(
                            KlineData.stock_code == data['stock_code'],
                            KlineData.trade_date == data['trade_date'],
                            KlineData.period == data['period']
                        )
                    )
                    result = await self.session.execute(stmt)
                    existing_kline = result.scalar_one_or_none()
                    
                    if existing_kline:
                        # 更新现有K线数据
                        existing_kline.open_price = data['open_price']
                        existing_kline.high_price = data['high_price']
                        existing_kline.low_price = data['low_price']
                        existing_kline.close_price = data['close_price']
                        existing_kline.volume = data['volume']
                        existing_kline.turnover = data['turnover']
                        existing_kline.change = data.get('change', 0)
                        existing_kline.change_percent = data.get('change_percent', 0)
                    else:
                        # 创建新K线记录
                        new_kline = KlineData(
                            stock_code=data['stock_code'],
                            trade_date=data['trade_date'],
                            period=data['period'],
                            open_price=data['open_price'],
                            high_price=data['high_price'],
                            low_price=data['low_price'],
                            close_price=data['close_price'],
                            volume=data['volume'],
                            turnover=data['turnover'],
                            change=data.get('change', 0),
                            change_percent=data.get('change_percent', 0)
                        )
                        self.session.add(new_kline)
                    
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"保存K线数据失败: {e}")
                    continue
            
            await self.session.commit()
            logger.info(f"成功保存 {saved_count} 条K线数据")
            return saved_count
            
        except Exception as e:
            logger.error(f"批量保存K线数据失败: {e}")
            await self.session.rollback()
            return 0
    
    async def save_realtime_data(self, realtime_data: List[Dict[str, Any]]) -> int:
        """保存实时数据"""
        try:
            logger.info(f"开始保存 {len(realtime_data)} 条实时数据...")
            
            saved_count = 0
            for data in realtime_data:
                try:
                    # 实时数据直接插入，不检查重复
                    new_realtime = RealtimeData(
                        stock_code=data['stock_code'],
                        current_price=data['current_price'],
                        change=data.get('change', 0),
                        change_percent=data.get('change_percent', 0),
                        volume=data.get('volume', 0),
                        turnover=data.get('turnover', 0),
                        bid_price=data.get('bid_price', 0),
                        ask_price=data.get('ask_price', 0),
                        bid_volume=data.get('bid_volume', 0),
                        ask_volume=data.get('ask_volume', 0),
                        timestamp=data.get('timestamp', datetime.utcnow())
                    )
                    self.session.add(new_realtime)
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"保存实时数据失败: {e}")
                    continue
            
            await self.session.commit()
            logger.info(f"成功保存 {saved_count} 条实时数据")
            return saved_count
            
        except Exception as e:
            logger.error(f"批量保存实时数据失败: {e}")
            await self.session.rollback()
            return 0
    
    async def get_stock_list(
        self,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        is_active: bool = True,
        limit: int = 100,
        offset: int = 0
    ) -> List[Stock]:
        """获取股票列表"""
        try:
            stmt = select(Stock).where(Stock.is_active == is_active)

            if market:
                stmt = stmt.where(Stock.market == market.upper())
            if industry:
                stmt = stmt.where(Stock.industry == industry)

            stmt = stmt.order_by(Stock.stock_code).offset(offset).limit(limit)

            result = await self.session.execute(stmt)
            stocks = result.scalars().all()
            return stocks

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    async def get_stock_by_code(self, stock_code: str) -> Optional[Stock]:
        """根据股票代码查询股票信息"""
        try:
            stmt = select(Stock).where(
                and_(Stock.stock_code == stock_code, Stock.is_active == True)
            )
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"查询股票 {stock_code} 失败: {e}")
            return None

    async def get_stocks(
        self,
        market: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Stock]:
        """查询股票列表"""
        try:
            stmt = select(Stock).where(Stock.is_active == True)

            if market:
                stmt = stmt.where(Stock.market == market.upper())

            stmt = stmt.offset(offset).limit(limit)

            result = await self.session.execute(stmt)
            stocks = result.scalars().all()
            
            return list(stocks)
            
        except Exception as e:
            logger.error(f"查询股票列表失败: {e}")
            return []

    async def search_stocks(self, query: str, limit: int = 20) -> List[Stock]:
        """搜索股票（按代码或名称）"""
        try:
            # 构建搜索条件：股票代码或名称包含查询关键词
            stmt = select(Stock).where(
                and_(
                    Stock.is_active == True,
                    or_(
                        Stock.stock_code.ilike(f"%{query}%"),
                        Stock.stock_name.ilike(f"%{query}%")
                    )
                )
            ).order_by(Stock.stock_code).limit(limit)

            result = await self.session.execute(stmt)
            stocks = result.scalars().all()

            return list(stocks)

        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            return []

    async def get_kline_data(
        self,
        stock_code: str,
        period: str = "daily",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100
    ) -> List[KlineData]:
        """查询K线数据"""
        try:
            stmt = select(KlineData).where(
                and_(
                    KlineData.stock_code == stock_code,
                    KlineData.period == period
                )
            )
            
            if start_date:
                stmt = stmt.where(KlineData.trade_date >= start_date)
            if end_date:
                stmt = stmt.where(KlineData.trade_date <= end_date)
            
            stmt = stmt.order_by(desc(KlineData.trade_date)).limit(limit)
            
            result = await self.session.execute(stmt)
            klines = result.scalars().all()
            
            return list(reversed(klines))  # 按日期正序返回
            
        except Exception as e:
            logger.error(f"查询K线数据失败: {e}")
            return []
    
    async def get_latest_realtime_data(self, stock_codes: List[str]) -> List[RealtimeData]:
        """获取最新实时数据"""
        try:
            # 子查询：获取每只股票的最新时间戳
            subquery = (
                select(
                    RealtimeData.stock_code,
                    func.max(RealtimeData.timestamp).label('max_timestamp')
                )
                .where(RealtimeData.stock_code.in_(stock_codes))
                .group_by(RealtimeData.stock_code)
                .subquery()
            )
            
            # 主查询：根据最新时间戳获取实时数据
            stmt = (
                select(RealtimeData)
                .join(
                    subquery,
                    and_(
                        RealtimeData.stock_code == subquery.c.stock_code,
                        RealtimeData.timestamp == subquery.c.max_timestamp
                    )
                )
            )
            
            result = await self.session.execute(stmt)
            realtime_data = result.scalars().all()
            
            return list(realtime_data)
            
        except Exception as e:
            logger.error(f"获取最新实时数据失败: {e}")
            return []
    
    async def get_stock_count(self, market: Optional[str] = None) -> int:
        """获取股票总数"""
        try:
            stmt = select(func.count(Stock.id)).where(Stock.is_active == True)
            
            if market:
                stmt = stmt.where(Stock.market == market.upper())
            
            result = await self.session.execute(stmt)
            count = result.scalar()
            
            return count or 0
            
        except Exception as e:
            logger.error(f"获取股票总数失败: {e}")
            return 0
