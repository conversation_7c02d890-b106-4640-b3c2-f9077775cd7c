import React, { useState } from 'react'
import {
  Switch,
  <PERSON>lt<PERSON>,
  Badge,
  Popover,
  Card,
  Space,
  Typography,
  Button,
  Divider,
  Tag,
} from 'antd'
import {
  DatabaseOutlined,
  CloudOutlined,
  SettingOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons'
import { useDataSourceSettings, useSettingsActions } from '@/stores/settingsStore'
import { getConfigInfo } from '@/config/dataConfig'

const { Text } = Typography

interface QuickDataSourceSwitchProps {
  size?: 'small' | 'middle' | 'large'
  showLabel?: boolean
  placement?: 'top' | 'bottom' | 'left' | 'right'
}

const QuickDataSourceSwitch: React.FC<QuickDataSourceSwitchProps> = ({
  size = 'middle',
  showLabel = true,
  placement = 'bottom'
}) => {
  const [popoverVisible, setPopoverVisible] = useState(false)
  const dataSourceSettings = useDataSourceSettings()
  const { updateDataSourceSettings } = useSettingsActions()
  const configInfo = getConfigInfo()

  const handleSwitchChange = (checked: boolean) => {
    updateDataSourceSettings({ useRealAPI: checked })
  }

  const getStatusColor = () => {
    if (dataSourceSettings.useRealAPI) {
      return 'success'
    }
    return 'default'
  }

  const getStatusText = () => {
    if (dataSourceSettings.useRealAPI) {
      return '真实API'
    }
    return '模拟数据'
  }

  const popoverContent = (
    <Card size="small" style={{ width: 300 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>当前数据源配置</Text>
        </div>
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>数据源类型:</Text>
          <Tag color={dataSourceSettings.useRealAPI ? 'green' : 'blue'}>
            {getStatusText()}
          </Tag>
        </div>

        {dataSourceSettings.useRealAPI && (
          <>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>API地址:</Text>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {dataSourceSettings.apiBaseUrl}
              </Text>
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>WebSocket:</Text>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {dataSourceSettings.wsUrl}
              </Text>
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>超时时间:</Text>
              <Text type="secondary">
                {dataSourceSettings.timeout / 1000}秒
              </Text>
            </div>
          </>
        )}

        {!dataSourceSettings.useRealAPI && (
          <div style={{ padding: '8px', background: '#f6f8fa', borderRadius: '4px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              当前使用模拟数据，适合开发和测试环境。
              切换到真实API可获取实时市场数据。
            </Text>
          </div>
        )}

        <Divider style={{ margin: '8px 0' }} />
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>快速切换:</Text>
          <Switch
            size="small"
            checked={dataSourceSettings.useRealAPI}
            onChange={handleSwitchChange}
            checkedChildren="API"
            unCheckedChildren="模拟"
          />
        </div>

        <Button 
          type="link" 
          size="small" 
          icon={<SettingOutlined />}
          onClick={() => {
            setPopoverVisible(false)
            // 这里可以导航到设置页面
            window.location.href = '/data-settings'
          }}
          style={{ padding: 0 }}
        >
          详细设置
        </Button>
      </Space>
    </Card>
  )

  return (
    <Popover
      content={popoverContent}
      title="数据源设置"
      trigger="click"
      placement={placement}
      open={popoverVisible}
      onOpenChange={setPopoverVisible}
    >
      <div style={{ cursor: 'pointer', display: 'inline-flex', alignItems: 'center' }}>
        <Badge status={getStatusColor()} />
        <Space size="small">
          {dataSourceSettings.useRealAPI ? (
            <CloudOutlined style={{ color: '#52c41a' }} />
          ) : (
            <DatabaseOutlined style={{ color: '#1890ff' }} />
          )}
          {showLabel && (
            <Text style={{ fontSize: size === 'small' ? '12px' : '14px' }}>
              {getStatusText()}
            </Text>
          )}
          <Tooltip title="点击查看和切换数据源">
            <InfoCircleOutlined style={{ color: '#8c8c8c', fontSize: '12px' }} />
          </Tooltip>
        </Space>
      </div>
    </Popover>
  )
}

export default QuickDataSourceSwitch
