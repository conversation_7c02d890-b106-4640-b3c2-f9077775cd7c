#!/usr/bin/env python3
"""
通过API强制更新数据
"""

import requests
import json
import time

def force_update_spot_data():
    """强制更新实时行情数据"""
    url = "http://localhost:8000/api/v1/data-management/akshare/update-spot-data"
    
    try:
        print("🚀 开始调用实时行情数据更新API...")
        response = requests.post(url, timeout=120)  # 增加超时时间到120秒
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"📝 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📝 错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API调用超时，但后台任务可能仍在执行")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def check_data_after_update():
    """检查更新后的数据"""
    url = "http://localhost:8000/api/v1/akshare/spot-data"
    
    try:
        print("\n🔍 检查更新后的数据...")
        response = requests.get(f"{url}?limit=3", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                print("✅ 成功获取数据!")
                print(f"📊 数据条数: {len(data)}")
                
                # 检查是否是真实数据
                first_stock = data[0]
                if first_stock.get("current_price") != 10.5:  # 模拟数据的固定价格
                    print("🎉 数据库已更新为真实数据!")
                    for i, stock in enumerate(data[:3]):
                        print(f"  {i+1}. {stock.get('stock_name')} ({stock.get('stock_code')}): ¥{stock.get('current_price')}")
                    return True
                else:
                    print("⚠️  数据库仍然是模拟数据")
                    return False
            else:
                print("❌ 没有获取到数据")
                return False
        else:
            print(f"❌ 获取数据失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔄 强制更新股票数据")
    print("=" * 80)
    
    # 1. 强制更新数据
    update_success = force_update_spot_data()
    
    if update_success:
        # 等待一段时间让更新完成
        print("\n⏳ 等待30秒让更新任务完成...")
        time.sleep(30)
        
        # 2. 检查更新结果
        data_success = check_data_after_update()
        
        if data_success:
            print("\n🎉 数据更新成功! 前端现在应该显示真实的股票数据了。")
        else:
            print("\n⚠️  数据更新可能仍在进行中，请稍后再检查。")
    else:
        print("\n❌ 数据更新失败，请检查后端服务状态。")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()
