// 数据完整性管理服务
export interface StockDataIntegrity {
  stock_code: string
  stock_name: string
  market: string
  list_date?: string
  
  // 日K数据状态
  daily_data_start?: string
  daily_data_end?: string
  daily_data_count: number
  daily_missing_days: number
  daily_completeness: number
  
  // 周K数据状态
  weekly_data_start?: string
  weekly_data_end?: string
  weekly_data_count: number
  weekly_missing_weeks: number
  weekly_completeness: number
  
  // 月K数据状态
  monthly_data_start?: string
  monthly_data_end?: string
  monthly_data_count: number
  monthly_missing_months: number
  monthly_completeness: number
  
  // 整体状态
  overall_status: 'complete' | 'partial' | 'missing'
  last_update?: string
}

export interface DataIntegrityStats {
  total_stocks: number
  complete_stocks: number
  partial_stocks: number
  missing_stocks: number
  
  // 数据覆盖统计
  daily_coverage: number
  weekly_coverage: number
  monthly_coverage: number
  
  // 时间范围
  earliest_date?: string
  latest_date?: string
  
  // 更新统计
  last_check_time: string
}

export interface DataUpdateRequest {
  update_type: 'fill_missing' | 'specific_stocks' | 'date_range' | 'full_update'
  stock_codes?: string[]
  start_date?: string
  end_date?: string
  periods?: ('daily' | 'weekly' | 'monthly')[]
  force_update?: boolean
}

export interface DataUpdateResponse {
  message: string
  task_id: string
  update_type: string
}

class DataIntegrityService {
  private baseUrl = '/api/v1/data-management'

  // 获取数据完整性统计
  async getDataIntegrityStats(): Promise<DataIntegrityStats> {
    const response = await fetch(`${this.baseUrl}/data-integrity/stats`)
    if (!response.ok) {
      throw new Error(`获取数据完整性统计失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  // 获取股票数据完整性列表
  async getStocksDataIntegrity(
    page: number = 1,
    pageSize: number = 50,
    stockCode?: string,
    statusFilter?: 'complete' | 'partial' | 'missing'
  ): Promise<StockDataIntegrity[]> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString()
    })
    
    if (stockCode) {
      params.append('stock_code', stockCode)
    }
    
    if (statusFilter) {
      params.append('status_filter', statusFilter)
    }

    const response = await fetch(`${this.baseUrl}/data-integrity/stocks?${params}`)
    if (!response.ok) {
      throw new Error(`获取股票数据完整性失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  // 执行数据更新
  async updateStockData(request: DataUpdateRequest): Promise<DataUpdateResponse> {
    const response = await fetch(`${this.baseUrl}/data-integrity/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    })
    
    if (!response.ok) {
      throw new Error(`数据更新失败: ${response.status} ${response.statusText}`)
    }
    return await response.json()
  }

  // 补缺数据更新
  async fillMissingData(
    stockCodes?: string[],
    periods?: ('daily' | 'weekly' | 'monthly')[],
    startDate?: string,
    endDate?: string
  ): Promise<DataUpdateResponse> {
    return this.updateStockData({
      update_type: 'fill_missing',
      stock_codes: stockCodes,
      periods: periods,
      start_date: startDate,
      end_date: endDate
    })
  }

  // 指定股票更新
  async updateSpecificStocks(
    stockCodes: string[],
    periods?: ('daily' | 'weekly' | 'monthly')[],
    startDate?: string,
    endDate?: string,
    forceUpdate?: boolean
  ): Promise<DataUpdateResponse> {
    return this.updateStockData({
      update_type: 'specific_stocks',
      stock_codes: stockCodes,
      periods: periods,
      start_date: startDate,
      end_date: endDate,
      force_update: forceUpdate
    })
  }

  // 指定时间范围更新
  async updateDateRange(
    startDate: string,
    endDate: string,
    stockCodes?: string[],
    periods?: ('daily' | 'weekly' | 'monthly')[],
    forceUpdate?: boolean
  ): Promise<DataUpdateResponse> {
    return this.updateStockData({
      update_type: 'date_range',
      stock_codes: stockCodes,
      start_date: startDate,
      end_date: endDate,
      periods: periods,
      force_update: forceUpdate
    })
  }

  // 全量数据更新
  async fullDataUpdate(
    periods?: ('daily' | 'weekly' | 'monthly')[],
    startDate?: string,
    endDate?: string
  ): Promise<DataUpdateResponse> {
    return this.updateStockData({
      update_type: 'full_update',
      periods: periods,
      start_date: startDate,
      end_date: endDate,
      force_update: true
    })
  }

  // 获取状态显示文本
  getStatusText(status: string): string {
    const statusMap = {
      'complete': '完整',
      'partial': '部分',
      'missing': '缺失'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }

  // 获取状态颜色
  getStatusColor(status: string): string {
    const colorMap = {
      'complete': '#52c41a',
      'partial': '#faad14',
      'missing': '#ff4d4f'
    }
    return colorMap[status as keyof typeof colorMap] || '#d9d9d9'
  }

  // 格式化完整性百分比
  formatCompleteness(completeness: number): string {
    return `${completeness.toFixed(1)}%`
  }

  // 格式化日期
  formatDate(dateStr?: string): string {
    if (!dateStr) return '-'
    return dateStr
  }

  // 获取数据范围描述
  getDataRangeDescription(start?: string, end?: string, count: number): string {
    if (!start || !end || count === 0) {
      return '无数据'
    }
    return `${start} 至 ${end} (${count}条)`
  }
}

export const dataIntegrityService = new DataIntegrityService()
export { DataIntegrityService }
