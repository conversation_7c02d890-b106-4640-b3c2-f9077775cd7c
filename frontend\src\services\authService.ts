import axios from 'axios'

const API_BASE_URL = 'http://localhost:8000/api/v1'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理token过期
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refresh_token')
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken
          })

          if (response.data.code === 200) {
            const { access_token, refresh_token } = response.data.data
            localStorage.setItem('access_token', access_token)
            localStorage.setItem('refresh_token', refresh_token)
            
            // 重新发送原请求
            originalRequest.headers.Authorization = `Bearer ${access_token}`
            return apiClient(originalRequest)
          }
        }
      } catch (refreshError) {
        // 刷新token失败，清除本地存储并跳转到登录页
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user')
        window.location.href = '/login'
      }
    }

    return Promise.reject(error)
  }
)

export interface LoginRequest {
  email?: string
  username?: string
  password: string
}

export interface RegisterRequest {
  username?: string
  email: string
  password: string
  full_name?: string
  phone?: string
}

export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  avatar?: string
  bio?: string
  location?: string
  is_verified: boolean
  email_verified: boolean
  phone_verified: boolean
  is_premium: boolean
  two_factor_enabled: boolean
  api_calls_count: number
  api_calls_limit: number
  created_at: string
  last_login?: string
}

export interface AuthResponse {
  code: number
  message: string
  data: {
    access_token: string
    refresh_token: string
    token_type: string
    expires_in: number
    user: User
  }
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export const authService = {
  // 用户登录
  async login(data: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post('/auth/login', data)
    return response.data
  },

  // 用户注册
  async register(data: RegisterRequest): Promise<ApiResponse> {
    const response = await apiClient.post('/auth/register', data)
    return response.data
  },

  // 刷新token
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await apiClient.post('/auth/refresh', {
      refresh_token: refreshToken
    })
    return response.data
  },

  // 用户登出
  async logout(): Promise<ApiResponse> {
    const response = await apiClient.post('/auth/logout')
    return response.data
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<ApiResponse<User>> {
    const response = await apiClient.get('/auth/me')
    return response.data
  },

  // 检查密码强度
  async checkPasswordStrength(password: string): Promise<ApiResponse> {
    const formData = new FormData()
    formData.append('password', password)
    
    const response = await apiClient.post('/auth/check-password', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  // 验证邮箱格式
  async validateEmail(email: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/auth/validate-email/${email}`)
    return response.data
  },

  // 验证用户名可用性
  async validateUsername(username: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/auth/validate-username/${username}`)
    return response.data
  },

  // 保存token到本地存储
  saveTokens(accessToken: string, refreshToken: string) {
    localStorage.setItem('access_token', accessToken)
    localStorage.setItem('refresh_token', refreshToken)
  },

  // 保存用户信息到本地存储
  saveUser(user: User) {
    localStorage.setItem('user', JSON.stringify(user))
  },

  // 从本地存储获取token
  getAccessToken(): string | null {
    return localStorage.getItem('access_token')
  },

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token')
  },

  // 从本地存储获取用户信息
  getUser(): User | null {
    const userStr = localStorage.getItem('user')
    return userStr ? JSON.parse(userStr) : null
  },

  // 清除本地存储
  clearStorage() {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
  },

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!this.getAccessToken()
  },
}

export default apiClient
