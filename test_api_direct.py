#!/usr/bin/env python3
"""
直接测试API
"""

import requests
import json
import time

def test_api():
    """测试API"""
    try:
        print("🔍 测试API连接...")
        
        # 测试根路径
        try:
            response = requests.get("http://localhost:8000/", timeout=5)
            print(f"✅ 根路径连接成功: {response.status_code}")
        except Exception as e:
            print(f"❌ 根路径连接失败: {e}")
            return False
        
        # 测试健康检查
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            print(f"✅ 健康检查: {response.status_code}")
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
        
        # 测试spot-data API
        try:
            url = "http://localhost:8000/api/v1/akshare/spot-data?limit=3"
            print(f"📡 调用API: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"✅ API调用成功: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"📊 返回数据: {len(data)} 条")
                
                for i, item in enumerate(data):
                    print(f"  {i+1}. {item.get('stock_name')} ({item.get('stock_code')}): ¥{item.get('current_price')}")
                
                # 检查是否是真实数据
                if data and float(data[0].get('current_price')) != 10.5:
                    print("🎉 API返回真实数据!")
                    return True
                else:
                    print("⚠️  API返回模拟数据")
                    return False
            else:
                print(f"❌ API返回错误状态码: {response.status_code}")
                print(f"错误内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 API直接测试工具")
    print("=" * 60)
    
    result = test_api()
    
    print("\n" + "=" * 60)
    print(f"📋 测试结果: {'✅ 成功' if result else '❌ 失败'}")
    print("=" * 60)
