/**
 * 性能分析组件
 * 分析加载性能并提供优化建议
 */

import React from 'react'
import { Card, Alert, List, Typography, Row, Col, Tag, Statistic, Space } from 'antd'
import { 
  ThunderboltOutlined, 
  ClockCircleOutlined, 
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { LoadingStep } from '../loading/StockAnalysisLoader'

const { Text, Title } = Typography

interface PerformanceAnalyzerProps {
  steps: LoadingStep[]
  visible: boolean
}

interface PerformanceMetrics {
  totalDuration: number
  averageDuration: number
  slowestStep: LoadingStep | null
  fastestStep: LoadingStep | null
  errorCount: number
  successRate: number
  recommendations: string[]
}

const PerformanceAnalyzer: React.FC<PerformanceAnalyzerProps> = ({
  steps,
  visible
}) => {
  if (!visible) return null

  const calculateMetrics = (): PerformanceMetrics => {
    const completedSteps = steps.filter(step => step.duration !== undefined)
    const errorSteps = steps.filter(step => step.status === 'error')
    
    if (completedSteps.length === 0) {
      return {
        totalDuration: 0,
        averageDuration: 0,
        slowestStep: null,
        fastestStep: null,
        errorCount: 0,
        successRate: 0,
        recommendations: []
      }
    }

    const totalDuration = completedSteps.reduce((sum, step) => sum + (step.duration || 0), 0)
    const averageDuration = totalDuration / completedSteps.length
    
    const slowestStep = completedSteps.reduce((prev, current) => 
      (current.duration || 0) > (prev.duration || 0) ? current : prev
    )
    
    const fastestStep = completedSteps.reduce((prev, current) => 
      (current.duration || 0) < (prev.duration || 0) ? current : prev
    )

    const successRate = ((steps.length - errorSteps.length) / steps.length) * 100

    // 生成优化建议
    const recommendations: string[] = []
    
    if (totalDuration > 10000) {
      recommendations.push('总加载时间超过10秒，建议启用数据缓存机制')
    }
    
    if (slowestStep && (slowestStep.duration || 0) > 3000) {
      recommendations.push(`${slowestStep.title}耗时较长，建议优化API响应速度`)
    }
    
    if (errorSteps.length > 0) {
      recommendations.push(`${errorSteps.length}个步骤失败，建议检查网络连接和API状态`)
    }
    
    if (successRate < 80) {
      recommendations.push('成功率较低，建议添加重试机制')
    }
    
    const apiSteps = completedSteps.filter(step => 
      ['kline', 'realtime', 'fundamentals'].includes(step.key)
    )
    const avgApiDuration = apiSteps.reduce((sum, step) => sum + (step.duration || 0), 0) / apiSteps.length
    
    if (avgApiDuration > 2000) {
      recommendations.push('API调用较慢，建议实现并行请求优化')
    }

    return {
      totalDuration,
      averageDuration,
      slowestStep,
      fastestStep,
      errorCount: errorSteps.length,
      successRate,
      recommendations
    }
  }

  const metrics = calculateMetrics()

  const getPerformanceLevel = () => {
    if (metrics.totalDuration < 3000 && metrics.successRate > 95) return 'excellent'
    if (metrics.totalDuration < 6000 && metrics.successRate > 90) return 'good'
    if (metrics.totalDuration < 10000 && metrics.successRate > 80) return 'fair'
    return 'poor'
  }

  const performanceLevel = getPerformanceLevel()
  const performanceConfig = {
    excellent: { color: '#52c41a', text: '优秀', icon: <CheckCircleOutlined /> },
    good: { color: '#1890ff', text: '良好', icon: <ThunderboltOutlined /> },
    fair: { color: '#faad14', text: '一般', icon: <WarningOutlined /> },
    poor: { color: '#ff4d4f', text: '较差', icon: <ExclamationCircleOutlined /> }
  }

  return (
    <Card
      title={
        <Space>
          <ThunderboltOutlined />
          <span>性能分析报告</span>
          <Tag color={performanceConfig[performanceLevel].color}>
            {performanceConfig[performanceLevel].icon}
            {performanceConfig[performanceLevel].text}
          </Tag>
        </Space>
      }
      size="small"
      style={{ marginTop: '16px' }}
    >
      {/* 性能指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Statistic
            title="总耗时"
            value={metrics.totalDuration}
            suffix="ms"
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: metrics.totalDuration > 10000 ? '#ff4d4f' : '#1890ff' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="平均耗时"
            value={Math.round(metrics.averageDuration)}
            suffix="ms"
            valueStyle={{ color: metrics.averageDuration > 2000 ? '#faad14' : '#52c41a' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="成功率"
            value={Math.round(metrics.successRate)}
            suffix="%"
            valueStyle={{ color: metrics.successRate > 90 ? '#52c41a' : '#ff4d4f' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="失败步骤"
            value={metrics.errorCount}
            valueStyle={{ color: metrics.errorCount > 0 ? '#ff4d4f' : '#52c41a' }}
          />
        </Col>
      </Row>

      {/* 最慢和最快步骤 */}
      {metrics.slowestStep && metrics.fastestStep && (
        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col span={12}>
            <Alert
              message="最慢步骤"
              description={
                <div>
                  <Text strong>{metrics.slowestStep.title}</Text>
                  <br />
                  <Text type="secondary">耗时: {metrics.slowestStep.duration}ms</Text>
                </div>
              }
              type="warning"
              size="small"
              showIcon
            />
          </Col>
          <Col span={12}>
            <Alert
              message="最快步骤"
              description={
                <div>
                  <Text strong>{metrics.fastestStep.title}</Text>
                  <br />
                  <Text type="secondary">耗时: {metrics.fastestStep.duration}ms</Text>
                </div>
              }
              type="success"
              size="small"
              showIcon
            />
          </Col>
        </Row>
      )}

      {/* 优化建议 */}
      {metrics.recommendations.length > 0 && (
        <div>
          <Title level={5} style={{ marginBottom: '12px' }}>
            <ExclamationCircleOutlined style={{ marginRight: '8px' }} />
            优化建议
          </Title>
          <List
            size="small"
            dataSource={metrics.recommendations}
            renderItem={(item, index) => (
              <List.Item>
                <Text>
                  {index + 1}. {item}
                </Text>
              </List.Item>
            )}
          />
        </div>
      )}
    </Card>
  )
}

export default PerformanceAnalyzer
