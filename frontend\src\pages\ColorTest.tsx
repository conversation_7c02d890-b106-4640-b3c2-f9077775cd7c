/**
 * 颜色测试页面
 * 用于验证股票涨跌颜色是否正确显示
 */

import React from 'react'
import { Card, Row, Col, Statistic, Tag, Progress, Alert, Space, Typography } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons'
import { STOCK_COLORS, getChangeColor, getAntdStatus } from '@/config/colors'

const { Text, Title } = Typography

const ColorTest: React.FC = () => {
  // 模拟股票数据
  const stockData = [
    { name: '平安银行', code: '000001', price: 12.34, change: -0.15, changePercent: -1.20 },
    { name: '万科A', code: '000002', price: 18.56, change: 0.23, changePercent: 1.25 },
    { name: '中国平安', code: '601318', price: 45.67, change: -1.23, changePercent: -2.62 },
    { name: '贵州茅台', code: '600519', price: 1678.90, change: 15.60, changePercent: 0.94 }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>颜色配置测试页面</Title>
      <Text type="secondary">验证股票涨跌颜色显示是否正确：上涨红色，下跌绿色</Text>
      
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        {/* 颜色配置说明 */}
        <Col span={24}>
          <Card title="颜色配置说明" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ padding: '16px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
                  <div style={{ color: STOCK_COLORS.UP, fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
                    <ArrowUpOutlined /> 上涨颜色 (红色)
                  </div>
                  <div>RGB: {STOCK_COLORS.UP}</div>
                  <div>用于：股价上涨、阳线、买入信号</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ padding: '16px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
                  <div style={{ color: STOCK_COLORS.DOWN, fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
                    <ArrowDownOutlined /> 下跌颜色 (绿色)
                  </div>
                  <div>RGB: {STOCK_COLORS.DOWN}</div>
                  <div>用于：股价下跌、阴线、卖出信号</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 股票数据展示 */}
        <Col span={24}>
          <Card title="股票数据展示测试" size="small">
            <Row gutter={[16, 16]}>
              {stockData.map((stock) => (
                <Col span={6} key={stock.code}>
                  <Card size="small" style={{ backgroundColor: '#ffffff' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong style={{ color: '#1890ff' }}>{stock.code}</Text>
                        <br />
                        <Text>{stock.name}</Text>
                      </div>
                      
                      <Statistic
                        title="现价"
                        value={stock.price}
                        precision={2}
                        prefix="¥"
                        valueStyle={{
                          color: getChangeColor(stock.change),
                          fontSize: '20px'
                        }}
                      />
                      
                      <div>
                        <Space>
                          {stock.change >= 0 ? (
                            <RiseOutlined style={{ color: getChangeColor(stock.change) }} />
                          ) : (
                            <FallOutlined style={{ color: getChangeColor(stock.change) }} />
                          )}
                          <Text style={{ color: getChangeColor(stock.change), fontWeight: 'bold' }}>
                            {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)}
                            ({stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%)
                          </Text>
                        </Space>
                      </div>
                      
                      <Alert
                        message={`当前价格: ¥${stock.price}`}
                        type={getAntdStatus(stock.change)}
                        showIcon
                        size="small"
                      />
                      
                      <Progress
                        percent={Math.abs(stock.changePercent) * 10}
                        strokeColor={getChangeColor(stock.change)}
                        format={() => `${stock.changePercent.toFixed(2)}%`}
                        size="small"
                      />
                      
                      <Tag color={stock.change >= 0 ? 'red' : 'green'}>
                        {stock.change >= 0 ? '上涨' : '下跌'}
                      </Tag>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* 信号颜色测试 */}
        <Col span={24}>
          <Card title="交易信号颜色测试" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ 
                    color: STOCK_COLORS.UP, 
                    fontSize: '24px', 
                    marginBottom: '8px' 
                  }}>
                    <ArrowUpOutlined />
                  </div>
                  <Tag color="red">买入信号</Tag>
                  <div style={{ marginTop: '8px', color: STOCK_COLORS.UP }}>
                    使用红色表示买入
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ 
                    color: STOCK_COLORS.DOWN, 
                    fontSize: '24px', 
                    marginBottom: '8px' 
                  }}>
                    <ArrowDownOutlined />
                  </div>
                  <Tag color="green">卖出信号</Tag>
                  <div style={{ marginTop: '8px', color: STOCK_COLORS.DOWN }}>
                    使用绿色表示卖出
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ 
                    color: STOCK_COLORS.NEUTRAL, 
                    fontSize: '24px', 
                    marginBottom: '8px' 
                  }}>
                    ⚪
                  </div>
                  <Tag color="orange">持有信号</Tag>
                  <div style={{ marginTop: '8px', color: STOCK_COLORS.NEUTRAL }}>
                    使用橙色表示持有
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ColorTest
