"""
AKShare数据管理器
统一的数据获取和处理服务
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from loguru import logger

# AKShare导入
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
    logger.info("AKShare库加载成功")
except ImportError:
    AKSHARE_AVAILABLE = False
    logger.warning("AKShare库未安装，将使用模拟数据")

from app.models.stock_redesign import (
    StockBasicInfo, StockKlineDaily, StockRealtimeData, 
    StockFinancialData, DataUpdateLog
)


class StockCodeConverter:
    """股票代码格式转换器"""
    
    @staticmethod
    def to_akshare_format(stock_code: str) -> str:
        """转换为AKShare格式 (添加交易所前缀)"""
        if len(stock_code) != 6:
            return stock_code
            
        if stock_code.startswith('6'):
            return f"sh{stock_code}"  # 上海交易所
        elif stock_code.startswith(('0', '3')):
            return f"sz{stock_code}"  # 深圳交易所
        return stock_code
    
    @staticmethod
    def from_akshare_format(ak_code: str) -> str:
        """从AKShare格式转换为标准格式"""
        if ak_code.startswith(('sh', 'sz')):
            return ak_code[2:]
        return ak_code
    
    @staticmethod
    def get_exchange(stock_code: str) -> str:
        """获取交易所代码"""
        if stock_code.startswith('6'):
            return 'SH'
        elif stock_code.startswith(('0', '3')):
            return 'SZ'
        return 'UNKNOWN'


class AKShareDataManager:
    """AKShare数据管理器"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.use_real_api = AKSHARE_AVAILABLE
        self.code_converter = StockCodeConverter()
        self.rate_limit_delay = 0.1  # API调用间隔
        
    async def _rate_limit(self):
        """API调用频率限制"""
        await asyncio.sleep(self.rate_limit_delay)
    
    async def _log_update(self, data_type: str, stock_code: Optional[str], 
                         records_count: int, status: str, error_message: Optional[str] = None,
                         execution_time: Optional[int] = None):
        """记录数据更新日志"""
        log_entry = DataUpdateLog(
            data_type=data_type,
            stock_code=stock_code,
            update_date=date.today(),
            records_count=records_count,
            status=status,
            error_message=error_message,
            execution_time=execution_time,
            source_api="akshare"
        )
        self.db.add(log_entry)
        await self.db.commit()
    
    # ==================== 股票基础信息 ====================
    
    async def update_stock_basic_info(self) -> Tuple[int, int]:
        """更新股票基础信息"""
        start_time = datetime.now()
        success_count = 0
        error_count = 0
        
        try:
            logger.info("开始更新股票基础信息...")
            
            if not self.use_real_api:
                logger.warning("AKShare不可用，跳过基础信息更新")
                return 0, 0
            
            # 获取A股股票列表
            await self._rate_limit()
            loop = asyncio.get_event_loop()
            
            try:
                df = await asyncio.wait_for(
                    loop.run_in_executor(None, ak.stock_zh_a_spot_em),
                    timeout=60.0
                )
                logger.info(f"获取到 {len(df)} 只股票的基础信息")
            except asyncio.TimeoutError:
                logger.error("获取股票列表超时")
                await self._log_update("basic_info", None, 0, "failed", "API调用超时")
                return 0, 1
            
            # 处理每只股票
            for _, row in df.iterrows():
                try:
                    stock_code = str(row['代码']).zfill(6)
                    stock_name = str(row['名称'])
                    
                    # 检查是否已存在
                    result = await self.db.execute(
                        select(StockBasicInfo).where(StockBasicInfo.stock_code == stock_code)
                    )
                    existing_stock = result.scalar_one_or_none()
                    
                    if existing_stock:
                        # 更新现有记录
                        existing_stock.stock_name = stock_name
                        existing_stock.updated_at = datetime.utcnow()
                    else:
                        # 创建新记录
                        new_stock = StockBasicInfo(
                            stock_code=stock_code,
                            stock_name=stock_name,
                            exchange=self.code_converter.get_exchange(stock_code),
                            status="active"
                        )
                        self.db.add(new_stock)
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"处理股票 {stock_code} 失败: {e}")
                    error_count += 1
                    continue
            
            await self.db.commit()
            
            execution_time = int((datetime.now() - start_time).total_seconds())
            await self._log_update("basic_info", None, success_count, "success", 
                                 execution_time=execution_time)
            
            logger.info(f"股票基础信息更新完成: 成功 {success_count}, 失败 {error_count}")
            return success_count, error_count
            
        except Exception as e:
            logger.error(f"更新股票基础信息失败: {e}")
            await self._log_update("basic_info", None, 0, "failed", str(e))
            return 0, 1
    
    # ==================== K线数据 ====================
    
    async def update_kline_data(self, stock_code: str, period: str = "daily", 
                               days: int = 30) -> Tuple[int, int]:
        """更新K线数据"""
        start_time = datetime.now()
        success_count = 0
        error_count = 0
        
        try:
            if not self.use_real_api:
                logger.warning(f"AKShare不可用，跳过 {stock_code} K线数据更新")
                return 0, 0
            
            # 转换股票代码格式
            ak_code = self.code_converter.to_akshare_format(stock_code)
            
            # 计算日期范围
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            logger.info(f"更新 {stock_code} K线数据: {start_date} 到 {end_date}")
            
            await self._rate_limit()
            loop = asyncio.get_event_loop()
            
            try:
                df = await asyncio.wait_for(
                    loop.run_in_executor(
                        None,
                        lambda: ak.stock_zh_a_hist(
                            symbol=ak_code,
                            period=period,
                            start_date=start_date.strftime("%Y%m%d"),
                            end_date=end_date.strftime("%Y%m%d"),
                            adjust="qfq"
                        )
                    ),
                    timeout=30.0
                )
                
                if df is None or df.empty:
                    logger.warning(f"股票 {stock_code} 没有K线数据")
                    return 0, 0
                
                logger.info(f"获取到 {len(df)} 条K线数据")
                
            except asyncio.TimeoutError:
                logger.error(f"获取 {stock_code} K线数据超时")
                await self._log_update("kline", stock_code, 0, "failed", "API调用超时")
                return 0, 1
            
            # 处理K线数据
            for _, row in df.iterrows():
                try:
                    trade_date = row['日期'].date() if hasattr(row['日期'], 'date') else row['日期']
                    
                    # 检查是否已存在
                    result = await self.db.execute(
                        select(StockKlineDaily).where(
                            and_(
                                StockKlineDaily.stock_code == stock_code,
                                StockKlineDaily.trade_date == trade_date
                            )
                        )
                    )
                    existing_kline = result.scalar_one_or_none()
                    
                    kline_data = {
                        'stock_code': stock_code,
                        'trade_date': trade_date,
                        'open_price': Decimal(str(row['开盘'])),
                        'high_price': Decimal(str(row['最高'])),
                        'low_price': Decimal(str(row['最低'])),
                        'close_price': Decimal(str(row['收盘'])),
                        'volume': int(row['成交量']),
                        'turnover': Decimal(str(row['成交额'])),
                        'change_percent': Decimal(str(row['涨跌幅'])) if '涨跌幅' in row else None,
                        'turnover_rate': Decimal(str(row['换手率'])) if '换手率' in row else None,
                        'amplitude': Decimal(str(row['振幅'])) if '振幅' in row else None
                    }
                    
                    if existing_kline:
                        # 更新现有记录
                        for key, value in kline_data.items():
                            if key != 'stock_code':  # 不更新主键
                                setattr(existing_kline, key, value)
                    else:
                        # 创建新记录
                        new_kline = StockKlineDaily(**kline_data)
                        self.db.add(new_kline)
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"处理K线数据失败 {stock_code} {trade_date}: {e}")
                    error_count += 1
                    continue
            
            await self.db.commit()
            
            execution_time = int((datetime.now() - start_time).total_seconds())
            await self._log_update("kline", stock_code, success_count, "success",
                                 execution_time=execution_time)
            
            logger.info(f"{stock_code} K线数据更新完成: 成功 {success_count}, 失败 {error_count}")
            return success_count, error_count
            
        except Exception as e:
            logger.error(f"更新 {stock_code} K线数据失败: {e}")
            await self._log_update("kline", stock_code, 0, "failed", str(e))
            return 0, 1
    
    # ==================== 实时行情 ====================
    
    async def update_realtime_data(self, stock_codes: Optional[List[str]] = None) -> Tuple[int, int]:
        """更新实时行情数据"""
        start_time = datetime.now()
        success_count = 0
        error_count = 0
        
        try:
            if not self.use_real_api:
                logger.warning("AKShare不可用，跳过实时行情更新")
                return 0, 0
            
            logger.info("开始更新实时行情数据...")
            
            await self._rate_limit()
            loop = asyncio.get_event_loop()
            
            try:
                df = await asyncio.wait_for(
                    loop.run_in_executor(None, ak.stock_zh_a_spot_em),
                    timeout=60.0
                )
                logger.info(f"获取到 {len(df)} 只股票的实时行情")
            except asyncio.TimeoutError:
                logger.error("获取实时行情超时")
                await self._log_update("realtime", None, 0, "failed", "API调用超时")
                return 0, 1
            
            # 如果指定了股票代码，则过滤
            if stock_codes:
                df = df[df['代码'].isin(stock_codes)]
                logger.info(f"过滤后剩余 {len(df)} 只股票")
            
            # 处理实时数据
            for _, row in df.iterrows():
                try:
                    stock_code = str(row['代码']).zfill(6)
                    
                    # 检查是否已存在
                    result = await self.db.execute(
                        select(StockRealtimeData).where(StockRealtimeData.stock_code == stock_code)
                    )
                    existing_data = result.scalar_one_or_none()
                    
                    realtime_data = {
                        'stock_code': stock_code,
                        'current_price': Decimal(str(row['最新价'])),
                        'change_amount': Decimal(str(row['涨跌额'])),
                        'change_percent': Decimal(str(row['涨跌幅'])),
                        'open_price': Decimal(str(row['今开'])),
                        'high_price': Decimal(str(row['最高'])),
                        'low_price': Decimal(str(row['最低'])),
                        'pre_close': Decimal(str(row['昨收'])),
                        'volume': int(row['成交量']),
                        'turnover': Decimal(str(row['成交额'])),
                        'turnover_rate': Decimal(str(row['换手率'])) if '换手率' in row else None,
                        'pe_ratio': Decimal(str(row['市盈率-动态'])) if '市盈率-动态' in row else None,
                        'pb_ratio': Decimal(str(row['市净率'])) if '市净率' in row else None,
                        'total_market_cap': Decimal(str(row['总市值'])) if '总市值' in row else None,
                        'trade_date': date.today(),
                        'update_time': datetime.utcnow()
                    }
                    
                    if existing_data:
                        # 更新现有记录
                        for key, value in realtime_data.items():
                            if key != 'stock_code':
                                setattr(existing_data, key, value)
                    else:
                        # 创建新记录
                        new_data = StockRealtimeData(**realtime_data)
                        self.db.add(new_data)
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"处理实时数据失败 {stock_code}: {e}")
                    error_count += 1
                    continue
            
            await self.db.commit()
            
            execution_time = int((datetime.now() - start_time).total_seconds())
            await self._log_update("realtime", None, success_count, "success",
                                 execution_time=execution_time)
            
            logger.info(f"实时行情更新完成: 成功 {success_count}, 失败 {error_count}")
            return success_count, error_count
            
        except Exception as e:
            logger.error(f"更新实时行情失败: {e}")
            await self._log_update("realtime", None, 0, "failed", str(e))
            return 0, 1
    
    # ==================== 批量更新 ====================
    
    async def batch_update_kline(self, stock_codes: List[str], days: int = 30) -> Dict[str, Any]:
        """批量更新K线数据"""
        total_success = 0
        total_error = 0
        results = {}
        
        logger.info(f"开始批量更新 {len(stock_codes)} 只股票的K线数据")
        
        for i, stock_code in enumerate(stock_codes):
            try:
                success, error = await self.update_kline_data(stock_code, days=days)
                total_success += success
                total_error += error
                results[stock_code] = {"success": success, "error": error}
                
                # 进度日志
                if (i + 1) % 10 == 0:
                    logger.info(f"批量更新进度: {i + 1}/{len(stock_codes)}")
                
            except Exception as e:
                logger.error(f"批量更新 {stock_code} 失败: {e}")
                total_error += 1
                results[stock_code] = {"success": 0, "error": 1}
        
        logger.info(f"批量K线更新完成: 总成功 {total_success}, 总失败 {total_error}")
        
        return {
            "total_success": total_success,
            "total_error": total_error,
            "details": results
        }

    # ==================== 数据查询 ====================

    async def get_stock_list(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            result = await self.db.execute(
                select(StockBasicInfo)
                .where(StockBasicInfo.status == "active")
                .order_by(StockBasicInfo.stock_code)
                .limit(limit)
                .offset(offset)
            )
            stocks = result.scalars().all()

            return [
                {
                    "stock_code": stock.stock_code,
                    "stock_name": stock.stock_name,
                    "exchange": stock.exchange,
                    "industry": stock.industry,
                    "status": stock.status,
                    "list_date": stock.list_date.isoformat() if stock.list_date else None,
                    "updated_at": stock.updated_at.isoformat()
                }
                for stock in stocks
            ]
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    async def get_kline_data(self, stock_code: str, days: int = 30) -> List[Dict[str, Any]]:
        """获取K线数据"""
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)

            result = await self.db.execute(
                select(StockKlineDaily)
                .where(
                    and_(
                        StockKlineDaily.stock_code == stock_code,
                        StockKlineDaily.trade_date >= start_date,
                        StockKlineDaily.trade_date <= end_date
                    )
                )
                .order_by(StockKlineDaily.trade_date)
            )
            klines = result.scalars().all()

            return [
                {
                    "trade_date": kline.trade_date.isoformat(),
                    "open": float(kline.open_price),
                    "high": float(kline.high_price),
                    "low": float(kline.low_price),
                    "close": float(kline.close_price),
                    "volume": kline.volume,
                    "turnover": float(kline.turnover),
                    "change_percent": float(kline.change_percent) if kline.change_percent else None
                }
                for kline in klines
            ]
        except Exception as e:
            logger.error(f"获取K线数据失败 {stock_code}: {e}")
            return []

    async def get_realtime_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取实时行情数据"""
        try:
            result = await self.db.execute(
                select(StockRealtimeData).where(StockRealtimeData.stock_code == stock_code)
            )
            realtime = result.scalar_one_or_none()

            if not realtime:
                return None

            return {
                "stock_code": realtime.stock_code,
                "current_price": float(realtime.current_price),
                "change_amount": float(realtime.change_amount),
                "change_percent": float(realtime.change_percent),
                "open_price": float(realtime.open_price),
                "high_price": float(realtime.high_price),
                "low_price": float(realtime.low_price),
                "pre_close": float(realtime.pre_close),
                "volume": realtime.volume,
                "turnover": float(realtime.turnover),
                "pe_ratio": float(realtime.pe_ratio) if realtime.pe_ratio else None,
                "pb_ratio": float(realtime.pb_ratio) if realtime.pb_ratio else None,
                "total_market_cap": float(realtime.total_market_cap) if realtime.total_market_cap else None,
                "update_time": realtime.update_time.isoformat()
            }
        except Exception as e:
            logger.error(f"获取实时数据失败 {stock_code}: {e}")
            return None
