"""
测试前后端连接
"""

import requests
import json
import time

def test_cors_and_api():
    """测试CORS和API连接"""
    base_url = "http://localhost:8000"
    
    print("🚀 测试前后端连接")
    
    # 测试CORS预检请求
    print("\n1. 测试CORS配置")
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options(f"{base_url}/api/v1/stocks/list", headers=headers)
        print(f"OPTIONS请求状态码: {response.status_code}")
        print(f"CORS头: {dict(response.headers)}")
    except Exception as e:
        print(f"CORS测试失败: {e}")
    
    # 测试从前端域名访问API
    print("\n2. 测试API访问")
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json'
        }
        
        # 测试股票列表
        response = requests.get(f"{base_url}/api/v1/stocks/list?limit=3", headers=headers)
        print(f"股票列表API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"返回股票数量: {len(data.get('data', {}).get('stocks', []))}")
            stocks = data.get('data', {}).get('stocks', [])
            if stocks:
                print(f"第一只股票: {stocks[0]['stock_code']} - {stocks[0]['stock_name']}")
        
        # 测试股票详情
        if response.status_code == 200 and stocks:
            stock_code = stocks[0]['stock_code']
            detail_response = requests.get(f"{base_url}/api/v1/stocks/{stock_code}/info", headers=headers)
            print(f"股票详情API状态码: {detail_response.status_code}")
            
            # 测试K线数据
            kline_response = requests.get(f"{base_url}/api/v1/stocks/{stock_code}/kline?limit=5", headers=headers)
            print(f"K线数据API状态码: {kline_response.status_code}")
            if kline_response.status_code == 200:
                kline_data = kline_response.json()
                klines = kline_data.get('data', {}).get('klines', [])
                print(f"K线数据数量: {len(klines)}")
        
    except Exception as e:
        print(f"API测试失败: {e}")
    
    # 测试AI预测API
    print("\n3. 测试AI预测API")
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json'
        }
        response = requests.post(f"{base_url}/api/v1/ai/000001/predict?days=3", headers=headers)
        print(f"AI预测API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            prediction = data.get('data', {}).get('prediction_result', {})
            print(f"预测结果: {prediction.get('prediction')} (置信度: {prediction.get('confidence')})")
    except Exception as e:
        print(f"AI预测测试失败: {e}")
    
    # 测试技术指标API
    print("\n4. 测试技术指标API")
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json'
        }
        response = requests.get(f"{base_url}/api/v1/indicators/000001/calculate", headers=headers)
        print(f"技术指标API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            indicators_count = data.get('data', {}).get('indicators_count', 0)
            print(f"计算的技术指标数量: {indicators_count}")
    except Exception as e:
        print(f"技术指标测试失败: {e}")

def test_frontend_api_calls():
    """模拟前端API调用"""
    print("\n" + "="*50)
    print("🌐 模拟前端API调用")
    
    # 模拟前端axios请求
    import requests
    
    # 创建session模拟前端
    session = requests.Session()
    session.headers.update({
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000',
        'Referer': 'http://localhost:3000/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    base_url = "http://localhost:8000/api/v1"
    
    try:
        # 1. 获取股票列表
        print("\n1. 获取股票列表")
        response = session.get(f"{base_url}/stocks/list", params={'limit': 5})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            stocks = data.get('data', {}).get('stocks', [])
            print(f"获取到 {len(stocks)} 只股票")
            for stock in stocks[:3]:
                print(f"  - {stock['stock_code']}: {stock['stock_name']}")
        
        # 2. 获取股票详情
        if response.status_code == 200 and stocks:
            print("\n2. 获取股票详情")
            stock_code = stocks[0]['stock_code']
            response = session.get(f"{base_url}/stocks/{stock_code}/info")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                stock_info = data.get('data', {})
                print(f"股票信息: {stock_info.get('stock_name')} ({stock_info.get('market')})")
        
        # 3. 获取K线数据
        if response.status_code == 200:
            print("\n3. 获取K线数据")
            response = session.get(f"{base_url}/stocks/{stock_code}/kline", params={'limit': 10})
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                klines = data.get('data', {}).get('klines', [])
                print(f"获取到 {len(klines)} 条K线数据")
                if klines:
                    latest = klines[-1]
                    print(f"最新价格: {latest.get('close_price')} (日期: {latest.get('trade_date')})")
        
        print("\n✅ 前后端连接测试完成！")
        
    except Exception as e:
        print(f"❌ 前端API调用测试失败: {e}")

if __name__ == "__main__":
    test_cors_and_api()
    test_frontend_api_calls()
