import React, { useMemo } from 'react'
import { Card, Typography, Space, Tag, Select } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { CHART_CONFIG } from '@/config/dataConfig'

const { Text } = Typography
const { Option } = Select

// 计算移动平均线
const calculateMA = (data: number[], period: number): (number | null)[] => {
  const result: (number | null)[] = []
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(null)
    } else {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
      result.push(sum / period)
    }
  }
  return result
}

interface CandleData {
  time?: string
  timestamp?: string
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

interface CandlestickChartProps {
  title?: string
  data: CandleData[]
  height?: number
  showVolume?: boolean
  showMA?: boolean
  onTimeframeChange?: (timeframe: string) => void
}

const CandlestickChart: React.FC<CandlestickChartProps> = ({
  title = 'K线图',
  data,
  height = 300,
  showVolume = true,
  showMA = true,
  onTimeframeChange
}) => {
  if (!data || data.length === 0) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">暂无K线数据</Text>
        </div>
      </Card>
    )
  }

  const latestCandle = data[data.length - 1]
  const previousCandle = data.length > 1 ? data[data.length - 2] : null
  
  const change = previousCandle ? latestCandle.close - previousCandle.close : 0
  const changePercent = previousCandle ? (change / previousCandle.close) * 100 : 0
  
  const isPositive = change >= 0
  const changeColor = isPositive ? CHART_CONFIG.CANDLESTICK.UP_COLOR : CHART_CONFIG.CANDLESTICK.DOWN_COLOR

  // 准备ECharts配置
  const echartsOption = useMemo(() => {
    // K线数据格式: [open, close, low, high]
    const candlestickData = data.map(item => [
      item.open,
      item.close,
      item.low,
      item.high
    ])

    // 成交量数据
    const volumeData = data.map(item => item.volume || 0)

    // X轴数据（时间）- 支持time或timestamp字段
    const xAxisData = data.map(item => {
      const timeValue = item.time || item.timestamp
      if (!timeValue) return 'N/A'

      // 如果是时间戳格式，转换为日期字符串
      if (typeof timeValue === 'string' && timeValue.includes('T')) {
        return timeValue.split('T')[0] // 只取日期部分
      }
      return timeValue
    })

    // 计算移动平均线
    const closeData = data.map(item => item.close)
    const ma5 = showMA ? calculateMA(closeData, 5) : []
    const ma10 = showMA ? calculateMA(closeData, 10) : []
    const ma20 = showMA ? calculateMA(closeData, 20) : []
    const ma60 = showMA ? calculateMA(closeData, 60) : []

    return {
      animation: false, // 已经禁用动画
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function (params: any) {
          const data = params[0]
          if (data && data.data) {
            const [open, close, low, high] = data.data
            return `
              <div>
                <div>${data.axisValue}</div>
                <div>开盘: ${open.toFixed(2)}</div>
                <div>收盘: ${close.toFixed(2)}</div>
                <div>最高: ${high.toFixed(2)}</div>
                <div>最低: ${low.toFixed(2)}</div>
              </div>
            `
          }
          return ''
        }
      },
      grid: showVolume ? [
        {
          left: '10%',
          right: '8%',
          top: '10%',
          height: '60%'
        },
        {
          left: '10%',
          right: '8%',
          top: '75%',
          height: '20%'
        }
      ] : [
        {
          left: '10%',
          right: '8%',
          top: '10%',
          bottom: '15%'
        }
      ],
      xAxis: showVolume ? [
        {
          type: 'category',
          data: xAxisData,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax'
        },
        {
          type: 'category',
          gridIndex: 1,
          data: xAxisData,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax'
        }
      ] : [
        {
          type: 'category',
          data: xAxisData,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax'
        }
      ],
      yAxis: showVolume ? [
        {
          scale: true,
          splitArea: {
            show: true
          }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ] : [
        {
          scale: true,
          splitArea: {
            show: true
          }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: showVolume ? [0, 1] : [0],
          start: 50,
          end: 100,
          filterMode: 'none', // 防止数据过滤导致的重置
          throttle: 50, // 节流，提高性能
          zoomLock: false, // 允许缩放
          moveOnMouseMove: true, // 鼠标移动时平移
          moveOnMouseWheel: false, // 禁用鼠标滚轮平移，避免冲突
          preventDefaultMouseMove: false
        },
        {
          show: true,
          xAxisIndex: showVolume ? [0, 1] : [0],
          type: 'slider',
          top: '90%',
          start: 50,
          end: 100,
          filterMode: 'none',
          throttle: 50,
          brushSelect: false, // 禁用刷选功能，避免意外重置
          zoomLock: false
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: candlestickData,
          itemStyle: {
            color: CHART_CONFIG.CANDLESTICK.UP_COLOR,
            color0: CHART_CONFIG.CANDLESTICK.DOWN_COLOR,
            borderColor: CHART_CONFIG.CANDLESTICK.UP_COLOR,
            borderColor0: CHART_CONFIG.CANDLESTICK.DOWN_COLOR
          }
        },
        // 移动平均线
        ...(showMA ? [
          {
            name: 'MA5',
            type: 'line',
            data: ma5,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#FF6B6B',
              width: 1
            }
          },
          {
            name: 'MA10',
            type: 'line',
            data: ma10,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#4ECDC4',
              width: 1
            }
          },
          {
            name: 'MA20',
            type: 'line',
            data: ma20,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#45B7D1',
              width: 1
            }
          },
          {
            name: 'MA60',
            type: 'line',
            data: ma60,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#F7DC6F',
              width: 1
            }
          }
        ] : []),
        // 成交量
        ...(showVolume ? [{
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: volumeData,
          itemStyle: {
            color: function (params: any) {
              const dataIndex = params.dataIndex
              const candleData = data[dataIndex]
              return candleData.close >= candleData.open
                ? CHART_CONFIG.CANDLESTICK.UP_COLOR
                : CHART_CONFIG.CANDLESTICK.DOWN_COLOR
            }
          }
        }] : [])
      ]
    }
  }, [data, showVolume])

  return (
    <Card
      title={
        <Space>
          <span>{title}</span>
          <Tag color={isPositive ? 'red' : 'green'}>
            {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            {change.toFixed(2)} ({changePercent.toFixed(2)}%)
          </Tag>
        </Space>
      }
      extra={
        onTimeframeChange && (
          <Select defaultValue="1D" onChange={onTimeframeChange} size="small">
            <Option value="1D">日线</Option>
            <Option value="1W">周线</Option>
            <Option value="1M">月线</Option>
          </Select>
        )
      }
      size="small"
    >
      <ReactECharts
        option={echartsOption}
        style={{ height: height }}
        opts={{
          renderer: 'canvas',
          useDirtyRect: true // 启用脏矩形优化
        }}
        notMerge={true} // 防止配置合并导致的问题
        lazyUpdate={true} // 延迟更新提高性能
      />
    </Card>
  )
}

export default CandlestickChart
