-- 创建数据库
CREATE DATABASE stock_analyzer;
CREATE DATABASE stock_analyzer_ts;

-- 连接到stock_analyzer数据库
\c stock_analyzer;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建索引优化查询
-- 这些索引将在应用启动时通过SQLAlchemy自动创建

-- 连接到TimescaleDB数据库
\c stock_analyzer_ts;

-- 启用TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 创建时序数据表（用于高频数据存储）
CREATE TABLE IF NOT EXISTS kline_ts (
    time TIMESTAMPTZ NOT NULL,
    stock_code TEXT NOT NULL,
    period TEXT NOT NULL,
    open_price DECIMAL(10,3),
    high_price DECIMAL(10,3),
    low_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    volume BIGINT,
    turnover DECIMAL(15,2)
);

-- 创建超表（TimescaleDB特性）
SELECT create_hypertable('kline_ts', 'time', if_not_exists => TRUE);

-- 创建实时数据时序表
CREATE TABLE IF NOT EXISTS realtime_ts (
    time TIMESTAMPTZ NOT NULL,
    stock_code TEXT NOT NULL,
    price DECIMAL(10,3),
    volume BIGINT,
    turnover DECIMAL(15,2)
);

SELECT create_hypertable('realtime_ts', 'time', if_not_exists => TRUE);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_kline_ts_stock_time ON kline_ts (stock_code, time DESC);
CREATE INDEX IF NOT EXISTS idx_realtime_ts_stock_time ON realtime_ts (stock_code, time DESC);
