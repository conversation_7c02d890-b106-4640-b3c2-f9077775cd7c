#!/usr/bin/env python3
"""
测试数据完整性API功能
"""

import asyncio
import aiohttp
import json
from datetime import datetime

BASE_URL = "http://localhost:8000/api/v1/data-management"

async def test_data_integrity_stats():
    """测试获取数据完整性统计"""
    print("1. 测试获取数据完整性统计")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/data-integrity/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ 获取数据完整性统计成功")
                    print(f"  股票总数: {data.get('total_stocks', 0)}")
                    print(f"  完整数据: {data.get('complete_stocks', 0)}")
                    print(f"  部分数据: {data.get('partial_stocks', 0)}")
                    print(f"  缺失数据: {data.get('missing_stocks', 0)}")
                    print(f"  日K覆盖率: {data.get('daily_coverage', 0):.1f}%")
                    print(f"  周K覆盖率: {data.get('weekly_coverage', 0):.1f}%")
                    print(f"  月K覆盖率: {data.get('monthly_coverage', 0):.1f}%")
                    return True
                else:
                    print(f"❌ 获取数据完整性统计失败: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
                    return False
    except Exception as e:
        print(f"❌ 获取数据完整性统计异常: {e}")
        return False

async def test_stocks_data_integrity():
    """测试获取股票数据完整性列表"""
    print("\n2. 测试获取股票数据完整性列表")
    try:
        async with aiohttp.ClientSession() as session:
            params = {
                'page': 1,
                'page_size': 10
            }
            async with session.get(f"{BASE_URL}/data-integrity/stocks", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ 获取股票数据完整性列表成功")
                    print(f"  返回股票数量: {len(data)}")
                    
                    if data:
                        stock = data[0]
                        print(f"  示例股票: {stock.get('stock_code')} - {stock.get('stock_name')}")
                        print(f"  整体状态: {stock.get('overall_status')}")
                        print(f"  日K完整性: {stock.get('daily_completeness', 0):.1f}%")
                        print(f"  周K完整性: {stock.get('weekly_completeness', 0):.1f}%")
                        print(f"  月K完整性: {stock.get('monthly_completeness', 0):.1f}%")
                    
                    return True
                else:
                    print(f"❌ 获取股票数据完整性列表失败: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
                    return False
    except Exception as e:
        print(f"❌ 获取股票数据完整性列表异常: {e}")
        return False

async def test_data_update():
    """测试数据更新功能"""
    print("\n3. 测试数据更新功能")
    try:
        async with aiohttp.ClientSession() as session:
            # 测试补缺更新
            update_request = {
                "update_type": "fill_missing",
                "periods": ["daily"],
                "force_update": False
            }
            
            async with session.post(
                f"{BASE_URL}/data-integrity/update",
                json=update_request,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ 启动数据更新任务成功")
                    print(f"  任务ID: {data.get('task_id')}")
                    print(f"  更新类型: {data.get('update_type')}")
                    print(f"  消息: {data.get('message')}")
                    return True
                else:
                    print(f"❌ 启动数据更新任务失败: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
                    return False
    except Exception as e:
        print(f"❌ 启动数据更新任务异常: {e}")
        return False

async def test_specific_stock_update():
    """测试指定股票更新"""
    print("\n4. 测试指定股票更新")
    try:
        async with aiohttp.ClientSession() as session:
            update_request = {
                "update_type": "specific_stocks",
                "stock_codes": ["000001", "000002"],
                "periods": ["daily", "weekly"],
                "force_update": True
            }
            
            async with session.post(
                f"{BASE_URL}/data-integrity/update",
                json=update_request,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ 启动指定股票更新任务成功")
                    print(f"  任务ID: {data.get('task_id')}")
                    print(f"  更新类型: {data.get('update_type')}")
                    return True
                else:
                    print(f"❌ 启动指定股票更新任务失败: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
                    return False
    except Exception as e:
        print(f"❌ 启动指定股票更新任务异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 50)
    print("数据完整性管理功能测试")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行测试
    tests = [
        test_data_integrity_stats,
        test_stocks_data_integrity,
        test_data_update,
        test_specific_stock_update
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！数据完整性管理功能正常")
    else:
        print("❌ 部分测试失败，请检查相关功能")
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
