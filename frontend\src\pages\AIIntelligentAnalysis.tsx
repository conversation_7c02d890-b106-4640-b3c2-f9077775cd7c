import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Button,
  Table,
  Tabs,
  Typography,
  Space,
  Tag,
  Statistic,
  Progress,
  Alert,
  Spin,
  Tooltip,
  Descriptions,
  Badge,
} from 'antd'
import {
  RobotOutlined,
  ArrowUpOutlined,
  <PERSON>DownOutlined,
  <PERSON>Outlined,
  CheckCircleOutlined,
  ReloadOutlined,
  <PERSON><PERSON>Outlined,
  EyeOutlined,
} from '@ant-design/icons'
import { Line, Column, Gauge, Radar } from '@ant-design/plots'
import MLPredictionEngine, { EnsemblePrediction, MLPrediction } from '@/utils/mlPredictionEngine'
import AdvancedChartAnalysis, { WaveAnalysisResult, GannAnalysisResult } from '@/utils/advancedChartAnalysis'
import RiskManagement, { VaRResult, RiskAlert, DynamicStopLoss } from '@/utils/riskManagement'
import QuantitativeStrategy, { BacktestResult, FactorModel } from '@/utils/quantitativeStrategy'
import { PriceData } from '@/utils/advancedIndicators'
import { stockService } from '@/services/apiService'
import { useStockDataStore, usePriceData, useSelectedStock } from '@/stores/stockDataStore'

const { Title, Text } = Typography
const { Option } = Select

interface AIAnalysisData {
  mlPrediction: EnsemblePrediction
  waveAnalysis: WaveAnalysisResult
  gannAnalysis: GannAnalysisResult
  riskAssessment: {
    var: VaRResult
    alerts: RiskAlert[]
    stopLoss: DynamicStopLoss
  }
  factorModel: FactorModel
  strategyRecommendations: {
    momentum: { score: number, recommendation: string }
    meanReversion: { score: number, recommendation: string }
    factor: { score: number, recommendation: string }
  }
}

const AIIntelligentAnalysis: React.FC = () => {
  // 使用真实数据store
  const selectedStock = useSelectedStock()
  const priceData = usePriceData()
  const { setSelectedStock } = useStockDataStore()

  const [timeframe, setTimeframe] = useState('1D')
  const [loading, setLoading] = useState(false)
  const [analysisData, setAnalysisData] = useState<AIAnalysisData | null>(null)
  const [activeTab, setActiveTab] = useState('ml_prediction')

  // 当选择的股票或时间框架改变时，重新加载分析数据
  useEffect(() => {
    if (selectedStock && priceData && priceData.length > 0) {
      loadAIAnalysis()
    }
  }, [selectedStock, timeframe, priceData])

  const loadAIAnalysis = async () => {
    setLoading(true)
    try {
      // 检查是否有真实价格数据
      if (!priceData || priceData.length === 0) {
        console.warn('没有价格数据，使用模拟数据进行分析')
        setLoading(false)
        return
      }

      await new Promise(resolve => setTimeout(resolve, 2000))

      // 使用真实价格数据进行分析
      const realPriceData = priceData

      // ML预测
      const features = MLPredictionEngine.extractFeatures(realPriceData)[0]
      const mlPrediction = MLPredictionEngine.ensemblePredict(features)

      // 波浪分析
      const waveAnalysis = AdvancedChartAnalysis.analyzeElliottWaves(realPriceData)

      // 江恩分析
      const gannAnalysis = AdvancedChartAnalysis.analyzeGannAngles(realPriceData)

      // 风险评估
      const returns = realPriceData.slice(1).map((d, i) =>
        (d.close - realPriceData[i].close) / realPriceData[i].close)
      const var_result = RiskManagement.calculateVaR(returns, 0.95, 1, 'historical')
      const riskMetrics = RiskManagement.calculateRiskMetrics(returns)
      const alerts = RiskManagement.generateRiskAlerts(var_result, riskMetrics, {
        totalRisk: 0.15,
        diversificationBenefit: 0.05,
        concentrationRisk: 0.25,
        liquidityRisk: 0.08,
        marketRisk: 0.12,
        specificRisk: 0.03,
        riskContribution: {}
      })

      // 获取当前价格和持仓信息
      const currentPrice = realPriceData[realPriceData.length - 1]?.close || 0
      const stopLoss = RiskManagement.calculateDynamicStopLoss(realPriceData, currentPrice, 1000, 0.02)

      // 因子模型
      const factorModel = QuantitativeStrategy.buildFactorModel([{
        symbol: selectedStock?.code || '000001',
        data: realPriceData,
        fundamentals: {}
      }], realPriceData)

      // 策略推荐
      const momentumStrategy = QuantitativeStrategy.momentumStrategy(realPriceData, 20, 5)
      const meanReversionStrategy = QuantitativeStrategy.meanReversionStrategy(realPriceData, 20, 2)
      
      setAnalysisData({
        mlPrediction,
        waveAnalysis,
        gannAnalysis,
        riskAssessment: {
          var: var_result,
          alerts,
          stopLoss
        },
        factorModel,
        strategyRecommendations: {
          momentum: {
            score: momentumStrategy.score,
            recommendation: momentumStrategy.score > 0.3 ? '适合动量策略' : '不适合动量策略'
          },
          meanReversion: {
            score: meanReversionStrategy.score,
            recommendation: meanReversionStrategy.score > 0.3 ? '适合均值回归策略' : '不适合均值回归策略'
          },
          factor: {
            score: Math.abs(factorModel.expectedReturn) * 10,
            recommendation: factorModel.expectedReturn > 0 ? '因子模型看多' : '因子模型看空'
          }
        }
      })
    } catch (error) {
      console.error('Failed to load AI analysis:', error)
    } finally {
      setLoading(false)
    }
  }

  const renderMLPrediction = () => {
    if (!analysisData) return null

    const { mlPrediction } = analysisData

    const modelData = mlPrediction.model_predictions.map(pred => ({
      model: pred.model,
      prediction: pred.prediction * 100,
      confidence: pred.confidence * 100
    }))

    const getDirectionColor = (direction: string) => {
      switch (direction) {
        case 'up': return '#52c41a'
        case 'down': return '#ff4d4f'
        default: return '#faad14'
      }
    }

    const getRiskColor = (risk: string) => {
      switch (risk) {
        case 'low': return 'success'
        case 'medium': return 'warning'
        case 'high': return 'error'
        default: return 'default'
      }
    }

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="集成预测结果" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <Statistic
                  title="预测涨跌幅"
                  value={mlPrediction.final_prediction * 100}
                  precision={2}
                  suffix="%"
                  valueStyle={{ 
                    color: getDirectionColor(mlPrediction.direction),
                    fontSize: '24px'
                  }}
                  prefix={mlPrediction.direction === 'up' ? <ArrowUpOutlined /> :
                          mlPrediction.direction === 'down' ? <ArrowDownOutlined /> : null}
                />
              </div>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="置信度"
                    value={mlPrediction.confidence * 100}
                    precision={1}
                    suffix="%"
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="一致性"
                    value={mlPrediction.consensus_strength * 100}
                    precision={1}
                    suffix="%"
                  />
                </Col>
                <Col span={8}>
                  <div>
                    <Text strong>风险等级</Text>
                    <br />
                    <Badge status={getRiskColor(mlPrediction.risk_level)} text={mlPrediction.risk_level.toUpperCase()} />
                  </div>
                </Col>
              </Row>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="模型预测对比" size="small">
            <Column
              data={modelData}
              xField="model"
              yField="prediction"
              height={200}
              color="#1890ff"
              label={{
                position: 'top',
                formatter: (datum: any) => `${datum.prediction.toFixed(2)}%`
              }}
            />
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="概率分布" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Progress
                  type="circle"
                  percent={mlPrediction.model_predictions[0].probability.up * 100}
                  format={() => '上涨'}
                  strokeColor="#52c41a"
                  size={80}
                />
                <div style={{ textAlign: 'center', marginTop: 8 }}>
                  <Text>{(mlPrediction.model_predictions[0].probability.up * 100).toFixed(1)}%</Text>
                </div>
              </Col>
              <Col span={8}>
                <Progress
                  type="circle"
                  percent={mlPrediction.model_predictions[0].probability.neutral * 100}
                  format={() => '横盘'}
                  strokeColor="#faad14"
                  size={80}
                />
                <div style={{ textAlign: 'center', marginTop: 8 }}>
                  <Text>{(mlPrediction.model_predictions[0].probability.neutral * 100).toFixed(1)}%</Text>
                </div>
              </Col>
              <Col span={8}>
                <Progress
                  type="circle"
                  percent={mlPrediction.model_predictions[0].probability.down * 100}
                  format={() => '下跌'}
                  strokeColor="#ff4d4f"
                  size={80}
                />
                <div style={{ textAlign: 'center', marginTop: 8 }}>
                  <Text>{(mlPrediction.model_predictions[0].probability.down * 100).toFixed(1)}%</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    )
  }

  const renderWaveAnalysis = () => {
    if (!analysisData) return null

    const { waveAnalysis, gannAnalysis } = analysisData

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="艾略特波浪分析" size="small">
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="当前波浪">第{waveAnalysis.elliottWave.currentWave}浪</Descriptions.Item>
              <Descriptions.Item label="波浪进度">
                <Progress percent={waveAnalysis.elliottWave.waveProgress * 100} size="small" />
              </Descriptions.Item>
              <Descriptions.Item label="下一目标">¥{waveAnalysis.elliottWave.nextTarget.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="失效位">¥{waveAnalysis.elliottWave.invalidationLevel.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="分析置信度">{(waveAnalysis.confidence * 100).toFixed(1)}%</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="江恩分析" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>价格目标：</Text>
                {gannAnalysis.priceTargets.slice(0, 3).map((target, index) => (
                  <Tag key={index} color="blue" style={{ margin: '2px' }}>
                    ¥{target.toFixed(2)}
                  </Tag>
                ))}
              </div>
              <div>
                <Text strong>时间目标：</Text>
                {gannAnalysis.timeTargets.slice(0, 3).map((target, index) => (
                  <Tag key={index} color="green" style={{ margin: '2px' }}>
                    {target}
                  </Tag>
                ))}
              </div>
              <div>
                <Text strong>活跃角度线：</Text>
                <Text>{gannAnalysis.gannAngles.filter(a => a.support || a.resistance).length}条</Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="斐波那契关键位" size="small">
            <Table
              dataSource={waveAnalysis.fibonacciLevels.slice(0, 8)}
              size="small"
              pagination={false}
              columns={[
                {
                  title: '类型',
                  dataIndex: 'type',
                  key: 'type',
                  render: (type: string) => (
                    <Tag color={type === 'retracement' ? 'blue' : 'orange'}>
                      {type === 'retracement' ? '回调' : '扩展'}
                    </Tag>
                  )
                },
                {
                  title: '比例',
                  dataIndex: 'level',
                  key: 'level',
                  render: (level: number) => `${(level * 100).toFixed(1)}%`
                },
                {
                  title: '价格',
                  dataIndex: 'price',
                  key: 'price',
                  render: (price: number) => `¥${price.toFixed(2)}`
                },
                {
                  title: '重要性',
                  dataIndex: 'significance',
                  key: 'significance',
                  render: (significance: string) => (
                    <Tag color={significance === 'high' ? 'red' : significance === 'medium' ? 'orange' : 'green'}>
                      {significance === 'high' ? '高' : significance === 'medium' ? '中' : '低'}
                    </Tag>
                  )
                }
              ]}
            />
          </Card>
        </Col>
      </Row>
    )
  }

  const renderRiskAssessment = () => {
    if (!analysisData) return null

    const { riskAssessment } = analysisData

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="风险价值 (VaR)" size="small">
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Statistic
                  title="95% VaR"
                  value={riskAssessment.var.var95 * 100}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: riskAssessment.var.var95 > 0.05 ? '#ff4d4f' : '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="99% VaR"
                  value={riskAssessment.var.var99 * 100}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: riskAssessment.var.var99 > 0.08 ? '#ff4d4f' : '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="期望损失 95%"
                  value={riskAssessment.var.expectedShortfall95 * 100}
                  precision={2}
                  suffix="%"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="期望损失 99%"
                  value={riskAssessment.var.expectedShortfall99 * 100}
                  precision={2}
                  suffix="%"
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="动态止损建议" size="small">
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="推荐止损价">¥{riskAssessment.stopLoss.recommendedStop.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="止损类型">
                <Tag color="blue">{riskAssessment.stopLoss.stopType.toUpperCase()}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="风险金额">¥{riskAssessment.stopLoss.riskAmount.toFixed(0)}</Descriptions.Item>
              <Descriptions.Item label="风险比例">{(riskAssessment.stopLoss.riskPercentage * 100).toFixed(2)}%</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="风险预警" size="small">
            {riskAssessment.alerts.length > 0 ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                {riskAssessment.alerts.map((alert, index) => (
                  <Alert
                    key={index}
                    message={alert.message}
                    description={alert.recommendation}
                    type={alert.severity === 'critical' ? 'error' : alert.severity === 'high' ? 'warning' : 'info'}
                    showIcon
                    icon={alert.severity === 'critical' ? <WarningOutlined /> : undefined}
                  />
                ))}
              </Space>
            ) : (
              <Alert
                message="无风险预警"
                description="当前风险水平在可接受范围内"
                type="success"
                showIcon
                icon={<CheckCircleOutlined />}
              />
            )}
          </Card>
        </Col>
      </Row>
    )
  }

  const renderFactorAnalysis = () => {
    if (!analysisData) return null

    const { factorModel, strategyRecommendations } = analysisData

    const factorData = [
      ...factorModel.styleFactors.map(f => ({ ...f, category: '风格因子' })),
      ...factorModel.industryFactors.map(f => ({ ...f, category: '行业因子' })),
      ...factorModel.macroFactors.map(f => ({ ...f, category: '宏观因子' }))
    ]

    const radarData = factorData.map(factor => ({
      factor: factor.name,
      value: Math.abs(factor.value) * 100,
      category: factor.category
    }))

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="因子暴露分析" size="small">
            <Radar
              data={radarData}
              xField="factor"
              yField="value"
              seriesField="category"
              height={250}
              area={{}}
              point={{
                size: 2,
              }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="策略适应性评估" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>动量策略：</Text>
                <Progress 
                  percent={strategyRecommendations.momentum.score * 100} 
                  size="small"
                  status={strategyRecommendations.momentum.score > 0.3 ? 'success' : 'exception'}
                />
                <Text type="secondary">{strategyRecommendations.momentum.recommendation}</Text>
              </div>
              
              <div>
                <Text strong>均值回归策略：</Text>
                <Progress 
                  percent={strategyRecommendations.meanReversion.score * 100} 
                  size="small"
                  status={strategyRecommendations.meanReversion.score > 0.3 ? 'success' : 'exception'}
                />
                <Text type="secondary">{strategyRecommendations.meanReversion.recommendation}</Text>
              </div>
              
              <div>
                <Text strong>因子策略：</Text>
                <Progress 
                  percent={strategyRecommendations.factor.score * 100} 
                  size="small"
                  status={strategyRecommendations.factor.score > 0.3 ? 'success' : 'exception'}
                />
                <Text type="secondary">{strategyRecommendations.factor.recommendation}</Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="因子贡献分析" size="small">
            <Table
              dataSource={factorData}
              size="small"
              pagination={false}
              columns={[
                { title: '因子名称', dataIndex: 'name', key: 'name' },
                { title: '类型', dataIndex: 'category', key: 'category' },
                { 
                  title: '因子值', 
                  dataIndex: 'value', 
                  key: 'value',
                  render: (value: number) => (
                    <span style={{ color: value > 0 ? '#52c41a' : '#ff4d4f' }}>
                      {(value * 100).toFixed(3)}%
                    </span>
                  )
                },
                { 
                  title: '权重', 
                  dataIndex: 'weight', 
                  key: 'weight',
                  render: (weight: number) => `${(weight * 100).toFixed(1)}%`
                },
                { 
                  title: '显著性', 
                  dataIndex: 'significance', 
                  key: 'significance',
                  render: (significance: number) => (
                    <Progress 
                      percent={significance * 100} 
                      size="small" 
                      showInfo={false}
                      strokeColor={significance > 0.8 ? '#52c41a' : significance > 0.6 ? '#faad14' : '#ff4d4f'}
                    />
                  )
                }
              ]}
            />
          </Card>
        </Col>
      </Row>
    )
  }

  const tabItems = [
    {
      key: 'ml_prediction',
      label: (
        <span>
          <RobotOutlined />
          AI预测
        </span>
      ),
      children: renderMLPrediction(),
    },
    {
      key: 'wave_analysis',
      label: (
        <span>
          <EyeOutlined />
          波浪分析
        </span>
      ),
      children: renderWaveAnalysis(),
    },
    {
      key: 'risk_assessment',
      label: (
        <span>
          <WarningOutlined />
          风险评估
        </span>
      ),
      children: renderRiskAssessment(),
    },
    {
      key: 'factor_analysis',
      label: (
        <span>
          <ThunderboltOutlined />
          因子分析
        </span>
      ),
      children: renderFactorAnalysis(),
    },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>AI智能分析</Title>
        <Space>
          <Select
            value={selectedStock?.code || '000001'}
            onChange={(value) => setSelectedStock({ code: value, name: value })}
            style={{ width: 120 }}
          >
            <Option value="000001">平安银行</Option>
            <Option value="600519">贵州茅台</Option>
            <Option value="300750">宁德时代</Option>
            <Option value="002594">比亚迪</Option>
          </Select>
          <Select
            value={timeframe}
            onChange={setTimeframe}
            style={{ width: 100 }}
          >
            <Option value="1D">日线</Option>
            <Option value="1W">周线</Option>
            <Option value="1M">月线</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadAIAnalysis}
            loading={loading}
          >
            刷新分析
          </Button>
        </Space>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>AI正在进行深度分析...</Text>
          </div>
        </div>
      ) : (
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      )}
    </div>
  )
}

export default AIIntelligentAnalysis
