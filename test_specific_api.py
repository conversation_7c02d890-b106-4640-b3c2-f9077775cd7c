#!/usr/bin/env python3
"""
测试特定的API端点
"""

import requests
import json

def test_specific_stock_api():
    """测试特定股票的API"""
    try:
        print("🔍 测试特定股票API端点...")
        
        # 测试平安银行的数据
        stock_code = "000001"
        url = f"http://localhost:8000/api/v1/akshare/spot-data?stock_codes={stock_code}&limit=1"
        
        print(f"📡 调用API: {url}")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功，返回 {len(data)} 条数据")
            
            if data and len(data) > 0:
                stock = data[0]
                print(f"📊 股票数据:")
                print(f"  股票代码: {stock.get('stock_code')}")
                print(f"  股票名称: {stock.get('stock_name')}")
                print(f"  当前价格: ¥{stock.get('current_price')}")
                print(f"  涨跌幅: {stock.get('change_percent'):+.2f}%")
                print(f"  交易日期: {stock.get('trade_date')}")
                print(f"  更新时间: {stock.get('update_time')}")
                
                # 检查是否是真实数据
                current_price = float(stock.get('current_price'))
                if current_price != 10.5:
                    print("🎉 API返回真实数据!")
                    return True
                else:
                    print("⚠️  API返回模拟数据")
                    return False
            else:
                print("❌ API返回空数据")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_general_spot_api():
    """测试通用的spot-data API"""
    try:
        print("\n" + "=" * 50)
        print("🔍 测试通用spot-data API端点...")
        
        url = "http://localhost:8000/api/v1/akshare/spot-data?limit=5"
        
        print(f"📡 调用API: {url}")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功，返回 {len(data)} 条数据")
            
            if data and len(data) > 0:
                for i, stock in enumerate(data):
                    print(f"  {i+1}. {stock.get('stock_name')} ({stock.get('stock_code')}): ¥{stock.get('current_price')} ({stock.get('change_percent'):+.2f}%)")
                
                # 检查是否是真实数据
                first_price = float(data[0].get('current_price'))
                if first_price != 10.5:
                    print("🎉 API返回真实数据!")
                    return True
                else:
                    print("⚠️  API返回模拟数据")
                    return False
            else:
                print("❌ API返回空数据")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 80)
    print("🧪 API端点测试工具")
    print("=" * 80)
    
    # 测试特定股票API
    specific_result = test_specific_stock_api()
    
    # 测试通用API
    general_result = test_general_spot_api()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结")
    print("=" * 80)
    print(f"特定股票API: {'✅ 真实数据' if specific_result else '❌ 模拟数据'}")
    print(f"通用spot-data API: {'✅ 真实数据' if general_result else '❌ 模拟数据'}")
    
    if specific_result and general_result:
        print("🎉 所有API都返回真实数据!")
    elif specific_result or general_result:
        print("⚠️  部分API返回真实数据，部分返回模拟数据")
    else:
        print("❌ 所有API都返回模拟数据")
    
    print("=" * 80)
