import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Form,
  Select,
  InputNumber,
  Button,
  Table,
  Space,
  Typography,
  Tag,
  Progress,
  Tabs,
  Slider,
  Switch,
  Badge,
  Tooltip,
  message,
  Spin,
  Empty,
} from 'antd'
import {
  SearchOutlined,
  StarOutlined,
  TrophyOutlined,
  RobotOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select

interface ScreenerCriteria {
  // 基础指标
  marketCap: [number, number]
  peRatio: [number, number]
  pbRatio: [number, number]
  
  // 技术指标
  rsi: [number, number]
  macd: 'bullish' | 'bearish' | 'any'
  volume: 'increasing' | 'decreasing' | 'any'
  
  // AI预测
  aiPrediction: 'bullish' | 'bearish' | 'any'
  aiConfidence: [number, number]
  
  // K线形态
  patterns: string[]
  
  // 其他条件
  sector: string[]
  priceRange: [number, number]
}

interface ScreenerResult {
  code: string
  name: string
  currentPrice: number
  change: number
  changePercent: number
  marketCap: number
  peRatio: number
  pbRatio: number
  
  // 技术评分
  technicalScore: number
  fundamentalScore: number
  aiScore: number
  overallScore: number
  
  // AI预测
  aiPrediction: 'bullish' | 'bearish'
  aiConfidence: number
  aiTargetPrice: number
  
  // 技术指标
  rsi: number
  macd: number
  volumeRatio: number
  
  // 形态识别
  patterns: string[]
  
  sector: string
  recommendation: 'strong_buy' | 'buy' | 'hold' | 'sell' | 'strong_sell'
}

const SmartScreener: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<ScreenerResult[]>([])
  const [activeTab, setActiveTab] = useState('criteria')
  const [selectedCriteria, setSelectedCriteria] = useState<ScreenerCriteria>({
    marketCap: [1000000000, 1000000000000], // 10亿到1万亿
    peRatio: [0, 50],
    pbRatio: [0, 10],
    rsi: [30, 70],
    macd: 'any',
    volume: 'any',
    aiPrediction: 'any',
    aiConfidence: [60, 100],
    patterns: [],
    sector: [],
    priceRange: [1, 1000],
  })

  // 模拟选股结果
  const mockResults: ScreenerResult[] = [
    {
      code: 'AAPL',
      name: '苹果公司',
      currentPrice: 175.43,
      change: 2.15,
      changePercent: 1.24,
      marketCap: 2800000000000,
      peRatio: 28.5,
      pbRatio: 12.3,
      technicalScore: 85,
      fundamentalScore: 78,
      aiScore: 92,
      overallScore: 85,
      aiPrediction: 'bullish',
      aiConfidence: 87,
      aiTargetPrice: 185.50,
      rsi: 65,
      macd: 1.2,
      volumeRatio: 1.15,
      patterns: ['突破上升三角形', '金叉'],
      sector: '科技',
      recommendation: 'strong_buy'
    },
    {
      code: 'GOOGL',
      name: '谷歌',
      currentPrice: 2847.63,
      change: -15.42,
      changePercent: -0.54,
      marketCap: 1900000000000,
      peRatio: 25.8,
      pbRatio: 5.2,
      technicalScore: 72,
      fundamentalScore: 85,
      aiScore: 78,
      overallScore: 78,
      aiPrediction: 'bullish',
      aiConfidence: 75,
      aiTargetPrice: 2950.00,
      rsi: 45,
      macd: -0.8,
      volumeRatio: 0.95,
      patterns: ['整理形态'],
      sector: '科技',
      recommendation: 'buy'
    },
    {
      code: 'MSFT',
      name: '微软',
      currentPrice: 378.85,
      change: 4.23,
      changePercent: 1.13,
      marketCap: 2820000000000,
      peRatio: 32.1,
      pbRatio: 11.8,
      technicalScore: 88,
      fundamentalScore: 82,
      aiScore: 85,
      overallScore: 85,
      aiPrediction: 'bullish',
      aiConfidence: 82,
      aiTargetPrice: 395.00,
      rsi: 58,
      macd: 2.1,
      volumeRatio: 1.25,
      patterns: ['多头排列', 'MACD金叉'],
      sector: '科技',
      recommendation: 'strong_buy'
    }
  ]

  // 执行选股
  const handleScreen = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 根据条件过滤结果
      const filteredResults = mockResults.filter(stock => {
        return (
          stock.marketCap >= selectedCriteria.marketCap[0] &&
          stock.marketCap <= selectedCriteria.marketCap[1] &&
          stock.peRatio >= selectedCriteria.peRatio[0] &&
          stock.peRatio <= selectedCriteria.peRatio[1] &&
          stock.rsi >= selectedCriteria.rsi[0] &&
          stock.rsi <= selectedCriteria.rsi[1] &&
          stock.aiConfidence >= selectedCriteria.aiConfidence[0] &&
          stock.aiConfidence <= selectedCriteria.aiConfidence[1]
        )
      })
      
      setResults(filteredResults)
      setActiveTab('results')
      message.success(`找到 ${filteredResults.length} 只符合条件的股票`)
    } catch (error) {
      message.error('选股失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 重置条件
  const handleReset = () => {
    form.resetFields()
    setSelectedCriteria({
      marketCap: [1000000000, 1000000000000],
      peRatio: [0, 50],
      pbRatio: [0, 10],
      rsi: [30, 70],
      macd: 'any',
      volume: 'any',
      aiPrediction: 'any',
      aiConfidence: [60, 100],
      patterns: [],
      sector: [],
      priceRange: [1, 1000],
    })
    setResults([])
  }

  // 获取推荐标签颜色
  const getRecommendationColor = (recommendation: string) => {
    const colors = {
      strong_buy: 'red',
      buy: 'orange',
      hold: 'blue',
      sell: 'purple',
      strong_sell: 'magenta'
    }
    return colors[recommendation as keyof typeof colors] || 'default'
  }

  // 获取推荐文字
  const getRecommendationText = (recommendation: string) => {
    const texts = {
      strong_buy: '强烈买入',
      buy: '买入',
      hold: '持有',
      sell: '卖出',
      strong_sell: '强烈卖出'
    }
    return texts[recommendation as keyof typeof texts] || recommendation
  }

  // 表格列定义
  const columns = [
    {
      title: '股票',
      key: 'stock',
      render: (record: ScreenerResult) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.name}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{record.code}</div>
        </div>
      ),
    },
    {
      title: '当前价格',
      key: 'price',
      render: (record: ScreenerResult) => (
        <div>
          <div>${record.currentPrice.toFixed(2)}</div>
          <div style={{ 
            color: record.change >= 0 ? '#52c41a' : '#ff4d4f',
            fontSize: '12px'
          }}>
            {record.change >= 0 ? '+' : ''}{record.change.toFixed(2)} ({record.changePercent.toFixed(2)}%)
          </div>
        </div>
      ),
    },
    {
      title: '综合评分',
      dataIndex: 'overallScore',
      key: 'overallScore',
      render: (score: number) => (
        <div style={{ width: 80 }}>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={score >= 80 ? '#52c41a' : score >= 60 ? '#faad14' : '#ff4d4f'}
            showInfo={false}
          />
          <div style={{ textAlign: 'center', fontSize: '12px', marginTop: '2px' }}>
            {score}分
          </div>
        </div>
      ),
    },
    {
      title: 'AI预测',
      key: 'aiPrediction',
      render: (record: ScreenerResult) => (
        <div>
          <Tag color={record.aiPrediction === 'bullish' ? 'green' : 'red'}>
            {record.aiPrediction === 'bullish' ? '看涨' : '看跌'}
          </Tag>
          <div style={{ fontSize: '12px', color: '#666' }}>
            置信度: {record.aiConfidence}%
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            目标价: ${record.aiTargetPrice.toFixed(2)}
          </div>
        </div>
      ),
    },
    {
      title: '技术指标',
      key: 'technical',
      render: (record: ScreenerResult) => (
        <div>
          <div style={{ fontSize: '12px' }}>RSI: {record.rsi.toFixed(1)}</div>
          <div style={{ fontSize: '12px' }}>MACD: {record.macd.toFixed(2)}</div>
          <div style={{ fontSize: '12px' }}>成交量: {record.volumeRatio.toFixed(2)}x</div>
        </div>
      ),
    },
    {
      title: '形态识别',
      dataIndex: 'patterns',
      key: 'patterns',
      render: (patterns: string[]) => (
        <div>
          {patterns.map((pattern, index) => (
            <Tag key={index} size="small" style={{ marginBottom: '2px' }}>
              {pattern}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '推荐',
      dataIndex: 'recommendation',
      key: 'recommendation',
      render: (recommendation: string) => (
        <Tag color={getRecommendationColor(recommendation)}>
          {getRecommendationText(recommendation)}
        </Tag>
      ),
    },
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <TrophyOutlined /> 智能选股
      </Title>
      
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        items={[
          {
            key: 'criteria',
            label: (
              <span>
                <FilterOutlined />
                筛选条件
              </span>
            ),
            children: (
              <Card>
                <Form form={form} layout="vertical">
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <Title level={4}>基础指标</Title>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="市值范围 (亿元)">
                        <Slider
                          range
                          min={10}
                          max={50000}
                          value={[selectedCriteria.marketCap[0] / 100000000, selectedCriteria.marketCap[1] / 100000000]}
                          onChange={(value) => setSelectedCriteria(prev => ({
                            ...prev,
                            marketCap: [value[0] * 100000000, value[1] * 100000000]
                          }))}
                          tooltip={{ formatter: (value) => `${value}亿` }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="市盈率 (PE)">
                        <Slider
                          range
                          min={0}
                          max={100}
                          value={selectedCriteria.peRatio}
                          onChange={(value) => setSelectedCriteria(prev => ({ ...prev, peRatio: value }))}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="市净率 (PB)">
                        <Slider
                          range
                          min={0}
                          max={20}
                          value={selectedCriteria.pbRatio}
                          onChange={(value) => setSelectedCriteria(prev => ({ ...prev, pbRatio: value }))}
                        />
                      </Form.Item>
                    </Col>
                    
                    <Col span={24}>
                      <Title level={4}>技术指标</Title>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="RSI范围">
                        <Slider
                          range
                          min={0}
                          max={100}
                          value={selectedCriteria.rsi}
                          onChange={(value) => setSelectedCriteria(prev => ({ ...prev, rsi: value }))}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="MACD信号">
                        <Select
                          value={selectedCriteria.macd}
                          onChange={(value) => setSelectedCriteria(prev => ({ ...prev, macd: value }))}
                        >
                          <Option value="any">任意</Option>
                          <Option value="bullish">多头信号</Option>
                          <Option value="bearish">空头信号</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="成交量">
                        <Select
                          value={selectedCriteria.volume}
                          onChange={(value) => setSelectedCriteria(prev => ({ ...prev, volume: value }))}
                        >
                          <Option value="any">任意</Option>
                          <Option value="increasing">放量</Option>
                          <Option value="decreasing">缩量</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    
                    <Col span={24}>
                      <Title level={4}>AI预测</Title>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="AI预测方向">
                        <Select
                          value={selectedCriteria.aiPrediction}
                          onChange={(value) => setSelectedCriteria(prev => ({ ...prev, aiPrediction: value }))}
                        >
                          <Option value="any">任意</Option>
                          <Option value="bullish">看涨</Option>
                          <Option value="bearish">看跌</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="AI置信度 (%)">
                        <Slider
                          range
                          min={0}
                          max={100}
                          value={selectedCriteria.aiConfidence}
                          onChange={(value) => setSelectedCriteria(prev => ({ ...prev, aiConfidence: value }))}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
                    <Col>
                      <Button 
                        type="primary" 
                        icon={<SearchOutlined />}
                        onClick={handleScreen}
                        loading={loading}
                        size="large"
                      >
                        开始选股
                      </Button>
                    </Col>
                    <Col>
                      <Button 
                        icon={<ReloadOutlined />}
                        onClick={handleReset}
                        size="large"
                      >
                        重置条件
                      </Button>
                    </Col>
                  </Row>
                </Form>
              </Card>
            )
          },
          {
            key: 'results',
            label: (
              <span>
                <BarChartOutlined />
                选股结果 {results.length > 0 && <Badge count={results.length} />}
              </span>
            ),
            children: (
              <Card>
                {loading ? (
                  <div style={{ textAlign: 'center', padding: '50px' }}>
                    <Spin size="large" />
                    <div style={{ marginTop: '16px' }}>
                      <Text>AI正在分析股票数据...</Text>
                    </div>
                  </div>
                ) : results.length > 0 ? (
                  <>
                    <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                      <Text strong>找到 {results.length} 只符合条件的股票</Text>
                      <Button icon={<DownloadOutlined />} size="small">
                        导出结果
                      </Button>
                    </div>
                    <Table
                      dataSource={results}
                      columns={columns}
                      rowKey="code"
                      pagination={{ pageSize: 10 }}
                      size="small"
                    />
                  </>
                ) : (
                  <Empty 
                    description="暂无选股结果，请设置筛选条件后开始选股"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Card>
            )
          }
        ]}
      />
    </div>
  )
}

export default SmartScreener
