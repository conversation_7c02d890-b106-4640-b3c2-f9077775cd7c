/**
 * 股票分析加载状态管理Hook
 */

import { useState, useCallback } from 'react'
import { LoadingStep } from '../components/loading/StockAnalysisLoader'

export interface UseStockAnalysisLoaderReturn {
  loading: boolean
  steps: LoadingStep[]
  currentStep: number
  overallProgress: number
  startLoading: () => void
  updateStep: (key: string, status: LoadingStep['status'], error?: string, duration?: number) => void
  finishLoading: () => void
  resetLoading: () => void
}

const initialSteps: LoadingStep[] = [
  {
    key: 'kline',
    title: '获取K线数据',
    description: '正在从数据源获取历史价格数据...',
    status: 'waiting'
  },
  {
    key: 'realtime',
    title: '获取实时行情',
    description: '正在获取最新的股票价格和涨跌信息...',
    status: 'waiting'
  },
  {
    key: 'fundamentals',
    title: '获取基本面数据',
    description: '正在获取财务指标和基本面信息...',
    status: 'waiting'
  },
  {
    key: 'rsi',
    title: '计算RSI指标',
    description: '正在计算相对强弱指标...',
    status: 'waiting'
  },
  {
    key: 'macd',
    title: '计算MACD指标',
    description: '正在计算MACD指标和信号线...',
    status: 'waiting'
  },
  {
    key: 'kdj',
    title: '计算KDJ指标',
    description: '正在计算随机指标K、D、J值...',
    status: 'waiting'
  },
  {
    key: 'bollinger',
    title: '计算布林带指标',
    description: '正在计算布林带上下轨线...',
    status: 'waiting'
  },
  {
    key: 'keltner',
    title: '计算肯特纳通道',
    description: '正在计算肯特纳通道指标...',
    status: 'waiting'
  },
  {
    key: 'ai',
    title: '生成AI分析',
    description: '正在进行智能分析和预测...',
    status: 'waiting'
  }
]

export const useStockAnalysisLoader = (): UseStockAnalysisLoaderReturn => {
  const [loading, setLoading] = useState(false)
  const [steps, setSteps] = useState<LoadingStep[]>(initialSteps)
  const [currentStep, setCurrentStep] = useState(0)

  const startLoading = useCallback(() => {
    setLoading(true)
    setSteps(initialSteps.map(step => ({ ...step, status: 'waiting' as const })))
    setCurrentStep(0)
  }, [])

  const updateStep = useCallback((
    key: string, 
    status: LoadingStep['status'], 
    error?: string, 
    duration?: number
  ) => {
    setSteps(prevSteps => {
      const newSteps = prevSteps.map(step => {
        if (step.key === key) {
          return {
            ...step,
            status,
            error,
            duration
          }
        }
        return step
      })

      // 更新当前步骤索引
      if (status === 'process') {
        const stepIndex = newSteps.findIndex(step => step.key === key)
        setCurrentStep(stepIndex)
      }

      return newSteps
    })
  }, [])

  const finishLoading = useCallback(() => {
    setLoading(false)
    setCurrentStep(steps.length)
  }, [steps.length])

  const resetLoading = useCallback(() => {
    setLoading(false)
    setSteps(initialSteps.map(step => ({ ...step, status: 'waiting' as const })))
    setCurrentStep(0)
  }, [])

  // 计算整体进度
  const overallProgress = Math.round(
    (steps.filter(step => step.status === 'finish').length / steps.length) * 100
  )

  return {
    loading,
    steps,
    currentStep,
    overallProgress,
    startLoading,
    updateStep,
    finishLoading,
    resetLoading
  }
}

// 辅助函数：创建步骤执行器
export const createStepExecutor = (
  updateStep: UseStockAnalysisLoaderReturn['updateStep']
) => {
  return async <T>(
    stepKey: string,
    asyncFunction: () => Promise<T>
  ): Promise<T> => {
    const startTime = Date.now()
    
    try {
      updateStep(stepKey, 'process')
      const result = await asyncFunction()
      const duration = Date.now() - startTime
      updateStep(stepKey, 'finish', undefined, duration)
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      updateStep(stepKey, 'error', errorMessage, duration)
      throw error
    }
  }
}

// 性能优化建议
export const getPerformanceRecommendations = (steps: LoadingStep[]) => {
  const recommendations: string[] = []
  
  const slowSteps = steps.filter(step => 
    step.duration && step.duration > 2000 && step.status === 'finish'
  )
  
  if (slowSteps.length > 0) {
    recommendations.push(`发现 ${slowSteps.length} 个步骤耗时较长，建议优化网络连接或数据源`)
  }
  
  const errorSteps = steps.filter(step => step.status === 'error')
  if (errorSteps.length > 0) {
    recommendations.push(`${errorSteps.length} 个步骤失败，建议检查网络连接和API状态`)
  }
  
  const totalDuration = steps
    .filter(step => step.duration)
    .reduce((total, step) => total + (step.duration || 0), 0)
  
  if (totalDuration > 10000) {
    recommendations.push('总加载时间超过10秒，建议启用数据缓存或优化API调用')
  }
  
  return recommendations
}
