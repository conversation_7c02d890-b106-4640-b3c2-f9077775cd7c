/**
 * 专业级数据库管理服务
 * 提供完整的数据库管理API调用
 */

import axios from 'axios'

const API_BASE_URL = 'http://localhost:8000/api/v1/professional-db'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('[API Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`[API Response] ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('[API Response Error]', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export interface DatabaseOverview {
  database_info: {
    total_tables: number
    total_records: number
    active_stocks: number
    last_update: string | null
  }
  tables_info: Array<{
    table_name: string
    display_name: string
    category: string
    record_count: number
    latest_update: string | null
  }>
  category_stats: Record<string, { tables: number; records: number }>
  recent_updates: Array<{
    id: number
    data_type: string
    stock_code: string | null
    status: string
    records_count: number
    update_time: string
    duration: number
  }>
}

export interface TableStructure {
  table_name: string
  model_name: string
  columns: Array<{
    name: string
    type: string
    python_type?: string
    nullable: boolean
    primary_key: boolean
    foreign_key: boolean
    unique: boolean
    comment: string | null
  }>
  indexes: Array<{
    name: string
    columns: string[]
    unique: boolean
  }>
}

export interface TableDataResponse {
  table_name: string
  data: any[]
  pagination: {
    page: number
    page_size: number
    total_records: number
    total_pages: number
  }
  query_info: {
    sort_by: string | null
    sort_order: string
    filters: string | null
    search: string | null
  }
}

export interface TableDataParams {
  page?: number
  page_size?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  filters?: string
  search?: string
}

class ProfessionalDbService {
  /**
   * 获取数据库概览
   */
  async getDatabaseOverview(): Promise<DatabaseOverview> {
    try {
      const response = await apiClient.get('/overview')
      return response.data
    } catch (error) {
      console.error('获取数据库概览失败:', error)
      throw new Error('获取数据库概览失败')
    }
  }

  /**
   * 获取所有表的结构信息
   */
  async getTablesInfo(): Promise<{ tables: TableStructure[]; total_tables: number }> {
    try {
      const response = await apiClient.get('/tables')
      return response.data
    } catch (error) {
      console.error('获取表信息失败:', error)
      throw new Error('获取表信息失败')
    }
  }

  /**
   * 获取指定表的详细结构
   */
  async getTableStructure(tableName: string): Promise<TableStructure> {
    try {
      const response = await apiClient.get(`/tables/${tableName}/structure`)
      return response.data
    } catch (error) {
      console.error(`获取表 ${tableName} 结构失败:`, error)
      throw new Error(`获取表 ${tableName} 结构失败`)
    }
  }

  /**
   * 获取表数据
   */
  async getTableData(tableName: string, params: TableDataParams = {}): Promise<TableDataResponse> {
    try {
      const queryParams = new URLSearchParams()
      
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.page_size) queryParams.append('page_size', params.page_size.toString())
      if (params.sort_by) queryParams.append('sort_by', params.sort_by)
      if (params.sort_order) queryParams.append('sort_order', params.sort_order)
      if (params.filters) queryParams.append('filters', params.filters)
      if (params.search) queryParams.append('search', params.search)

      const response = await apiClient.get(`/tables/${tableName}/data?${queryParams.toString()}`)
      return response.data
    } catch (error) {
      console.error(`获取表 ${tableName} 数据失败:`, error)
      throw new Error(`获取表 ${tableName} 数据失败`)
    }
  }

  /**
   * 获取单条记录详情
   */
  async getRecordDetail(tableName: string, recordId: number): Promise<{
    table_name: string
    record_id: number
    data: any
    fields_metadata: Array<{
      name: string
      type: string
      nullable: boolean
      primary_key: boolean
      foreign_key: boolean
      comment: string | null
      editable: boolean
    }>
  }> {
    try {
      const response = await apiClient.get(`/tables/${tableName}/record/${recordId}`)
      return response.data
    } catch (error) {
      console.error(`获取记录详情失败:`, error)
      throw new Error('获取记录详情失败')
    }
  }

  /**
   * 更新记录
   */
  async updateRecord(tableName: string, recordId: number, updateData: Record<string, any>): Promise<{
    message: string
    table_name: string
    record_id: number
    updated_fields: string[]
  }> {
    try {
      const response = await apiClient.put(`/tables/${tableName}/record/${recordId}`, updateData)
      return response.data
    } catch (error) {
      console.error(`更新记录失败:`, error)
      throw new Error('更新记录失败')
    }
  }

  /**
   * 删除记录
   */
  async deleteRecord(tableName: string, recordId: number): Promise<{
    message: string
    table_name: string
    record_id: number
  }> {
    try {
      const response = await apiClient.delete(`/tables/${tableName}/record/${recordId}`)
      return response.data
    } catch (error) {
      console.error(`删除记录失败:`, error)
      throw new Error('删除记录失败')
    }
  }

  /**
   * 批量删除记录
   */
  async batchDeleteRecords(tableName: string, recordIds: number[]): Promise<{
    message: string
    table_name: string
    deleted_count: number
    deleted_ids: number[]
  }> {
    try {
      const response = await apiClient.post(`/tables/${tableName}/batch-delete`, recordIds)
      return response.data
    } catch (error) {
      console.error(`批量删除记录失败:`, error)
      throw new Error('批量删除记录失败')
    }
  }

  /**
   * 数据导入
   */
  async importData(tableName: string, file: File, options: {
    format: 'csv' | 'excel' | 'json'
    overwrite: boolean
    validate: boolean
  }): Promise<{
    message: string
    imported_count: number
    errors: string[]
  }> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('format', options.format)
      formData.append('overwrite', options.overwrite.toString())
      formData.append('validate', options.validate.toString())

      const response = await apiClient.post(`/tables/${tableName}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      return response.data
    } catch (error) {
      console.error(`数据导入失败:`, error)
      throw new Error('数据导入失败')
    }
  }

  /**
   * 数据导出
   */
  async exportData(tableName: string, options: {
    format: 'csv' | 'excel' | 'json'
    filters?: string
    columns?: string[]
  }): Promise<Blob> {
    try {
      const params = new URLSearchParams()
      params.append('format', options.format)
      if (options.filters) params.append('filters', options.filters)
      if (options.columns) params.append('columns', options.columns.join(','))

      const response = await apiClient.get(`/tables/${tableName}/export?${params.toString()}`, {
        responseType: 'blob',
      })
      return response.data
    } catch (error) {
      console.error(`数据导出失败:`, error)
      throw new Error('数据导出失败')
    }
  }

  /**
   * 数据质量检查
   */
  async checkDataQuality(tableName?: string): Promise<{
    table_name?: string
    quality_score: number
    issues: Array<{
      type: 'missing' | 'duplicate' | 'invalid' | 'inconsistent'
      description: string
      count: number
      severity: 'low' | 'medium' | 'high'
    }>
    recommendations: string[]
  }> {
    try {
      const url = tableName ? `/quality/check/${tableName}` : '/quality/check'
      const response = await apiClient.get(url)
      return response.data
    } catch (error) {
      console.error(`数据质量检查失败:`, error)
      throw new Error('数据质量检查失败')
    }
  }

  /**
   * 执行SQL查询
   */
  async executeQuery(sql: string, params?: any[]): Promise<{
    columns: string[]
    data: any[]
    row_count: number
    execution_time: number
  }> {
    try {
      const response = await apiClient.post('/query/execute', {
        sql,
        params: params || []
      })
      return response.data
    } catch (error) {
      console.error(`SQL查询执行失败:`, error)
      throw new Error('SQL查询执行失败')
    }
  }

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<{
    database_status: 'connected' | 'disconnected'
    total_connections: number
    memory_usage: number
    disk_usage: number
    last_backup: string | null
    uptime: number
  }> {
    try {
      const response = await apiClient.get('/system/status')
      return response.data
    } catch (error) {
      console.error(`获取系统状态失败:`, error)
      throw new Error('获取系统状态失败')
    }
  }
}

export const professionalDbService = new ProfessionalDbService()
export default professionalDbService
