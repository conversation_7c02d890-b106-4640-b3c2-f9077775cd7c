"""
选股系统相关API端点
"""

from typing import List, Optional
from datetime import date, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from app.core.database import get_db
from app.core.logging import get_logger
from app.services.stock_selection import StockScoringService, StockSelectionService
from app.models.stock_selection import StockScore, SelectionStrategy, SelectionResult
from app.tasks.analysis_tasks import stock_selection_task

router = APIRouter()
logger = get_logger(__name__)


@router.post("/{stock_code}/score")
async def calculate_stock_score(
    stock_code: str,
    background_tasks: BackgroundTasks
):
    """计算单只股票评分"""
    try:
        # 启动后台任务
        task = stock_selection_task.delay(stock_code, "single_score")
        
        # 同时进行同步计算
        async with StockScoringService() as scoring_service:
            score_result = await scoring_service.calculate_stock_score(stock_code)
            
            if "error" not in score_result:
                # 保存评分结果
                await scoring_service.save_stock_score(score_result)
        
        return {
            "code": 200,
            "message": "股票评分计算完成",
            "data": {
                "task_id": task.id,
                "score_result": score_result
            }
        }
    except Exception as e:
        logger.error(f"计算股票评分失败: {e}")
        raise HTTPException(status_code=500, detail="计算股票评分失败")


@router.get("/{stock_code}/score")
async def get_stock_score(
    stock_code: str,
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(30, ge=1, le=100, description="返回数量限制"),
    db: AsyncSession = Depends(get_db)
):
    """获取股票评分历史"""
    try:
        stmt = select(StockScore).where(StockScore.stock_code == stock_code)
        
        if start_date:
            stmt = stmt.where(StockScore.score_date >= start_date)
        if end_date:
            stmt = stmt.where(StockScore.score_date <= end_date)
        
        stmt = stmt.order_by(desc(StockScore.score_date)).limit(limit)
        
        result = await db.execute(stmt)
        scores = result.scalars().all()
        
        scores_data = []
        for score in scores:
            scores_data.append({
                "id": score.id,
                "stock_code": score.stock_code,
                "score_date": score.score_date.isoformat(),
                "total_score": float(score.total_score),
                "score_level": score.score_level,
                "technical_score": float(score.technical_score),
                "ai_score": float(score.ai_score) if score.ai_score else None,
                "momentum_score": float(score.momentum_score),
                "trend_score": float(score.trend_score),
                "volume_score": float(score.volume_score),
                "volatility_score": float(score.volatility_score),
                "recommendation": score.recommendation,
                "confidence": float(score.confidence),
                "score_details": score.score_details
            })
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock_code,
                "scores": scores_data,
                "total": len(scores_data)
            }
        }
    except Exception as e:
        logger.error(f"获取股票评分失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票评分失败")


@router.post("/strategies/{strategy_name}/run")
async def run_selection_strategy(
    strategy_name: str,
    background_tasks: BackgroundTasks,
    stock_list: Optional[List[str]] = None
):
    """执行选股策略"""
    try:
        # 启动后台任务
        task = stock_selection_task.delay(strategy_name, "strategy_run", stock_list)
        
        # 同时进行同步执行（用于立即返回结果）
        async with StockSelectionService() as selection_service:
            selection_result = await selection_service.run_selection_strategy(strategy_name, stock_list)
        
        return {
            "code": 200,
            "message": "选股策略执行完成",
            "data": {
                "task_id": task.id,
                "selection_result": selection_result
            }
        }
    except Exception as e:
        logger.error(f"执行选股策略失败: {e}")
        raise HTTPException(status_code=500, detail="执行选股策略失败")


@router.get("/strategies")
async def get_strategies(
    strategy_type: Optional[str] = Query(None, description="策略类型"),
    is_active: bool = Query(True, description="是否启用"),
    db: AsyncSession = Depends(get_db)
):
    """获取选股策略列表"""
    try:
        stmt = select(SelectionStrategy).where(SelectionStrategy.is_active == is_active)
        
        if strategy_type:
            stmt = stmt.where(SelectionStrategy.strategy_type == strategy_type)
        
        stmt = stmt.order_by(SelectionStrategy.created_at)
        
        result = await db.execute(stmt)
        strategies = result.scalars().all()
        
        strategies_data = []
        for strategy in strategies:
            strategies_data.append({
                "id": strategy.id,
                "strategy_name": strategy.strategy_name,
                "strategy_type": strategy.strategy_type,
                "description": strategy.description,
                "strategy_logic": strategy.strategy_logic,
                "filter_conditions": strategy.filter_conditions,
                "scoring_weights": strategy.scoring_weights,
                "min_score": float(strategy.min_score),
                "max_stocks": strategy.max_stocks,
                "is_active": strategy.is_active,
                "created_at": strategy.created_at.isoformat()
            })
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "strategies": strategies_data,
                "total": len(strategies_data)
            }
        }
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略列表失败")


@router.get("/results")
async def get_selection_results(
    strategy_name: Optional[str] = Query(None, description="策略名称"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    db: AsyncSession = Depends(get_db)
):
    """获取选股结果历史"""
    try:
        stmt = select(SelectionResult)
        
        conditions = []
        if start_date:
            conditions.append(SelectionResult.selection_date >= start_date)
        if end_date:
            conditions.append(SelectionResult.selection_date <= end_date)
        
        if strategy_name:
            # 需要关联策略表查询
            strategy_stmt = select(SelectionStrategy.id).where(SelectionStrategy.strategy_name == strategy_name)
            strategy_result = await db.execute(strategy_stmt)
            strategy_id = strategy_result.scalar_one_or_none()
            if strategy_id:
                conditions.append(SelectionResult.strategy_id == strategy_id)
        
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        stmt = stmt.order_by(desc(SelectionResult.selection_date)).limit(limit)
        
        result = await db.execute(stmt)
        results = result.scalars().all()
        
        results_data = []
        for res in results:
            # 获取策略名称
            strategy_stmt = select(SelectionStrategy.strategy_name).where(SelectionStrategy.id == res.strategy_id)
            strategy_result = await db.execute(strategy_stmt)
            strategy_name_db = strategy_result.scalar_one_or_none()
            
            results_data.append({
                "id": res.id,
                "strategy_id": res.strategy_id,
                "strategy_name": strategy_name_db,
                "selection_date": res.selection_date.isoformat(),
                "selected_stocks": res.selected_stocks,
                "total_count": res.total_count,
                "avg_score": float(res.avg_score),
                "score_distribution": res.score_distribution,
                "execution_time": float(res.execution_time),
                "processed_stocks": res.processed_stocks,
                "created_at": res.created_at.isoformat()
            })
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "results": results_data,
                "total": len(results_data),
                "filters": {
                    "strategy_name": strategy_name,
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None
                }
            }
        }
    except Exception as e:
        logger.error(f"获取选股结果失败: {e}")
        raise HTTPException(status_code=500, detail="获取选股结果失败")


@router.get("/rankings")
async def get_stock_rankings(
    score_date: Optional[date] = Query(None, description="评分日期"),
    min_score: float = Query(60, ge=0, le=100, description="最低评分"),
    score_level: Optional[str] = Query(None, description="评分等级"),
    recommendation: Optional[str] = Query(None, description="推荐等级"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    db: AsyncSession = Depends(get_db)
):
    """获取股票评分排行榜"""
    try:
        if not score_date:
            score_date = date.today()
        
        stmt = select(StockScore).where(
            and_(
                StockScore.score_date == score_date,
                StockScore.total_score >= min_score
            )
        )
        
        if score_level:
            stmt = stmt.where(StockScore.score_level == score_level)
        if recommendation:
            stmt = stmt.where(StockScore.recommendation == recommendation)
        
        stmt = stmt.order_by(desc(StockScore.total_score)).limit(limit)
        
        result = await db.execute(stmt)
        scores = result.scalars().all()
        
        rankings_data = []
        for i, score in enumerate(scores, 1):
            rankings_data.append({
                "rank": i,
                "stock_code": score.stock_code,
                "total_score": float(score.total_score),
                "score_level": score.score_level,
                "recommendation": score.recommendation,
                "confidence": float(score.confidence),
                "technical_score": float(score.technical_score),
                "ai_score": float(score.ai_score) if score.ai_score else None,
                "momentum_score": float(score.momentum_score),
                "trend_score": float(score.trend_score)
            })
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "rankings": rankings_data,
                "total": len(rankings_data),
                "score_date": score_date.isoformat(),
                "filters": {
                    "min_score": min_score,
                    "score_level": score_level,
                    "recommendation": recommendation
                }
            }
        }
    except Exception as e:
        logger.error(f"获取股票排行榜失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票排行榜失败")


@router.post("/strategies/init")
async def initialize_strategies():
    """初始化默认选股策略"""
    try:
        async with StockSelectionService() as selection_service:
            await selection_service.create_default_strategies()
        
        return {
            "code": 200,
            "message": "默认选股策略初始化完成",
            "data": {
                "initialized_at": date.today().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"初始化策略失败: {e}")
        raise HTTPException(status_code=500, detail="初始化策略失败")
