import React, { useState } from 'react'
import { Form, Input, But<PERSON>, Card, Typography, message, Tabs, Divider } from 'antd'
import { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { authService } from '@/services/authService'
import { useAuthStore } from '@/stores/authStore'

const { Title, Text, Link } = Typography
const { TabPane } = Tabs

interface LoginForm {
  email?: string
  username?: string
  password: string
}

interface RegisterForm {
  username?: string
  email: string
  password: string
  confirmPassword: string
  full_name?: string
  phone?: string
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('login')
  const navigate = useNavigate()
  const { setUser, setToken } = useAuthStore()

  const onLogin = async (values: LoginForm) => {
    setLoading(true)
    try {
      const response = await authService.login(values)
      if (response.code === 200) {
        const { access_token, user } = response.data
        setToken(access_token)
        setUser(user)
        message.success('登录成功')
        navigate('/')
      } else {
        message.error(response.message || '登录失败')
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '登录失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const onRegister = async (values: RegisterForm) => {
    if (values.password !== values.confirmPassword) {
      message.error('两次输入的密码不一致')
      return
    }

    setLoading(true)
    try {
      const { confirmPassword, ...registerData } = values
      const response = await authService.register(registerData)
      if (response.code === 200) {
        message.success('注册成功，请登录')
        setActiveTab('login')
      } else {
        message.error(response.message || '注册失败')
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '注册失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const LoginForm = () => (
    <Form
      name="login"
      onFinish={onLogin}
      autoComplete="off"
      size="large"
    >
      <Form.Item
        name="email"
        rules={[
          { required: true, message: '请输入邮箱或用户名' },
        ]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="邮箱或用户名"
        />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码' },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码"
        />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  )

  const RegisterForm = () => (
    <Form
      name="register"
      onFinish={onRegister}
      autoComplete="off"
      size="large"
    >
      <Form.Item
        name="email"
        rules={[
          { required: true, message: '请输入邮箱' },
          { type: 'email', message: '请输入有效的邮箱地址' },
        ]}
      >
        <Input
          prefix={<MailOutlined />}
          placeholder="邮箱"
        />
      </Form.Item>

      <Form.Item
        name="username"
        rules={[
          { min: 3, message: '用户名至少3个字符' },
          { max: 20, message: '用户名最多20个字符' },
        ]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="用户名（可选，系统将自动生成）"
        />
      </Form.Item>

      <Form.Item
        name="full_name"
      >
        <Input
          placeholder="真实姓名（可选）"
        />
      </Form.Item>

      <Form.Item
        name="phone"
        rules={[
          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
        ]}
      >
        <Input
          prefix={<PhoneOutlined />}
          placeholder="手机号（可选）"
        />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码' },
          { min: 8, message: '密码至少8位' },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码"
        />
      </Form.Item>

      <Form.Item
        name="confirmPassword"
        rules={[
          { required: true, message: '请确认密码' },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="确认密码"
        />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
        >
          注册
        </Button>
      </Form.Item>
    </Form>
  )

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            📈 智能股票分析平台
          </Title>
          <Text type="secondary">
            专业的股票分析与投资决策平台
          </Text>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          centered
        >
          <TabPane tab="登录" key="login">
            <LoginForm />
          </TabPane>
          <TabPane tab="注册" key="register">
            <RegisterForm />
          </TabPane>
        </Tabs>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            登录即表示您同意我们的服务条款和隐私政策
          </Text>
        </div>
      </Card>
    </div>
  )
}

export default Login
