/**
 * KDJ随机指标图表组件
 */

import React, { useMemo } from 'react'
import ReactECharts from 'echarts-for-react'

interface PriceData {
  timestamp: string
  high: number
  low: number
  close: number
}

interface KDJChartProps {
  data: PriceData[]
  height?: number
  timeFrame?: string
}

// 计算KDJ
const calculateKDJ = (data: PriceData[], period: number = 9, k_period: number = 3, d_period: number = 3) => {
  const rsv: number[] = []
  const k: number[] = []
  const d: number[] = []
  const j: number[] = []

  // 计算RSV
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      rsv.push(50) // 前period-1个值设为50
    } else {
      const periodData = data.slice(i - period + 1, i + 1)
      const highest = Math.max(...periodData.map(item => item.high))
      const lowest = Math.min(...periodData.map(item => item.low))
      const current = data[i].close
      
      if (highest === lowest) {
        rsv.push(50)
      } else {
        rsv.push(((current - lowest) / (highest - lowest)) * 100)
      }
    }
  }

  // 计算K值 (RSV的移动平均)
  k[0] = 50
  for (let i = 1; i < rsv.length; i++) {
    k[i] = (k[i - 1] * (k_period - 1) + rsv[i]) / k_period
  }

  // 计算D值 (K值的移动平均)
  d[0] = 50
  for (let i = 1; i < k.length; i++) {
    d[i] = (d[i - 1] * (d_period - 1) + k[i]) / d_period
  }

  // 计算J值
  for (let i = 0; i < k.length; i++) {
    j[i] = 3 * k[i] - 2 * d[i]
  }

  return { k, d, j }
}

const KDJChart: React.FC<KDJChartProps> = ({
  data,
  height = 250,
  timeFrame = '1D'
}) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null

    const kdjData = calculateKDJ(data)

    // 准备时间轴
    const timeAxis = data.map(item => {
      const date = new Date(item.timestamp)
      if (timeFrame === '1m' || timeFrame === '5m' || timeFrame === '15m' || timeFrame === '30m' || timeFrame === '1h') {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    })

    return {
      timeAxis,
      ...kdjData
    }
  }, [data, timeFrame])

  const option = useMemo(() => {
    if (!chartData) return {}

    return {
      backgroundColor: '#ffffff',
      grid: {
        left: '8%',
        right: '8%',
        top: '15%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.timeAxis,
        axisLabel: {
          fontSize: 10
        },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          fontSize: 10
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        textStyle: {
          color: '#000',
          fontSize: 12
        }
      },
      legend: {
        data: ['K', 'D', 'J'],
        top: '5%',
        textStyle: {
          fontSize: 10
        }
      },
      series: [
        {
          name: 'K',
          type: 'line',
          data: chartData.k,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          symbol: 'none'
        },
        {
          name: 'D',
          type: 'line',
          data: chartData.d,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 2
          },
          symbol: 'none'
        },
        {
          name: 'J',
          type: 'line',
          data: chartData.j,
          smooth: true,
          lineStyle: {
            color: '#faad14',
            width: 2
          },
          symbol: 'none'
        }
      ],
      // 添加超买超卖线
      markLine: {
        data: [
          { yAxis: 80, lineStyle: { color: '#ff4d4f', type: 'dashed' } },
          { yAxis: 20, lineStyle: { color: '#52c41a', type: 'dashed' } }
        ],
        label: {
          show: true,
          position: 'end',
          fontSize: 10
        }
      }
    }
  }, [chartData])

  if (!chartData) {
    return (
      <div style={{
        height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>暂无数据</div>
      </div>
    )
  }

  return (
    <ReactECharts
      option={option}
      style={{ height, width: '100%' }}
      opts={{ renderer: 'canvas' }}
    />
  )
}

export default KDJChart
