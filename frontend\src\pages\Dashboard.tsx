import React, { useState, useEffect } from 'react'
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Table,
  Tag,
  Progress,
  List,
  Avatar,
  Button,
  Spin,
  Badge,
  Divider,
  Tooltip,
  Flex,
  theme
} from 'antd'
import {
  <PERSON>UpOutlined,
  <PERSON>DownOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  WifiOutlined,
  DisconnectOutlined,
  RiseOutlined,
  FallOutlined,
  EyeOutlined,
  SettingOutlined,
  ReloadOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  PieChartOutlined
} from '@ant-design/icons'
import { useAuthStore } from '@/stores/authStore'
import { useRealtimeData } from '@/hooks/useRealtimeData'
import Simple<PERSON><PERSON> from '@/components/charts/SimpleChart'

const { Title, Text } = Typography

interface MarketData {
  index: string
  current: number
  change: number
  changePercent: number
  volume?: number
  turnover?: number
}

interface StockData {
  code: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  pe?: number
  sector?: string
}

interface AlertData {
  id: number
  type: string
  message: string
  time: string
  level: 'info' | 'warning' | 'error'
  stockCode?: string
}

interface PortfolioData {
  totalValue: number
  todayProfit: number
  totalReturn: number
  positions: {
    stocks: number
    funds: number
    cash: number
  }
}

interface QuickAction {
  key: string
  title: string
  icon: React.ReactNode
  color: string
  action: () => void
}

const Dashboard: React.FC = () => {
  const { user } = useAuthStore()
  const { token } = theme.useToken()
  const [loading, setLoading] = useState(true)
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [hotStocks, setHotStocks] = useState<StockData[]>([])
  const [alerts, setAlerts] = useState<AlertData[]>([])
  const [portfolio, setPortfolio] = useState<PortfolioData>({
    totalValue: 0,
    todayProfit: 0,
    totalReturn: 0,
    positions: { stocks: 0, funds: 0, cash: 0 }
  })
  const [refreshing, setRefreshing] = useState(false)

  // 实时数据
  const { data: realtimeData, connectionStatus, stats } = useRealtimeData({
    enableStockUpdates: true,
    enableAISignals: true,
    enableAlerts: true
  })

  // 快速操作按钮
  const quickActions: QuickAction[] = [
    {
      key: 'analysis',
      title: '股票分析',
      icon: <LineChartOutlined />,
      color: '#1890ff',
      action: () => window.location.href = '/analysis'
    },
    {
      key: 'selection',
      title: '智能选股',
      icon: <BarChartOutlined />,
      color: '#52c41a',
      action: () => window.location.href = '/selection'
    },
    {
      key: 'alerts',
      title: '预警设置',
      icon: <BellOutlined />,
      color: '#faad14',
      action: () => window.location.href = '/alerts'
    },
    {
      key: 'portfolio',
      title: '投资组合',
      icon: <PieChartOutlined />,
      color: '#722ed1',
      action: () => window.location.href = '/portfolio'
    }
  ]

  // 刷新数据
  const refreshData = async () => {
    setRefreshing(true)
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    loadData()
    setRefreshing(false)
  }

  const loadData = () => {
    setMarketData([
      {
        index: '上证指数',
        current: 3245.67,
        change: 15.23,
        changePercent: 0.47,
        volume: 234567890,
        turnover: 345678901234
      },
      {
        index: '深证成指',
        current: 12456.78,
        change: -23.45,
        changePercent: -0.19,
        volume: 187654321,
        turnover: 298765432109
      },
      {
        index: '创业板指',
        current: 2567.89,
        change: 8.90,
        changePercent: 0.35,
        volume: 156789012,
        turnover: 234567890123
      },
      {
        index: '科创50',
        current: 1234.56,
        change: -5.67,
        changePercent: -0.46,
        volume: 98765432,
        turnover: 156789012345
      },
    ])

    setHotStocks([
      {
        code: '000001',
        name: '平安银行',
        price: 12.45,
        change: 0.23,
        changePercent: 1.88,
        volume: 123456789,
        marketCap: 234567890123,
        pe: 5.67,
        sector: '银行'
      },
      {
        code: '000002',
        name: '万科A',
        price: 18.67,
        change: -0.45,
        changePercent: -2.35,
        volume: 98765432,
        marketCap: 198765432109,
        pe: 8.90,
        sector: '房地产'
      },
      {
        code: '600000',
        name: '浦发银行',
        price: 9.87,
        change: 0.12,
        changePercent: 1.23,
        volume: 87654321,
        marketCap: 156789012345,
        pe: 4.56,
        sector: '银行'
      },
      {
        code: '600036',
        name: '招商银行',
        price: 45.67,
        change: 1.23,
        changePercent: 2.77,
        volume: 76543210,
        marketCap: 345678901234,
        pe: 6.78,
        sector: '银行'
      },
      {
        code: '000858',
        name: '五粮液',
        price: 156.78,
        change: -2.34,
        changePercent: -1.47,
        volume: 65432109,
        marketCap: 567890123456,
        pe: 23.45,
        sector: '食品饮料'
      },
    ])

    setAlerts([
      {
        id: 1,
        type: '价格预警',
        message: '平安银行突破阻力位 12.50',
        time: '10:30',
        level: 'info',
        stockCode: '000001'
      },
      {
        id: 2,
        type: 'AI信号',
        message: '万科A出现强烈买入信号',
        time: '09:45',
        level: 'warning',
        stockCode: '000002'
      },
      {
        id: 3,
        type: '技术指标',
        message: '招商银行RSI达到超买区域',
        time: '09:15',
        level: 'error',
        stockCode: '600036'
      },
      {
        id: 4,
        type: '成交量',
        message: '五粮液成交量异常放大',
        time: '08:50',
        level: 'warning',
        stockCode: '000858'
      },
    ])

    setPortfolio({
      totalValue: 125678.90,
      todayProfit: 1234.56,
      totalReturn: 15.67,
      positions: {
        stocks: 75,
        funds: 20,
        cash: 5
      }
    })
  }

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      loadData()
      setLoading(false)
    }, 1000)
  }, [])

  const marketColumns = [
    {
      title: '指数名称',
      dataIndex: 'index',
      key: 'index',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '当前点位',
      dataIndex: 'current',
      key: 'current',
      render: (value: number) => (
        <Text style={{ fontSize: '16px', fontWeight: 500 }}>
          {value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
        </Text>
      ),
    },
    {
      title: '涨跌',
      dataIndex: 'change',
      key: 'change',
      render: (value: number) => (
        <Space>
          {value >= 0 ? <RiseOutlined style={{ color: '#52c41a' }} /> : <FallOutlined style={{ color: '#ff4d4f' }} />}
          <Text type={value >= 0 ? 'success' : 'danger'} style={{ fontWeight: 500 }}>
            {value >= 0 ? '+' : ''}{value.toFixed(2)}
          </Text>
        </Space>
      ),
    },
    {
      title: '涨跌幅',
      dataIndex: 'changePercent',
      key: 'changePercent',
      render: (value: number) => (
        <Tag color={value >= 0 ? 'green' : 'red'} style={{ fontWeight: 500 }}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Tag>
      ),
    },
  ]

  const stockColumns = [
    {
      title: '股票信息',
      key: 'stock',
      render: (record: StockData) => (
        <Space direction="vertical" size={0}>
          <Space>
            <Text strong style={{ color: token.colorPrimary }}>{record.code}</Text>
            <Text>{record.name}</Text>
          </Space>
          {record.sector && (
            <Tag size="small" color="blue">{record.sector}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '现价',
      dataIndex: 'price',
      key: 'price',
      render: (value: number) => (
        <Text style={{ fontSize: '16px', fontWeight: 500 }}>
          ¥{value.toFixed(2)}
        </Text>
      ),
    },
    {
      title: '涨跌幅',
      dataIndex: 'changePercent',
      key: 'changePercent',
      render: (value: number, record: StockData) => (
        <Space direction="vertical" size={0}>
          <Tag color={value >= 0 ? 'green' : 'red'} style={{ fontWeight: 500 }}>
            {value >= 0 ? '+' : ''}{value.toFixed(2)}%
          </Tag>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {value >= 0 ? '+' : ''}{record.change.toFixed(2)}
          </Text>
        </Space>
      ),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      render: (value: number) => (
        <Text type="secondary">
          {(value / 10000).toFixed(0)}万
        </Text>
      ),
    },
  ]

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div style={{ padding: '0 24px', background: token.colorBgContainer }}>
      {/* 顶部欢迎区域 */}
      <div style={{
        background: `linear-gradient(135deg, ${token.colorPrimary} 0%, ${token.colorPrimaryBorder} 100%)`,
        borderRadius: token.borderRadius,
        padding: '32px',
        marginBottom: 24,
        color: 'white'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={2} style={{ color: 'white', margin: 0 }}>
              欢迎回来，{user?.full_name || user?.username || '投资者'}！
            </Title>
            <Text style={{ color: 'rgba(255,255,255,0.85)', fontSize: '16px' }}>
              {new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
              })} · 让数据驱动投资决策
            </Text>
          </Col>
          <Col>
            <Space size="large">
              {/* 连接状态 */}
              <Tooltip title={connectionStatus === 'connected' ? '实时数据连接正常' : '实时数据连接异常'}>
                <Badge
                  status={connectionStatus === 'connected' ? 'success' : 'error'}
                  text={
                    <Space style={{ color: 'white' }}>
                      {connectionStatus === 'connected' ? <WifiOutlined /> : <DisconnectOutlined />}
                      {connectionStatus === 'connected' ? '实时连接' : '连接断开'}
                    </Space>
                  }
                />
              </Tooltip>

              {/* 刷新按钮 */}
              <Tooltip title="刷新数据">
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  loading={refreshing}
                  onClick={refreshData}
                  style={{ color: 'white' }}
                />
              </Tooltip>

              {/* 预警提示 */}
              {stats.unreadAlerts > 0 && (
                <Badge count={stats.unreadAlerts} style={{ backgroundColor: '#f50' }}>
                  <BellOutlined style={{ fontSize: 18, color: 'white' }} />
                </Badge>
              )}
            </Space>
          </Col>
        </Row>
      </div>

      {/* 快速操作区域 */}
      <Card style={{ marginBottom: 24 }} styles={{ body: { padding: '16px 24px' } }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Text strong style={{ fontSize: '16px' }}>快速操作</Text>
          </Col>
          <Col>
            <Space size="large">
              {quickActions.map(action => (
                <Tooltip key={action.key} title={action.title}>
                  <Button
                    type="text"
                    icon={action.icon}
                    onClick={action.action}
                    style={{
                      color: action.color,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      height: 'auto',
                      padding: '8px 16px'
                    }}
                  >
                    <div style={{ fontSize: '20px', marginBottom: '4px' }}>
                      {action.icon}
                    </div>
                    <div style={{ fontSize: '12px' }}>
                      {action.title}
                    </div>
                  </Button>
                </Tooltip>
              ))}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 核心数据统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              borderLeft: `4px solid ${token.colorSuccess}`,
              background: `linear-gradient(135deg, ${token.colorBgContainer} 0%, ${token.colorFillQuaternary} 100%)`
            }}
          >
            <Statistic
              title="投资组合总值"
              value={portfolio.totalValue}
              precision={2}
              valueStyle={{ color: token.colorSuccess, fontSize: '24px', fontWeight: 'bold' }}
              prefix={<DollarOutlined style={{ color: token.colorSuccess }} />}
              suffix="¥"
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              较昨日 +2.34%
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              borderLeft: `4px solid ${portfolio.todayProfit >= 0 ? token.colorSuccess : token.colorError}`,
              background: `linear-gradient(135deg, ${token.colorBgContainer} 0%, ${token.colorFillQuaternary} 100%)`
            }}
          >
            <Statistic
              title="今日收益"
              value={portfolio.todayProfit}
              precision={2}
              valueStyle={{
                color: portfolio.todayProfit >= 0 ? token.colorSuccess : token.colorError,
                fontSize: '24px',
                fontWeight: 'bold'
              }}
              prefix={portfolio.todayProfit >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              suffix="¥"
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {portfolio.todayProfit >= 0 ? '盈利' : '亏损'} {Math.abs((portfolio.todayProfit / portfolio.totalValue) * 100).toFixed(2)}%
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              borderLeft: `4px solid ${token.colorWarning}`,
              background: `linear-gradient(135deg, ${token.colorBgContainer} 0%, ${token.colorFillQuaternary} 100%)`
            }}
          >
            <Statistic
              title="总收益率"
              value={portfolio.totalReturn}
              precision={2}
              valueStyle={{ color: token.colorWarning, fontSize: '24px', fontWeight: 'bold' }}
              prefix={<TrophyOutlined style={{ color: token.colorWarning }} />}
              suffix="%"
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              年化收益率 18.9%
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              borderLeft: `4px solid ${token.colorError}`,
              background: `linear-gradient(135deg, ${token.colorBgContainer} 0%, ${token.colorFillQuaternary} 100%)`
            }}
          >
            <Statistic
              title="活跃预警"
              value={alerts.length}
              valueStyle={{ color: token.colorError, fontSize: '24px', fontWeight: 'bold' }}
              prefix={<AlertOutlined style={{ color: token.colorError }} />}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {alerts.filter(a => a.level === 'error').length} 个高风险预警
            </Text>
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        {/* 市场概览 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <BarChartOutlined style={{ color: token.colorPrimary }} />
                <Text strong>市场概览</Text>
              </Space>
            }
            extra={
              <Space>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  更新时间: {new Date().toLocaleTimeString()}
                </Text>
                <Button type="link" size="small" icon={<EyeOutlined />}>
                  查看更多
                </Button>
              </Space>
            }
            hoverable
          >
            <Table
              dataSource={marketData}
              columns={marketColumns}
              pagination={false}
              size="middle"
              rowKey="index"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>

        {/* 热门股票 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <StarOutlined style={{ color: token.colorWarning }} />
                <Text strong>热门股票</Text>
              </Space>
            }
            extra={
              <Space>
                <Tag color="blue">实时</Tag>
                <Button type="link" size="small" icon={<EyeOutlined />}>
                  查看更多
                </Button>
              </Space>
            }
            hoverable
          >
            <Table
              dataSource={hotStocks}
              columns={stockColumns}
              pagination={false}
              size="middle"
              rowKey="code"
              style={{ marginTop: 8 }}
              onRow={(record) => ({
                onClick: () => {
                  window.location.href = `/analysis?stock=${record.code}`
                },
                style: { cursor: 'pointer' }
              })}
            />
          </Card>
        </Col>

        {/* 最新预警 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <BellOutlined style={{ color: token.colorError }} />
                <Text strong>最新预警</Text>
                <Badge count={alerts.length} style={{ backgroundColor: token.colorError }} />
              </Space>
            }
            extra={
              <Space>
                <Button type="link" size="small" icon={<SettingOutlined />}>
                  设置
                </Button>
                <Button type="link" size="small" icon={<EyeOutlined />}>
                  查看全部
                </Button>
              </Space>
            }
            hoverable
          >
            <List
              dataSource={alerts}
              renderItem={(item) => (
                <List.Item
                  style={{
                    padding: '12px 0',
                    borderBottom: `1px solid ${token.colorBorderSecondary}`,
                    cursor: 'pointer'
                  }}
                  onClick={() => {
                    if (item.stockCode) {
                      window.location.href = `/analysis?stock=${item.stockCode}`
                    }
                  }}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<AlertOutlined />}
                        style={{
                          backgroundColor:
                            item.level === 'error' ? token.colorError :
                            item.level === 'warning' ? token.colorWarning :
                            token.colorInfo
                        }}
                      />
                    }
                    title={
                      <Space>
                        <Text strong>{item.type}</Text>
                        {item.stockCode && (
                          <Tag size="small" color="blue">{item.stockCode}</Tag>
                        )}
                      </Space>
                    }
                    description={
                      <Text type="secondary" style={{ fontSize: '13px' }}>
                        {item.message}
                      </Text>
                    }
                  />
                  <Space direction="vertical" align="end">
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {item.time}
                    </Text>
                    <Tag
                      color={
                        item.level === 'error' ? 'red' :
                        item.level === 'warning' ? 'orange' : 'blue'
                      }
                      size="small"
                    >
                      {item.level === 'error' ? '高风险' :
                       item.level === 'warning' ? '中风险' : '提醒'}
                    </Tag>
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 投资组合表现 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <PieChartOutlined style={{ color: token.colorSuccess }} />
                <Text strong>投资组合表现</Text>
              </Space>
            }
            extra={
              <Button type="link" size="small" icon={<EyeOutlined />}>
                详细分析
              </Button>
            }
            hoverable
          >
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <Flex justify="space-between" align="center" style={{ marginBottom: 8 }}>
                  <Text strong>股票配置</Text>
                  <Text type="secondary">{portfolio.positions.stocks}%</Text>
                </Flex>
                <Progress
                  percent={portfolio.positions.stocks}
                  strokeColor={token.colorSuccess}
                  trailColor={token.colorFillQuaternary}
                  size={[null, 8]}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  ¥{(portfolio.totalValue * portfolio.positions.stocks / 100).toLocaleString()}
                </Text>
              </div>

              <div>
                <Flex justify="space-between" align="center" style={{ marginBottom: 8 }}>
                  <Text strong>基金配置</Text>
                  <Text type="secondary">{portfolio.positions.funds}%</Text>
                </Flex>
                <Progress
                  percent={portfolio.positions.funds}
                  strokeColor={token.colorPrimary}
                  trailColor={token.colorFillQuaternary}
                  size={[null, 8]}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  ¥{(portfolio.totalValue * portfolio.positions.funds / 100).toLocaleString()}
                </Text>
              </div>

              <div>
                <Flex justify="space-between" align="center" style={{ marginBottom: 8 }}>
                  <Text strong>现金配置</Text>
                  <Text type="secondary">{portfolio.positions.cash}%</Text>
                </Flex>
                <Progress
                  percent={portfolio.positions.cash}
                  strokeColor={token.colorWarning}
                  trailColor={token.colorFillQuaternary}
                  size={[null, 8]}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  ¥{(portfolio.totalValue * portfolio.positions.cash / 100).toLocaleString()}
                </Text>
              </div>

              <Divider style={{ margin: '16px 0' }} />

              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="持仓股票"
                    value={12}
                    valueStyle={{ fontSize: '18px', color: token.colorText }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="盈利股票"
                    value={8}
                    valueStyle={{ fontSize: '18px', color: token.colorSuccess }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="亏损股票"
                    value={4}
                    valueStyle={{ fontSize: '18px', color: token.colorError }}
                  />
                </Col>
              </Row>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
