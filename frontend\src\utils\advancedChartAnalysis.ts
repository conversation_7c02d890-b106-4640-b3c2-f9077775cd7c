/**
 * 高级图表分析 - 艾略特波浪理论和江恩理论
 * 实现专业的波浪分析和时间周期分析
 */

import { PriceData } from './advancedIndicators'

export interface ElliottWave {
  waveType: 'impulse' | 'corrective'
  waveNumber: number
  startPrice: number
  endPrice: number
  startTime: string
  endTime: string
  confidence: number
  subWaves?: ElliottWave[]
}

export interface WaveCount {
  primaryWave: ElliottWave[]
  alternativeCount?: ElliottWave[]
  currentWave: number
  waveProgress: number
  nextTarget: number
  invalidationLevel: number
}

export interface FibonacciLevel {
  level: number
  price: number
  type: 'retracement' | 'extension'
  significance: 'high' | 'medium' | 'low'
}

export interface GannAngle {
  angle: number
  slope: number
  startPrice: number
  startTime: string
  currentPrice: number
  support: boolean
  resistance: boolean
}

export interface TimeCycle {
  period: number
  nextCycleDate: string
  cycleType: 'major' | 'intermediate' | 'minor'
  confidence: number
  historicalAccuracy: number
}

export interface GannSquare {
  centerPrice: number
  squareSize: number
  supportLevels: number[]
  resistanceLevels: number[]
  timeSquares: string[]
}

export interface WaveAnalysisResult {
  elliottWave: WaveCount
  fibonacciLevels: FibonacciLevel[]
  waveTargets: {
    bullishTarget: number
    bearishTarget: number
    neutralTarget: number
  }
  confidence: number
}

export interface GannAnalysisResult {
  gannAngles: GannAngle[]
  timeCycles: TimeCycle[]
  gannSquare: GannSquare
  priceTargets: number[]
  timeTargets: string[]
}

/**
 * 高级图表分析器
 */
export class AdvancedChartAnalysis {

  /**
   * 艾略特波浪分析
   */
  static analyzeElliottWaves(data: PriceData[]): WaveAnalysisResult {
    const waves = this.identifyWaves(data)
    const fibLevels = this.calculateFibonacciLevels(data)
    const targets = this.calculateWaveTargets(waves, data)
    
    return {
      elliottWave: {
        primaryWave: waves,
        currentWave: this.getCurrentWaveNumber(waves),
        waveProgress: this.calculateWaveProgress(waves, data),
        nextTarget: targets.bullishTarget,
        invalidationLevel: this.calculateInvalidationLevel(waves)
      },
      fibonacciLevels: fibLevels,
      waveTargets: targets,
      confidence: this.calculateWaveConfidence(waves)
    }
  }

  /**
   * 识别波浪结构
   */
  private static identifyWaves(data: PriceData[]): ElliottWave[] {
    const waves: ElliottWave[] = []
    const pivots = this.findPivotPoints(data)
    
    // 简化的波浪识别逻辑
    for (let i = 0; i < pivots.length - 1; i++) {
      const startPivot = pivots[i]
      const endPivot = pivots[i + 1]
      
      const wave: ElliottWave = {
        waveType: i % 2 === 0 ? 'impulse' : 'corrective',
        waveNumber: (i % 5) + 1,
        startPrice: startPivot.price,
        endPrice: endPivot.price,
        startTime: startPivot.time,
        endTime: endPivot.time,
        confidence: this.calculateWaveConfidence([])
      }
      
      waves.push(wave)
    }
    
    return waves
  }

  /**
   * 寻找关键转折点
   */
  private static findPivotPoints(data: PriceData[]): { price: number, time: string, type: 'high' | 'low' }[] {
    const pivots = []
    const lookback = 5
    
    for (let i = lookback; i < data.length - lookback; i++) {
      const current = data[i]
      const isHigh = this.isLocalHigh(data, i, lookback)
      const isLow = this.isLocalLow(data, i, lookback)
      
      if (isHigh) {
        pivots.push({ price: current.high, time: current.timestamp, type: 'high' as const })
      } else if (isLow) {
        pivots.push({ price: current.low, time: current.timestamp, type: 'low' as const })
      }
    }
    
    return pivots
  }

  private static isLocalHigh(data: PriceData[], index: number, lookback: number): boolean {
    const current = data[index].high
    
    for (let i = index - lookback; i <= index + lookback; i++) {
      if (i !== index && data[i] && data[i].high >= current) {
        return false
      }
    }
    return true
  }

  private static isLocalLow(data: PriceData[], index: number, lookback: number): boolean {
    const current = data[index].low
    
    for (let i = index - lookback; i <= index + lookback; i++) {
      if (i !== index && data[i] && data[i].low <= current) {
        return false
      }
    }
    return true
  }

  /**
   * 计算斐波那契回调和扩展位
   */
  static calculateFibonacciLevels(data: PriceData[]): FibonacciLevel[] {
    const levels: FibonacciLevel[] = []
    const pivots = this.findPivotPoints(data)
    
    if (pivots.length < 2) return levels
    
    const lastHigh = pivots.filter(p => p.type === 'high').pop()
    const lastLow = pivots.filter(p => p.type === 'low').pop()
    
    if (!lastHigh || !lastLow) return levels
    
    const range = Math.abs(lastHigh.price - lastLow.price)
    const isUptrend = lastHigh.price > lastLow.price
    
    // 斐波那契回调位
    const retracementLevels = [0.236, 0.382, 0.5, 0.618, 0.786]
    retracementLevels.forEach(ratio => {
      const price = isUptrend 
        ? lastHigh.price - (range * ratio)
        : lastLow.price + (range * ratio)
      
      levels.push({
        level: ratio,
        price,
        type: 'retracement',
        significance: ratio === 0.618 ? 'high' : ratio === 0.382 ? 'medium' : 'low'
      })
    })
    
    // 斐波那契扩展位
    const extensionLevels = [1.272, 1.414, 1.618, 2.618]
    extensionLevels.forEach(ratio => {
      const price = isUptrend 
        ? lastHigh.price + (range * (ratio - 1))
        : lastLow.price - (range * (ratio - 1))
      
      levels.push({
        level: ratio,
        price,
        type: 'extension',
        significance: ratio === 1.618 ? 'high' : 'medium'
      })
    })
    
    return levels
  }

  /**
   * 江恩角度线分析
   */
  static analyzeGannAngles(data: PriceData[]): GannAnalysisResult {
    const gannAngles = this.calculateGannAngles(data)
    const timeCycles = this.identifyTimeCycles(data)
    const gannSquare = this.calculateGannSquare(data)
    const priceTargets = this.calculateGannPriceTargets(data)
    const timeTargets = this.calculateGannTimeTargets(data)
    
    return {
      gannAngles,
      timeCycles,
      gannSquare,
      priceTargets,
      timeTargets
    }
  }

  /**
   * 计算江恩角度线
   */
  private static calculateGannAngles(data: PriceData[]): GannAngle[] {
    const angles: GannAngle[] = []
    const pivots = this.findPivotPoints(data)
    
    if (pivots.length < 2) return angles
    
    const lastPivot = pivots[pivots.length - 1]
    const currentPrice = data[data.length - 1].close
    
    // 主要江恩角度
    const gannAngles = [
      { angle: 45, ratio: 1 },    // 1x1
      { angle: 63.75, ratio: 2 }, // 2x1
      { angle: 71.25, ratio: 3 }, // 3x1
      { angle: 75, ratio: 4 },    // 4x1
      { angle: 26.25, ratio: 0.5 }, // 1x2
      { angle: 18.75, ratio: 0.33 }, // 1x3
      { angle: 15, ratio: 0.25 }  // 1x4
    ]
    
    gannAngles.forEach(({ angle, ratio }) => {
      const slope = Math.tan(angle * Math.PI / 180)
      
      angles.push({
        angle,
        slope,
        startPrice: lastPivot.price,
        startTime: lastPivot.time,
        currentPrice: lastPivot.price + (slope * 10), // 简化计算
        support: lastPivot.type === 'low',
        resistance: lastPivot.type === 'high'
      })
    })
    
    return angles
  }

  /**
   * 识别时间周期
   */
  private static identifyTimeCycles(data: PriceData[]): TimeCycle[] {
    const cycles: TimeCycle[] = []
    
    // 常见的江恩时间周期
    const cyclePeriods = [7, 14, 30, 60, 90, 120, 180, 360]
    
    cyclePeriods.forEach(period => {
      const nextCycleDate = new Date()
      nextCycleDate.setDate(nextCycleDate.getDate() + period)
      
      cycles.push({
        period,
        nextCycleDate: nextCycleDate.toISOString().split('T')[0],
        cycleType: period <= 30 ? 'minor' : period <= 120 ? 'intermediate' : 'major',
        confidence: 0.6 + Math.random() * 0.3,
        historicalAccuracy: 0.5 + Math.random() * 0.4
      })
    })
    
    return cycles
  }

  /**
   * 计算江恩四方形
   */
  private static calculateGannSquare(data: PriceData[]): GannSquare {
    const currentPrice = data[data.length - 1].close
    const squareSize = this.calculateSquareSize(currentPrice)
    
    const supportLevels = []
    const resistanceLevels = []
    
    // 计算支撑和阻力位
    for (let i = 1; i <= 8; i++) {
      supportLevels.push(currentPrice - (squareSize * i))
      resistanceLevels.push(currentPrice + (squareSize * i))
    }
    
    // 时间四方形
    const timeSquares = []
    const currentDate = new Date()
    for (let i = 1; i <= 4; i++) {
      const futureDate = new Date(currentDate)
      futureDate.setDate(currentDate.getDate() + (squareSize * i))
      timeSquares.push(futureDate.toISOString().split('T')[0])
    }
    
    return {
      centerPrice: currentPrice,
      squareSize,
      supportLevels,
      resistanceLevels,
      timeSquares
    }
  }

  private static calculateSquareSize(price: number): number {
    // 根据价格计算合适的四方形大小
    if (price < 10) return 0.1
    if (price < 100) return 1
    if (price < 1000) return 10
    return 100
  }

  private static calculateGannPriceTargets(data: PriceData[]): number[] {
    const currentPrice = data[data.length - 1].close
    const squareSize = this.calculateSquareSize(currentPrice)
    
    return [
      currentPrice + squareSize,
      currentPrice + squareSize * 2,
      currentPrice + squareSize * 3,
      currentPrice - squareSize,
      currentPrice - squareSize * 2,
      currentPrice - squareSize * 3
    ]
  }

  private static calculateGannTimeTargets(data: PriceData[]): string[] {
    const targets = []
    const currentDate = new Date()
    
    // 重要的江恩时间周期
    const timePeriods = [7, 14, 30, 45, 60, 90]
    
    timePeriods.forEach(days => {
      const targetDate = new Date(currentDate)
      targetDate.setDate(currentDate.getDate() + days)
      targets.push(targetDate.toISOString().split('T')[0])
    })
    
    return targets
  }

  // 辅助方法
  private static getCurrentWaveNumber(waves: ElliottWave[]): number {
    return waves.length > 0 ? waves[waves.length - 1].waveNumber : 1
  }

  private static calculateWaveProgress(waves: ElliottWave[], data: PriceData[]): number {
    if (waves.length === 0) return 0
    
    const currentWave = waves[waves.length - 1]
    const currentPrice = data[data.length - 1].close
    const waveRange = Math.abs(currentWave.endPrice - currentWave.startPrice)
    const currentProgress = Math.abs(currentPrice - currentWave.startPrice)
    
    return Math.min(1, currentProgress / waveRange)
  }

  private static calculateWaveTargets(waves: ElliottWave[], data: PriceData[]): {
    bullishTarget: number
    bearishTarget: number
    neutralTarget: number
  } {
    const currentPrice = data[data.length - 1].close
    const volatility = this.calculateVolatility(data.slice(-20))
    
    return {
      bullishTarget: currentPrice * (1 + volatility * 2),
      bearishTarget: currentPrice * (1 - volatility * 2),
      neutralTarget: currentPrice
    }
  }

  private static calculateInvalidationLevel(waves: ElliottWave[]): number {
    if (waves.length === 0) return 0
    
    const lastWave = waves[waves.length - 1]
    return lastWave.waveType === 'impulse' ? lastWave.startPrice : lastWave.endPrice
  }

  private static calculateWaveConfidence(waves: ElliottWave[]): number {
    // 简化的置信度计算
    return 0.6 + Math.random() * 0.3
  }

  private static calculateVolatility(data: PriceData[]): number {
    const returns = []
    for (let i = 1; i < data.length; i++) {
      returns.push((data[i].close - data[i - 1].close) / data[i - 1].close)
    }
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length
    
    return Math.sqrt(variance)
  }
}

export default AdvancedChartAnalysis
