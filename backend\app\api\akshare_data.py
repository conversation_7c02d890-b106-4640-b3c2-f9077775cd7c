"""
AKShare数据接口API
"""

from loguru import logger
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from pydantic import BaseModel

from app.core.database import get_db
from app.models.stock import (
    Stock, StockDetailInfo, SectorData, SectorStock, StockSpotData,
    MarketSummary, RegionTradingData, IndustryTradingData, StockBidAsk
)

router = APIRouter()

# Pydantic模型定义
class StockDetailResponse(BaseModel):
    stock_code: str
    stock_name: Optional[str] = None
    full_name: Optional[str] = None
    market: Optional[str] = None
    exchange: Optional[str] = None
    list_date: Optional[str] = None
    total_shares: Optional[float] = None
    float_shares: Optional[float] = None
    total_market_cap: Optional[float] = None
    float_market_cap: Optional[float] = None
    industry: Optional[str] = None
    sector: Optional[str] = None
    concept: Optional[str] = None
    pe_ratio: Optional[float] = None
    pb_ratio: Optional[float] = None
    roe: Optional[float] = None
    roa: Optional[float] = None
    legal_representative: Optional[str] = None
    general_manager: Optional[str] = None
    secretary: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    main_business: Optional[str] = None
    business_scope: Optional[str] = None
    company_profile: Optional[str] = None
    employee_count: Optional[int] = None
    registered_capital: Optional[float] = None
    actual_controller: Optional[str] = None
    data_date: Optional[str] = None
    updated_at: Optional[str] = None

class SectorDataResponse(BaseModel):
    sector_code: str
    sector_name: str
    sector_type: str
    stock_count: int
    total_market_cap: Optional[float] = None
    avg_pe_ratio: Optional[float] = None
    avg_pb_ratio: Optional[float] = None
    up_count: int
    down_count: int
    flat_count: int
    total_volume: Optional[float] = None
    total_turnover: Optional[float] = None
    avg_turnover_rate: Optional[float] = None
    sector_change: Optional[float] = None
    sector_change_amount: Optional[float] = None
    trade_date: str

class StockSpotResponse(BaseModel):
    stock_code: str
    stock_name: str
    current_price: float
    open_price: float
    high_price: float
    low_price: float
    pre_close: float
    change_amount: float
    change_percent: float
    amplitude: float
    volume: int
    turnover: float
    turnover_rate: float
    volume_ratio: float
    pe_ratio: Optional[float] = None
    pb_ratio: Optional[float] = None
    total_market_cap: Optional[float] = None
    float_market_cap: Optional[float] = None
    speed: Optional[float] = None
    change_5min: Optional[float] = None
    change_60day: Optional[float] = None
    change_ytd: Optional[float] = None
    trade_date: str
    update_time: str

class MarketSummaryResponse(BaseModel):
    exchange: str
    market_type: str
    listed_count: int
    stock_count: int
    total_shares: float
    float_shares: float
    total_market_cap: float
    float_market_cap: float
    avg_pe_ratio: Optional[float] = None
    turnover_amount: Optional[float] = None
    turnover_volume: Optional[float] = None
    report_date: str

class RegionTradingResponse(BaseModel):
    region_name: str
    rank: int
    total_turnover: float
    market_share: float
    stock_turnover: float
    fund_turnover: float
    bond_turnover: float
    trade_month: str

class IndustryTradingResponse(BaseModel):
    industry_code: str
    industry_name: str
    industry_name_cn: str
    trading_days: int
    turnover_amount: int
    turnover_amount_ratio: float
    turnover_volume: int
    turnover_volume_ratio: float
    trade_count: int
    trade_count_ratio: float
    period_type: str
    trade_period: str

class StockBidAskResponse(BaseModel):
    stock_code: str
    # 卖盘
    sell_5_price: float
    sell_5_volume: int
    sell_4_price: float
    sell_4_volume: int
    sell_3_price: float
    sell_3_volume: int
    sell_2_price: float
    sell_2_volume: int
    sell_1_price: float
    sell_1_volume: int
    # 买盘
    buy_1_price: float
    buy_1_volume: int
    buy_2_price: float
    buy_2_volume: int
    buy_3_price: float
    buy_3_volume: int
    buy_4_price: float
    buy_4_volume: int
    buy_5_price: float
    buy_5_volume: int
    # 当前行情
    current_price: float
    avg_price: float
    change_amount: float
    change_percent: float
    total_volume: int
    turnover_amount: float
    turnover_rate: float
    volume_ratio: float
    high_price: float
    low_price: float
    open_price: float
    pre_close: float
    limit_up: float
    limit_down: float
    outer_volume: int
    inner_volume: int
    quote_time: str

# API接口定义
@router.get("/stock-detail/{stock_code}", response_model=StockDetailResponse)
async def get_stock_detail(
    stock_code: str,
    db: AsyncSession = Depends(get_db)
):
    """获取个股详细信息"""
    try:
        result = await db.execute(
            select(StockDetailInfo).where(StockDetailInfo.stock_code == stock_code)
        )
        stock_detail = result.scalar_one_or_none()
        
        if not stock_detail:
            raise HTTPException(status_code=404, detail=f"股票 {stock_code} 的详细信息未找到")
        
        return StockDetailResponse(
            stock_code=stock_detail.stock_code,
            stock_name=stock_detail.stock_name,
            full_name=stock_detail.full_name,
            market=stock_detail.market,
            exchange=stock_detail.exchange,
            list_date=stock_detail.list_date.isoformat() if stock_detail.list_date else None,
            total_shares=float(stock_detail.total_shares) if stock_detail.total_shares else None,
            float_shares=float(stock_detail.float_shares) if stock_detail.float_shares else None,
            total_market_cap=float(stock_detail.total_market_cap) if stock_detail.total_market_cap else None,
            float_market_cap=float(stock_detail.float_market_cap) if stock_detail.float_market_cap else None,
            industry=stock_detail.industry,
            sector=stock_detail.sector,
            concept=stock_detail.concept,
            pe_ratio=float(stock_detail.pe_ratio) if stock_detail.pe_ratio else None,
            pb_ratio=float(stock_detail.pb_ratio) if stock_detail.pb_ratio else None,
            roe=float(stock_detail.roe) if stock_detail.roe else None,
            roa=float(stock_detail.roa) if stock_detail.roa else None,
            legal_representative=stock_detail.legal_representative,
            general_manager=stock_detail.general_manager,
            secretary=stock_detail.secretary,
            phone=stock_detail.phone,
            email=stock_detail.email,
            website=stock_detail.website,
            main_business=stock_detail.main_business,
            business_scope=stock_detail.business_scope,
            company_profile=stock_detail.company_profile,
            employee_count=stock_detail.employee_count,
            registered_capital=float(stock_detail.registered_capital) if stock_detail.registered_capital else None,
            actual_controller=stock_detail.actual_controller,
            data_date=stock_detail.data_date.isoformat() if stock_detail.data_date else None,
            updated_at=stock_detail.updated_at.isoformat() if stock_detail.updated_at else None
        )
        
    except Exception as e:
        logger.error(f"获取股票详细信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票详细信息失败")

@router.get("/sectors", response_model=List[SectorDataResponse])
async def get_sectors(
    sector_type: Optional[str] = Query(None, description="板块类型: industry/concept/region"),
    trade_date: Optional[str] = Query(None, description="交易日期 YYYY-MM-DD"),
    limit: int = Query(100, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    db: AsyncSession = Depends(get_db)
):
    """获取板块数据列表"""
    try:
        query = select(SectorData)
        
        # 添加筛选条件
        conditions = []
        if sector_type:
            conditions.append(SectorData.sector_type == sector_type)
        if trade_date:
            conditions.append(SectorData.trade_date == datetime.strptime(trade_date, "%Y-%m-%d").date())
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序和分页
        query = query.order_by(desc(SectorData.trade_date), desc(SectorData.sector_change)).offset(offset).limit(limit)
        
        result = await db.execute(query)
        sectors = result.scalars().all()
        
        return [
            SectorDataResponse(
                sector_code=sector.sector_code,
                sector_name=sector.sector_name,
                sector_type=sector.sector_type,
                stock_count=sector.stock_count,
                total_market_cap=float(sector.total_market_cap) if sector.total_market_cap else None,
                avg_pe_ratio=float(sector.avg_pe_ratio) if sector.avg_pe_ratio else None,
                avg_pb_ratio=float(sector.avg_pb_ratio) if sector.avg_pb_ratio else None,
                up_count=sector.up_count,
                down_count=sector.down_count,
                flat_count=sector.flat_count,
                total_volume=float(sector.total_volume) if sector.total_volume else None,
                total_turnover=float(sector.total_turnover) if sector.total_turnover else None,
                avg_turnover_rate=float(sector.avg_turnover_rate) if sector.avg_turnover_rate else None,
                sector_change=float(sector.sector_change) if sector.sector_change else None,
                sector_change_amount=float(sector.sector_change_amount) if sector.sector_change_amount else None,
                trade_date=sector.trade_date.isoformat()
            )
            for sector in sectors
        ]
        
    except Exception as e:
        logger.error(f"获取板块数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取板块数据失败")

@router.get("/sector/{sector_code}/stocks")
async def get_sector_stocks(
    sector_code: str,
    effective_date: Optional[str] = Query(None, description="生效日期 YYYY-MM-DD"),
    db: AsyncSession = Depends(get_db)
):
    """获取板块包含的股票列表"""
    try:
        query = select(SectorStock).where(SectorStock.sector_code == sector_code)

        if effective_date:
            query = query.where(SectorStock.effective_date == datetime.strptime(effective_date, "%Y-%m-%d").date())

        query = query.order_by(desc(SectorStock.weight))

        result = await db.execute(query)
        sector_stocks = result.scalars().all()

        return [
            {
                "stock_code": ss.stock_code,
                "weight": float(ss.weight) if ss.weight else None,
                "market_cap_weight": float(ss.market_cap_weight) if ss.market_cap_weight else None,
                "effective_date": ss.effective_date.isoformat()
            }
            for ss in sector_stocks
        ]

    except Exception as e:
        logger.error(f"获取板块股票列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取板块股票列表失败")

@router.get("/spot-data", response_model=List[StockSpotResponse])
async def get_spot_data(
    stock_codes: Optional[str] = Query(None, description="股票代码列表，逗号分隔"),
    trade_date: Optional[str] = Query(None, description="交易日期 YYYY-MM-DD"),
    limit: int = Query(100, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    db: AsyncSession = Depends(get_db)
):
    """获取股票实时行情数据"""
    try:
        import os
        from app.core.config import settings

        logger.info(f"🌐 API调用: stock_codes={stock_codes}, trade_date={trade_date}, limit={limit}, offset={offset}")
        logger.info(f"🗄️ 当前工作目录: {os.getcwd()}")
        logger.info(f"🗄️ 数据库配置路径: {settings.SQLITE_DB_PATH}")
        logger.info(f"🗄️ 数据库URI: {settings.SQLALCHEMY_DATABASE_URI}")

        query = select(StockSpotData)

        # 添加筛选条件
        conditions = []
        if stock_codes:
            code_list = [code.strip() for code in stock_codes.split(",")]
            conditions.append(StockSpotData.stock_code.in_(code_list))
            logger.info(f"📋 筛选股票代码: {code_list}")
        if trade_date:
            conditions.append(StockSpotData.trade_date == datetime.strptime(trade_date, "%Y-%m-%d").date())
            logger.info(f"📅 筛选交易日期: {trade_date}")

        if conditions:
            query = query.where(and_(*conditions))

        # 排序和分页
        query = query.order_by(desc(StockSpotData.update_time)).offset(offset).limit(limit)

        logger.info(f"🔍 执行数据库查询...")
        logger.info(f"🔍 查询SQL: {str(query)}")

        result = await db.execute(query)
        spot_data = result.scalars().all()

        logger.info(f"📊 查询结果: 返回 {len(spot_data)} 条数据")
        logger.info(f"📊 查询结果类型: {type(spot_data)}")

        # 记录前几条数据用于调试
        for i, data in enumerate(spot_data[:3]):
            logger.info(f"  {i+1}. {data.stock_code} {data.stock_name}: ¥{data.current_price} ({data.change_percent:+.2f}%) - {data.update_time}")
            logger.info(f"      数据对象类型: {type(data)}")
            logger.info(f"      数据对象ID: {id(data)}")

        if spot_data and float(spot_data[0].current_price) != 10.5:
            logger.info("🎉 API返回真实数据!")
        elif spot_data:
            logger.warning("⚠️  API返回模拟数据")
        else:
            logger.warning("⚠️  API返回空数据")

        return [
            StockSpotResponse(
                stock_code=data.stock_code,
                stock_name=data.stock_name,
                current_price=float(data.current_price),
                open_price=float(data.open_price),
                high_price=float(data.high_price),
                low_price=float(data.low_price),
                pre_close=float(data.pre_close),
                change_amount=float(data.change_amount),
                change_percent=float(data.change_percent),
                amplitude=float(data.amplitude),
                volume=data.volume,
                turnover=float(data.turnover),
                turnover_rate=float(data.turnover_rate),
                volume_ratio=float(data.volume_ratio),
                pe_ratio=float(data.pe_ratio) if data.pe_ratio else None,
                pb_ratio=float(data.pb_ratio) if data.pb_ratio else None,
                total_market_cap=float(data.total_market_cap) if data.total_market_cap else None,
                float_market_cap=float(data.float_market_cap) if data.float_market_cap else None,
                speed=float(data.speed) if data.speed else None,
                change_5min=float(data.change_5min) if data.change_5min else None,
                change_60day=float(data.change_60day) if data.change_60day else None,
                change_ytd=float(data.change_ytd) if data.change_ytd else None,
                trade_date=data.trade_date.isoformat(),
                update_time=data.update_time.isoformat()
            )
            for data in spot_data
        ]

    except Exception as e:
        logger.error(f"获取实时行情数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取实时行情数据失败")

@router.get("/market-summary", response_model=List[MarketSummaryResponse])
async def get_market_summary(
    exchange: Optional[str] = Query(None, description="交易所: SSE/SZSE"),
    report_date: Optional[str] = Query(None, description="报告日期 YYYY-MM-DD"),
    db: AsyncSession = Depends(get_db)
):
    """获取市场总貌数据"""
    try:
        query = select(MarketSummary)

        # 添加筛选条件
        conditions = []
        if exchange:
            conditions.append(MarketSummary.exchange == exchange)
        if report_date:
            conditions.append(MarketSummary.report_date == datetime.strptime(report_date, "%Y-%m-%d").date())

        if conditions:
            query = query.where(and_(*conditions))

        query = query.order_by(desc(MarketSummary.report_date))

        result = await db.execute(query)
        summaries = result.scalars().all()

        return [
            MarketSummaryResponse(
                exchange=summary.exchange,
                market_type=summary.market_type,
                listed_count=summary.listed_count,
                stock_count=summary.stock_count,
                total_shares=float(summary.total_shares),
                float_shares=float(summary.float_shares),
                total_market_cap=float(summary.total_market_cap),
                float_market_cap=float(summary.float_market_cap),
                avg_pe_ratio=float(summary.avg_pe_ratio) if summary.avg_pe_ratio else None,
                turnover_amount=float(summary.turnover_amount) if summary.turnover_amount else None,
                turnover_volume=float(summary.turnover_volume) if summary.turnover_volume else None,
                report_date=summary.report_date.isoformat()
            )
            for summary in summaries
        ]

    except Exception as e:
        logger.error(f"获取市场总貌数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取市场总貌数据失败")

@router.get("/region-trading", response_model=List[RegionTradingResponse])
async def get_region_trading(
    trade_month: Optional[str] = Query(None, description="交易月份 YYYYMM"),
    limit: int = Query(50, description="返回数量限制"),
    db: AsyncSession = Depends(get_db)
):
    """获取地区交易排序数据"""
    try:
        query = select(RegionTradingData)

        if trade_month:
            query = query.where(RegionTradingData.trade_month == trade_month)

        query = query.order_by(RegionTradingData.rank).limit(limit)

        result = await db.execute(query)
        region_data = result.scalars().all()

        return [
            RegionTradingResponse(
                region_name=data.region_name,
                rank=data.rank,
                total_turnover=float(data.total_turnover),
                market_share=float(data.market_share),
                stock_turnover=float(data.stock_turnover),
                fund_turnover=float(data.fund_turnover),
                bond_turnover=float(data.bond_turnover),
                trade_month=data.trade_month
            )
            for data in region_data
        ]

    except Exception as e:
        logger.error(f"获取地区交易数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取地区交易数据失败")

@router.get("/industry-trading", response_model=List[IndustryTradingResponse])
async def get_industry_trading(
    trade_period: Optional[str] = Query(None, description="交易周期 YYYYMM"),
    period_type: Optional[str] = Query(None, description="统计周期: 当月/当年"),
    db: AsyncSession = Depends(get_db)
):
    """获取行业成交数据"""
    try:
        query = select(IndustryTradingData)

        conditions = []
        if trade_period:
            conditions.append(IndustryTradingData.trade_period == trade_period)
        if period_type:
            conditions.append(IndustryTradingData.period_type == period_type)

        if conditions:
            query = query.where(and_(*conditions))

        query = query.order_by(desc(IndustryTradingData.turnover_amount_ratio))

        result = await db.execute(query)
        industry_data = result.scalars().all()

        return [
            IndustryTradingResponse(
                industry_code=data.industry_code,
                industry_name=data.industry_name,
                industry_name_cn=data.industry_name_cn,
                trading_days=data.trading_days,
                turnover_amount=data.turnover_amount,
                turnover_amount_ratio=float(data.turnover_amount_ratio),
                turnover_volume=data.turnover_volume,
                turnover_volume_ratio=float(data.turnover_volume_ratio),
                trade_count=data.trade_count,
                trade_count_ratio=float(data.trade_count_ratio),
                period_type=data.period_type,
                trade_period=data.trade_period
            )
            for data in industry_data
        ]

    except Exception as e:
        logger.error(f"获取行业成交数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取行业成交数据失败")

@router.get("/bid-ask/{stock_code}", response_model=StockBidAskResponse)
async def get_stock_bid_ask(
    stock_code: str,
    db: AsyncSession = Depends(get_db)
):
    """获取股票买卖盘数据"""
    try:
        result = await db.execute(
            select(StockBidAsk)
            .where(StockBidAsk.stock_code == stock_code)
            .order_by(desc(StockBidAsk.quote_time))
            .limit(1)
        )
        bid_ask = result.scalar_one_or_none()

        if not bid_ask:
            raise HTTPException(status_code=404, detail=f"股票 {stock_code} 的买卖盘数据未找到")

        return StockBidAskResponse(
            stock_code=bid_ask.stock_code,
            sell_5_price=float(bid_ask.sell_5_price),
            sell_5_volume=bid_ask.sell_5_volume,
            sell_4_price=float(bid_ask.sell_4_price),
            sell_4_volume=bid_ask.sell_4_volume,
            sell_3_price=float(bid_ask.sell_3_price),
            sell_3_volume=bid_ask.sell_3_volume,
            sell_2_price=float(bid_ask.sell_2_price),
            sell_2_volume=bid_ask.sell_2_volume,
            sell_1_price=float(bid_ask.sell_1_price),
            sell_1_volume=bid_ask.sell_1_volume,
            buy_1_price=float(bid_ask.buy_1_price),
            buy_1_volume=bid_ask.buy_1_volume,
            buy_2_price=float(bid_ask.buy_2_price),
            buy_2_volume=bid_ask.buy_2_volume,
            buy_3_price=float(bid_ask.buy_3_price),
            buy_3_volume=bid_ask.buy_3_volume,
            buy_4_price=float(bid_ask.buy_4_price),
            buy_4_volume=bid_ask.buy_4_volume,
            buy_5_price=float(bid_ask.buy_5_price),
            buy_5_volume=bid_ask.buy_5_volume,
            current_price=float(bid_ask.current_price),
            avg_price=float(bid_ask.avg_price),
            change_amount=float(bid_ask.change_amount),
            change_percent=float(bid_ask.change_percent),
            total_volume=bid_ask.total_volume,
            turnover_amount=float(bid_ask.turnover_amount),
            turnover_rate=float(bid_ask.turnover_rate),
            volume_ratio=float(bid_ask.volume_ratio),
            high_price=float(bid_ask.high_price),
            low_price=float(bid_ask.low_price),
            open_price=float(bid_ask.open_price),
            pre_close=float(bid_ask.pre_close),
            limit_up=float(bid_ask.limit_up),
            limit_down=float(bid_ask.limit_down),
            outer_volume=bid_ask.outer_volume,
            inner_volume=bid_ask.inner_volume,
            quote_time=bid_ask.quote_time.isoformat()
        )

    except Exception as e:
        logger.error(f"获取股票买卖盘数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票买卖盘数据失败")
