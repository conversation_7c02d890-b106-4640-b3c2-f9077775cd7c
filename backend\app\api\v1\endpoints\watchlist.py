"""
自选股相关API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from decimal import Decimal

from app.core.logging import get_logger
from app.services.watchlist_service import WatchlistService
from app.api.v1.endpoints.auth import get_current_user
from app.models.user import User

router = APIRouter()
logger = get_logger(__name__)


class WatchlistCreate(BaseModel):
    """创建自选股列表请求模型"""
    name: str
    description: Optional[str] = None
    color: Optional[str] = "#1890ff"
    icon: Optional[str] = "star"
    is_default: Optional[bool] = False
    is_public: Optional[bool] = False


class StockAdd(BaseModel):
    """添加股票请求模型"""
    stock_code: str
    added_reason: Optional[str] = None
    tags: Optional[dict] = None
    alert_enabled: Optional[bool] = True
    notes: Optional[str] = None
    target_price: Optional[float] = None
    stop_loss_price: Optional[float] = None


class StockSettings(BaseModel):
    """股票设置更新模型"""
    target_price: Optional[float] = None
    stop_loss_price: Optional[float] = None
    alert_enabled: Optional[bool] = None
    notes: Optional[str] = None
    tags: Optional[dict] = None


class StockNoteCreate(BaseModel):
    """创建股票笔记请求模型"""
    stock_code: str
    title: str
    content: str
    note_type: Optional[str] = "idea"  # analysis/news/idea/trade
    related_price: Optional[float] = None
    related_date: Optional[str] = None
    tags: Optional[dict] = None
    is_private: Optional[bool] = True
    is_important: Optional[bool] = False
    reminder_at: Optional[str] = None


@router.post("/lists")
async def create_watchlist(
    watchlist_data: WatchlistCreate,
    current_user: User = Depends(get_current_user)
):
    """创建自选股列表"""
    try:
        async with WatchlistService() as watchlist_service:
            watchlist_id = await watchlist_service.create_watchlist(
                current_user.id, watchlist_data.dict()
            )
        
        if watchlist_id > 0:
            return {
                "code": 200,
                "message": "自选股列表创建成功",
                "data": {
                    "watchlist_id": watchlist_id
                }
            }
        else:
            raise HTTPException(status_code=400, detail="创建自选股列表失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建自选股列表失败: {e}")
        raise HTTPException(status_code=500, detail="创建自选股列表失败")


@router.get("/lists")
async def get_watchlists(current_user: User = Depends(get_current_user)):
    """获取用户的自选股列表"""
    try:
        async with WatchlistService() as watchlist_service:
            watchlists = await watchlist_service.get_user_watchlists(current_user.id)
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "watchlists": watchlists,
                "total": len(watchlists)
            }
        }
    except Exception as e:
        logger.error(f"获取自选股列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取自选股列表失败")


@router.post("/lists/{watchlist_id}/stocks")
async def add_stock_to_watchlist(
    watchlist_id: int,
    stock_data: StockAdd,
    current_user: User = Depends(get_current_user)
):
    """添加股票到自选股列表"""
    try:
        async with WatchlistService() as watchlist_service:
            success = await watchlist_service.add_stock_to_watchlist(
                current_user.id, watchlist_id, stock_data.dict()
            )
        
        if success:
            return {
                "code": 200,
                "message": "股票添加成功",
                "data": {
                    "stock_code": stock_data.stock_code,
                    "watchlist_id": watchlist_id
                }
            }
        else:
            raise HTTPException(status_code=400, detail="股票已存在或添加失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加股票到自选股失败: {e}")
        raise HTTPException(status_code=500, detail="添加股票失败")


@router.get("/lists/{watchlist_id}/stocks")
async def get_watchlist_stocks(
    watchlist_id: int,
    include_market_data: bool = Query(True, description="是否包含市场数据"),
    current_user: User = Depends(get_current_user)
):
    """获取自选股列表中的股票"""
    try:
        async with WatchlistService() as watchlist_service:
            stocks = await watchlist_service.get_watchlist_stocks(
                current_user.id, watchlist_id, include_market_data
            )
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "stocks": stocks,
                "total": len(stocks),
                "watchlist_id": watchlist_id,
                "include_market_data": include_market_data
            }
        }
    except Exception as e:
        logger.error(f"获取自选股股票失败: {e}")
        raise HTTPException(status_code=500, detail="获取自选股股票失败")


@router.delete("/lists/{watchlist_id}/stocks/{stock_code}")
async def remove_stock_from_watchlist(
    watchlist_id: int,
    stock_code: str,
    current_user: User = Depends(get_current_user)
):
    """从自选股列表移除股票"""
    try:
        async with WatchlistService() as watchlist_service:
            success = await watchlist_service.remove_stock_from_watchlist(
                current_user.id, watchlist_id, stock_code
            )
        
        if success:
            return {
                "code": 200,
                "message": "股票移除成功",
                "data": {
                    "stock_code": stock_code,
                    "watchlist_id": watchlist_id
                }
            }
        else:
            raise HTTPException(status_code=404, detail="股票不存在或移除失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除股票失败: {e}")
        raise HTTPException(status_code=500, detail="移除股票失败")


@router.put("/stocks/{stock_id}/settings")
async def update_stock_settings(
    stock_id: int,
    settings: StockSettings,
    current_user: User = Depends(get_current_user)
):
    """更新股票设置"""
    try:
        async with WatchlistService() as watchlist_service:
            success = await watchlist_service.update_stock_settings(
                current_user.id, stock_id, settings.dict(exclude_unset=True)
            )
        
        if success:
            return {
                "code": 200,
                "message": "股票设置更新成功",
                "data": {
                    "stock_id": stock_id
                }
            }
        else:
            raise HTTPException(status_code=404, detail="股票不存在或更新失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新股票设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新股票设置失败")


@router.post("/notes")
async def create_stock_note(
    note_data: StockNoteCreate,
    current_user: User = Depends(get_current_user)
):
    """创建股票笔记"""
    try:
        async with WatchlistService() as watchlist_service:
            note_id = await watchlist_service.create_stock_note(
                current_user.id, note_data.dict()
            )
        
        if note_id > 0:
            return {
                "code": 200,
                "message": "股票笔记创建成功",
                "data": {
                    "note_id": note_id
                }
            }
        else:
            raise HTTPException(status_code=400, detail="创建股票笔记失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建股票笔记失败: {e}")
        raise HTTPException(status_code=500, detail="创建股票笔记失败")


@router.get("/notes")
async def get_stock_notes(
    stock_code: Optional[str] = Query(None, description="股票代码"),
    note_type: Optional[str] = Query(None, description="笔记类型"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    current_user: User = Depends(get_current_user)
):
    """获取股票笔记"""
    try:
        async with WatchlistService() as watchlist_service:
            notes = await watchlist_service.get_stock_notes(
                current_user.id, stock_code, note_type, limit
            )
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "notes": notes,
                "total": len(notes),
                "filters": {
                    "stock_code": stock_code,
                    "note_type": note_type,
                    "limit": limit
                }
            }
        }
    except Exception as e:
        logger.error(f"获取股票笔记失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票笔记失败")


@router.get("/stocks/{stock_code}/summary")
async def get_stock_summary(
    stock_code: str,
    current_user: User = Depends(get_current_user)
):
    """获取股票在用户自选股中的汇总信息"""
    try:
        async with WatchlistService() as watchlist_service:
            # 获取用户所有自选股列表
            watchlists = await watchlist_service.get_user_watchlists(current_user.id)
            
            # 查找包含该股票的列表
            containing_lists = []
            for watchlist in watchlists:
                stocks = await watchlist_service.get_watchlist_stocks(
                    current_user.id, watchlist["id"], include_market_data=False
                )
                
                for stock in stocks:
                    if stock["stock_code"] == stock_code:
                        containing_lists.append({
                            "watchlist_id": watchlist["id"],
                            "watchlist_name": watchlist["name"],
                            "stock_info": stock
                        })
                        break
            
            # 获取股票笔记
            notes = await watchlist_service.get_stock_notes(
                current_user.id, stock_code, limit=10
            )
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock_code,
                "in_watchlists": containing_lists,
                "watchlist_count": len(containing_lists),
                "recent_notes": notes,
                "note_count": len(notes)
            }
        }
    except Exception as e:
        logger.error(f"获取股票汇总信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票汇总信息失败")


@router.get("/overview")
async def get_watchlist_overview(current_user: User = Depends(get_current_user)):
    """获取自选股总览"""
    try:
        async with WatchlistService() as watchlist_service:
            # 获取所有自选股列表
            watchlists = await watchlist_service.get_user_watchlists(current_user.id)
            
            # 统计信息
            total_lists = len(watchlists)
            total_stocks = sum(w["stock_count"] for w in watchlists)
            total_value = sum(w["total_value"] for w in watchlists)
            total_change = sum(w["total_change"] for w in watchlists)
            
            # 获取最近笔记
            recent_notes = await watchlist_service.get_stock_notes(
                current_user.id, limit=5
            )
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "summary": {
                    "total_lists": total_lists,
                    "total_stocks": total_stocks,
                    "total_value": total_value,
                    "total_change": total_change,
                    "total_change_pct": (total_change / total_value * 100) if total_value > 0 else 0
                },
                "watchlists": watchlists,
                "recent_notes": recent_notes
            }
        }
    except Exception as e:
        logger.error(f"获取自选股总览失败: {e}")
        raise HTTPException(status_code=500, detail="获取自选股总览失败")
