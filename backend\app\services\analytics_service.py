"""
统计分析服务模块
"""

import asyncio
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func, or_

from app.core.logging import get_logger
from app.core.database import AsyncSessionLocal
from app.models.analytics import (
    Portfolio, PortfolioHolding, PortfolioTransaction, PortfolioPerformance,
    BacktestResult, RiskMetrics, MarketBenchmark
)
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


class PortfolioAnalyticsService:
    """投资组合分析服务"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def create_portfolio(self, user_id: int, portfolio_data: Dict[str, Any]) -> int:
        """创建投资组合"""
        try:
            logger.info(f"用户 {user_id} 创建投资组合: {portfolio_data.get('name')}")
            
            portfolio = Portfolio(
                user_id=user_id,
                name=portfolio_data["name"],
                description=portfolio_data.get("description"),
                portfolio_type=portfolio_data.get("portfolio_type", "virtual"),
                initial_capital=Decimal(str(portfolio_data["initial_capital"])),
                current_value=Decimal(str(portfolio_data["initial_capital"])),
                cash_balance=Decimal(str(portfolio_data["initial_capital"]))
            )
            
            self.session.add(portfolio)
            await self.session.commit()
            await self.session.refresh(portfolio)
            
            logger.info(f"投资组合创建成功，ID: {portfolio.id}")
            return portfolio.id
            
        except Exception as e:
            logger.error(f"创建投资组合失败: {e}")
            await self.session.rollback()
            return 0
    
    async def add_transaction(self, portfolio_id: int, transaction_data: Dict[str, Any]) -> bool:
        """添加交易记录"""
        try:
            logger.info(f"组合 {portfolio_id} 添加交易: {transaction_data}")
            
            # 创建交易记录
            transaction = PortfolioTransaction(
                portfolio_id=portfolio_id,
                stock_code=transaction_data["stock_code"],
                transaction_type=transaction_data["transaction_type"],
                shares=Decimal(str(transaction_data["shares"])),
                price=Decimal(str(transaction_data["price"])),
                amount=Decimal(str(transaction_data["amount"])),
                commission=Decimal(str(transaction_data.get("commission", 0))),
                tax=Decimal(str(transaction_data.get("tax", 0))),
                total_cost=Decimal(str(transaction_data["total_cost"])),
                reason=transaction_data.get("reason"),
                strategy=transaction_data.get("strategy"),
                transaction_date=transaction_data.get("transaction_date", date.today())
            )
            
            self.session.add(transaction)
            
            # 更新持仓
            await self._update_holding(portfolio_id, transaction_data)
            
            # 更新组合现金余额
            await self._update_portfolio_cash(portfolio_id, transaction_data)
            
            await self.session.commit()
            
            logger.info(f"交易记录添加成功")
            return True
            
        except Exception as e:
            logger.error(f"添加交易记录失败: {e}")
            await self.session.rollback()
            return False
    
    async def calculate_portfolio_performance(self, portfolio_id: int, 
                                            start_date: Optional[date] = None,
                                            end_date: Optional[date] = None) -> Dict[str, Any]:
        """计算投资组合表现"""
        try:
            logger.info(f"计算组合 {portfolio_id} 的表现")
            
            # 获取组合信息
            portfolio = await self._get_portfolio(portfolio_id)
            if not portfolio:
                return {"error": "投资组合不存在"}
            
            # 设置默认日期范围
            if not end_date:
                end_date = date.today()
            if not start_date:
                start_date = portfolio.created_at.date()
            
            # 获取持仓数据
            holdings = await self._get_portfolio_holdings(portfolio_id)
            
            # 获取历史表现数据
            performance_data = await self._get_performance_data(portfolio_id, start_date, end_date)
            
            # 计算基础指标
            basic_metrics = await self._calculate_basic_metrics(portfolio, holdings)
            
            # 计算风险指标
            risk_metrics = await self._calculate_risk_metrics(portfolio_id, performance_data)
            
            # 计算基准比较
            benchmark_comparison = await self._calculate_benchmark_comparison(
                portfolio_id, start_date, end_date
            )
            
            return {
                "portfolio_id": portfolio_id,
                "portfolio_name": portfolio.name,
                "calculation_date": end_date.isoformat(),
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": (end_date - start_date).days
                },
                "basic_metrics": basic_metrics,
                "risk_metrics": risk_metrics,
                "benchmark_comparison": benchmark_comparison,
                "holdings": [
                    {
                        "stock_code": h.stock_code,
                        "shares": float(h.shares),
                        "avg_cost": float(h.avg_cost),
                        "current_price": float(h.current_price) if h.current_price else None,
                        "market_value": float(h.market_value) if h.market_value else None,
                        "weight": float(h.weight) if h.weight else None,
                        "unrealized_pnl": float(h.unrealized_pnl) if h.unrealized_pnl else None,
                        "unrealized_pnl_pct": float(h.unrealized_pnl_pct) if h.unrealized_pnl_pct else None
                    }
                    for h in holdings
                ],
                "performance_chart": performance_data[-30:] if performance_data else []  # 最近30天
            }
            
        except Exception as e:
            logger.error(f"计算投资组合表现失败: {e}")
            return {"error": str(e)}
    
    async def calculate_risk_metrics(self, portfolio_id: int, period_days: int = 252) -> Dict[str, Any]:
        """计算风险指标"""
        try:
            logger.info(f"计算组合 {portfolio_id} 的风险指标")
            
            # 获取历史收益率数据
            end_date = date.today()
            start_date = end_date - timedelta(days=period_days + 50)  # 多获取一些数据
            
            performance_data = await self._get_performance_data(portfolio_id, start_date, end_date)
            
            if len(performance_data) < 30:  # 至少需要30个数据点
                return {"error": "数据不足，无法计算风险指标"}
            
            # 转换为pandas DataFrame
            df = pd.DataFrame(performance_data)
            df['performance_date'] = pd.to_datetime(df['performance_date'])
            df = df.set_index('performance_date').sort_index()
            
            # 计算日收益率
            returns = df['daily_return_pct'].dropna() / 100  # 转换为小数
            
            if len(returns) < 30:
                return {"error": "收益率数据不足"}
            
            # 基础统计指标
            mean_return = float(returns.mean())
            volatility = float(returns.std() * np.sqrt(252))  # 年化波动率
            skewness = float(returns.skew())
            kurtosis = float(returns.kurtosis())
            
            # 夏普比率 (假设无风险利率为3%)
            risk_free_rate = 0.03
            sharpe_ratio = float((mean_return * 252 - risk_free_rate) / volatility) if volatility > 0 else 0
            
            # VaR计算
            var_95 = float(returns.quantile(0.05))
            var_99 = float(returns.quantile(0.01))
            
            # CVaR计算
            cvar_95 = float(returns[returns <= var_95].mean())
            cvar_99 = float(returns[returns <= var_99].mean())
            
            # 最大回撤计算
            cumulative_returns = (1 + returns).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdowns = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = float(drawdowns.min())
            avg_drawdown = float(drawdowns[drawdowns < 0].mean()) if len(drawdowns[drawdowns < 0]) > 0 else 0
            
            # 保存风险指标到数据库
            risk_metrics = RiskMetrics(
                portfolio_id=portfolio_id,
                calculation_date=end_date,
                period_days=period_days,
                mean_return=Decimal(str(mean_return)),
                volatility=Decimal(str(volatility)),
                skewness=Decimal(str(skewness)),
                kurtosis=Decimal(str(kurtosis)),
                var_95=Decimal(str(var_95)),
                var_99=Decimal(str(var_99)),
                cvar_95=Decimal(str(cvar_95)),
                cvar_99=Decimal(str(cvar_99)),
                max_drawdown=Decimal(str(max_drawdown)),
                avg_drawdown=Decimal(str(avg_drawdown))
            )
            
            self.session.add(risk_metrics)
            await self.session.commit()
            
            return {
                "calculation_date": end_date.isoformat(),
                "period_days": period_days,
                "return_metrics": {
                    "mean_daily_return": mean_return,
                    "annualized_return": mean_return * 252,
                    "volatility": volatility,
                    "sharpe_ratio": sharpe_ratio,
                    "skewness": skewness,
                    "kurtosis": kurtosis
                },
                "risk_metrics": {
                    "var_95": var_95,
                    "var_99": var_99,
                    "cvar_95": cvar_95,
                    "cvar_99": cvar_99,
                    "max_drawdown": max_drawdown,
                    "avg_drawdown": avg_drawdown
                },
                "interpretation": {
                    "risk_level": self._interpret_risk_level(volatility, max_drawdown),
                    "return_quality": self._interpret_return_quality(sharpe_ratio, skewness),
                    "recommendations": self._generate_risk_recommendations(volatility, max_drawdown, sharpe_ratio)
                }
            }
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {"error": str(e)}
    
    async def _update_holding(self, portfolio_id: int, transaction_data: Dict[str, Any]):
        """更新持仓"""
        try:
            stock_code = transaction_data["stock_code"]
            transaction_type = transaction_data["transaction_type"]
            shares = Decimal(str(transaction_data["shares"]))
            price = Decimal(str(transaction_data["price"]))
            
            # 获取现有持仓
            stmt = select(PortfolioHolding).where(
                and_(
                    PortfolioHolding.portfolio_id == portfolio_id,
                    PortfolioHolding.stock_code == stock_code
                )
            )
            result = await self.session.execute(stmt)
            holding = result.scalar_one_or_none()
            
            if transaction_type == "buy":
                if holding:
                    # 更新现有持仓
                    total_cost = holding.shares * holding.avg_cost + shares * price
                    total_shares = holding.shares + shares
                    holding.avg_cost = total_cost / total_shares
                    holding.shares = total_shares
                else:
                    # 创建新持仓
                    holding = PortfolioHolding(
                        portfolio_id=portfolio_id,
                        stock_code=stock_code,
                        shares=shares,
                        avg_cost=price
                    )
                    self.session.add(holding)
            
            elif transaction_type == "sell" and holding:
                # 卖出股票
                if holding.shares >= shares:
                    holding.shares -= shares
                    # 如果全部卖出，删除持仓
                    if holding.shares == 0:
                        await self.session.delete(holding)
                else:
                    raise ValueError("卖出数量超过持仓数量")
            
        except Exception as e:
            logger.error(f"更新持仓失败: {e}")
            raise
    
    async def _update_portfolio_cash(self, portfolio_id: int, transaction_data: Dict[str, Any]):
        """更新组合现金余额"""
        try:
            portfolio = await self._get_portfolio(portfolio_id)
            if not portfolio:
                return
            
            transaction_type = transaction_data["transaction_type"]
            total_cost = Decimal(str(transaction_data["total_cost"]))
            
            if transaction_type == "buy":
                portfolio.cash_balance -= total_cost
            elif transaction_type == "sell":
                portfolio.cash_balance += total_cost
            
            await self.session.commit()
            
        except Exception as e:
            logger.error(f"更新组合现金余额失败: {e}")
            raise
    
    async def _get_portfolio(self, portfolio_id: int) -> Optional[Portfolio]:
        """获取投资组合"""
        try:
            stmt = select(Portfolio).where(Portfolio.id == portfolio_id)
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取投资组合失败: {e}")
            return None
    
    async def _get_portfolio_holdings(self, portfolio_id: int) -> List[PortfolioHolding]:
        """获取投资组合持仓"""
        try:
            stmt = select(PortfolioHolding).where(PortfolioHolding.portfolio_id == portfolio_id)
            result = await self.session.execute(stmt)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"获取投资组合持仓失败: {e}")
            return []
    
    async def _get_performance_data(self, portfolio_id: int, start_date: date, end_date: date) -> List[Dict]:
        """获取表现数据"""
        try:
            stmt = select(PortfolioPerformance).where(
                and_(
                    PortfolioPerformance.portfolio_id == portfolio_id,
                    PortfolioPerformance.performance_date >= start_date,
                    PortfolioPerformance.performance_date <= end_date
                )
            ).order_by(PortfolioPerformance.performance_date)
            
            result = await self.session.execute(stmt)
            performances = result.scalars().all()
            
            return [
                {
                    "performance_date": p.performance_date.isoformat(),
                    "total_value": float(p.total_value),
                    "daily_return_pct": float(p.daily_return_pct) if p.daily_return_pct else 0,
                    "cumulative_return_pct": float(p.cumulative_return_pct) if p.cumulative_return_pct else 0
                }
                for p in performances
            ]
        except Exception as e:
            logger.error(f"获取表现数据失败: {e}")
            return []
    
    async def _calculate_basic_metrics(self, portfolio: Portfolio, holdings: List[PortfolioHolding]) -> Dict[str, Any]:
        """计算基础指标"""
        try:
            total_market_value = sum(float(h.market_value or 0) for h in holdings)
            total_value = total_market_value + float(portfolio.cash_balance or 0)
            
            total_return = total_value - float(portfolio.initial_capital)
            total_return_pct = (total_return / float(portfolio.initial_capital)) * 100
            
            return {
                "initial_capital": float(portfolio.initial_capital),
                "current_value": total_value,
                "cash_balance": float(portfolio.cash_balance or 0),
                "stock_value": total_market_value,
                "total_return": total_return,
                "total_return_pct": total_return_pct,
                "holdings_count": len(holdings)
            }
        except Exception as e:
            logger.error(f"计算基础指标失败: {e}")
            return {}
    
    async def _calculate_risk_metrics(self, portfolio_id: int, performance_data: List[Dict]) -> Dict[str, Any]:
        """计算风险指标"""
        try:
            if len(performance_data) < 2:
                return {}
            
            returns = [p["daily_return_pct"] for p in performance_data if p["daily_return_pct"] is not None]
            
            if len(returns) < 2:
                return {}
            
            returns_array = np.array(returns) / 100  # 转换为小数
            
            volatility = float(np.std(returns_array) * np.sqrt(252))  # 年化波动率
            mean_return = float(np.mean(returns_array) * 252)  # 年化收益率
            
            # 夏普比率
            risk_free_rate = 0.03
            sharpe_ratio = (mean_return - risk_free_rate) / volatility if volatility > 0 else 0
            
            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            rolling_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = float(np.min(drawdowns))
            
            return {
                "volatility": volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "annualized_return": mean_return
            }
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {}
    
    async def _calculate_benchmark_comparison(self, portfolio_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        """计算基准比较"""
        try:
            # 这里可以与沪深300等基准指数比较
            # 暂时返回模拟数据
            return {
                "benchmark_name": "沪深300",
                "benchmark_return": 8.5,
                "portfolio_return": 12.3,
                "excess_return": 3.8,
                "tracking_error": 2.1,
                "information_ratio": 1.8
            }
        except Exception as e:
            logger.error(f"计算基准比较失败: {e}")
            return {}
    
    def _interpret_risk_level(self, volatility: float, max_drawdown: float) -> str:
        """解释风险水平"""
        if volatility > 0.25 or abs(max_drawdown) > 0.2:
            return "高风险"
        elif volatility > 0.15 or abs(max_drawdown) > 0.1:
            return "中等风险"
        else:
            return "低风险"
    
    def _interpret_return_quality(self, sharpe_ratio: float, skewness: float) -> str:
        """解释收益质量"""
        if sharpe_ratio > 1.5 and skewness > -0.5:
            return "优秀"
        elif sharpe_ratio > 1.0 and skewness > -1.0:
            return "良好"
        elif sharpe_ratio > 0.5:
            return "一般"
        else:
            return "较差"
    
    def _generate_risk_recommendations(self, volatility: float, max_drawdown: float, sharpe_ratio: float) -> List[str]:
        """生成风险建议"""
        recommendations = []
        
        if volatility > 0.25:
            recommendations.append("投资组合波动率较高，建议增加低风险资产配置")
        
        if abs(max_drawdown) > 0.15:
            recommendations.append("最大回撤较大，建议设置止损策略")
        
        if sharpe_ratio < 0.5:
            recommendations.append("风险调整后收益较低，建议优化投资策略")
        
        if len(recommendations) == 0:
            recommendations.append("投资组合风险控制良好，继续保持当前策略")
        
        return recommendations
