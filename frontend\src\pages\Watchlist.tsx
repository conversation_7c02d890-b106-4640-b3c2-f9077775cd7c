import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
} from 'antd'
import {
  StarFilled,
  StarOutlined,
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select

interface WatchlistStock {
  code: string
  name: string
  currentPrice: number
  change: number
  changePercent: number
  volume: string
  marketCap: string
  pe: number
  pb: number
  addedDate: string
  sector: string
  high52w: number
  low52w: number
}

const Watchlist: React.FC = () => {
  const [watchlist, setWatchlist] = useState<WatchlistStock[]>([])
  const [loading, setLoading] = useState(true)
  const [searchValue, setSearchValue] = useState('')
  const [sortBy, setSortBy] = useState('addedDate')

  useEffect(() => {
    loadWatchlist()
  }, [])

  const loadWatchlist = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const mockWatchlist: WatchlistStock[] = [
        {
          code: '000001',
          name: '平安银行',
          currentPrice: 12.45,
          change: 0.23,
          changePercent: 1.88,
          volume: '45.6亿',
          marketCap: '2,456亿',
          pe: 5.2,
          pb: 0.8,
          addedDate: '2024-01-15',
          sector: '银行',
          high52w: 15.67,
          low52w: 9.23,
        },
        {
          code: '600519',
          name: '贵州茅台',
          currentPrice: 1678.90,
          change: -23.45,
          changePercent: -1.38,
          volume: '12.3亿',
          marketCap: '21,234亿',
          pe: 28.5,
          pb: 8.9,
          addedDate: '2024-01-10',
          sector: '食品饮料',
          high52w: 1890.00,
          low52w: 1456.78,
        },
        {
          code: '300750',
          name: '宁德时代',
          currentPrice: 234.56,
          change: 12.34,
          changePercent: 5.55,
          volume: '89.7亿',
          marketCap: '10,567亿',
          pe: 35.6,
          pb: 4.2,
          addedDate: '2024-01-08',
          sector: '新能源',
          high52w: 345.67,
          low52w: 178.90,
        },
        {
          code: '002594',
          name: '比亚迪',
          currentPrice: 189.34,
          change: -5.67,
          changePercent: -2.91,
          volume: '67.8亿',
          marketCap: '5,678亿',
          pe: 22.3,
          pb: 3.1,
          addedDate: '2024-01-05',
          sector: '汽车',
          high52w: 267.89,
          low52w: 145.23,
        },
        {
          code: '000858',
          name: '五粮液',
          currentPrice: 156.78,
          change: 3.45,
          changePercent: 2.25,
          volume: '34.5亿',
          marketCap: '6,789亿',
          pe: 18.9,
          pb: 2.8,
          addedDate: '2024-01-03',
          sector: '食品饮料',
          high52w: 189.45,
          low52w: 123.67,
        },
      ]

      setWatchlist(mockWatchlist)
    } catch (error) {
      message.error('加载自选股失败')
    } finally {
      setLoading(false)
    }
  }

  const removeFromWatchlist = async (code: string) => {
    try {
      setWatchlist(prev => prev.filter(stock => stock.code !== code))
      message.success('已从自选股中移除')
    } catch (error) {
      message.error('移除失败')
    }
  }

  const addToWatchlist = () => {
    // 这里可以打开添加股票的模态框
    message.info('添加功能开发中...')
  }

  const columns = [
    {
      title: '股票',
      key: 'stock',
      fixed: 'left' as const,
      width: 120,
      render: (record: WatchlistStock) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.code}</Text>
          <Text style={{ fontSize: '12px', color: '#666' }}>{record.name}</Text>
        </Space>
      ),
    },
    {
      title: '现价',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      width: 80,
      render: (value: number) => (
        <Text strong>¥{value.toFixed(2)}</Text>
      ),
      sorter: (a: WatchlistStock, b: WatchlistStock) => a.currentPrice - b.currentPrice,
    },
    {
      title: '涨跌',
      key: 'change',
      width: 100,
      render: (record: WatchlistStock) => (
        <Space direction="vertical" size={0}>
          <Tag
            color={record.change >= 0 ? 'green' : 'red'}
            icon={record.change >= 0 ? <RiseOutlined /> : <FallOutlined />}
          >
            {record.change >= 0 ? '+' : ''}{record.change.toFixed(2)}
          </Tag>
          <Text style={{ fontSize: '12px', color: record.change >= 0 ? '#3f8600' : '#cf1322' }}>
            {record.changePercent >= 0 ? '+' : ''}{record.changePercent.toFixed(2)}%
          </Text>
        </Space>
      ),
      sorter: (a: WatchlistStock, b: WatchlistStock) => a.changePercent - b.changePercent,
    },
    {
      title: '成交额',
      dataIndex: 'volume',
      key: 'volume',
      width: 80,
    },
    {
      title: '市值',
      dataIndex: 'marketCap',
      key: 'marketCap',
      width: 80,
    },
    {
      title: 'PE/PB',
      key: 'valuation',
      width: 80,
      render: (record: WatchlistStock) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: '12px' }}>PE: {record.pe.toFixed(1)}</Text>
          <Text style={{ fontSize: '12px' }}>PB: {record.pb.toFixed(1)}</Text>
        </Space>
      ),
    },
    {
      title: '52周',
      key: 'range52w',
      width: 100,
      render: (record: WatchlistStock) => {
        const position = ((record.currentPrice - record.low52w) / (record.high52w - record.low52w)) * 100
        return (
          <Space direction="vertical" size={0}>
            <Text style={{ fontSize: '12px' }}>
              {record.low52w.toFixed(2)} - {record.high52w.toFixed(2)}
            </Text>
            <div style={{ width: '60px', height: '4px', background: '#f0f0f0', borderRadius: '2px' }}>
              <div
                style={{
                  width: `${position}%`,
                  height: '100%',
                  background: position > 70 ? '#ff4d4f' : position > 30 ? '#faad14' : '#52c41a',
                  borderRadius: '2px',
                }}
              />
            </div>
          </Space>
        )
      },
    },
    {
      title: '板块',
      dataIndex: 'sector',
      key: 'sector',
      width: 80,
      render: (sector: string) => <Tag>{sector}</Tag>,
    },
    {
      title: '添加日期',
      dataIndex: 'addedDate',
      key: 'addedDate',
      width: 100,
      sorter: (a: WatchlistStock, b: WatchlistStock) => 
        new Date(a.addedDate).getTime() - new Date(b.addedDate).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 80,
      render: (record: WatchlistStock) => (
        <Space>
          <Popconfirm
            title="确定要移除这只股票吗？"
            onConfirm={() => removeFromWatchlist(record.code)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const filteredWatchlist = watchlist.filter(stock =>
    stock.code.includes(searchValue) || 
    stock.name.includes(searchValue) ||
    stock.sector.includes(searchValue)
  )

  const gainers = watchlist.filter(s => s.changePercent > 0).length
  const losers = watchlist.filter(s => s.changePercent < 0).length
  const totalValue = watchlist.reduce((sum, s) => {
    const marketCapValue = parseFloat(s.marketCap.replace(/[亿万,]/g, ''))
    return sum + marketCapValue
  }, 0)

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>
          <StarFilled style={{ color: '#faad14' }} /> 我的自选股
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={addToWatchlist}
        >
          添加股票
        </Button>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="自选股数量"
              value={watchlist.length}
              suffix="只"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="上涨股票"
              value={gainers}
              suffix={`/ ${watchlist.length}`}
              valueStyle={{ color: '#3f8600' }}
              prefix={<RiseOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="下跌股票"
              value={losers}
              suffix={`/ ${watchlist.length}`}
              valueStyle={{ color: '#cf1322' }}
              prefix={<FallOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="总市值"
              value={totalValue.toFixed(0)}
              suffix="亿"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Input
              placeholder="搜索股票代码、名称或板块"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              prefix={<SearchOutlined />}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Select
              placeholder="排序方式"
              value={sortBy}
              onChange={setSortBy}
              style={{ width: '100%' }}
            >
              <Option value="addedDate">添加时间</Option>
              <Option value="changePercent">涨跌幅</Option>
              <Option value="currentPrice">股价</Option>
              <Option value="volume">成交额</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 自选股列表 */}
      <Card>
        <Table
          dataSource={filteredWatchlist}
          columns={columns}
          rowKey="code"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 只股票`,
          }}
          scroll={{ x: 1000 }}
          size="small"
        />
      </Card>
    </div>
  )
}

export default Watchlist
