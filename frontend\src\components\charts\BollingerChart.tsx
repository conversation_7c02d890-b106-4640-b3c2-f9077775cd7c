/**
 * 布林带指标图表组件
 */

import React, { useMemo } from 'react'
import ReactECharts from 'echarts-for-react'

interface PriceData {
  timestamp: string
  close: number
}

interface BollingerChartProps {
  data: PriceData[]
  height?: number
  timeFrame?: string
}

// 计算移动平均
const calculateSMA = (data: number[], period: number): number[] => {
  const sma: number[] = []
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      sma.push(data[i]) // 前period-1个值直接使用原值
    } else {
      const sum = data.slice(i - period + 1, i + 1).reduce((acc, val) => acc + val, 0)
      sma.push(sum / period)
    }
  }
  
  return sma
}

// 计算标准差
const calculateStandardDeviation = (data: number[], period: number, sma: number[]): number[] => {
  const std: number[] = []
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      std.push(0)
    } else {
      const periodData = data.slice(i - period + 1, i + 1)
      const mean = sma[i]
      const variance = periodData.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / period
      std.push(Math.sqrt(variance))
    }
  }
  
  return std
}

// 计算布林带
const calculateBollingerBands = (data: number[], period: number = 20, multiplier: number = 2) => {
  const sma = calculateSMA(data, period)
  const std = calculateStandardDeviation(data, period, sma)
  
  const upperBand = sma.map((value, index) => value + (std[index] * multiplier))
  const lowerBand = sma.map((value, index) => value - (std[index] * multiplier))
  
  return {
    middle: sma,
    upper: upperBand,
    lower: lowerBand
  }
}

const BollingerChart: React.FC<BollingerChartProps> = ({
  data,
  height = 250,
  timeFrame = '1D'
}) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null

    const closes = data.map(item => item.close)
    const bollinger = calculateBollingerBands(closes)

    // 准备时间轴
    const timeAxis = data.map(item => {
      const date = new Date(item.timestamp)
      if (timeFrame === '1m' || timeFrame === '5m' || timeFrame === '15m' || timeFrame === '30m' || timeFrame === '1h') {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    })

    return {
      timeAxis,
      closes,
      ...bollinger
    }
  }, [data, timeFrame])

  const option = useMemo(() => {
    if (!chartData) return {}

    return {
      backgroundColor: '#ffffff',
      grid: {
        left: '8%',
        right: '8%',
        top: '15%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.timeAxis,
        axisLabel: {
          fontSize: 10
        },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        textStyle: {
          color: '#000',
          fontSize: 12
        }
      },
      legend: {
        data: ['价格', '上轨', '中轨', '下轨'],
        top: '5%',
        textStyle: {
          fontSize: 10
        }
      },
      series: [
        {
          name: '价格',
          type: 'line',
          data: chartData.closes,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          symbol: 'none'
        },
        {
          name: '上轨',
          type: 'line',
          data: chartData.upper,
          smooth: true,
          lineStyle: {
            color: '#ff4d4f',
            width: 1
          },
          symbol: 'none'
        },
        {
          name: '中轨',
          type: 'line',
          data: chartData.middle,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 1
          },
          symbol: 'none'
        },
        {
          name: '下轨',
          type: 'line',
          data: chartData.lower,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 1
          },
          symbol: 'none'
        }
      ]
    }
  }, [chartData])

  if (!chartData) {
    return (
      <div style={{
        height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>暂无数据</div>
      </div>
    )
  }

  return (
    <ReactECharts
      option={option}
      style={{ height, width: '100%' }}
      opts={{ renderer: 'canvas' }}
    />
  )
}

export default BollingerChart
