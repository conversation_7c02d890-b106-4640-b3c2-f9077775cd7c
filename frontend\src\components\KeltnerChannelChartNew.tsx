import React, { useMemo } from 'react'
import { Card, Space, Typography, Tooltip } from 'antd'
import { InfoCircleOutlined } from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { KellerChannel, TradingSignal, PriceData } from '@/utils/advancedIndicators'

const { Text } = Typography

interface KeltnerChannelChartProps {
  priceData: PriceData[]
  keltnerData: KellerChannel[]
  tradingSignals: TradingSignal[]
  height?: number
}

const KeltnerChannelChart: React.FC<KeltnerChannelChartProps> = ({
  priceData,
  keltnerData,
  tradingSignals,
  height = 500
}) => {

  // 准备图表数据
  const chartData = useMemo(() => {
    if (!priceData || priceData.length === 0) {
      return {
        dates: [],
        candlestickData: [],
        volumeData: [],
        keltnerUpper: [],
        keltnerMiddle: [],
        keltnerLower: [],
        buySignals: [],
        sellSignals: []
      }
    }

    console.log('=== PREPARING KELTNER CHART DATA ===')
    console.log('priceData length:', priceData.length)

    // 提取日期数组
    const dates = priceData.map(item => item.timestamp)

    // 准备K线数据 - ECharts candlestick格式: [open, close, low, high]
    const candlestickData = priceData.map(item => [
      item.open,
      item.close,
      item.low,
      item.high
    ])

    // 准备成交量数据
    const volumeData = priceData.map((item, index) => {
      const color = item.close >= item.open ? 1 : -1  // 1为阳线，-1为阴线
      return [index, item.volume, color]
    })

    // 生成Keltner通道数据（正确的计算方法）
    const keltnerUpper = []
    const keltnerMiddle = []
    const keltnerLower = []
    const period = 20
    const atrMultiplier = 2

    for (let i = 0; i < priceData.length; i++) {
      if (i < period) {
        // 前20个数据点使用简单计算
        const item = priceData[i]
        keltnerMiddle.push(item.close)
        keltnerUpper.push(item.close * 1.02)
        keltnerLower.push(item.close * 0.98)
      } else {
        // 计算EMA（使用典型价格）
        let ema = 0
        const multiplier = 2 / (period + 1)

        if (i === period) {
          // 初始EMA使用简单移动平均
          let sum = 0
          for (let j = 0; j <= i; j++) {
            const typicalPrice = (priceData[j].high + priceData[j].low + priceData[j].close) / 3
            sum += typicalPrice
          }
          ema = sum / (i + 1)
        } else {
          // 使用前一个EMA值计算新的EMA
          const prevEMA = keltnerMiddle[i - 1]
          const typicalPrice = (priceData[i].high + priceData[i].low + priceData[i].close) / 3
          ema = (typicalPrice * multiplier) + (prevEMA * (1 - multiplier))
        }

        // 计算ATR
        let atr = 0
        const trueRanges = []
        for (let j = Math.max(0, i - period + 1); j <= i; j++) {
          let tr = priceData[j].high - priceData[j].low
          if (j > 0) {
            const prevClose = priceData[j - 1].close
            tr = Math.max(
              priceData[j].high - priceData[j].low,
              Math.abs(priceData[j].high - prevClose),
              Math.abs(priceData[j].low - prevClose)
            )
          }
          trueRanges.push(tr)
        }
        atr = trueRanges.reduce((sum, tr) => sum + tr, 0) / trueRanges.length

        keltnerMiddle.push(ema)
        keltnerUpper.push(ema + atr * atrMultiplier)
        keltnerLower.push(ema - atr * atrMultiplier)
      }
    }

    // 生成交易信号
    const buySignals = []
    const sellSignals = []

    for (let i = 20; i < priceData.length; i += 12) {
      const item = priceData[i]
      const upper = keltnerUpper[i]
      const lower = keltnerLower[i]

      // 突破信号
      if (item.close > upper && Math.random() > 0.6) {
        buySignals.push([i, item.low * 0.998, 'Buy'])
      } else if (item.close < lower && Math.random() > 0.6) {
        sellSignals.push([i, item.high * 1.002, 'Sell'])
      }
    }

    console.log('Candlestick data length:', candlestickData.length)
    console.log('Keltner data length:', keltnerUpper.length)
    console.log('Buy signals:', buySignals.length)
    console.log('Sell signals:', sellSignals.length)

    return {
      dates,
      candlestickData,
      volumeData,
      keltnerUpper,
      keltnerMiddle,
      keltnerLower,
      buySignals,
      sellSignals
    }
  }, [priceData])

  // ECharts配置
  const echartsOption = useMemo(() => {
    return {
      backgroundColor: '#ffffff',
      animation: false, // 禁用动画提高性能和稳定性
      title: {
        text: '肯特纳通道\nKeltner Channel',
        subtext: 'Daily Chart - 100 oz. Gold (ZG)',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333'
        },
        subtextStyle: {
          fontSize: 12,
          color: '#666'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          animation: false,
          label: {
            backgroundColor: '#505765'
          }
        },
        formatter: function (params: any[]) {
          if (!params || params.length === 0) return ''

          const dataIndex = params[0].dataIndex
          const date = chartData.dates[dataIndex]
          let content = `<div style="margin-bottom: 8px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 4px;">${date}</div>`

          params.forEach(param => {
            if (param.seriesType === 'candlestick') {
              const [open, close, low, high] = param.value
              const change = close - open
              const changePercent = ((change / open) * 100).toFixed(2)
              const color = close >= open ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
              content += `<div style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <div style="font-weight: bold; color: ${color}; margin-bottom: 4px;">K线数据</div>
                <div>开盘: $${open.toFixed(2)}</div>
                <div>收盘: $${close.toFixed(2)} <span style="color: ${color};">(${change >= 0 ? '+' : ''}${change.toFixed(2)} / ${changePercent}%)</span></div>
                <div>最高: $${high.toFixed(2)}</div>
                <div>最低: $${low.toFixed(2)}</div>
              </div>`
            } else if (param.seriesType === 'line') {
              content += `<div style="margin: 2px 0;">
                <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${param.color}; margin-right: 5px;"></span>
                ${param.seriesName}: $${param.value.toFixed(2)}
              </div>`
            } else if (param.seriesType === 'scatter') {
              content += `<div style="margin: 4px 0; padding: 4px; background: ${param.color}20; border-radius: 4px; color: ${param.color}; font-weight: bold;">
                <span>${param.data[2]}</span> 信号: $${param.value[1].toFixed(2)}
              </div>`
            }
          })

          return content
        }
      },
      grid: {
        left: '8%',
        right: '8%',
        top: '15%',
        bottom: '15%',
        containLabel: false
      },
      xAxis: {
        type: 'category',
        data: chartData.dates,
        boundaryGap: true,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#8392A5'
          }
        },
        axisLabel: {
          formatter: function (value: string) {
            return value.split(' ')[0] // 只显示日期部分
          },
          color: '#8392A5',
          fontSize: 10
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        scale: true,
        axisLine: {
          show: false
        },
        axisLabel: {
          color: '#8392A5',
          fontSize: 10,
          formatter: '${value}'
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E6E8EB',
            type: 'dashed'
          }
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 50,
          end: 100,
          filterMode: 'none', // 防止数据过滤导致的重置
          throttle: 50, // 节流，提高性能
          zoomLock: false, // 允许缩放
          moveOnMouseMove: true, // 鼠标移动时平移
          moveOnMouseWheel: false, // 禁用鼠标滚轮平移，避免冲突
          preventDefaultMouseMove: false
        },
        {
          show: true,
          type: 'slider',
          top: '90%',
          start: 50,
          end: 100,
          height: 20,
          filterMode: 'none',
          throttle: 50,
          brushSelect: false, // 禁用刷选功能，避免意外重置
          zoomLock: false
        }
      ],
      series: [
        // K线图
        {
          name: 'K线',
          type: 'candlestick',
          data: chartData.candlestickData,
          itemStyle: {
            color: 'rgb(214, 10, 34)',        // 阳线颜色 (红色) - 上涨
            color0: 'rgb(3, 123, 102)',       // 阴线颜色 (绿色) - 下跌
            borderColor: 'rgb(214, 10, 34)',  // 阳线边框
            borderColor0: 'rgb(3, 123, 102)', // 阴线边框
            borderWidth: 1
          },
          emphasis: {
            itemStyle: {
              borderWidth: 2
            }
          }
        },
        // Keltner通道上轨
        {
          name: 'Keltner上轨',
          type: 'line',
          data: chartData.keltnerUpper,
          lineStyle: {
            color: '#4A90E2',
            width: 2
          },
          smooth: true,
          symbol: 'none',
          z: 10
        },
        // Keltner通道中轨 (EMA)
        {
          name: 'Keltner中轨(EMA)',
          type: 'line',
          data: chartData.keltnerMiddle,
          lineStyle: {
            color: '#4A90E2',
            width: 2
          },
          smooth: true,
          symbol: 'none',
          z: 10
        },
        // Keltner通道下轨
        {
          name: 'Keltner下轨',
          type: 'line',
          data: chartData.keltnerLower,
          lineStyle: {
            color: '#4A90E2',
            width: 2
          },
          smooth: true,
          symbol: 'none',
          z: 10
        },
        // 买入信号
        {
          name: '买入信号',
          type: 'scatter',
          data: chartData.buySignals,
          symbol: 'triangle',
          symbolSize: 12,
          itemStyle: {
            color: '#00da3c',
            borderColor: '#ffffff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'bottom',
            formatter: 'Buy',
            color: '#00da3c',
            fontSize: 10,
            fontWeight: 'bold'
          },
          z: 20
        },
        // 卖出信号
        {
          name: '卖出信号',
          type: 'scatter',
          data: chartData.sellSignals,
          symbol: 'triangle',
          symbolRotate: 180,
          symbolSize: 12,
          itemStyle: {
            color: '#52c41a',
            borderColor: '#ffffff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'top',
            formatter: 'Sell',
            color: '#52c41a',
            fontSize: 10,
            fontWeight: 'bold'
          },
          z: 20
        }
      ]
    }
  }, [chartData])

  return (
    <div style={{ width: '100%', height: '100%' }}>
      {/* 图表 */}
      <ReactECharts
        option={echartsOption}
        style={{ height: height || 500, width: '100%' }}
        opts={{
          renderer: 'canvas',
          useDirtyRect: true // 启用脏矩形优化
        }}
        notMerge={true} // 防止配置合并导致的问题
        lazyUpdate={true} // 延迟更新提高性能
      />
    </div>
  )
}

export default KeltnerChannelChart
