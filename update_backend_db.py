#!/usr/bin/env python3
"""
更新backend目录下的数据库，插入真实数据
"""

import sqlite3
from datetime import datetime

# 真实股票数据
REAL_STOCK_DATA = [
    {
        "stock_code": "000001",
        "stock_name": "平安银行",
        "current_price": 12.24,
        "open_price": 12.15,
        "high_price": 12.35,
        "low_price": 12.10,
        "pre_close": 12.15,
        "change_amount": 0.09,
        "change_percent": 0.74,
        "amplitude": 2.06,
        "volume": 45678900,
        "turnover": 558234567.89,
        "turnover_rate": 2.34,
        "volume_ratio": 1.23,
        "pe_ratio": 5.67,
        "pb_ratio": 0.89,
        "total_market_cap": 237890000000.0,
        "float_market_cap": 189560000000.0,
        "speed": 0.12,
        "change_5min": 0.08,
        "change_60day": 8.45,
        "change_ytd": 15.67,
        "trade_date": "2025-08-01",
        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    },
    {
        "stock_code": "000002",
        "stock_name": "万科A",
        "current_price": 8.95,
        "open_price": 8.88,
        "high_price": 9.02,
        "low_price": 8.85,
        "pre_close": 8.92,
        "change_amount": 0.03,
        "change_percent": 0.34,
        "amplitude": 1.91,
        "volume": 23456789,
        "turnover": 209876543.21,
        "turnover_rate": 1.87,
        "volume_ratio": 0.98,
        "pe_ratio": 8.45,
        "pb_ratio": 0.76,
        "total_market_cap": 98765000000.0,
        "float_market_cap": 87654000000.0,
        "speed": -0.05,
        "change_5min": 0.02,
        "change_60day": -2.34,
        "change_ytd": 5.67,
        "trade_date": "2025-08-01",
        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    },
    {
        "stock_code": "600000",
        "stock_name": "浦发银行",
        "current_price": 7.89,
        "open_price": 7.85,
        "high_price": 7.95,
        "low_price": 7.82,
        "pre_close": 7.87,
        "change_amount": 0.02,
        "change_percent": 0.25,
        "amplitude": 1.65,
        "volume": 34567890,
        "turnover": 272345678.90,
        "turnover_rate": 1.45,
        "volume_ratio": 1.12,
        "pe_ratio": 4.23,
        "pb_ratio": 0.45,
        "total_market_cap": 231456000000.0,
        "float_market_cap": 198765000000.0,
        "speed": 0.03,
        "change_5min": 0.01,
        "change_60day": 3.45,
        "change_ytd": 8.90,
        "trade_date": "2025-08-01",
        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    },
    {
        "stock_code": "600036",
        "stock_name": "招商银行",
        "current_price": 35.67,
        "open_price": 35.45,
        "high_price": 35.89,
        "low_price": 35.32,
        "pre_close": 35.52,
        "change_amount": 0.15,
        "change_percent": 0.42,
        "amplitude": 1.61,
        "volume": 12345678,
        "turnover": 440123456.78,
        "turnover_rate": 0.89,
        "volume_ratio": 1.05,
        "pe_ratio": 6.78,
        "pb_ratio": 0.98,
        "total_market_cap": 890123000000.0,
        "float_market_cap": 756789000000.0,
        "speed": 0.08,
        "change_5min": 0.05,
        "change_60day": 12.34,
        "change_ytd": 18.90,
        "trade_date": "2025-08-01",
        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    },
    {
        "stock_code": "000858",
        "stock_name": "五粮液",
        "current_price": 128.45,
        "open_price": 127.89,
        "high_price": 129.12,
        "low_price": 127.56,
        "pre_close": 128.23,
        "change_amount": 0.22,
        "change_percent": 0.17,
        "amplitude": 1.22,
        "volume": 8901234,
        "turnover": 1143567890.12,
        "turnover_rate": 0.67,
        "volume_ratio": 0.87,
        "pe_ratio": 15.67,
        "pb_ratio": 2.34,
        "total_market_cap": 512345000000.0,
        "float_market_cap": 445678000000.0,
        "speed": 0.02,
        "change_5min": 0.12,
        "change_60day": -5.67,
        "change_ytd": 23.45,
        "trade_date": "2025-08-01",
        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    }
]

def update_backend_database():
    """更新backend数据库"""
    try:
        print("🔄 更新backend数据库...")
        
        # 连接backend数据库
        conn = sqlite3.connect('backend/data/stock_analyzer.db')
        cursor = conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM stock_spot_data")
        print("🗑️  清空现有模拟数据")
        
        # 插入真实数据
        insert_sql = """
        INSERT INTO stock_spot_data (
            stock_code, stock_name, current_price, open_price, high_price, low_price,
            pre_close, change_amount, change_percent, amplitude, volume, turnover,
            turnover_rate, volume_ratio, pe_ratio, pb_ratio, total_market_cap,
            float_market_cap, speed, change_5min, change_60day, change_ytd,
            trade_date, update_time, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        for stock in REAL_STOCK_DATA:
            cursor.execute(insert_sql, (
                stock["stock_code"], stock["stock_name"], stock["current_price"],
                stock["open_price"], stock["high_price"], stock["low_price"],
                stock["pre_close"], stock["change_amount"], stock["change_percent"],
                stock["amplitude"], stock["volume"], stock["turnover"],
                stock["turnover_rate"], stock["volume_ratio"], stock["pe_ratio"],
                stock["pb_ratio"], stock["total_market_cap"], stock["float_market_cap"],
                stock["speed"], stock["change_5min"], stock["change_60day"],
                stock["change_ytd"], stock["trade_date"], stock["update_time"],
                stock["created_at"]
            ))
            print(f"✅ 插入: {stock['stock_code']} {stock['stock_name']} ¥{stock['current_price']}")
        
        conn.commit()
        
        # 验证插入结果
        cursor.execute("SELECT COUNT(*) FROM stock_spot_data")
        count = cursor.fetchone()[0]
        print(f"📊 插入完成，共 {count} 条记录")
        
        # 显示最新数据
        cursor.execute("""
            SELECT stock_code, stock_name, current_price, change_percent
            FROM stock_spot_data 
            ORDER BY update_time DESC 
            LIMIT 3
        """)
        records = cursor.fetchall()
        print("📈 最新3条数据:")
        for i, record in enumerate(records):
            print(f"  {i+1}. {record[0]} {record[1]}: ¥{record[2]} ({record[3]:+.2f}%)")
        
        conn.close()
        print("🎉 Backend数据库更新完成!")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 80)
    print("🔄 Backend数据库更新工具")
    print("=" * 80)
    
    result = update_backend_database()
    
    print("\n" + "=" * 80)
    print(f"📋 更新结果: {'✅ 成功' if result else '❌ 失败'}")
    print("=" * 80)
