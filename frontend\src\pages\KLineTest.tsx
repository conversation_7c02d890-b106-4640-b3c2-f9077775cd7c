/**
 * K线图测试页面
 * 用于测试K线图的拖动、缩放和时间周期功能
 */

import React, { useState, useMemo } from 'react'
import { Card, Row, Col, Typography, Space, Button, Alert, Divider } from 'antd'
import { ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons'
import EnhancedKLineChart from '@/components/charts/EnhancedKLineChart'

import KeltnerChannelChartNew from '@/components/KeltnerChannelChartNew'

const { Title, Text, Paragraph } = Typography

interface PriceData {
  timestamp: string
  date: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

const KLineTest: React.FC = () => {
  const [refreshKey, setRefreshKey] = useState(0)

  // 生成测试数据
  const generateTestData = useMemo(() => {
    const data: PriceData[] = []
    const basePrice = 100
    let currentPrice = basePrice

    for (let i = 0; i < 200; i++) {
      const date = new Date()
      date.setDate(date.getDate() - (199 - i))
      
      // 模拟价格波动
      const volatility = 0.02 // 2% 波动率
      const change = (Math.random() - 0.5) * 2 * volatility * currentPrice
      
      const open = currentPrice
      const close = open + change
      const high = Math.max(open, close) * (1 + Math.random() * 0.01)
      const low = Math.min(open, close) * (1 - Math.random() * 0.01)
      const volume = Math.floor(Math.random() * 10000000) + 1000000

      data.push({
        timestamp: date.toISOString(),
        date: date.toISOString().split('T')[0],
        open: Number(open.toFixed(2)),
        high: Number(high.toFixed(2)),
        low: Number(low.toFixed(2)),
        close: Number(close.toFixed(2)),
        volume
      })

      currentPrice = close
    }

    return data
  }, [refreshKey])



  const priceData = useMemo(() => {
    return generateTestData.map(item => ({
      date: item.date,
      timestamp: item.timestamp,
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close,
      volume: item.volume
    }))
  }, [generateTestData])

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>K线图交互功能测试</Title>
        <Paragraph>
          测试K线图的拖动、缩放和时间周期切换功能。请尝试以下操作：
        </Paragraph>
        
        <Alert
          message="测试说明"
          description={
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              <li><strong>拖动</strong>：在图表区域按住鼠标左键拖动查看历史数据</li>
              <li><strong>缩放</strong>：使用鼠标滚轮或双指手势进行缩放</li>
              <li><strong>滑块</strong>：使用底部滑块调整显示范围</li>
              <li><strong>时间周期</strong>：切换不同时间周期查看数据</li>
              <li><strong>图表类型</strong>：切换K线图、分时图、面积图</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />

        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            type="primary"
          >
            重新生成测试数据
          </Button>
          <Text type="secondary">数据点数量: {generateTestData.length}</Text>
        </Space>
      </div>

      <Row gutter={[16, 16]}>
        {/* 增强版K线图 */}
        <Col span={24}>
          <Card 
            title={
              <Space>
                <span>增强版K线图</span>
                <InfoCircleOutlined style={{ color: '#1890ff' }} />
              </Space>
            }
            size="small"
          >
            <EnhancedKLineChart
              data={generateTestData}
              title="测试股票 (TEST001)"
              height={500}
              showVolume={true}
              showMA={true}
              onTimeframeChange={(timeframe) => {
                console.log('时间周期切换:', timeframe)
              }}
              onRefresh={handleRefresh}
            />
          </Card>
        </Col>

        <Divider>其他图表组件测试</Divider>

        {/* Keltner通道图 - 新版 */}
        <Col span={24}>
          <Card
            title="Keltner通道图 - 新版 (简化版)"
            size="small"
          >
            <KeltnerChannelChartNew
              priceData={priceData}
              height={400}
            />
          </Card>
        </Col>
      </Row>

      <Divider />

      <Card title="技术说明" size="small">
        <Row gutter={16}>
          <Col span={8}>
            <Title level={4}>性能优化</Title>
            <ul>
              <li>禁用动画提高性能</li>
              <li>启用脏矩形优化</li>
              <li>使用Canvas渲染器</li>
              <li>延迟更新和节流处理</li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={4}>交互优化</Title>
            <ul>
              <li>防止数据过滤导致重置</li>
              <li>禁用刷选功能避免意外重置</li>
              <li>优化鼠标滚轮交互</li>
              <li>支持触摸设备手势</li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={4}>功能特性</Title>
            <ul>
              <li>多时间周期支持</li>
              <li>图表类型切换</li>
              <li>成交量显示开关</li>
              <li>移动平均线显示</li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default KLineTest
