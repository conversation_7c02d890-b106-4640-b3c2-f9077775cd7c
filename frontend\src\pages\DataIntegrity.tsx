import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Tag,
  Progress,
  Input,
  Select,
  Button,
  Space,
  Statistic,
  Row,
  Col,
  message,
  Tooltip,
  Modal,
  Form,
  DatePicker,
  Checkbox,
  Spin,
  Tabs,
  Descriptions
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
  Exclamation<PERSON>ircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { 
  dataIntegrityService, 
  StockDataIntegrity, 
  DataIntegrityStats,
  DataUpdateRequest 
} from '../services/dataIntegrityService'
import dayjs from 'dayjs'

const { Search } = Input
const { Option } = Select
const { RangePicker } = DatePicker

const DataIntegrity: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [statsLoading, setStatsLoading] = useState(false)
  const [stockData, setStockData] = useState<StockDataIntegrity[]>([])
  const [stats, setStats] = useState<DataIntegrityStats | null>(null)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(50)
  
  // 更新操作相关状态
  const [updateModalVisible, setUpdateModalVisible] = useState(false)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [updateForm] = Form.useForm()

  // 任务监控相关状态
  const [runningTasks, setRunningTasks] = useState<any[]>([])
  const [taskModalVisible, setTaskModalVisible] = useState(false)

  // 查看数据相关状态
  const [dataViewModalVisible, setDataViewModalVisible] = useState(false)
  const [selectedStockCode, setSelectedStockCode] = useState<string>('')
  const [stockDetailData, setStockDetailData] = useState<any>(null)
  const [dataViewLoading, setDataViewLoading] = useState(false)

  // 加载数据完整性统计
  const loadStats = async () => {
    try {
      setStatsLoading(true)
      const statsData = await dataIntegrityService.getDataIntegrityStats()
      setStats(statsData)
    } catch (error) {
      message.error('获取数据完整性统计失败')
      console.error('获取统计失败:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  // 加载股票数据完整性
  const loadStockData = async () => {
    try {
      setLoading(true)
      const data = await dataIntegrityService.getStocksDataIntegrity(
        currentPage,
        pageSize,
        searchText || undefined,
        statusFilter as any || undefined
      )
      setStockData(data)
    } catch (error) {
      message.error('获取股票数据完整性失败')
      console.error('获取数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 初始化加载
  useEffect(() => {
    loadStats()
    loadStockData()
  }, [currentPage, pageSize, searchText, statusFilter])

  // 刷新数据
  const handleRefresh = () => {
    loadStats()
    loadStockData()
  }

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  // 状态过滤处理
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value)
    setCurrentPage(1)
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'partial':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'missing':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      default:
        return null
    }
  }

  // 表格列定义
  const columns: ColumnsType<StockDataIntegrity> = [
    {
      title: '股票代码',
      dataIndex: 'stock_code',
      key: 'stock_code',
      width: 100,
      fixed: 'left',
      render: (text) => <strong>{text}</strong>
    },
    {
      title: '股票名称',
      dataIndex: 'stock_name',
      key: 'stock_name',
      width: 120,
      ellipsis: true
    },
    {
      title: '市场',
      dataIndex: 'market',
      key: 'market',
      width: 80
    },
    {
      title: '整体状态',
      dataIndex: 'overall_status',
      key: 'overall_status',
      width: 100,
      render: (status) => (
        <Space>
          {getStatusIcon(status)}
          <Tag color={dataIntegrityService.getStatusColor(status)}>
            {dataIntegrityService.getStatusText(status)}
          </Tag>
        </Space>
      )
    },
    {
      title: '日K数据',
      key: 'daily_data',
      width: 200,
      render: (_, record) => (
        <div>
          <div>
            <Progress 
              percent={record.daily_completeness} 
              size="small" 
              status={record.daily_completeness >= 90 ? 'success' : record.daily_completeness >= 50 ? 'active' : 'exception'}
            />
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dataIntegrityService.getDataRangeDescription(
              record.daily_data_start, 
              record.daily_data_end, 
              record.daily_data_count
            )}
          </div>
          {record.daily_missing_days > 0 && (
            <div style={{ fontSize: '12px', color: '#ff4d4f' }}>
              缺失 {record.daily_missing_days} 天
            </div>
          )}
        </div>
      )
    },
    {
      title: '周K数据',
      key: 'weekly_data',
      width: 200,
      render: (_, record) => (
        <div>
          <div>
            <Progress 
              percent={record.weekly_completeness} 
              size="small" 
              status={record.weekly_completeness >= 90 ? 'success' : record.weekly_completeness >= 50 ? 'active' : 'exception'}
            />
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dataIntegrityService.getDataRangeDescription(
              record.weekly_data_start, 
              record.weekly_data_end, 
              record.weekly_data_count
            )}
          </div>
          {record.weekly_missing_weeks > 0 && (
            <div style={{ fontSize: '12px', color: '#ff4d4f' }}>
              缺失 {record.weekly_missing_weeks} 周
            </div>
          )}
        </div>
      )
    },
    {
      title: '月K数据',
      key: 'monthly_data',
      width: 200,
      render: (_, record) => (
        <div>
          <div>
            <Progress 
              percent={record.monthly_completeness} 
              size="small" 
              status={record.monthly_completeness >= 90 ? 'success' : record.monthly_completeness >= 50 ? 'active' : 'exception'}
            />
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dataIntegrityService.getDataRangeDescription(
              record.monthly_data_start, 
              record.monthly_data_end, 
              record.monthly_data_count
            )}
          </div>
          {record.monthly_missing_months > 0 && (
            <div style={{ fontSize: '12px', color: '#ff4d4f' }}>
              缺失 {record.monthly_missing_months} 月
            </div>
          )}
        </div>
      )
    },
    {
      title: '最后更新',
      dataIndex: 'last_update',
      key: 'last_update',
      width: 150,
      render: (text) => dataIntegrityService.formatDate(text)
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => handleUpdateStock(record.stock_code)}
          >
            更新
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleFillMissing(record.stock_code)}
          >
            补缺
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewData(record.stock_code)}
          >
            查看数据
          </Button>
        </Space>
      )
    }
  ]

  // 更新单个股票
  const handleUpdateStock = (stockCode: string) => {
    Modal.confirm({
      title: '确认更新',
      content: `确定要更新股票 ${stockCode} 的数据吗？`,
      onOk: async () => {
        try {
          await dataIntegrityService.updateSpecificStocks([stockCode])
          message.success('更新任务已启动')
          setTimeout(() => {
            handleRefresh()
          }, 2000)
        } catch (error) {
          message.error('启动更新任务失败')
        }
      }
    })
  }

  // 补缺单个股票
  const handleFillMissing = (stockCode: string) => {
    Modal.confirm({
      title: '确认补缺',
      content: `确定要补充股票 ${stockCode} 的缺失数据吗？`,
      onOk: async () => {
        try {
          await dataIntegrityService.fillMissingData([stockCode])
          message.success('补缺任务已启动')
          setTimeout(() => {
            handleRefresh()
          }, 2000)
        } catch (error) {
          message.error('启动补缺任务失败')
        }
      }
    })
  }

  // 查看股票数据
  const handleViewData = async (stockCode: string) => {
    try {
      setSelectedStockCode(stockCode)
      setDataViewModalVisible(true)
      setDataViewLoading(true)

      // 获取股票详细数据
      const response = await fetch(`/api/v1/stocks/${stockCode}/details`)
      if (response.ok) {
        const data = await response.json()
        setStockDetailData(data)
      } else {
        message.error('获取股票数据失败')
      }
    } catch (error) {
      message.error('获取股票数据失败')
    } finally {
      setDataViewLoading(false)
    }
  }

  // 快速补缺操作
  const handleQuickFillMissing = () => {
    Modal.confirm({
      title: '快速补缺确认',
      content: '确定要对所有股票执行补缺操作吗？这将补充所有缺失的日K数据。',
      onOk: async () => {
        try {
          await dataIntegrityService.fillMissingData(undefined, ['daily'])
          message.success('快速补缺任务已启动')
          setTimeout(() => {
            handleRefresh()
          }, 2000)
        } catch (error) {
          message.error('启动快速补缺任务失败')
        }
      }
    })
  }

  // 快速全量更新
  const handleQuickFullUpdate = () => {
    Modal.confirm({
      title: '全量更新确认',
      content: '确定要执行全量数据更新吗？这将更新所有股票的所有周期数据，可能需要较长时间。',
      onOk: async () => {
        try {
          await dataIntegrityService.fullDataUpdate(['daily', 'weekly', 'monthly'])
          message.success('全量更新任务已启动')
          setTimeout(() => {
            handleRefresh()
          }, 2000)
        } catch (error) {
          message.error('启动全量更新任务失败')
        }
      }
    })
  }

  // 批量更新操作
  const handleBatchUpdate = async (values: any) => {
    try {
      setUpdateLoading(true)
      
      const request: DataUpdateRequest = {
        update_type: values.updateType,
        periods: values.periods,
        force_update: values.forceUpdate
      }

      if (values.stockCodes) {
        request.stock_codes = values.stockCodes.split(',').map((code: string) => code.trim())
      }

      if (values.dateRange) {
        request.start_date = values.dateRange[0].format('YYYY-MM-DD')
        request.end_date = values.dateRange[1].format('YYYY-MM-DD')
      }

      await dataIntegrityService.updateStockData(request)
      message.success('批量更新任务已启动')
      setUpdateModalVisible(false)
      updateForm.resetFields()
      
      setTimeout(() => {
        handleRefresh()
      }, 2000)
      
    } catch (error) {
      message.error('启动批量更新任务失败')
    } finally {
      setUpdateLoading(false)
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="股票总数"
              value={stats?.total_stocks || 0}
              loading={statsLoading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据完整"
              value={stats?.complete_stocks || 0}
              valueStyle={{ color: '#52c41a' }}
              loading={statsLoading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据部分"
              value={stats?.partial_stocks || 0}
              valueStyle={{ color: '#faad14' }}
              loading={statsLoading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据缺失"
              value={stats?.missing_stocks || 0}
              valueStyle={{ color: '#ff4d4f' }}
              loading={statsLoading}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据覆盖率 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>日K数据覆盖率</div>
              <Progress 
                type="circle" 
                percent={stats?.daily_coverage || 0} 
                size={80}
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>周K数据覆盖率</div>
              <Progress 
                type="circle" 
                percent={stats?.weekly_coverage || 0} 
                size={80}
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>月K数据覆盖率</div>
              <Progress 
                type="circle" 
                percent={stats?.monthly_coverage || 0} 
                size={80}
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 操作工具栏 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space>
              <Search
                placeholder="搜索股票代码或名称"
                allowClear
                style={{ width: 200 }}
                onSearch={handleSearch}
              />
              <Select
                placeholder="状态筛选"
                allowClear
                style={{ width: 120 }}
                onChange={handleStatusFilter}
              >
                <Option value="complete">完整</Option>
                <Option value="partial">部分</Option>
                <Option value="missing">缺失</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
                loading={loading || statsLoading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={() => setUpdateModalVisible(true)}
              >
                批量操作
              </Button>
              <Button
                type="default"
                onClick={handleQuickFillMissing}
              >
                快速补缺
              </Button>
              <Button
                type="default"
                onClick={handleQuickFullUpdate}
              >
                全量更新
              </Button>
              <Button
                type="default"
                onClick={() => setTaskModalVisible(true)}
              >
                任务监控
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={stockData}
          loading={loading}
          rowKey="stock_code"
          scroll={{ x: 1200 }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size || 50)
            }
          }}
        />
      </Card>

      {/* 批量更新模态框 */}
      <Modal
        title="批量数据更新"
        open={updateModalVisible}
        onCancel={() => setUpdateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={updateForm}
          layout="vertical"
          onFinish={handleBatchUpdate}
        >
          <Form.Item
            name="updateType"
            label="更新类型"
            rules={[{ required: true, message: '请选择更新类型' }]}
          >
            <Select placeholder="选择更新类型">
              <Option value="fill_missing">补缺更新</Option>
              <Option value="specific_stocks">指定股票更新</Option>
              <Option value="date_range">指定时间范围更新</Option>
              <Option value="full_update">全量数据更新</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="stockCodes"
            label="股票代码"
            tooltip="多个股票代码用逗号分隔，留空表示所有股票"
          >
            <Input.TextArea 
              placeholder="例如: 000001,000002,600000" 
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="时间范围"
            tooltip="留空表示使用默认时间范围"
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="periods"
            label="数据周期"
            initialValue={['daily']}
          >
            <Checkbox.Group>
              <Checkbox value="daily">日K</Checkbox>
              <Checkbox value="weekly">周K</Checkbox>
              <Checkbox value="monthly">月K</Checkbox>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            name="forceUpdate"
            valuePropName="checked"
            label="强制更新"
            tooltip="是否覆盖已有数据"
          >
            <Checkbox>强制覆盖已有数据</Checkbox>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={updateLoading}>
                开始更新
              </Button>
              <Button onClick={() => setUpdateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 任务监控模态框 */}
      <Modal
        title="数据更新任务监控"
        open={taskModalVisible}
        onCancel={() => setTaskModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setTaskModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <p>任务监控功能正在开发中...</p>
          <p>您可以在数据管理页面查看任务执行状态</p>
          <Button
            type="primary"
            onClick={() => {
              setTaskModalVisible(false)
              window.location.href = '/data-management'
            }}
          >
            前往数据管理页面
          </Button>
        </div>
      </Modal>

      {/* 查看数据模态框 */}
      <Modal
        title={`股票数据详情 - ${selectedStockCode}`}
        open={dataViewModalVisible}
        onCancel={() => {
          setDataViewModalVisible(false)
          setStockDetailData(null)
        }}
        footer={[
          <Button key="close" onClick={() => {
            setDataViewModalVisible(false)
            setStockDetailData(null)
          }}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        <Spin spinning={dataViewLoading}>
          {stockDetailData ? (
            <Tabs defaultActiveKey="basic" items={[
              {
                key: 'basic',
                label: '基本信息',
                children: (
                  <Descriptions column={2} bordered size="small">
                    <Descriptions.Item label="股票代码">{stockDetailData.stock_code}</Descriptions.Item>
                    <Descriptions.Item label="股票名称">{stockDetailData.stock_name}</Descriptions.Item>
                    <Descriptions.Item label="所属市场">{stockDetailData.market}</Descriptions.Item>
                    <Descriptions.Item label="行业">{stockDetailData.industry}</Descriptions.Item>
                    <Descriptions.Item label="上市日期">{stockDetailData.list_date}</Descriptions.Item>
                    <Descriptions.Item label="最后更新">{stockDetailData.last_update}</Descriptions.Item>
                  </Descriptions>
                )
              },
              {
                key: 'kline',
                label: 'K线数据',
                children: (
                  <div>
                    <h4>最近10条K线数据</h4>
                    <Table
                      dataSource={stockDetailData.recent_klines || []}
                      columns={[
                        { title: '日期', dataIndex: 'trade_date', key: 'trade_date' },
                        { title: '开盘价', dataIndex: 'open_price', key: 'open_price', render: (val) => val?.toFixed(2) },
                        { title: '最高价', dataIndex: 'high_price', key: 'high_price', render: (val) => val?.toFixed(2) },
                        { title: '最低价', dataIndex: 'low_price', key: 'low_price', render: (val) => val?.toFixed(2) },
                        { title: '收盘价', dataIndex: 'close_price', key: 'close_price', render: (val) => val?.toFixed(2) },
                        { title: '成交量', dataIndex: 'volume', key: 'volume', render: (val) => val?.toLocaleString() },
                        { title: '成交额', dataIndex: 'turnover', key: 'turnover', render: (val) => val?.toLocaleString() }
                      ]}
                      pagination={false}
                      size="small"
                      scroll={{ x: 800 }}
                    />
                  </div>
                )
              },
              {
                key: 'stats',
                label: '数据统计',
                children: (
                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic title="日K数据条数" value={stockDetailData.daily_count || 0} />
                    </Col>
                    <Col span={8}>
                      <Statistic title="周K数据条数" value={stockDetailData.weekly_count || 0} />
                    </Col>
                    <Col span={8}>
                      <Statistic title="月K数据条数" value={stockDetailData.monthly_count || 0} />
                    </Col>
                  </Row>
                )
              }
            ]} />
          ) : (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <p>暂无数据</p>
            </div>
          )}
        </Spin>
      </Modal>
    </div>
  )
}

export default DataIntegrity
