import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Progress,
  Space,
  Typography,
  Divider,
  Spin,
} from 'antd'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography

interface MarketIndex {
  name: string
  code: string
  current: number
  change: number
  changePercent: number
  volume: string
}

interface SectorData {
  name: string
  change: number
  changePercent: number
  volume: string
  leadingStock: string
}

interface TopStock {
  code: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: string
  turnover: number
}

const MarketOverview: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [marketIndices, setMarketIndices] = useState<MarketIndex[]>([])
  const [sectorData, setSectorData] = useState<SectorData[]>([])
  const [topGainers, setTopGainers] = useState<TopStock[]>([])
  const [topLosers, setTopLosers] = useState<TopStock[]>([])

  useEffect(() => {
    // 模拟数据加载
    const loadData = async () => {
      setLoading(true)
      
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟市场指数数据
      const indices: MarketIndex[] = [
        {
          name: '上证指数',
          code: '000001',
          current: 3245.67,
          change: 12.45,
          changePercent: 0.38,
          volume: '2,456亿',
        },
        {
          name: '深证成指',
          code: '399001',
          current: 12456.78,
          change: -23.45,
          changePercent: -0.19,
          volume: '1,876亿',
        },
        {
          name: '创业板指',
          code: '399006',
          current: 2567.89,
          change: 45.67,
          changePercent: 1.81,
          volume: '987亿',
        },
        {
          name: '科创50',
          code: '000688',
          current: 1234.56,
          change: -8.90,
          changePercent: -0.72,
          volume: '456亿',
        },
      ]

      // 模拟板块数据
      const sectors: SectorData[] = [
        { name: '新能源汽车', change: 3.45, changePercent: 2.1, volume: '234亿', leadingStock: '比亚迪' },
        { name: '人工智能', change: -1.23, changePercent: -0.8, volume: '189亿', leadingStock: '科大讯飞' },
        { name: '生物医药', change: 2.67, changePercent: 1.5, volume: '156亿', leadingStock: '恒瑞医药' },
        { name: '半导体', change: -2.34, changePercent: -1.2, volume: '298亿', leadingStock: '中芯国际' },
        { name: '新材料', change: 1.89, changePercent: 0.9, volume: '123亿', leadingStock: '宁德时代' },
      ]

      // 模拟涨幅榜
      const gainers: TopStock[] = [
        { code: '300750', name: '宁德时代', price: 234.56, change: 23.45, changePercent: 11.1, volume: '45亿', turnover: 8.9 },
        { code: '002594', name: '比亚迪', price: 189.34, change: 17.23, changePercent: 10.0, volume: '38亿', turnover: 7.2 },
        { code: '600519', name: '贵州茅台', price: 1678.90, change: 151.23, changePercent: 9.9, volume: '23亿', turnover: 3.4 },
      ]

      // 模拟跌幅榜
      const losers: TopStock[] = [
        { code: '000858', name: '五粮液', price: 123.45, change: -12.34, changePercent: -9.1, volume: '34亿', turnover: 6.7 },
        { code: '002415', name: '海康威视', price: 45.67, change: -4.12, changePercent: -8.3, volume: '28亿', turnover: 5.9 },
        { code: '000002', name: '万科A', price: 23.45, change: -1.89, changePercent: -7.5, volume: '19亿', turnover: 4.2 },
      ]

      setMarketIndices(indices)
      setSectorData(sectors)
      setTopGainers(gainers)
      setTopLosers(losers)
      setLoading(false)
    }

    loadData()
  }, [])

  const sectorColumns = [
    {
      title: '板块名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '涨跌幅',
      dataIndex: 'changePercent',
      key: 'changePercent',
      render: (value: number) => (
        <Tag color={value >= 0 ? 'green' : 'red'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Tag>
      ),
    },
    {
      title: '成交额',
      dataIndex: 'volume',
      key: 'volume',
    },
    {
      title: '龙头股',
      dataIndex: 'leadingStock',
      key: 'leadingStock',
    },
  ]

  const stockColumns = [
    {
      title: '股票代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '现价',
      dataIndex: 'price',
      key: 'price',
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '涨跌幅',
      dataIndex: 'changePercent',
      key: 'changePercent',
      render: (value: number) => (
        <Tag color={value >= 0 ? 'green' : 'red'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Tag>
      ),
    },
    {
      title: '成交额',
      dataIndex: 'volume',
      key: 'volume',
    },
  ]

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>正在加载市场数据...</Text>
        </div>
      </div>
    )
  }

  return (
    <div>
      <Title level={2}>市场概览</Title>
      
      {/* 市场指数 */}
      <Card title="主要指数" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {marketIndices.map((index) => (
            <Col xs={24} sm={12} md={6} key={index.code}>
              <Card size="small">
                <Statistic
                  title={index.name}
                  value={index.current}
                  precision={2}
                  valueStyle={{
                    color: index.change >= 0 ? '#3f8600' : '#cf1322',
                  }}
                  prefix={
                    index.change >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />
                  }
                  suffix={
                    <Space direction="vertical" size={0}>
                      <Text style={{ fontSize: '12px', color: index.change >= 0 ? '#3f8600' : '#cf1322' }}>
                        {index.change >= 0 ? '+' : ''}{index.change.toFixed(2)}
                      </Text>
                      <Text style={{ fontSize: '12px', color: index.change >= 0 ? '#3f8600' : '#cf1322' }}>
                        ({index.changePercent >= 0 ? '+' : ''}{index.changePercent.toFixed(2)}%)
                      </Text>
                    </Space>
                  }
                />
                <Divider style={{ margin: '8px 0' }} />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  成交额: {index.volume}
                </Text>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      <Row gutter={[16, 16]}>
        {/* 板块表现 */}
        <Col xs={24} lg={12}>
          <Card title="板块表现" style={{ height: '400px' }}>
            <Table
              dataSource={sectorData}
              columns={sectorColumns}
              pagination={false}
              size="small"
              rowKey="name"
            />
          </Card>
        </Col>

        {/* 涨跌幅榜 */}
        <Col xs={24} lg={12}>
          <Card title="涨跌幅榜" style={{ height: '400px' }}>
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>
                <RiseOutlined style={{ color: '#3f8600' }} /> 涨幅榜
              </Title>
              <Table
                dataSource={topGainers}
                columns={stockColumns}
                pagination={false}
                size="small"
                rowKey="code"
              />
            </div>
            
            <Divider />
            
            <div>
              <Title level={5}>
                <FallOutlined style={{ color: '#cf1322' }} /> 跌幅榜
              </Title>
              <Table
                dataSource={topLosers}
                columns={stockColumns}
                pagination={false}
                size="small"
                rowKey="code"
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default MarketOverview
