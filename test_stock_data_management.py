#!/usr/bin/env python3
"""
测试重构后的股票数据管理API
"""

import asyncio
import requests
import json
from datetime import datetime
from typing import Dict, Any

BASE_URL = "http://localhost:8000/api/v1/stock-data"

def test_api_endpoint(endpoint: str, method: str = "GET", data: Dict[str, Any] = None) -> Dict[str, Any]:
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        print(f"\n🔍 测试 {method} {endpoint}")
        
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=30)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功")
            
            # 显示部分结果
            if isinstance(result, list):
                print(f"📊 返回列表，长度: {len(result)}")
                if result:
                    print(f"📝 第一条记录: {json.dumps(result[0], indent=2, ensure_ascii=False)}")
            elif isinstance(result, dict):
                print(f"📝 返回结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return {"success": True, "data": result}
        else:
            error_text = response.text
            print(f"❌ 请求失败: {error_text}")
            return {"success": False, "error": error_text}
            
    except requests.exceptions.Timeout:
        print(f"⏰ 请求超时")
        return {"success": False, "error": "请求超时"}
    except Exception as e:
        print(f"💥 请求异常: {e}")
        return {"success": False, "error": str(e)}


def test_data_query_apis():
    """测试数据查询API"""
    print("\n" + "="*50)
    print("🔍 测试数据查询API")
    print("="*50)
    
    # 1. 测试获取数据统计
    stats_result = test_api_endpoint("/stats")
    
    # 2. 测试获取股票列表
    stocks_result = test_api_endpoint("/stocks?limit=5")
    
    # 3. 如果有股票数据，测试获取K线和实时数据
    if stocks_result["success"] and stocks_result["data"]:
        stock_code = stocks_result["data"][0]["stock_code"]
        stock_name = stocks_result["data"][0]["stock_name"]
        
        print(f"\n📈 使用股票 {stock_code} ({stock_name}) 测试详细数据...")
        
        # 测试K线数据
        test_api_endpoint(f"/stocks/{stock_code}/kline?days=5")
        
        # 测试实时数据
        test_api_endpoint(f"/stocks/{stock_code}/realtime")


def test_data_update_apis():
    """测试数据更新API"""
    print("\n" + "="*50)
    print("🔄 测试数据更新API")
    print("="*50)
    
    # 1. 测试更新基础信息（后台任务）
    test_api_endpoint("/update/basic-info", method="POST")
    
    # 2. 测试更新实时行情
    test_api_endpoint("/update/realtime", method="POST")
    
    # 3. 获取一只股票来测试K线更新
    stocks_result = test_api_endpoint("/stocks?limit=1")
    if stocks_result["success"] and stocks_result["data"]:
        stock_code = stocks_result["data"][0]["stock_code"]
        
        print(f"\n📊 测试更新股票 {stock_code} 的K线数据...")
        test_api_endpoint(f"/update/kline/{stock_code}?days=5", method="POST")


def test_batch_operations():
    """测试批量操作"""
    print("\n" + "="*50)
    print("📦 测试批量操作")
    print("="*50)
    
    # 获取几只股票进行批量测试
    stocks_result = test_api_endpoint("/stocks?limit=3")
    if stocks_result["success"] and stocks_result["data"]:
        stock_codes = [stock["stock_code"] for stock in stocks_result["data"]]
        codes_str = ",".join(stock_codes)
        
        print(f"\n📈 批量更新股票: {codes_str}")
        test_api_endpoint(f"/update/batch-kline?stock_codes={codes_str}&days=5", method="POST")


def test_error_handling():
    """测试错误处理"""
    print("\n" + "="*50)
    print("🚨 测试错误处理")
    print("="*50)
    
    # 测试不存在的股票
    test_api_endpoint("/stocks/999999/kline")
    test_api_endpoint("/stocks/999999/realtime")
    
    # 测试无效参数
    test_api_endpoint("/stocks?limit=abc")


def check_backend_status():
    """检查后端服务状态"""
    try:
        print("🔍 检查后端服务状态...")
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务运行正常")
            return True
        else:
            print(f"❌ 后端服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试重构后的股票数据管理API")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查后端服务状态
    if not check_backend_status():
        print("\n💡 请确保后端服务正在运行:")
        print("   cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return
    
    try:
        # 1. 测试数据查询API
        test_data_query_apis()
        
        # 2. 测试数据更新API
        test_data_update_apis()
        
        # 3. 测试批量操作
        test_batch_operations()
        
        # 4. 测试错误处理
        test_error_handling()
        
        print("\n" + "="*50)
        print("🎉 API测试完成!")
        print("="*50)
        
        # 最终统计
        print("\n📊 测试总结:")
        print("✅ 数据查询API - 已测试")
        print("✅ 数据更新API - 已测试")
        print("✅ 批量操作API - 已测试")
        print("✅ 错误处理 - 已测试")
        
        print("\n💡 下一步建议:")
        print("1. 运行数据库初始化脚本: python backend/scripts/init_redesigned_db.py")
        print("2. 在前端集成新的数据管理服务")
        print("3. 测试完整的数据流程")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
