import { useState, useEffect, useCallback, useMemo } from 'react'
import { apiClient } from '@/services/api'
import { message } from 'antd'

export interface StockInfo {
  code: string
  name: string
  current_price: number
  change: number
  change_percent: number
  volume: number
  market_cap: number
  pe_ratio: number
  pb_ratio: number
  sector: string
  is_watched: boolean
  high: number
  low: number
  open: number
  close: number
  turnover_rate?: number
  amplitude?: number
}

export interface TechnicalIndicators {
  ma: {
    ma5: number
    ma10: number
    ma20: number
    ma60: number
  }
  rsi: {
    rsi6: number
    rsi12: number
    rsi24: number
  }
  macd: {
    macd: number
    signal: number
    histogram: number
  }
  kdj: {
    k: number
    d: number
    j: number
  }
  boll: {
    upper: number
    middle: number
    lower: number
  }
}

export interface HistoryData {
  date: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

interface UseStockDataOptions {
  enableCache?: boolean
  cacheTimeout?: number // 缓存超时时间（毫秒）
  autoRefresh?: boolean
  refreshInterval?: number // 自动刷新间隔（毫秒）
}

interface CacheItem<T> {
  data: T
  timestamp: number
}

export const useStockData = (options: UseStockDataOptions = {}) => {
  const {
    enableCache = true,
    cacheTimeout = 5 * 60 * 1000, // 5分钟缓存
    autoRefresh = false,
    refreshInterval = 30 * 1000, // 30秒刷新
  } = options

  // 状态管理
  const [stockList, setStockList] = useState<StockInfo[]>([])
  const [selectedStock, setSelectedStock] = useState<StockInfo | null>(null)
  const [indicators, setIndicators] = useState<TechnicalIndicators | null>(null)
  const [historyData, setHistoryData] = useState<HistoryData[]>([])
  
  // 加载状态
  const [loading, setLoading] = useState({
    stockList: false,
    indicators: false,
    history: false,
  })

  // 缓存管理
  const [cache] = useState(new Map<string, CacheItem<any>>())

  // 缓存工具函数 - 使用useRef避免依赖项循环
  const getCacheKey = useCallback((type: string, params?: any) => {
    return `${type}_${params ? JSON.stringify(params) : 'default'}`
  }, [])

  const setCache = useCallback((key: string, data: any) => {
    if (enableCache) {
      cache.set(key, {
        data,
        timestamp: Date.now(),
      })
    }
  }, [enableCache]) // 移除cache依赖

  const getCache = useCallback((key: string) => {
    if (!enableCache) return null

    const item = cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > cacheTimeout) {
      cache.delete(key)
      return null
    }

    return item.data
  }, [enableCache, cacheTimeout]) // 移除cache依赖

  // 模拟数据生成器
  const generateMockStocks = useCallback((): StockInfo[] => {
    const sectors = ['银行', '房地产', '科技', '医药', '消费', '能源']
    const stockCodes = ['000001', '000002', '600000', '600036', '000858', '002415']
    const stockNames = ['平安银行', '万科A', '浦发银行', '招商银行', '五粮液', '海康威视']
    
    return stockCodes.map((code, index) => {
      const basePrice = 10 + Math.random() * 100
      const change = (Math.random() - 0.5) * 5
      const changePercent = (change / basePrice) * 100
      
      return {
        code,
        name: stockNames[index],
        current_price: Number((basePrice + change).toFixed(2)),
        change: Number(change.toFixed(2)),
        change_percent: Number(changePercent.toFixed(2)),
        volume: Math.floor(Math.random() * 200000000),
        market_cap: Math.floor(Math.random() * 1000000000000),
        pe_ratio: Number((Math.random() * 30 + 5).toFixed(1)),
        pb_ratio: Number((Math.random() * 5 + 0.5).toFixed(1)),
        sector: sectors[index % sectors.length],
        is_watched: Math.random() > 0.5,
        high: Number((basePrice + change + Math.random() * 2).toFixed(2)),
        low: Number((basePrice + change - Math.random() * 2).toFixed(2)),
        open: Number((basePrice + (Math.random() - 0.5) * 1).toFixed(2)),
        close: Number((basePrice + change).toFixed(2)),
        turnover_rate: Number((Math.random() * 5).toFixed(2)),
        amplitude: Number((Math.random() * 10).toFixed(2)),
      }
    })
  }, [])

  const generateMockIndicators = useCallback((stockCode: string): TechnicalIndicators => {
    return {
      ma: {
        ma5: Number((Math.random() * 50 + 10).toFixed(2)),
        ma10: Number((Math.random() * 50 + 10).toFixed(2)),
        ma20: Number((Math.random() * 50 + 10).toFixed(2)),
        ma60: Number((Math.random() * 50 + 10).toFixed(2)),
      },
      rsi: {
        rsi6: Number((Math.random() * 100).toFixed(1)),
        rsi12: Number((Math.random() * 100).toFixed(1)),
        rsi24: Number((Math.random() * 100).toFixed(1)),
      },
      macd: {
        macd: Number(((Math.random() - 0.5) * 2).toFixed(3)),
        signal: Number(((Math.random() - 0.5) * 2).toFixed(3)),
        histogram: Number(((Math.random() - 0.5) * 1).toFixed(3)),
      },
      kdj: {
        k: Number((Math.random() * 100).toFixed(1)),
        d: Number((Math.random() * 100).toFixed(1)),
        j: Number((Math.random() * 120).toFixed(1)),
      },
      boll: {
        upper: Number((Math.random() * 50 + 20).toFixed(2)),
        middle: Number((Math.random() * 50 + 15).toFixed(2)),
        lower: Number((Math.random() * 50 + 10).toFixed(2)),
      },
    }
  }, [])

  const generateMockHistory = useCallback((stockCode: string, days: number = 30): HistoryData[] => {
    return Array.from({ length: days }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (days - 1 - i))
      
      const basePrice = 12.0 + Math.random() * 20
      const open = basePrice + (Math.random() - 0.5) * 2
      const close = open + (Math.random() - 0.5) * 3
      const high = Math.max(open, close) + Math.random() * 1
      const low = Math.min(open, close) - Math.random() * 1
      
      return {
        date: date.toISOString().split('T')[0],
        open: Number(open.toFixed(2)),
        high: Number(high.toFixed(2)),
        low: Number(low.toFixed(2)),
        close: Number(close.toFixed(2)),
        volume: Math.floor(Math.random() * 200000000),
      }
    })
  }, [])

  // API调用函数
  const fetchStockList = useCallback(async (searchTerm?: string) => {
    const cacheKey = getCacheKey('stockList', searchTerm)
    const cachedData = getCache(cacheKey)
    
    if (cachedData) {
      setStockList(cachedData)
      return cachedData
    }

    setLoading(prev => ({ ...prev, stockList: true }))
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      const mockData = generateMockStocks()
      
      const filteredData = searchTerm 
        ? mockData.filter(stock => 
            stock.name.includes(searchTerm) || 
            stock.code.includes(searchTerm)
          )
        : mockData
      
      setCache(cacheKey, filteredData)
      setStockList(filteredData)
      return filteredData
    } catch (error) {
      message.error('获取股票列表失败')
      return []
    } finally {
      setLoading(prev => ({ ...prev, stockList: false }))
    }
  }, [getCacheKey, getCache, setCache, generateMockStocks])

  const fetchTechnicalIndicators = useCallback(async (stockCode: string) => {
    const cacheKey = getCacheKey('indicators', stockCode)
    const cachedData = getCache(cacheKey)
    
    if (cachedData) {
      setIndicators(cachedData)
      return cachedData
    }

    setLoading(prev => ({ ...prev, indicators: true }))
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      const mockData = generateMockIndicators(stockCode)
      
      setCache(cacheKey, mockData)
      setIndicators(mockData)
      return mockData
    } catch (error) {
      message.error('获取技术指标失败')
      return null
    } finally {
      setLoading(prev => ({ ...prev, indicators: false }))
    }
  }, [getCacheKey, getCache, setCache, generateMockIndicators])

  const fetchHistoryData = useCallback(async (stockCode: string, days: number = 30) => {
    const cacheKey = getCacheKey('history', { stockCode, days })
    const cachedData = getCache(cacheKey)
    
    if (cachedData) {
      setHistoryData(cachedData)
      return cachedData
    }

    setLoading(prev => ({ ...prev, history: true }))
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 400))
      const mockData = generateMockHistory(stockCode, days)
      
      setCache(cacheKey, mockData)
      setHistoryData(mockData)
      return mockData
    } catch (error) {
      message.error('获取历史数据失败')
      return []
    } finally {
      setLoading(prev => ({ ...prev, history: false }))
    }
  }, [getCacheKey, getCache, setCache, generateMockHistory])

  // 选择股票
  const selectStock = useCallback(async (stock: StockInfo) => {
    setSelectedStock(stock)
    
    // 并行加载技术指标和历史数据
    await Promise.all([
      fetchTechnicalIndicators(stock.code),
      fetchHistoryData(stock.code),
    ])
  }, [fetchTechnicalIndicators, fetchHistoryData])

  // 切换自选股状态
  const toggleWatchlist = useCallback(async (stock: StockInfo) => {
    try {
      // 更新本地状态
      const updatedStock = { ...stock, is_watched: !stock.is_watched }
      
      setStockList(prev =>
        prev.map(s => s.code === stock.code ? updatedStock : s)
      )

      if (selectedStock?.code === stock.code) {
        setSelectedStock(updatedStock)
      }

      // 清除相关缓存
      cache.delete(getCacheKey('stockList'))
      
      message.success(stock.is_watched ? '已从自选股中移除' : '已添加到自选股')
      return updatedStock
    } catch (error) {
      message.error('操作失败')
      return stock
    }
  }, [selectedStock, cache, getCacheKey])

  // 刷新数据
  const refreshData = useCallback(async () => {
    if (selectedStock) {
      await Promise.all([
        fetchTechnicalIndicators(selectedStock.code),
        fetchHistoryData(selectedStock.code),
      ])
    }
    await fetchStockList()
  }, [selectedStock, fetchTechnicalIndicators, fetchHistoryData, fetchStockList])

  // 清除缓存
  const clearCache = useCallback(() => {
    cache.clear()
    message.success('缓存已清除')
  }, [cache])

  // 自动刷新 - 使用useRef避免依赖项循环
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      // 直接调用刷新逻辑，避免依赖refreshData函数
      if (selectedStock) {
        fetchTechnicalIndicators(selectedStock.code)
        fetchHistoryData(selectedStock.code)
      }
      fetchStockList()
    }, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval]) // 移除refreshData依赖

  // 计算衍生状态
  const stats = useMemo(() => ({
    totalStocks: stockList.length,
    watchedStocks: stockList.filter(s => s.is_watched).length,
    gainers: stockList.filter(s => s.change > 0).length,
    losers: stockList.filter(s => s.change < 0).length,
    cacheSize: cache.size,
  }), [stockList]) // 移除cache.size依赖，避免循环

  return {
    // 数据状态
    stockList,
    selectedStock,
    indicators,
    historyData,
    loading,
    stats,
    
    // 操作函数
    fetchStockList,
    fetchTechnicalIndicators,
    fetchHistoryData,
    selectStock,
    toggleWatchlist,
    refreshData,
    clearCache,
    
    // 工具函数
    setSelectedStock,
    setStockList,
  }
}

export default useStockData
