#!/usr/bin/env python3
"""
数据管理命令行工具
提供离线数据存储和管理功能
"""

import asyncio
import sys
import os
import argparse
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.data_manager import DataManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def show_stats():
    """显示数据统计"""
    try:
        data_manager = DataManager()
        stats = await data_manager.get_data_statistics()
        
        print("\n📊 数据统计报告")
        print("=" * 50)
        print(f"📈 股票信息:")
        print(f"   总股票数: {stats['stocks']['total']}")
        print(f"   活跃股票数: {stats['stocks']['active']}")
        print(f"   有数据股票数: {stats['stocks']['with_data']}")
        print(f"   数据覆盖率: {stats['stocks']['coverage_rate']}%")
        
        print(f"\n📊 K线数据:")
        print(f"   总记录数: {stats['klines']['total']}")
        print(f"   最新数据日期: {stats['klines']['latest_date']}")
        
        if stats['klines']['by_period']:
            print(f"   按周期分布:")
            for period, count in stats['klines']['by_period'].items():
                print(f"     {period}: {count} 条")
        
        print(f"\n🕒 更新时间: {stats['updated_at']}")
        
        # 数据质量评估
        if stats['klines']['total'] > 0:
            avg_records = stats['klines']['total'] / max(stats['stocks']['with_data'], 1)
            print(f"\n📊 数据质量评估:")
            print(f"   平均每只股票记录数: {avg_records:.1f}")
            
            if avg_records >= 100:
                print("   ✅ 数据质量: 优秀 (>100条/股)")
            elif avg_records >= 60:
                print("   ✅ 数据质量: 良好 (60-100条/股)")
            elif avg_records >= 20:
                print("   ⚠️ 数据质量: 一般 (20-60条/股)")
            else:
                print("   ❌ 数据质量: 较差 (<20条/股)")
        
    except Exception as e:
        logger.error(f"获取数据统计失败: {e}")

async def update_stocks(stock_codes=None, days_back=30):
    """更新股票数据"""
    try:
        data_manager = DataManager()
        
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days_back)
        
        print(f"\n🔄 开始更新股票数据")
        print(f"📅 日期范围: {start_date} 到 {end_date}")
        
        if stock_codes:
            print(f"🎯 目标股票: {stock_codes}")
        else:
            print(f"🎯 目标: 所有活跃股票")
        
        result = await data_manager.update_stock_data(
            stock_codes=stock_codes,
            start_date=start_date,
            end_date=end_date,
            period='daily'
        )
        
        print(f"\n✅ 数据更新完成:")
        print(f"   新增记录: {result['added']}")
        print(f"   更新记录: {result['updated']}")
        print(f"   错误数量: {result['errors']}")
        
    except Exception as e:
        logger.error(f"更新股票数据失败: {e}")

async def init_stock_list(force=False):
    """初始化股票列表"""
    try:
        data_manager = DataManager()
        
        print(f"\n📋 开始初始化股票列表 (强制更新: {force})")
        
        count = await data_manager.initialize_stock_list(force_update=force)
        
        print(f"✅ 股票列表初始化完成，处理了 {count} 只股票")
        
    except Exception as e:
        logger.error(f"初始化股票列表失败: {e}")

async def cleanup_data(days_to_keep=1095):
    """清理旧数据"""
    try:
        data_manager = DataManager()
        
        print(f"\n🧹 开始清理旧数据 (保留 {days_to_keep} 天)")
        
        count = await data_manager.cleanup_old_data(days_to_keep)
        
        print(f"✅ 数据清理完成，删除了 {count} 条记录")
        
    except Exception as e:
        logger.error(f"清理数据失败: {e}")

async def backup_data():
    """备份数据"""
    try:
        print("\n💾 数据备份功能")
        print("提示: 数据已存储在PostgreSQL数据库中")
        print("建议使用 pg_dump 进行数据库备份:")
        print("pg_dump -h localhost -U postgres -d stock_analysis > backup.sql")
        
    except Exception as e:
        logger.error(f"备份数据失败: {e}")

async def health_check():
    """健康检查"""
    try:
        data_manager = DataManager()
        stats = await data_manager.get_data_statistics()
        
        print("\n🏥 数据服务健康检查")
        print("=" * 50)
        
        # 检查数据新鲜度
        latest_date = stats['klines'].get('latest_date')
        if latest_date:
            latest_datetime = datetime.fromisoformat(latest_date)
            days_old = (datetime.now().date() - latest_datetime.date()).days
        else:
            days_old = float('inf')
        
        # 判断健康状态
        if days_old <= 1:
            health_status = "🟢 健康"
        elif days_old <= 7:
            health_status = "🟡 警告"
        else:
            health_status = "🔴 不健康"
        
        print(f"状态: {health_status}")
        print(f"最新数据: {latest_date or '无数据'}")
        print(f"数据延迟: {days_old if days_old != float('inf') else '无限'} 天")
        print(f"数据覆盖率: {stats['stocks']['coverage_rate']}%")
        
        # 建议
        print(f"\n💡 建议:")
        if days_old > 7:
            print("   - 数据过期，建议立即更新")
        elif days_old > 1:
            print("   - 数据稍有延迟，建议更新")
        else:
            print("   - 数据新鲜，状态良好")
        
        if stats['stocks']['coverage_rate'] < 80:
            print("   - 数据覆盖率较低，建议初始化更多股票数据")
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票数据管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 统计命令
    subparsers.add_parser('stats', help='显示数据统计')
    
    # 更新命令
    update_parser = subparsers.add_parser('update', help='更新股票数据')
    update_parser.add_argument('--stocks', nargs='+', help='指定股票代码')
    update_parser.add_argument('--days', type=int, default=30, help='回溯天数')
    
    # 初始化命令
    init_parser = subparsers.add_parser('init', help='初始化股票列表')
    init_parser.add_argument('--force', action='store_true', help='强制更新')
    
    # 清理命令
    cleanup_parser = subparsers.add_parser('cleanup', help='清理旧数据')
    cleanup_parser.add_argument('--keep-days', type=int, default=1095, help='保留天数')
    
    # 备份命令
    subparsers.add_parser('backup', help='备份数据')
    
    # 健康检查命令
    subparsers.add_parser('health', help='健康检查')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 执行对应命令
    if args.command == 'stats':
        asyncio.run(show_stats())
    elif args.command == 'update':
        asyncio.run(update_stocks(args.stocks, args.days))
    elif args.command == 'init':
        asyncio.run(init_stock_list(args.force))
    elif args.command == 'cleanup':
        asyncio.run(cleanup_data(args.keep_days))
    elif args.command == 'backup':
        asyncio.run(backup_data())
    elif args.command == 'health':
        asyncio.run(health_check())

if __name__ == "__main__":
    main()
