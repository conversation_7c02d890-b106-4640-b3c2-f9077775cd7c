/**
 * 数据分类管理服务
 * 提供数据分类相关的API调用
 */

import axios from 'axios'

const API_BASE_URL = 'http://localhost:8000/api/v1/data-classification'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`[Data Classification API] ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('[Data Classification API Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('[Data Classification API Response Error]', error)
    if (error.response?.status === 404) {
      throw new Error('请求的资源不存在')
    } else if (error.response?.status === 500) {
      throw new Error('服务器内部错误')
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('请求超时')
    }
    throw error
  }
)

export interface DataCategory {
  category_key: string
  category_name: string
  description: string
  subcategories: Array<{
    key: string
    name: string
    table: string
    record_count: number
  }>
  total_records: number
  last_update: string | null
}

export interface SubcategoryStats {
  subcategory_key: string
  subcategory_name: string
  table_name: string
  record_count: number
  latest_record_time: string | null
  data_size_mb: number
  data_quality_score: number
}

export interface SampleData {
  category_key: string
  subcategory_key: string
  table_name: string
  sample_count: number
  total_columns: number
  columns: string[]
  sample_data: any[]
}

export interface CleanupOptions {
  remove_duplicates: boolean
  remove_old_data: boolean
  days_to_keep: number
}

export interface CleanupResult {
  message: string
  category_key: string
  subcategory_key: string
  table_name: string
  cleaned_records: number
  cleanup_options: CleanupOptions
}

class DataClassificationService {
  /**
   * 获取所有数据分类
   */
  async getCategories(): Promise<DataCategory[]> {
    try {
      const response = await apiClient.get('/categories')
      return response.data
    } catch (error) {
      console.error('获取数据分类失败:', error)
      throw new Error('获取数据分类失败')
    }
  }

  /**
   * 获取指定分类的子分类统计信息
   */
  async getSubcategoryStats(categoryKey: string): Promise<SubcategoryStats[]> {
    try {
      const response = await apiClient.get(`/categories/${categoryKey}/subcategories`)
      return response.data
    } catch (error) {
      console.error('获取子分类统计失败:', error)
      throw new Error('获取子分类统计失败')
    }
  }

  /**
   * 获取子分类的样本数据
   */
  async getSampleData(
    categoryKey: string, 
    subcategoryKey: string, 
    limit: number = 5
  ): Promise<SampleData> {
    try {
      const response = await apiClient.get(
        `/categories/${categoryKey}/subcategories/${subcategoryKey}/sample`,
        { params: { limit } }
      )
      return response.data
    } catch (error) {
      console.error('获取样本数据失败:', error)
      throw new Error('获取样本数据失败')
    }
  }

  /**
   * 清理子分类数据
   */
  async cleanupData(
    categoryKey: string,
    subcategoryKey: string,
    options: CleanupOptions
  ): Promise<CleanupResult> {
    try {
      const response = await apiClient.post(
        `/categories/${categoryKey}/subcategories/${subcategoryKey}/cleanup`,
        options
      )
      return response.data
    } catch (error) {
      console.error('数据清理失败:', error)
      throw new Error('数据清理失败')
    }
  }

  /**
   * 获取数据分类概览统计
   */
  async getCategoryOverview(): Promise<{
    total_categories: number
    total_subcategories: number
    total_records: number
    average_quality_score: number
    categories_summary: Array<{
      category_key: string
      category_name: string
      record_count: number
      quality_score: number
    }>
  }> {
    try {
      const categories = await this.getCategories()
      
      let totalRecords = 0
      let totalQualityScore = 0
      let subcategoryCount = 0
      const categoriesSummary = []

      for (const category of categories) {
        totalRecords += category.total_records
        
        // 获取子分类统计来计算质量评分
        const subcategoryStats = await this.getSubcategoryStats(category.category_key)
        subcategoryCount += subcategoryStats.length
        
        const categoryQualityScore = subcategoryStats.length > 0
          ? subcategoryStats.reduce((sum, stat) => sum + stat.data_quality_score, 0) / subcategoryStats.length
          : 0
        
        totalQualityScore += categoryQualityScore
        
        categoriesSummary.push({
          category_key: category.category_key,
          category_name: category.category_name,
          record_count: category.total_records,
          quality_score: Math.round(categoryQualityScore * 10) / 10
        })
      }

      return {
        total_categories: categories.length,
        total_subcategories: subcategoryCount,
        total_records: totalRecords,
        average_quality_score: categories.length > 0 
          ? Math.round((totalQualityScore / categories.length) * 10) / 10 
          : 0,
        categories_summary: categoriesSummary
      }
    } catch (error) {
      console.error('获取分类概览失败:', error)
      throw new Error('获取分类概览失败')
    }
  }

  /**
   * 批量清理多个子分类数据
   */
  async batchCleanup(
    cleanupTasks: Array<{
      category_key: string
      subcategory_key: string
      options: CleanupOptions
    }>
  ): Promise<CleanupResult[]> {
    try {
      const results = []
      
      for (const task of cleanupTasks) {
        const result = await this.cleanupData(
          task.category_key,
          task.subcategory_key,
          task.options
        )
        results.push(result)
      }
      
      return results
    } catch (error) {
      console.error('批量清理失败:', error)
      throw new Error('批量清理失败')
    }
  }

  /**
   * 验证数据分类完整性
   */
  async validateDataIntegrity(): Promise<{
    is_valid: boolean
    issues: Array<{
      category_key: string
      subcategory_key: string
      issue_type: string
      description: string
      severity: 'low' | 'medium' | 'high'
    }>
    recommendations: string[]
  }> {
    try {
      const categories = await this.getCategories()
      const issues = []
      const recommendations = []

      for (const category of categories) {
        const subcategoryStats = await this.getSubcategoryStats(category.category_key)
        
        for (const stat of subcategoryStats) {
          // 检查数据质量
          if (stat.data_quality_score < 50) {
            issues.push({
              category_key: category.category_key,
              subcategory_key: stat.subcategory_key,
              issue_type: 'low_quality',
              description: `数据质量评分过低: ${stat.data_quality_score}%`,
              severity: 'high'
            })
          }
          
          // 检查数据新鲜度
          if (!stat.latest_record_time) {
            issues.push({
              category_key: category.category_key,
              subcategory_key: stat.subcategory_key,
              issue_type: 'no_data',
              description: '没有数据记录',
              severity: 'high'
            })
          } else {
            const daysSinceUpdate = Math.floor(
              (Date.now() - new Date(stat.latest_record_time).getTime()) / (1000 * 60 * 60 * 24)
            )
            
            if (daysSinceUpdate > 7) {
              issues.push({
                category_key: category.category_key,
                subcategory_key: stat.subcategory_key,
                issue_type: 'stale_data',
                description: `数据已过期 ${daysSinceUpdate} 天`,
                severity: daysSinceUpdate > 30 ? 'high' : 'medium'
              })
            }
          }
          
          // 检查数据量
          if (stat.record_count === 0) {
            issues.push({
              category_key: category.category_key,
              subcategory_key: stat.subcategory_key,
              issue_type: 'empty_table',
              description: '表为空',
              severity: 'medium'
            })
          }
        }
      }

      // 生成建议
      if (issues.some(issue => issue.issue_type === 'stale_data')) {
        recommendations.push('建议设置自动数据更新任务')
      }
      if (issues.some(issue => issue.issue_type === 'low_quality')) {
        recommendations.push('建议执行数据清理和质量检查')
      }
      if (issues.some(issue => issue.issue_type === 'empty_table')) {
        recommendations.push('建议检查数据导入流程')
      }

      return {
        is_valid: issues.length === 0,
        issues,
        recommendations
      }
    } catch (error) {
      console.error('数据完整性验证失败:', error)
      throw new Error('数据完整性验证失败')
    }
  }
}

export const dataClassificationService = new DataClassificationService()
export default dataClassificationService
