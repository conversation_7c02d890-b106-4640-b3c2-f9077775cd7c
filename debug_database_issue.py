#!/usr/bin/env python3
"""
调试数据库问题
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import AsyncSessionLocal
from app.models.stock import StockSpotData
from sqlalchemy import select, text
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(name)s | %(message)s')
logger = logging.getLogger(__name__)

async def debug_database():
    """调试数据库问题"""
    try:
        logger.info("=" * 80)
        logger.info("🔍 调试数据库问题")
        logger.info("=" * 80)
        
        async with AsyncSessionLocal() as session:
            # 1. 检查表是否存在
            logger.info("📋 检查stock_spot_data表是否存在")
            result = await session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_spot_data'"))
            table_exists = result.fetchone()
            
            if table_exists:
                logger.info("✅ stock_spot_data表存在")
            else:
                logger.error("❌ stock_spot_data表不存在")
                return
            
            # 2. 检查表结构
            logger.info("📊 检查表结构")
            result = await session.execute(text("PRAGMA table_info(stock_spot_data)"))
            columns = result.fetchall()
            logger.info(f"表有 {len(columns)} 列:")
            for col in columns:
                logger.info(f"  {col[1]} ({col[2]})")
            
            # 3. 检查数据总数
            logger.info("📈 检查数据总数")
            result = await session.execute(text("SELECT COUNT(*) FROM stock_spot_data"))
            total_count = result.scalar()
            logger.info(f"表中共有 {total_count} 条记录")
            
            if total_count == 0:
                logger.warning("⚠️  表中没有数据")
                return
            
            # 4. 查看所有数据
            logger.info("📊 查看所有数据")
            result = await session.execute(text("SELECT * FROM stock_spot_data ORDER BY update_time DESC"))
            all_records = result.fetchall()
            
            for i, record in enumerate(all_records):
                logger.info(f"  {i+1}. ID={record[0]}, 代码={record[1]}, 名称={record[2]}, 价格={record[3]}, 涨跌幅={record[8]}")
            
            # 5. 使用SQLAlchemy ORM查询
            logger.info("🔍 使用SQLAlchemy ORM查询")
            result = await session.execute(select(StockSpotData).order_by(StockSpotData.update_time.desc()))
            orm_records = result.scalars().all()
            
            logger.info(f"ORM查询返回 {len(orm_records)} 条记录")
            for i, record in enumerate(orm_records):
                logger.info(f"  {i+1}. {record.stock_code} {record.stock_name}: ¥{record.current_price} ({record.change_percent:+.2f}%)")
            
            # 6. 模拟API查询条件
            logger.info("🌐 模拟API查询条件")
            
            # 模拟不带条件的查询（limit=5）
            result = await session.execute(
                select(StockSpotData)
                .order_by(StockSpotData.update_time.desc())
                .limit(5)
            )
            api_like_records = result.scalars().all()
            
            logger.info(f"模拟API查询返回 {len(api_like_records)} 条记录")
            for i, record in enumerate(api_like_records):
                logger.info(f"  {i+1}. {record.stock_code} {record.stock_name}: ¥{record.current_price} ({record.change_percent:+.2f}%)")
            
            # 7. 检查特定股票代码
            logger.info("🎯 检查特定股票代码 000001")
            result = await session.execute(
                select(StockSpotData)
                .where(StockSpotData.stock_code == "000001")
                .order_by(StockSpotData.update_time.desc())
            )
            specific_records = result.scalars().all()
            
            logger.info(f"股票000001有 {len(specific_records)} 条记录")
            for record in specific_records:
                logger.info(f"  {record.stock_name}: ¥{record.current_price} ({record.change_percent:+.2f}%) - {record.update_time}")
            
            # 8. 检查数据是否是真实数据
            if api_like_records:
                first_price = float(api_like_records[0].current_price)
                if first_price != 10.5:
                    logger.info("🎉 数据库包含真实数据!")
                    return True
                else:
                    logger.warning("⚠️  数据库仍然是模拟数据")
                    return False
            else:
                logger.error("❌ 没有查询到数据")
                return False
                
    except Exception as e:
        logger.error(f"❌ 调试失败: {e}", exc_info=True)
        return False

async def test_api_simulation():
    """模拟API调用过程"""
    try:
        logger.info("\n" + "=" * 50)
        logger.info("🌐 模拟API调用过程")
        logger.info("=" * 50)
        
        async with AsyncSessionLocal() as session:
            # 完全模拟API的查询逻辑
            query = select(StockSpotData)
            
            # 不添加任何筛选条件（模拟不带参数的API调用）
            
            # 排序和分页（模拟API的排序和限制）
            query = query.order_by(StockSpotData.update_time.desc()).offset(0).limit(5)
            
            result = await session.execute(query)
            spot_data = result.scalars().all()
            
            logger.info(f"模拟API查询返回 {len(spot_data)} 条数据")
            
            # 模拟API的响应格式化
            api_response = []
            for data in spot_data:
                response_item = {
                    "stock_code": data.stock_code,
                    "stock_name": data.stock_name,
                    "current_price": float(data.current_price),
                    "open_price": float(data.open_price),
                    "high_price": float(data.high_price),
                    "low_price": float(data.low_price),
                    "pre_close": float(data.pre_close),
                    "change_amount": float(data.change_amount),
                    "change_percent": float(data.change_percent),
                    "amplitude": float(data.amplitude),
                    "volume": data.volume,
                    "turnover": float(data.turnover),
                    "turnover_rate": float(data.turnover_rate),
                    "volume_ratio": float(data.volume_ratio),
                    "pe_ratio": float(data.pe_ratio) if data.pe_ratio else None,
                    "pb_ratio": float(data.pb_ratio) if data.pb_ratio else None,
                    "total_market_cap": float(data.total_market_cap) if data.total_market_cap else None,
                    "float_market_cap": float(data.float_market_cap) if data.float_market_cap else None,
                    "speed": float(data.speed) if data.speed else None,
                    "change_5min": float(data.change_5min) if data.change_5min else None,
                    "change_60day": float(data.change_60day) if data.change_60day else None,
                    "change_ytd": float(data.change_ytd) if data.change_ytd else None,
                    "trade_date": data.trade_date.isoformat(),
                    "update_time": data.update_time.isoformat()
                }
                api_response.append(response_item)
            
            logger.info("📊 模拟API响应:")
            for i, item in enumerate(api_response):
                logger.info(f"  {i+1}. {item['stock_name']} ({item['stock_code']}): ¥{item['current_price']} ({item['change_percent']:+.2f}%)")
            
            # 检查是否是真实数据
            if api_response and api_response[0]['current_price'] != 10.5:
                logger.info("🎉 模拟API返回真实数据!")
                return True
            else:
                logger.warning("⚠️  模拟API返回模拟数据")
                return False
                
    except Exception as e:
        logger.error(f"❌ 模拟API调用失败: {e}", exc_info=True)
        return False

async def main():
    """主函数"""
    # 1. 调试数据库
    db_result = await debug_database()
    
    # 2. 模拟API调用
    api_result = await test_api_simulation()
    
    logger.info("\n" + "=" * 80)
    logger.info("📋 调试结果总结")
    logger.info("=" * 80)
    logger.info(f"数据库查询: {'✅ 真实数据' if db_result else '❌ 模拟数据或无数据'}")
    logger.info(f"模拟API调用: {'✅ 真实数据' if api_result else '❌ 模拟数据'}")
    
    if db_result and api_result:
        logger.info("🎉 数据库和模拟API都返回真实数据 - 问题可能在其他地方")
    elif db_result and not api_result:
        logger.warning("⚠️  数据库有真实数据，但模拟API返回模拟数据 - 查询逻辑有问题")
    elif not db_result and api_result:
        logger.warning("⚠️  数据库是模拟数据，但模拟API返回真实数据 - 不可能的情况")
    else:
        logger.error("❌ 数据库和模拟API都返回模拟数据 - 数据没有正确插入")
    
    logger.info("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
