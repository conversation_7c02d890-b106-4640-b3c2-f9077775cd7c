/**
 * 自定义功能管理组件
 * 提供自定义技术指标、股票形态识别、回测策略的管理功能
 */

import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Space, Tag, Modal, Form, Input, Select, 
  Switch, message, Tabs, Row, Col, Statistic, Alert, Descriptions,
  Badge, Tooltip, Popconfirm, Typography, Drawer, Progress,
  List, Avatar, Timeline, CodeMirror
} from 'antd'
import {
  PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined,
  CodeOutlined, HistoryOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  BugOutlined, ReloadOutlined, SettingOutlined, TrophyOutlined,
  LineChartOutlined, SearchOutlined, ThunderboltOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { customFunctionalityService } from '../services/customFunctionalityService'

const { Option } = Select
const { TabPane } = Tabs
const { Text, Title, Paragraph } = Typography
const { TextArea } = Input

interface CustomFunction {
  function_id?: string
  function_name: string
  function_type: string
  description?: string
  code: string
  parameters: Record<string, any>
  is_active: boolean
  created_by?: string
  created_at?: string
  updated_at?: string
  version: string
  tags: string[]
}

interface ExecutionResult {
  execution_id: string
  function_id: string
  status: string
  result_data?: Record<string, any>
  error_message?: string
  execution_time?: number
  started_at: string
  completed_at?: string
}

const CustomFunctionalityManager: React.FC = () => {
  const [functions, setFunctions] = useState<CustomFunction[]>([])
  const [executions, setExecutions] = useState<ExecutionResult[]>([])
  const [statistics, setStatistics] = useState<any>({})
  const [templates, setTemplates] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [executionDrawerVisible, setExecutionDrawerVisible] = useState(false)
  const [editingFunction, setEditingFunction] = useState<CustomFunction | null>(null)
  const [selectedFunction, setSelectedFunction] = useState<CustomFunction | null>(null)
  const [activeTab, setActiveTab] = useState('functions')
  const [form] = Form.useForm()

  useEffect(() => {
    loadData()
    // 定时刷新执行状态
    const interval = setInterval(loadExecutions, 5000)
    return () => clearInterval(interval)
  }, [])

  const loadData = async () => {
    await Promise.all([
      loadFunctions(),
      loadExecutions(),
      loadStatistics(),
      loadTemplates()
    ])
  }

  const loadFunctions = async () => {
    try {
      setLoading(true)
      const data = await customFunctionalityService.getFunctions()
      setFunctions(data)
    } catch (error) {
      message.error('加载自定义功能失败')
      console.error('加载自定义功能失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadExecutions = async () => {
    try {
      // 获取最近的执行记录
      const allExecutions: ExecutionResult[] = []
      for (const func of functions) {
        if (func.function_id) {
          const executions = await customFunctionalityService.getFunctionExecutions(func.function_id)
          allExecutions.push(...executions)
        }
      }
      setExecutions(allExecutions.sort((a, b) => 
        new Date(b.started_at).getTime() - new Date(a.started_at).getTime()
      ))
    } catch (error) {
      console.error('加载执行记录失败:', error)
    }
  }

  const loadStatistics = async () => {
    try {
      const data = await customFunctionalityService.getStatistics()
      setStatistics(data)
    } catch (error) {
      console.error('加载统计信息失败:', error)
    }
  }

  const loadTemplates = async () => {
    try {
      const data = await customFunctionalityService.getTemplates()
      setTemplates(data)
    } catch (error) {
      console.error('加载模板失败:', error)
    }
  }

  const handleCreateFunction = () => {
    setEditingFunction(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEditFunction = (func: CustomFunction) => {
    setEditingFunction(func)
    form.setFieldsValue({
      ...func,
      tags: func.tags.join(',')
    })
    setModalVisible(true)
  }

  const handleSaveFunction = async () => {
    try {
      const values = await form.validateFields()
      
      const functionData: CustomFunction = {
        function_name: values.function_name,
        function_type: values.function_type,
        description: values.description,
        code: values.code,
        parameters: values.parameters || {},
        is_active: values.is_active,
        version: values.version || '1.0',
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : []
      }

      if (editingFunction?.function_id) {
        await customFunctionalityService.updateFunction(editingFunction.function_id, functionData)
        message.success('功能更新成功')
      } else {
        await customFunctionalityService.createFunction(functionData)
        message.success('功能创建成功')
      }

      setModalVisible(false)
      loadFunctions()
    } catch (error) {
      message.error('保存功能失败')
      console.error('保存功能失败:', error)
    }
  }

  const handleDeleteFunction = async (functionId: string) => {
    try {
      await customFunctionalityService.deleteFunction(functionId)
      message.success('功能删除成功')
      loadFunctions()
    } catch (error) {
      message.error('删除功能失败')
      console.error('删除功能失败:', error)
    }
  }

  const handleExecuteFunction = async (functionId: string) => {
    try {
      const result = await customFunctionalityService.executeFunction(functionId, {})
      message.success(`功能执行已启动: ${result.execution_id}`)
      loadExecutions()
    } catch (error) {
      message.error('执行功能失败')
      console.error('执行功能失败:', error)
    }
  }

  const handleValidateCode = async (functionId: string) => {
    try {
      const result = await customFunctionalityService.validateCode(functionId)
      if (result.is_valid) {
        message.success('代码验证通过')
      } else {
        message.error(`代码验证失败: ${result.error_message}`)
      }
    } catch (error) {
      message.error('代码验证失败')
      console.error('代码验证失败:', error)
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      running: 'blue',
      success: 'green',
      error: 'red'
    }
    return colors[status as keyof typeof colors] || 'default'
  }

  const getStatusIcon = (status: string) => {
    const icons = {
      running: <PlayCircleOutlined />,
      success: <CheckCircleOutlined />,
      error: <ExclamationCircleOutlined />
    }
    return icons[status as keyof typeof icons] || null
  }

  const getFunctionTypeIcon = (type: string) => {
    const icons = {
      technical_indicator: <LineChartOutlined />,
      pattern_recognition: <SearchOutlined />,
      backtesting_strategy: <TrophyOutlined />,
      screening_filter: <ThunderboltOutlined />
    }
    return icons[type as keyof typeof icons] || <SettingOutlined />
  }

  const functionColumns = [
    {
      title: '功能名称',
      dataIndex: 'function_name',
      key: 'function_name',
      render: (text: string, record: CustomFunction) => (
        <Space>
          {getFunctionTypeIcon(record.function_type)}
          <Text strong>{text}</Text>
          <Tag color={record.is_active ? 'green' : 'red'}>
            {record.is_active ? '激活' : '停用'}
          </Tag>
        </Space>
      )
    },
    {
      title: '功能类型',
      dataIndex: 'function_type',
      key: 'function_type',
      render: (type: string) => {
        const typeMap = {
          technical_indicator: { text: '技术指标', color: 'blue' },
          pattern_recognition: { text: '形态识别', color: 'green' },
          backtesting_strategy: { text: '回测策略', color: 'orange' },
          screening_filter: { text: '选股过滤器', color: 'purple' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (version: string) => <Badge count={version} size="small" />
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string[]) => (
        <Space wrap>
          {tags.map(tag => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (time: string) => (
        time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-'
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: CustomFunction) => (
        <Space>
          <Tooltip title="执行功能">
            <Button
              icon={<PlayCircleOutlined />}
              size="small"
              type="primary"
              onClick={() => handleExecuteFunction(record.function_id!)}
              disabled={!record.is_active}
            />
          </Tooltip>
          <Tooltip title="验证代码">
            <Button
              icon={<BugOutlined />}
              size="small"
              onClick={() => handleValidateCode(record.function_id!)}
            />
          </Tooltip>
          <Tooltip title="查看执行历史">
            <Button
              icon={<HistoryOutlined />}
              size="small"
              onClick={() => {
                setSelectedFunction(record)
                setExecutionDrawerVisible(true)
              }}
            />
          </Tooltip>
          <Tooltip title="编辑功能">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditFunction(record)}
            />
          </Tooltip>
          <Tooltip title="删除功能">
            <Popconfirm
              title="确定删除此功能吗？"
              onConfirm={() => handleDeleteFunction(record.function_id!)}
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ]

  const executionColumns = [
    {
      title: '功能名称',
      dataIndex: 'function_id',
      key: 'function_id',
      render: (functionId: string) => {
        const func = functions.find(f => f.function_id === functionId)
        return func ? func.function_name : functionId
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag icon={getStatusIcon(status)} color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: '执行时间',
      dataIndex: 'execution_time',
      key: 'execution_time',
      render: (time: number) => (
        time ? `${time.toFixed(2)}s` : '-'
      )
    },
    {
      title: '开始时间',
      dataIndex: 'started_at',
      key: 'started_at',
      render: (time: string) => dayjs(time).format('MM-DD HH:mm:ss')
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (time: string) => (
        time ? dayjs(time).format('MM-DD HH:mm:ss') : '-'
      )
    }
  ]

  const renderOverview = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总功能数"
            value={statistics.total_functions || 0}
            prefix={<SettingOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="激活功能"
            value={statistics.active_functions || 0}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="总执行次数"
            value={statistics.total_executions || 0}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: '#722ed1' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="成功率"
            value={statistics.success_rate || 0}
            suffix="%"
            precision={1}
            prefix={<TrophyOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
    </Row>
  )

  const renderTemplateSelector = () => {
    if (!templates.indicator_templates && !templates.pattern_templates) {
      return null
    }

    return (
      <Form.Item label="使用模板">
        <Select
          placeholder="选择模板快速创建"
          allowClear
          onChange={(templateKey) => {
            if (templateKey) {
              const template = templates.indicator_templates?.[templateKey] || 
                             templates.pattern_templates?.[templateKey]
              if (template) {
                form.setFieldsValue({
                  function_name: template.name,
                  description: template.description,
                  code: template.code,
                  parameters: template.parameters
                })
              }
            }
          }}
        >
          {templates.indicator_templates && Object.entries(templates.indicator_templates).map(([key, template]: [string, any]) => (
            <Option key={key} value={key}>
              <Space>
                <LineChartOutlined />
                {template.name}
              </Space>
            </Option>
          ))}
          {templates.pattern_templates && Object.entries(templates.pattern_templates).map(([key, template]: [string, any]) => (
            <Option key={key} value={key}>
              <Space>
                <SearchOutlined />
                {template.name}
              </Space>
            </Option>
          ))}
        </Select>
      </Form.Item>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title="自定义功能管理" 
        extra={
          <Space>
            <Button 
              icon={<PlusOutlined />} 
              type="primary" 
              onClick={handleCreateFunction}
            >
              新建功能
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadData} 
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {renderOverview()}
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="自定义功能" key="functions">
            <Table
              columns={functionColumns}
              dataSource={functions}
              rowKey="function_id"
              loading={loading}
              size="small"
            />
          </TabPane>
          
          <TabPane tab="执行记录" key="executions">
            <Table
              columns={executionColumns}
              dataSource={executions}
              rowKey="execution_id"
              loading={loading}
              size="small"
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 功能配置模态框 */}
      <Modal
        title={editingFunction ? '编辑自定义功能' : '新建自定义功能'}
        open={modalVisible}
        onOk={handleSaveFunction}
        onCancel={() => setModalVisible(false)}
        width={1000}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          {renderTemplateSelector()}
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="function_name"
                label="功能名称"
                rules={[{ required: true, message: '请输入功能名称' }]}
              >
                <Input placeholder="输入功能名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="function_type"
                label="功能类型"
                rules={[{ required: true, message: '请选择功能类型' }]}
              >
                <Select placeholder="选择功能类型">
                  <Option value="technical_indicator">技术指标</Option>
                  <Option value="pattern_recognition">形态识别</Option>
                  <Option value="backtesting_strategy">回测策略</Option>
                  <Option value="screening_filter">选股过滤器</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="功能描述"
          >
            <TextArea rows={2} placeholder="输入功能描述" />
          </Form.Item>

          <Form.Item
            name="code"
            label="功能代码"
            rules={[{ required: true, message: '请输入功能代码' }]}
          >
            <TextArea 
              rows={12} 
              placeholder="输入Python代码"
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="version" label="版本号" initialValue="1.0">
                <Input placeholder="版本号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="tags" label="标签">
                <Input placeholder="用逗号分隔多个标签" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="is_active" valuePropName="checked" initialValue={true}>
            <Switch checkedChildren="激活" unCheckedChildren="停用" />
            <span style={{ marginLeft: '8px' }}>功能状态</span>
          </Form.Item>
        </Form>
      </Modal>

      {/* 执行历史抽屉 */}
      <Drawer
        title={`执行历史 - ${selectedFunction?.function_name}`}
        placement="right"
        onClose={() => setExecutionDrawerVisible(false)}
        open={executionDrawerVisible}
        width={600}
      >
        {selectedFunction && (
          <Timeline>
            {executions
              .filter(e => e.function_id === selectedFunction.function_id)
              .map(execution => (
                <Timeline.Item
                  key={execution.execution_id}
                  color={getStatusColor(execution.status)}
                  dot={getStatusIcon(execution.status)}
                >
                  <div>
                    <Text strong>{execution.status.toUpperCase()}</Text>
                    <br />
                    <Text type="secondary">
                      {dayjs(execution.started_at).format('YYYY-MM-DD HH:mm:ss')}
                    </Text>
                    {execution.execution_time && (
                      <>
                        <br />
                        <Text>执行时间: {execution.execution_time.toFixed(2)}s</Text>
                      </>
                    )}
                    {execution.error_message && (
                      <>
                        <br />
                        <Text type="danger">{execution.error_message}</Text>
                      </>
                    )}
                  </div>
                </Timeline.Item>
              ))}
          </Timeline>
        )}
      </Drawer>
    </div>
  )
}

export default CustomFunctionalityManager
