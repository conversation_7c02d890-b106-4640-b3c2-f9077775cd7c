// 实时数据服务
class RealtimeService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000
  private listeners: Map<string, Function[]> = new Map()
  private heartbeatInterval: NodeJS.Timeout | null = null

  constructor() {
    this.connect()
  }

  private connect() {
    try {
      // 连接到真实的WebSocket服务器
      this.ws = new WebSocket('ws://localhost:8000/ws')

      this.ws.onopen = () => {
        console.log('WebSocket连接已建立')
        this.reconnectAttempts = 0

        // 发送心跳
        this.startHeartbeat()
      }

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('WebSocket连接已关闭')
        this.stopHeartbeat()
        this.handleReconnect()
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
      }

      // 备用：如果WebSocket连接失败，使用模拟连接
      setTimeout(() => {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
          console.warn('WebSocket连接失败，使用模拟数据')
          this.simulateConnection()
        }
      }, 3000)

    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.simulateConnection()
    }
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({
          type: 'ping',
          timestamp: new Date().toISOString()
        }))
      }
    }, 30000) // 每30秒发送一次心跳
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private handleMessage(message: any) {
    const { type, data } = message

    switch (type) {
      case 'connection':
        console.log('WebSocket连接确认:', message)
        break
      case 'stock_price_update':
        this.emit('stock_price_update', message)
        break
      case 'ai_signal':
        this.emit('ai_signal', message)
        break
      case 'alert':
        this.emit('alert', message)
        break
      case 'pong':
        // 心跳响应
        break
      default:
        console.log('收到未知类型消息:', message)
    }
  }

  private simulateConnection() {
    console.log('模拟WebSocket连接已建立')

    // 模拟实时股价数据推送
    setInterval(() => {
      this.simulateStockPriceUpdate()
    }, 2000)

    // 模拟AI预测信号推送
    setInterval(() => {
      this.simulateAISignal()
    }, 10000)

    // 模拟预警通知推送
    setInterval(() => {
      this.simulateAlert()
    }, 15000)
  }

  private simulateStockPriceUpdate() {
    const stocks = ['000001', '000002', '600000', '600036', '000858']
    const randomStock = stocks[Math.floor(Math.random() * stocks.length)]
    
    const priceChange = (Math.random() - 0.5) * 2 // -1 到 1 之间的随机变化
    const changePercent = (Math.random() - 0.5) * 4 // -2% 到 2% 之间的随机变化
    
    const data = {
      type: 'stock_price_update',
      data: {
        stock_code: randomStock,
        price_change: Number(priceChange.toFixed(2)),
        change_percent: Number(changePercent.toFixed(2)),
        timestamp: new Date().toISOString(),
        volume: Math.floor(Math.random() * 100000000)
      }
    }

    this.emit('stock_price_update', data)
  }

  private simulateAISignal() {
    const stocks = ['000001', '000002', '600000', '600036', '000858']
    const signals = ['buy', 'sell', 'hold']
    const randomStock = stocks[Math.floor(Math.random() * stocks.length)]
    const randomSignal = signals[Math.floor(Math.random() * signals.length)]
    
    const data = {
      type: 'ai_signal',
      data: {
        stock_code: randomStock,
        signal_type: randomSignal,
        confidence: Math.floor(Math.random() * 40) + 60, // 60-100之间
        strength: Math.floor(Math.random() * 40) + 60,
        timestamp: new Date().toISOString(),
        factors: ['技术突破', '基本面改善', '资金流入'].slice(0, Math.floor(Math.random() * 3) + 1)
      }
    }

    this.emit('ai_signal', data)
  }

  private simulateAlert() {
    const stocks = ['000001', '000002', '600000', '600036', '000858']
    const alertTypes = ['price_above', 'price_below', 'rsi_overbought', 'rsi_oversold']
    const randomStock = stocks[Math.floor(Math.random() * stocks.length)]
    const randomType = alertTypes[Math.floor(Math.random() * alertTypes.length)]
    
    const data = {
      type: 'alert',
      data: {
        stock_code: randomStock,
        alert_type: randomType,
        message: this.generateAlertMessage(randomStock, randomType),
        timestamp: new Date().toISOString(),
        trigger_value: Math.random() * 100,
        threshold_value: Math.random() * 100
      }
    }

    this.emit('alert', data)
  }

  private generateAlertMessage(stockCode: string, alertType: string): string {
    const stockNames: { [key: string]: string } = {
      '000001': '平安银行',
      '000002': '万科A',
      '600000': '浦发银行',
      '600036': '招商银行',
      '000858': '五粮液'
    }

    const stockName = stockNames[stockCode] || stockCode
    
    switch (alertType) {
      case 'price_above':
        return `${stockName}股价突破上限，当前价格 ${(Math.random() * 50 + 10).toFixed(2)} 元`
      case 'price_below':
        return `${stockName}股价跌破下限，当前价格 ${(Math.random() * 50 + 10).toFixed(2)} 元`
      case 'rsi_overbought':
        return `${stockName}RSI指标超买，当前值 ${(Math.random() * 20 + 70).toFixed(1)}`
      case 'rsi_oversold':
        return `${stockName}RSI指标超卖，当前值 ${(Math.random() * 20 + 10).toFixed(1)}`
      default:
        return `${stockName}触发预警`
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval)
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数')
    }
  }

  // 订阅事件
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  // 取消订阅
  off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(callback)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  // 触发事件
  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data))
    }
  }

  // 发送消息
  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.log('WebSocket未连接，无法发送消息:', message)
    }
  }

  // 订阅股票
  subscribeStocks(stockCodes: string[]) {
    this.send({
      type: 'subscribe',
      stock_codes: stockCodes
    })
  }

  // 取消订阅股票
  unsubscribeStocks(stockCodes: string[]) {
    this.send({
      type: 'unsubscribe',
      stock_codes: stockCodes
    })
  }

  // 断开连接
  disconnect() {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.listeners.clear()
  }

  // 获取连接状态
  isConnected(): boolean {
    return this.ws ? this.ws.readyState === WebSocket.OPEN : false
  }
}

// 创建单例实例
export const realtimeService = new RealtimeService()

// 导出类型定义
export interface StockPriceUpdate {
  stock_code: string
  price_change: number
  change_percent: number
  timestamp: string
  volume: number
}

export interface AISignal {
  stock_code: string
  signal_type: 'buy' | 'sell' | 'hold'
  confidence: number
  strength: number
  timestamp: string
  factors: string[]
}

export interface AlertNotification {
  stock_code: string
  alert_type: string
  message: string
  timestamp: string
  trigger_value: number
  threshold_value: number
}

export default realtimeService
