"""
数据获取服务模块
"""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, date, timedelta
import pandas as pd
import akshare as ak
import tushare as ts
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import time
import random
from decimal import Decimal

from app.core.config import settings
from app.core.logging import get_logger
from app.models.stock import Stock, KlineData, RealtimeData
from app.core.database import AsyncSessionLocal
from app.services.data_validator import DataValidator, DataCleaner

logger = get_logger(__name__)


class DataFetchError(Exception):
    """数据获取异常"""
    pass


class RateLimitError(DataFetchError):
    """频率限制异常"""
    pass


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        wait_time = delay * (2 ** attempt) + random.uniform(0, 1)
                        logger.warning(f"第{attempt + 1}次尝试失败: {e}, {wait_time:.2f}秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"所有重试都失败了: {e}")
            raise last_exception
        return wrapper
    return decorator


class DataFetcher:
    """数据获取器基类"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()


class AkshareDataFetcher(DataFetcher):
    """Akshare数据获取器"""

    def __init__(self):
        super().__init__()
        self.rate_limit_delay = 0.1  # 请求间隔

    async def _rate_limit(self):
        """频率限制"""
        await asyncio.sleep(self.rate_limit_delay)

    @retry_on_failure(max_retries=3, delay=1.0)
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            logger.info("开始获取股票列表...")

            # 获取A股股票列表
            await self._rate_limit()
            df_sh = ak.stock_info_a_code_name()

            if df_sh.empty:
                raise DataFetchError("获取到的股票列表为空")

            stocks = []
            for _, row in df_sh.iterrows():
                stock_data = {
                    'stock_code': row['code'],
                    'stock_name': row['name'],
                    'market': 'SH' if row['code'].startswith('6') else 'SZ'
                }
                stocks.append(stock_data)
            
            logger.info(f"获取到 {len(stocks)} 只股票")
            return stocks
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    async def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取股票基本信息"""
        try:
            logger.info(f"获取股票 {stock_code} 基本信息...")
            
            # 获取股票基本信息
            df = ak.stock_individual_info_em(symbol=stock_code)
            
            if df.empty:
                return None
                
            info = {}
            for _, row in df.iterrows():
                info[row['item']] = row['value']
            
            return {
                'stock_code': stock_code,
                'stock_name': info.get('股票简称', ''),
                'industry': info.get('所处行业', ''),
                'list_date': info.get('上市时间', ''),
                'total_share': info.get('总股本', ''),
                'market_cap': info.get('总市值', '')
            }
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 信息失败: {e}")
            return None
    
    @retry_on_failure(max_retries=3, delay=1.0)
    async def get_kline_data(
        self,
        stock_code: str,
        period: str = "daily",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """获取K线数据 - 使用AKShare stock_zh_a_hist接口"""
        try:
            logger.info(f"开始获取股票 {stock_code} K线数据, 周期: {period}")

            # 设置默认日期范围
            if not end_date:
                end_date = date.today()
            if not start_date:
                # 根据周期调整默认时间范围
                if period == "monthly":
                    start_date = end_date - timedelta(days=365*3)  # 月线获取3年数据
                elif period == "weekly":
                    start_date = end_date - timedelta(days=365*2)  # 周线获取2年数据
                else:
                    start_date = end_date - timedelta(days=365)    # 日线获取1年数据

            # 根据周期选择对应的akshare参数
            period_map = {
                "daily": "daily",
                "weekly": "weekly",
                "monthly": "monthly"
            }

            if period not in period_map:
                raise DataFetchError(f"不支持的周期类型: {period}")

            # 频率限制
            await self._rate_limit()

            # 在异步环境中运行同步的akshare函数
            loop = asyncio.get_event_loop()

            try:
                logger.info(f"调用AKShare API: stock_zh_a_hist(symbol={stock_code}, period={period_map[period]})")
                df = await asyncio.wait_for(
                    loop.run_in_executor(
                        None,
                        lambda: ak.stock_zh_a_hist(
                            symbol=stock_code,
                            period=period_map[period],
                            start_date=start_date.strftime("%Y%m%d"),
                            end_date=end_date.strftime("%Y%m%d"),
                            adjust="qfq"  # 前复权
                        )
                    ),
                    timeout=30.0  # 30秒超时
                )
                logger.info(f"AKShare API调用成功，获取到数据形状: {df.shape if df is not None else 'None'}")
            except asyncio.TimeoutError:
                logger.error(f"AKShare API调用超时（30秒），股票: {stock_code}")
                return []
            except Exception as e:
                logger.error(f"AKShare API调用失败: {e}")
                return []

            if df is None or df.empty:
                logger.warning(f"股票 {stock_code} 周期 {period} 没有获取到数据")
                return []

            kline_data = []
            for _, row in df.iterrows():
                try:
                    # 安全地获取数据
                    def safe_float(value, default=0.0):
                        try:
                            return float(value) if value is not None else default
                        except (ValueError, TypeError):
                            return default

                    def safe_int(value, default=0):
                        try:
                            return int(float(value)) if value is not None else default
                        except (ValueError, TypeError):
                            return default

                    data = {
                        'stock_code': stock_code,
                        'trade_date': pd.to_datetime(row['日期']).date(),
                        'period': period,
                        'open_price': safe_float(row['开盘']),
                        'high_price': safe_float(row['最高']),
                        'low_price': safe_float(row['最低']),
                        'close_price': safe_float(row['收盘']),
                        'volume': safe_int(row['成交量']),
                        'turnover': safe_float(row['成交额']),
                        'change': safe_float(row.get('涨跌额', 0)),
                        'change_percent': safe_float(row.get('涨跌幅', 0))
                    }
                    kline_data.append(data)
                except Exception as e:
                    logger.warning(f"解析K线数据行失败: {e}, 行数据: {row.to_dict()}")
                    continue

            logger.info(f"成功获取并解析 {len(kline_data)} 条K线数据")
            return kline_data
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} K线数据失败: {e}")
            return []
    
    async def get_realtime_data(self, stock_codes: List[str]) -> List[Dict[str, Any]]:
        """获取实时行情数据"""
        try:
            logger.info(f"获取 {len(stock_codes)} 只股票实时数据...")
            
            realtime_data = []
            
            for stock_code in stock_codes:
                try:
                    # 获取实时行情
                    df = ak.stock_zh_a_spot_em()
                    stock_data = df[df['代码'] == stock_code]
                    
                    if not stock_data.empty:
                        row = stock_data.iloc[0]
                        data = {
                            'stock_code': stock_code,
                            'current_price': float(row['最新价']),
                            'change': float(row['涨跌额']),
                            'change_percent': float(row['涨跌幅']),
                            'volume': int(row['成交量']),
                            'turnover': float(row['成交额']),
                            'timestamp': datetime.now()
                        }
                        realtime_data.append(data)
                        
                except Exception as e:
                    logger.warning(f"获取股票 {stock_code} 实时数据失败: {e}")
                    continue
            
            logger.info(f"获取到 {len(realtime_data)} 条实时数据")
            return realtime_data
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return []


class TushareDataFetcher(DataFetcher):
    """Tushare数据获取器（备用）"""
    
    def __init__(self):
        super().__init__()
        if settings.TUSHARE_TOKEN:
            ts.set_token(settings.TUSHARE_TOKEN)
            self.pro = ts.pro_api()
        else:
            self.pro = None
            logger.warning("Tushare token未配置，将无法使用Tushare数据源")
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        if not self.pro:
            return []
            
        try:
            logger.info("使用Tushare获取股票列表...")
            
            # 获取股票基本信息
            df = self.pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
            
            stocks = []
            for _, row in df.iterrows():
                stock_data = {
                    'stock_code': row['symbol'],
                    'stock_name': row['name'],
                    'market': 'SH' if row['ts_code'].endswith('.SH') else 'SZ',
                    'industry': row['industry'],
                    'list_date': pd.to_datetime(row['list_date']).date() if row['list_date'] else None
                }
                stocks.append(stock_data)
            
            logger.info(f"Tushare获取到 {len(stocks)} 只股票")
            return stocks
            
        except Exception as e:
            logger.error(f"Tushare获取股票列表失败: {e}")
            return []


class DataFetcherManager:
    """数据获取管理器"""

    def __init__(self):
        self.akshare_fetcher = AkshareDataFetcher()
        self.tushare_fetcher = TushareDataFetcher()
        self.primary_source = "akshare"  # 主要数据源
        self.validator = DataValidator()
        self.cleaner = DataCleaner()
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表（优先使用主要数据源）"""
        try:
            if self.primary_source == "akshare":
                async with self.akshare_fetcher as fetcher:
                    return await fetcher.get_stock_list()
            else:
                async with self.tushare_fetcher as fetcher:
                    return await fetcher.get_stock_list()
        except Exception as e:
            logger.error(f"主要数据源获取失败，尝试备用数据源: {e}")
            # 尝试备用数据源
            try:
                if self.primary_source == "akshare":
                    async with self.tushare_fetcher as fetcher:
                        return await fetcher.get_stock_list()
                else:
                    async with self.akshare_fetcher as fetcher:
                        return await fetcher.get_stock_list()
            except Exception as e2:
                logger.error(f"备用数据源也获取失败: {e2}")
                return []
    
    async def get_kline_data(
        self, 
        stock_code: str, 
        period: str = "daily",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """获取K线数据"""
        async with self.akshare_fetcher as fetcher:
            return await fetcher.get_kline_data(stock_code, period, start_date, end_date)
    
    async def get_realtime_data(self, stock_codes: List[str]) -> List[Dict[str, Any]]:
        """获取实时数据"""
        async with self.akshare_fetcher as fetcher:
            return await fetcher.get_realtime_data(stock_codes)

    async def get_cleaned_stock_list(self) -> List[Dict[str, Any]]:
        """获取清洗后的股票列表"""
        try:
            # 获取原始数据
            raw_data = await self.get_stock_list()

            if not raw_data:
                return []

            # 清洗和验证数据
            cleaned_data = []
            invalid_count = 0

            for stock_data in raw_data:
                try:
                    # 清洗数据
                    cleaned_stock = self.cleaner.clean_stock_data(stock_data)

                    # 验证数据
                    is_valid, errors = self.validator.validate_stock_data(cleaned_stock)

                    if is_valid:
                        cleaned_data.append(cleaned_stock)
                    else:
                        invalid_count += 1
                        logger.warning(f"股票数据验证失败 {cleaned_stock.get('stock_code')}: {errors}")

                except Exception as e:
                    invalid_count += 1
                    logger.error(f"处理股票数据时出错: {e}")
                    continue

            # 去重
            cleaned_data = self.cleaner.remove_duplicates(cleaned_data, ['stock_code'])

            logger.info(f"股票数据清洗完成: 原始{len(raw_data)}条, 有效{len(cleaned_data)}条, 无效{invalid_count}条")
            return cleaned_data

        except Exception as e:
            logger.error(f"获取清洗后的股票列表失败: {e}")
            return []

    async def get_cleaned_kline_data(
        self,
        stock_code: str,
        period: str = "daily",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """获取清洗后的K线数据"""
        try:
            # 获取原始数据
            raw_data = await self.get_kline_data(stock_code, period, start_date, end_date)

            if not raw_data:
                return []

            # 清洗和验证数据
            cleaned_data = []
            invalid_count = 0

            for kline_data in raw_data:
                try:
                    # 清洗数据
                    cleaned_kline = self.cleaner.clean_kline_data(kline_data)

                    # 验证数据
                    is_valid, errors = self.validator.validate_kline_data(cleaned_kline)

                    if is_valid:
                        cleaned_data.append(cleaned_kline)
                    else:
                        invalid_count += 1
                        logger.warning(f"K线数据验证失败 {stock_code} {cleaned_kline.get('trade_date')}: {errors}")

                except Exception as e:
                    invalid_count += 1
                    logger.error(f"处理K线数据时出错: {e}")
                    continue

            # 去重
            cleaned_data = self.cleaner.remove_duplicates(cleaned_data, ['stock_code', 'trade_date', 'period'])

            # 检测异常值
            outlier_indices = self.cleaner.detect_outliers_kline(cleaned_data)
            if outlier_indices:
                logger.warning(f"检测到 {len(outlier_indices)} 个异常值: {outlier_indices}")

            logger.info(f"K线数据清洗完成 {stock_code}: 原始{len(raw_data)}条, 有效{len(cleaned_data)}条, 无效{invalid_count}条")
            return cleaned_data

        except Exception as e:
            logger.error(f"获取清洗后的K线数据失败: {e}")
            return []

    async def get_cleaned_realtime_data(self, stock_codes: List[str]) -> List[Dict[str, Any]]:
        """获取清洗后的实时数据"""
        try:
            # 获取原始数据
            raw_data = await self.get_realtime_data(stock_codes)

            if not raw_data:
                return []

            # 清洗和验证数据
            cleaned_data = []
            invalid_count = 0

            for realtime_data in raw_data:
                try:
                    # 清洗数据
                    cleaned_realtime = self.cleaner.clean_realtime_data(realtime_data)

                    # 验证数据
                    is_valid, errors = self.validator.validate_realtime_data(cleaned_realtime)

                    if is_valid:
                        cleaned_data.append(cleaned_realtime)
                    else:
                        invalid_count += 1
                        logger.warning(f"实时数据验证失败 {cleaned_realtime.get('stock_code')}: {errors}")

                except Exception as e:
                    invalid_count += 1
                    logger.error(f"处理实时数据时出错: {e}")
                    continue

            logger.info(f"实时数据清洗完成: 原始{len(raw_data)}条, 有效{len(cleaned_data)}条, 无效{invalid_count}条")
            return cleaned_data

        except Exception as e:
            logger.error(f"获取清洗后的实时数据失败: {e}")
            return []
