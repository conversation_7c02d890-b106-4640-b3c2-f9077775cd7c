#!/usr/bin/env python3
"""
检查数据库中的所有数据
"""

import sqlite3
from datetime import datetime

def check_all_data():
    """检查数据库中的所有数据"""
    try:
        print("🔍 检查数据库中的所有数据...")
        
        # 连接数据库
        conn = sqlite3.connect('data/stock_analyzer.db')
        cursor = conn.cursor()
        
        # 检查数据总数
        cursor.execute("SELECT COUNT(*) FROM stock_spot_data")
        total_count = cursor.fetchone()[0]
        print(f"📊 表中共有 {total_count} 条记录")
        
        # 查看所有数据，按更新时间排序
        cursor.execute("""
            SELECT stock_code, stock_name, current_price, change_percent, update_time 
            FROM stock_spot_data 
            ORDER BY update_time DESC
        """)
        
        records = cursor.fetchall()
        print(f"📈 所有 {len(records)} 条数据 (按更新时间倒序):")
        
        for i, record in enumerate(records):
            stock_code, stock_name, current_price, change_percent, update_time = record
            print(f"  {i+1}. {stock_code} {stock_name}: ¥{current_price} ({change_percent:+.2f}%) - {update_time}")
        
        # 分析数据类型
        real_data_count = 0
        mock_data_count = 0
        
        for record in records:
            current_price = float(record[2])
            if current_price == 10.5:
                mock_data_count += 1
            else:
                real_data_count += 1
        
        print(f"\n📊 数据分析:")
        print(f"  真实数据: {real_data_count} 条")
        print(f"  模拟数据: {mock_data_count} 条")
        
        if mock_data_count > 0:
            print("⚠️  数据库中存在模拟数据，这可能是API返回模拟数据的原因")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    print("=" * 80)
    print("🗄️  数据库完整数据检查工具")
    print("=" * 80)
    
    check_all_data()
    
    print("=" * 80)
