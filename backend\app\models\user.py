"""
用户相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, DateTime, Boolean, Integer, Text, Date, Numeric, JSON, Index
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class User(Base):
    """用户表"""
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, comment="用户名")
    email: Mapped[str] = mapped_column(String(100), unique=True, index=True, comment="邮箱")
    hashed_password: Mapped[str] = mapped_column(String(255), comment="密码哈希")

    # 用户信息
    full_name: Mapped[Optional[str]] = mapped_column(String(100), comment="真实姓名")
    phone: Mapped[Optional[str]] = mapped_column(String(20), comment="手机号")
    avatar: Mapped[Optional[str]] = mapped_column(String(500), comment="头像URL")
    bio: Mapped[Optional[str]] = mapped_column(Text, comment="个人简介")

    # 身份信息
    id_card: Mapped[Optional[str]] = mapped_column(String(18), comment="身份证号")
    birth_date: Mapped[Optional[date]] = mapped_column(Date, comment="出生日期")
    gender: Mapped[Optional[str]] = mapped_column(String(10), comment="性别: male/female/other")
    location: Mapped[Optional[str]] = mapped_column(String(100), comment="所在地")

    # 投资偏好
    risk_tolerance: Mapped[Optional[str]] = mapped_column(String(20), comment="风险承受能力: conservative/moderate/aggressive")
    investment_experience: Mapped[Optional[str]] = mapped_column(String(20), comment="投资经验: beginner/intermediate/advanced")
    preferred_sectors: Mapped[Optional[dict]] = mapped_column(JSON, comment="偏好行业")
    investment_goals: Mapped[Optional[dict]] = mapped_column(JSON, comment="投资目标")

    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否超级用户")
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否已验证")
    email_verified: Mapped[bool] = mapped_column(Boolean, default=False, comment="邮箱是否已验证")
    phone_verified: Mapped[bool] = mapped_column(Boolean, default=False, comment="手机是否已验证")
    is_premium: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否高级用户")

    # 安全设置
    two_factor_enabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否启用双因子认证")
    login_attempts: Mapped[int] = mapped_column(Integer, default=0, comment="登录尝试次数")
    locked_until: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="锁定到期时间")

    # API访问
    api_key: Mapped[Optional[str]] = mapped_column(String(64), comment="API密钥")
    api_calls_count: Mapped[int] = mapped_column(Integer, default=0, comment="API调用次数")
    api_calls_limit: Mapped[int] = mapped_column(Integer, default=1000, comment="API调用限制")

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后登录时间")
    last_active_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后活跃时间")

    __table_args__ = (
        Index('ix_user_email_verified', 'email', 'email_verified'),
        Index('ix_user_phone_verified', 'phone', 'phone_verified'),
        Index('ix_user_active_verified', 'is_active', 'is_verified'),
    )


class UserProfile(Base):
    """用户详细资料表"""
    __tablename__ = "user_profiles"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")

    # 详细信息
    nickname: Mapped[Optional[str]] = mapped_column(String(50), comment="昵称")
    signature: Mapped[Optional[str]] = mapped_column(String(200), comment="个性签名")
    website: Mapped[Optional[str]] = mapped_column(String(200), comment="个人网站")
    company: Mapped[Optional[str]] = mapped_column(String(100), comment="公司")
    position: Mapped[Optional[str]] = mapped_column(String(100), comment="职位")

    # 社交媒体
    wechat: Mapped[Optional[str]] = mapped_column(String(50), comment="微信号")
    qq: Mapped[Optional[str]] = mapped_column(String(20), comment="QQ号")
    weibo: Mapped[Optional[str]] = mapped_column(String(100), comment="微博")

    # 投资相关
    total_assets: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="总资产")
    available_funds: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="可用资金")
    portfolio_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="持仓市值")

    # 偏好设置
    theme: Mapped[str] = mapped_column(String(20), default="light", comment="主题: light/dark")
    language: Mapped[str] = mapped_column(String(10), default="zh-CN", comment="语言")
    timezone: Mapped[str] = mapped_column(String(50), default="Asia/Shanghai", comment="时区")
    currency: Mapped[str] = mapped_column(String(10), default="CNY", comment="货币")

    # 通知设置
    notification_settings: Mapped[Optional[dict]] = mapped_column(JSON, comment="通知设置")
    privacy_settings: Mapped[Optional[dict]] = mapped_column(JSON, comment="隐私设置")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class UserSession(Base):
    """用户会话表"""
    __tablename__ = "user_sessions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")

    # 会话信息
    session_token: Mapped[str] = mapped_column(String(255), unique=True, index=True, comment="会话令牌")
    refresh_token: Mapped[str] = mapped_column(String(255), unique=True, index=True, comment="刷新令牌")
    device_info: Mapped[Optional[str]] = mapped_column(Text, comment="设备信息")
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), comment="IP地址")
    user_agent: Mapped[Optional[str]] = mapped_column(Text, comment="用户代理")
    location: Mapped[Optional[str]] = mapped_column(String(100), comment="登录地点")

    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否活跃")

    # 时间
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    expires_at: Mapped[datetime] = mapped_column(DateTime, comment="过期时间")
    last_used_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="最后使用时间")

    __table_args__ = (
        Index('ix_session_user_active', 'user_id', 'is_active'),
        Index('ix_session_expires', 'expires_at'),
    )


class UserLoginLog(Base):
    """用户登录日志表"""
    __tablename__ = "user_login_logs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, index=True, comment="用户ID")

    # 登录信息
    username: Mapped[Optional[str]] = mapped_column(String(50), comment="用户名")
    email: Mapped[Optional[str]] = mapped_column(String(100), comment="邮箱")
    login_type: Mapped[str] = mapped_column(String(20), comment="登录类型: password/sms/oauth")

    # 结果
    success: Mapped[bool] = mapped_column(Boolean, comment="是否成功")
    failure_reason: Mapped[Optional[str]] = mapped_column(String(100), comment="失败原因")

    # 环境信息
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), comment="IP地址")
    user_agent: Mapped[Optional[str]] = mapped_column(Text, comment="用户代理")
    device_info: Mapped[Optional[str]] = mapped_column(Text, comment="设备信息")
    location: Mapped[Optional[str]] = mapped_column(String(100), comment="地理位置")

    # 安全信息
    risk_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2), comment="风险评分")
    is_suspicious: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否可疑")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")

    __table_args__ = (
        Index('ix_login_log_user_time', 'user_id', 'created_at'),
        Index('ix_login_log_ip_time', 'ip_address', 'created_at'),
        Index('ix_login_log_success', 'success'),
    )


class UserWatchlist(Base):
    """用户自选股表"""
    __tablename__ = "user_watchlists"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    watchlist_name: Mapped[Optional[str]] = mapped_column(String(50), comment="自选股组名")
    notes: Mapped[Optional[str]] = mapped_column(Text, comment="备注")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_watchlist_user_stock', 'user_id', 'stock_code'),
    )


class UserAlert(Base):
    """用户预警设置表"""
    __tablename__ = "user_alerts"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    alert_type: Mapped[str] = mapped_column(String(20), comment="预警类型")
    alert_name: Mapped[str] = mapped_column(String(100), comment="预警名称")
    
    # 预警条件
    condition_type: Mapped[str] = mapped_column(String(20), comment="条件类型")
    condition_value: Mapped[str] = mapped_column(String(100), comment="条件值")
    condition_operator: Mapped[str] = mapped_column(String(10), comment="条件操作符")
    
    # 预警设置
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    notification_methods: Mapped[str] = mapped_column(String(100), comment="通知方式")
    trigger_count: Mapped[int] = mapped_column(Integer, default=0, comment="触发次数")
    last_triggered: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后触发时间")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_alert_user_stock', 'user_id', 'stock_code'),
        Index('ix_alert_type_active', 'alert_type', 'is_active'),
    )
