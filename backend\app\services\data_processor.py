"""
数据处理服务 - 实现多接口数据获取、处理和本地存储
数据流程: 多接口获取数据 → 数据处理 → 本地存储 → 前后端调用
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from app.models.stock import Stock, KlineData, FinancialNews, TechnicalIndicator
from app.services.data_sources import AKShareService, TushareService, SinaService
# from app.utils.technical_analysis import TechnicalAnalyzer

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器 - 协调多个数据源的数据获取和处理"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.akshare = AKShareService()
        self.tushare = TushareService()
        self.sina = SinaService()
        # self.technical_analyzer = TechnicalAnalyzer()
    
    async def update_stock_basic_info(self, task):
        """更新股票基础信息"""
        try:
            logger.info("开始更新股票基础信息")
            task.progress = 10
            
            # 从多个数据源获取股票列表
            stock_lists = await asyncio.gather(
                self.akshare.get_stock_list(),
                self.tushare.get_stock_list(),
                return_exceptions=True
            )
            
            # 合并和去重股票列表
            all_stocks = []
            for stock_list in stock_lists:
                if isinstance(stock_list, list):
                    all_stocks.extend(stock_list)
            
            # 去重处理
            unique_stocks = {}
            for stock in all_stocks:
                symbol = stock.get('symbol') or stock.get('ts_code')
                if symbol and symbol not in unique_stocks:
                    unique_stocks[symbol] = stock
            
            task.total_records = len(unique_stocks)
            task.progress = 30
            
            # 批量更新数据库
            processed_count = 0
            batch_size = 100
            
            for i in range(0, len(unique_stocks), batch_size):
                batch = list(unique_stocks.values())[i:i + batch_size]
                
                for stock_data in batch:
                    await self._update_single_stock(stock_data)
                    processed_count += 1
                    
                    # 更新进度
                    task.records_processed = processed_count
                    task.progress = 30 + int((processed_count / task.total_records) * 60)
                
                # 提交批次
                await self.db.commit()

                # 避免过于频繁的请求
                await asyncio.sleep(0.1)
            
            task.progress = 100
            logger.info(f"股票基础信息更新完成，处理 {processed_count} 只股票")
            
        except Exception as e:
            logger.error(f"更新股票基础信息失败: {e}")
            task.status = "failed"
            task.error_message = str(e)
            raise
    
    async def update_stock_prices(self, task):
        """更新股票价格数据"""
        try:
            logger.info("开始更新股票价格数据")
            task.progress = 10

            # 获取所有股票代码
            result = await self.db.execute(select(Stock))
            stocks = result.scalars().all()
            task.total_records = len(stocks)

            processed_count = 0
            batch_size = 50  # 价格数据请求频率较高，减小批次
            
            for i in range(0, len(stocks), batch_size):
                batch = stocks[i:i + batch_size]
                
                # 并发获取价格数据
                price_tasks = []
                for stock in batch:
                    price_tasks.append(self._get_stock_price_from_sources(stock.symbol))
                
                price_results = await asyncio.gather(*price_tasks, return_exceptions=True)
                
                # 处理结果
                for j, price_data in enumerate(price_results):
                    if isinstance(price_data, dict) and price_data:
                        await self._save_stock_price(batch[j].symbol, price_data)
                    
                    processed_count += 1
                    task.records_processed = processed_count
                    task.progress = 10 + int((processed_count / task.total_records) * 80)
                
                # 提交批次
                await self.db.commit()

                # 控制请求频率
                await asyncio.sleep(1)
            
            task.progress = 100
            logger.info(f"股票价格数据更新完成，处理 {processed_count} 只股票")
            
        except Exception as e:
            logger.error(f"更新股票价格数据失败: {e}")
            task.status = "failed"
            task.error_message = str(e)
            raise
    
    async def update_financial_news(self, task):
        """更新财经新闻"""
        try:
            logger.info("开始更新财经新闻")
            task.progress = 10
            
            # 从多个数据源获取新闻
            news_sources = await asyncio.gather(
                self.akshare.get_financial_news(),
                self.sina.get_financial_news(),
                return_exceptions=True
            )
            
            all_news = []
            for news_list in news_sources:
                if isinstance(news_list, list):
                    all_news.extend(news_list)
            
            task.total_records = len(all_news)
            task.progress = 30
            
            # 去重和保存新闻
            processed_count = 0
            for news_item in all_news:
                if await self._save_financial_news(news_item):
                    processed_count += 1
                
                task.records_processed = processed_count
                task.progress = 30 + int((processed_count / task.total_records) * 60)
            
            await self.db.commit()
            task.progress = 100
            logger.info(f"财经新闻更新完成，处理 {processed_count} 条新闻")
            
        except Exception as e:
            logger.error(f"更新财经新闻失败: {e}")
            task.status = "failed"
            task.error_message = str(e)
            raise
    
    async def calculate_technical_indicators(self, task):
        """计算技术指标"""
        try:
            logger.info("开始计算技术指标")
            task.progress = 10
            
            # 获取需要计算指标的股票
            result = await self.db.execute(select(Stock))
            stocks = result.scalars().all()
            task.total_records = len(stocks)

            processed_count = 0

            for stock in stocks:
                # 获取股票的历史价格数据
                stmt = select(KlineData).where(
                    and_(
                        KlineData.stock_code == stock.stock_code,
                        KlineData.period == 'daily'
                    )
                ).order_by(desc(KlineData.trade_date)).limit(200)
                result = await self.db.execute(stmt)
                price_data = result.scalars().all()
                
                if len(price_data) >= 20:  # 至少需要20个数据点
                    # 计算技术指标 (暂时跳过，需要实现技术分析器)
                    # indicators = self.technical_analyzer.calculate_all_indicators(price_data)

                    # 保存指标数据
                    # await self._save_technical_indicators(stock.stock_code, indicators)
                    pass
                
                processed_count += 1
                task.records_processed = processed_count
                task.progress = 10 + int((processed_count / task.total_records) * 80)
                
                # 每处理100只股票提交一次
                if processed_count % 100 == 0:
                    await self.db.commit()

            await self.db.commit()
            task.progress = 100
            logger.info(f"技术指标计算完成，处理 {processed_count} 只股票")
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            task.status = "failed"
            task.error_message = str(e)
            raise
    
    async def _update_single_stock(self, stock_data: Dict[str, Any]):
        """更新单只股票信息"""
        try:
            symbol = stock_data.get('symbol') or stock_data.get('ts_code')
            if not symbol:
                return

            # 查找或创建股票记录
            stmt = select(Stock).where(Stock.stock_code == symbol)
            result = await self.db.execute(stmt)
            stock = result.scalar_one_or_none()
            if not stock:
                stock = Stock(stock_code=symbol)
                self.db.add(stock)

            # 更新股票信息
            stock.stock_name = stock_data.get('name', stock.stock_name)
            stock.industry = stock_data.get('industry', stock.industry)
            stock.market = stock_data.get('market', stock.market)
            stock.list_date = stock_data.get('list_date', stock.list_date)
            stock.updated_at = datetime.now()

        except Exception as e:
            logger.error(f"更新股票 {symbol} 失败: {e}")
    
    async def _get_stock_price_from_sources(self, symbol: str) -> Optional[Dict[str, Any]]:
        """从多个数据源获取股票价格"""
        try:
            # 优先级顺序：新浪 > AKShare > Tushare
            sources = [
                ('sina', self.sina.get_stock_price),
                ('akshare', self.akshare.get_stock_price),
                ('tushare', self.tushare.get_stock_price)
            ]
            
            for source_name, get_price_func in sources:
                try:
                    price_data = await get_price_func(symbol)
                    if price_data:
                        price_data['source'] = source_name
                        return price_data
                except Exception as e:
                    logger.warning(f"从 {source_name} 获取 {symbol} 价格失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 价格失败: {e}")
            return None
    
    async def _save_stock_price(self, symbol: str, price_data: Dict[str, Any]):
        """保存股票价格数据"""
        try:
            # 检查是否已存在今日数据
            today = datetime.now().date()
            stmt = select(KlineData).where(
                and_(
                    KlineData.stock_code == symbol,
                    KlineData.trade_date == today,
                    KlineData.period == 'daily'
                )
            )
            result = await self.db.execute(stmt)
            existing_price = result.scalar_one_or_none()

            if existing_price:
                # 更新现有数据
                existing_price.close_price = price_data.get('current_price')
                existing_price.high_price = price_data.get('high_price')
                existing_price.low_price = price_data.get('low_price')
                existing_price.volume = price_data.get('volume')
                existing_price.created_at = datetime.now()
            else:
                # 创建新数据
                new_price = KlineData(
                    stock_code=symbol,
                    trade_date=today,
                    period='daily',
                    open_price=price_data.get('open_price'),
                    high_price=price_data.get('high_price'),
                    low_price=price_data.get('low_price'),
                    close_price=price_data.get('current_price'),
                    volume=price_data.get('volume'),
                    turnover=price_data.get('turnover', 0),
                    change=price_data.get('change_amount', 0),
                    change_percent=price_data.get('change_percent', 0)
                )
                self.db.add(new_price)

        except Exception as e:
            logger.error(f"保存股票 {symbol} 价格数据失败: {e}")
    
    async def _save_financial_news(self, news_item: Dict[str, Any]) -> bool:
        """保存财经新闻"""
        try:
            # 检查新闻是否已存在（基于标题和发布时间）
            title = news_item.get('title', '')
            publish_time = news_item.get('publish_time')
            
            if not title:
                return False
            
            stmt = select(FinancialNews).where(FinancialNews.title == title)
            result = await self.db.execute(stmt)
            existing_news = result.scalar_one_or_none()
            
            if existing_news:
                return False  # 已存在，跳过
            
            # 创建新闻记录
            news = FinancialNews(
                title=title,
                content=news_item.get('content', ''),
                source=news_item.get('source', ''),
                url=news_item.get('url', ''),
                publish_time=publish_time or datetime.now(),
                category=news_item.get('category', ''),
                keywords=news_item.get('keywords', [])
            )
            
            self.db.add(news)
            return True
            
        except Exception as e:
            logger.error(f"保存财经新闻失败: {e}")
            return False
    
    async def _save_technical_indicators(self, symbol: str, indicators: Dict[str, Any]):
        """保存技术指标数据"""
        try:
            today = datetime.now().date()

            # 检查是否已存在今日指标
            stmt = select(TechnicalIndicator).where(
                and_(
                    TechnicalIndicator.stock_code == symbol,
                    TechnicalIndicator.trade_date == today
                )
            )
            result = await self.db.execute(stmt)
            existing_indicator = result.scalar_one_or_none()

            if existing_indicator:
                # 更新现有指标
                for key, value in indicators.items():
                    if hasattr(existing_indicator, key):
                        setattr(existing_indicator, key, value)
                existing_indicator.updated_at = datetime.now()
            else:
                # 创建新指标记录
                indicator = TechnicalIndicator(
                    stock_code=symbol,
                    trade_date=today,
                    **indicators
                )
                self.db.add(indicator)

        except Exception as e:
            logger.error(f"保存股票 {symbol} 技术指标失败: {e}")
