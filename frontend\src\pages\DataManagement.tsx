import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  Switch,
  Button,
  Select,
  InputNumber,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  Alert,
  message,
  Tabs,
  Badge,
  Tooltip,
  Modal,
  List,
  Progress,
  Statistic,
  Table,
  Tag,
  Timeline,
  DatePicker,
  Spin,
  Empty,
  TimePicker,
} from 'antd'
import {
  SettingOutlined,
  DatabaseOutlined,
  CloudOutlined,
  SaveOutlined,
  ReloadOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ApiOutlined,
  BarChartOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileTextOutlined,
  LineChartOutlined,
  MonitorOutlined,
  WarningOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { dataManagementService, APIConfig } from '@/services/dataManagementService'
import APIConfigModal from '@/components/APIConfigModal'
import TaskDetailsModal from '@/components/TaskDetailsModal'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { confirm } = Modal
const { RangePicker } = DatePicker

// 数据接口配置
interface DataAPIConfig {
  id: string
  name: string
  type: 'akshare' | 'tushare' | 'sina'
  enabled: boolean
  api_key?: string
  endpoint?: string
  rate_limit: number
  timeout: number
  priority: number
  status: 'connected' | 'disconnected' | 'error'
  lastUpdate?: string
  dataCount?: number
}

// 数据库状态
interface DatabaseStatus {
  total_stocks: number
  today_updates: number
  last_update_time: string
  data_size: string
  news_count: number
  news_updated_today: number
}

// 更新任务
interface UpdateTask {
  id: string
  name: string
  type: 'stock_basic' | 'stock_price' | 'financial_news' | 'technical_indicators'
  status: 'running' | 'completed' | 'failed' | 'pending'
  progress: number
  start_time?: string
  end_time?: string
  records_processed?: number
  total_records?: number
  error_message?: string
}

const DataManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('apis')
  
  // 数据接口配置
  const [apiConfigs, setApiConfigs] = useState<DataAPIConfig[]>([])

  // 数据库状态
  const [dbStatus, setDbStatus] = useState<DatabaseStatus>({
    total_stocks: 0,
    today_updates: 0,
    last_update_time: '加载中...',
    data_size: '计算中...',
    news_count: 0,
    news_updated_today: 0
  })

  // 表统计数据
  const [tableStats, setTableStats] = useState<Array<{name: string, count: number, size: string}>>([])

  // 更新任务
  const [updateTasks, setUpdateTasks] = useState<UpdateTask[]>([])

  // 自动更新设置
  const [autoUpdateSettings, setAutoUpdateSettings] = useState({
    enabled: true,
    stockDataInterval: 5, // 分钟
    newsInterval: 30, // 分钟
    weekendUpdate: false,
    updateTime: '09:00', // 每日更新时间
    retryCount: 3,
    enableNotification: true
  })

  // API配置模态框状态
  const [configModalVisible, setConfigModalVisible] = useState(false)
  const [editingConfig, setEditingConfig] = useState<DataAPIConfig | undefined>(undefined)

  // 任务详情模态框状态
  const [taskDetailsVisible, setTaskDetailsVisible] = useState(false)
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)
  const [configLoading, setConfigLoading] = useState(false)

  // 加载数据
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    try {
      const [configs, dbStats, tasks, autoSettings] = await Promise.all([
        dataManagementService.getAPIConfigs(),
        dataManagementService.getDatabaseStats(),
        dataManagementService.getUpdateTasks(),
        dataManagementService.getAutoUpdateSettings()
      ])

      setApiConfigs(configs.map(config => ({
        ...config,
        status: 'disconnected' as const, // 默认状态，需要测试连接
        lastUpdate: '未更新',
        dataCount: 0
      })))

      setDbStatus({
        total_stocks: dbStats.total_stocks,
        today_updates: dbStats.today_updates,
        last_update_time: dbStats.last_update_time,
        data_size: dbStats.data_size,
        news_count: dbStats.news_count,
        news_updated_today: dbStats.news_updated_today
      })

      // 设置表统计数据
      setTableStats(dbStats.table_stats || [])

      setUpdateTasks(tasks)
      setAutoUpdateSettings(autoSettings)
    } catch (error) {
      message.error('加载数据失败')
      console.error('Load data error:', error)
    } finally {
      setLoading(false)
    }
  }

  // 保存API配置
  const handleSaveAPIConfigs = async () => {
    setLoading(true)
    try {
      const results = await Promise.all(
        apiConfigs.map(config => dataManagementService.updateAPIConfig(config))
      )

      if (results.every(result => result)) {
        message.success('API配置保存成功')
      } else {
        message.error('部分API配置保存失败')
      }
    } catch (error) {
      message.error('保存API配置失败')
      console.error('Save API configs error:', error)
    } finally {
      setLoading(false)
    }
  }

  // 测试API连接
  const handleTestConnection = async (apiId: string) => {
    setLoading(true)
    try {
      const result = await dataManagementService.testAPIConnection(apiId)
      if (result) {
        message.success(`${apiId} 连接测试成功`)
        // 更新状态
        setApiConfigs(prev => prev.map(api =>
          api.id === apiId ? { ...api, status: 'connected' } : api
        ))
      } else {
        message.error(`${apiId} 连接测试失败`)
        setApiConfigs(prev => prev.map(api =>
          api.id === apiId ? { ...api, status: 'error' } : api
        ))
      }
    } catch (error) {
      message.error('连接测试失败')
      console.error('Test connection error:', error)
    } finally {
      setLoading(false)
    }
  }

  // 测试所有连接
  const handleTestAllConnections = async () => {
    setLoading(true)
    try {
      const results = await Promise.all(
        apiConfigs.filter(api => api.enabled).map(async (api) => {
          const result = await dataManagementService.testAPIConnection(api.id)
          return { id: api.id, success: result }
        })
      )

      // 更新状态
      setApiConfigs(prev => prev.map(api => {
        const result = results.find(r => r.id === api.id)
        if (result) {
          return { ...api, status: result.success ? 'connected' : 'error' }
        }
        return api
      }))

      const successCount = results.filter(r => r.success).length
      message.success(`连接测试完成: ${successCount}/${results.length} 个接口连接成功`)
    } catch (error) {
      message.error('批量连接测试失败')
      console.error('Test all connections error:', error)
    } finally {
      setLoading(false)
    }
  }

  // 打开配置模态框
  const handleOpenConfigModal = (config?: DataAPIConfig) => {
    setEditingConfig(config)
    setConfigModalVisible(true)
  }

  // 关闭配置模态框
  const handleCloseConfigModal = () => {
    setConfigModalVisible(false)
    setEditingConfig(undefined)
  }

  // 查看任务详情
  const handleViewTaskDetails = (taskId: string) => {
    setSelectedTaskId(taskId)
    setTaskDetailsVisible(true)
  }

  // 关闭任务详情模态框
  const handleCloseTaskDetails = () => {
    setTaskDetailsVisible(false)
    setSelectedTaskId(null)
  }

  // 保存API配置
  const handleSaveAPIConfig = async (config: APIConfig) => {
    setConfigLoading(true)
    try {
      await dataManagementService.updateAPIConfig(config)

      // 更新本地状态
      setApiConfigs(prev => {
        const index = prev.findIndex(api => api.id === config.id)
        if (index >= 0) {
          // 更新现有配置
          const updated = [...prev]
          updated[index] = {
            ...updated[index],
            ...config,
            status: 'disconnected', // 重置状态，需要重新测试
            lastUpdate: new Date().toLocaleString()
          }
          return updated
        } else {
          // 添加新配置
          return [...prev, {
            ...config,
            status: 'disconnected',
            lastUpdate: new Date().toLocaleString(),
            dataCount: 0
          }]
        }
      })

      message.success(editingConfig ? '配置更新成功' : '配置添加成功')
      handleCloseConfigModal()
    } catch (error) {
      message.error('保存配置失败')
      console.error('Save config error:', error)
    } finally {
      setConfigLoading(false)
    }
  }

  // API配置表格列
  const apiColumns = [
    {
      title: '接口名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DataAPIConfig) => (
        <Space>
          <ApiOutlined />
          <strong>{text}</strong>
          <Badge 
            status={record.status === 'connected' ? 'success' : record.status === 'error' ? 'error' : 'default'} 
            text={record.status === 'connected' ? '已连接' : record.status === 'error' ? '错误' : '未连接'}
          />
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean, record: DataAPIConfig) => (
        <Switch
          checked={enabled}
          onChange={(checked) => {
            setApiConfigs(prev => prev.map(api => 
              api.id === record.id ? { ...api, enabled: checked } : api
            ))
          }}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: number) => <Tag color="blue">P{priority}</Tag>,
    },
    {
      title: '数据量',
      dataIndex: 'dataCount',
      key: 'dataCount',
      render: (count: number) => count?.toLocaleString() || '-',
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DataAPIConfig) => (
        <Space>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleOpenConfigModal(record)}
          >
            配置
          </Button>
          <Button
            size="small"
            icon={<SyncOutlined />}
            loading={loading}
            onClick={() => handleTestConnection(record.id)}
          >
            测试
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DatabaseOutlined /> 数据管理中心
      </Title>
      
      {/* 数据库状态概览 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title="股票总数"
              value={dbStatus.total_stocks}
              prefix={<LineChartOutlined />}
              suffix="只"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="今日更新"
              value={dbStatus.today_updates}
              prefix={<SyncOutlined />}
              suffix="只"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="数据库大小"
              value={dbStatus.data_size}
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="财经新闻"
              value={dbStatus.news_count}
              prefix={<FileTextOutlined />}
              suffix={`条 (今日+${dbStatus.news_updated_today})`}
            />
          </Col>
        </Row>
        <Divider />
        <Text type="secondary">
          最后更新时间: {dbStatus.last_update_time}
        </Text>
      </Card>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'apis',
            label: (
              <span>
                <ApiOutlined />
                接口管理
              </span>
            ),
            children: (
              <Card>
                <div style={{ marginBottom: '16px' }}>
                  <Space>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleOpenConfigModal()}
                    >
                      添加接口
                    </Button>
                    <Button
                      icon={<SaveOutlined />}
                      loading={loading}
                      onClick={handleSaveAPIConfigs}
                    >
                      保存配置
                    </Button>
                    <Button
                      icon={<SyncOutlined />}
                      loading={loading}
                      onClick={handleTestAllConnections}
                    >
                      测试所有连接
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => {
                        confirm({
                          title: '确认重置配置',
                          content: '这将重置所有API配置为默认值，确定继续吗？',
                          onOk() {
                            loadData()
                            message.success('配置已重置')
                          },
                        })
                      }}
                    >
                      重置配置
                    </Button>
                  </Space>
                </div>
                <Table
                  columns={apiColumns}
                  dataSource={apiConfigs}
                  rowKey="id"
                  pagination={false}
                />
              </Card>
            )
          },
          {
            key: 'database',
            label: (
              <span>
                <DatabaseOutlined />
                数据库状态
              </span>
            ),
            children: (
              <div>
                <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                  <Col span={12}>
                    <Card title="数据表统计" size="small">
                      <List
                        size="small"
                        dataSource={tableStats}
                        renderItem={item => (
                          <List.Item>
                            <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
                              <span>{item.name}</span>
                              <span>
                                <Tag color="blue">{item.count.toLocaleString()}</Tag>
                                <Tag color="green">{item.size}</Tag>
                              </span>
                            </div>
                          </List.Item>
                        )}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="今日数据更新" size="small">
                      <Timeline
                        size="small"
                        items={[
                          {
                            color: 'green',
                            children: '09:00 - 股票基础信息更新完成 (4521条)',
                          },
                          {
                            color: 'blue',
                            children: '09:30 - 实时价格数据更新中 (3391/4521)',
                          },
                          {
                            color: 'orange',
                            children: '10:00 - 财经新闻更新待开始',
                          },
                          {
                            color: 'gray',
                            children: '15:30 - 技术指标计算待开始',
                          },
                        ]}
                      />
                    </Card>
                  </Col>
                </Row>

                <Card title="数据更新任务" size="small">
                  <Table
                    size="small"
                    columns={[
                      {
                        title: '任务名称',
                        dataIndex: 'name',
                        key: 'name',
                        render: (text: string, record: UpdateTask) => (
                          <Space>
                            {record.type === 'stock_basic' && <LineChartOutlined />}
                            {record.type === 'stock_price' && <BarChartOutlined />}
                            {record.type === 'financial_news' && <FileTextOutlined />}
                            {record.type === 'technical_indicators' && <MonitorOutlined />}
                            {text}
                          </Space>
                        ),
                      },
                      {
                        title: '状态',
                        dataIndex: 'status',
                        key: 'status',
                        render: (status: string) => {
                          const statusConfig = {
                            running: { color: 'processing', text: '运行中' },
                            completed: { color: 'success', text: '已完成' },
                            failed: { color: 'error', text: '失败' },
                            pending: { color: 'default', text: '等待中' }
                          }
                          const config = statusConfig[status as keyof typeof statusConfig]
                          return <Badge status={config.color as any} text={config.text} />
                        },
                      },
                      {
                        title: '进度',
                        dataIndex: 'progress',
                        key: 'progress',
                        render: (progress: number, record: UpdateTask) => (
                          <div style={{ width: '120px' }}>
                            <Progress
                              percent={progress}
                              size="small"
                              status={record.status === 'failed' ? 'exception' : undefined}
                            />
                            {record.records_processed && record.total_records && (
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {record.records_processed}/{record.total_records}
                              </Text>
                            )}
                          </div>
                        ),
                      },
                      {
                        title: '开始时间',
                        dataIndex: 'start_time',
                        key: 'start_time',
                      },
                      {
                        title: '操作',
                        key: 'action',
                        render: (_, record: UpdateTask) => (
                          <Space>
                            {record.status === 'running' && (
                              <Button size="small" icon={<PauseCircleOutlined />} danger>
                                停止
                              </Button>
                            )}
                            {record.status === 'pending' && (
                              <Button size="small" icon={<PlayCircleOutlined />} type="primary">
                                开始
                              </Button>
                            )}
                            <Button
                              size="small"
                              icon={<EyeOutlined />}
                              onClick={() => handleViewTaskDetails(record.id)}
                            >
                              详情
                            </Button>
                          </Space>
                        ),
                      },
                    ]}
                    dataSource={updateTasks}
                    rowKey="id"
                    pagination={false}
                  />
                </Card>
              </div>
            )
          },
          {
            key: 'automation',
            label: (
              <span>
                <ClockCircleOutlined />
                自动化设置
              </span>
            ),
            children: (
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Card title="自动更新配置" size="small">
                    <Form layout="vertical">
                      <Form.Item label="启用自动更新">
                        <Switch
                          checked={autoUpdateSettings.enabled}
                          onChange={(checked) =>
                            setAutoUpdateSettings(prev => ({ ...prev, enabled: checked }))
                          }
                          checkedChildren="启用"
                          unCheckedChildren="禁用"
                        />
                      </Form.Item>

                      <Form.Item label="股票数据更新间隔 (分钟)">
                        <InputNumber
                          value={autoUpdateSettings.stockDataInterval}
                          onChange={(value) =>
                            setAutoUpdateSettings(prev => ({ ...prev, stockDataInterval: value || 5 }))
                          }
                          min={1}
                          max={60}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>

                      <Form.Item label="新闻更新间隔 (分钟)">
                        <InputNumber
                          value={autoUpdateSettings.newsInterval}
                          onChange={(value) =>
                            setAutoUpdateSettings(prev => ({ ...prev, newsInterval: value || 30 }))
                          }
                          min={5}
                          max={120}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>

                      <Form.Item label="每日更新时间">
                        <TimePicker
                          value={autoUpdateSettings.updateTime ?
                            new Date(`2024-01-01 ${autoUpdateSettings.updateTime}:00`) as any : null
                          }
                          onChange={(time) => {
                            if (time) {
                              const timeStr = time.format('HH:mm')
                              setAutoUpdateSettings(prev => ({ ...prev, updateTime: timeStr }))
                            }
                          }}
                          format="HH:mm"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>

                      <Form.Item label="周末更新">
                        <Switch
                          checked={autoUpdateSettings.weekendUpdate}
                          onChange={(checked) =>
                            setAutoUpdateSettings(prev => ({ ...prev, weekendUpdate: checked }))
                          }
                          checkedChildren="启用"
                          unCheckedChildren="禁用"
                        />
                      </Form.Item>

                      <Form.Item label="重试次数">
                        <InputNumber
                          value={autoUpdateSettings.retryCount}
                          onChange={(value) =>
                            setAutoUpdateSettings(prev => ({ ...prev, retryCount: value || 3 }))
                          }
                          min={1}
                          max={10}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>

                      <Form.Item label="启用通知">
                        <Switch
                          checked={autoUpdateSettings.enableNotification}
                          onChange={(checked) =>
                            setAutoUpdateSettings(prev => ({ ...prev, enableNotification: checked }))
                          }
                          checkedChildren="启用"
                          unCheckedChildren="禁用"
                        />
                      </Form.Item>

                      <Form.Item>
                        <Space>
                          <Button
                            type="primary"
                            icon={<SaveOutlined />}
                            loading={loading}
                            onClick={async () => {
                              setLoading(true)
                              try {
                                const result = await dataManagementService.updateAutoUpdateSettings(autoUpdateSettings)
                                if (result) {
                                  message.success('自动更新设置保存成功')
                                } else {
                                  message.error('保存设置失败')
                                }
                              } catch (error) {
                                message.error('保存操作失败')
                              } finally {
                                setLoading(false)
                              }
                            }}
                          >
                            保存设置
                          </Button>
                          <Button
                            icon={<ReloadOutlined />}
                            onClick={() => {
                              confirm({
                                title: '确认重置设置',
                                content: '这将重置自动更新设置为默认值，确定继续吗？',
                                onOk: async () => {
                                  try {
                                    const defaultSettings = await dataManagementService.getAutoUpdateSettings()
                                    setAutoUpdateSettings(defaultSettings)
                                    message.success('设置已重置')
                                  } catch (error) {
                                    message.error('重置失败')
                                  }
                                },
                              })
                            }}
                          >
                            重置
                          </Button>
                        </Space>
                      </Form.Item>
                    </Form>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card title="手动更新操作" size="small" style={{ marginBottom: '16px' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        type="primary"
                        icon={<SyncOutlined />}
                        block
                        loading={loading}
                        onClick={async () => {
                          setLoading(true)
                          try {
                            const result = await dataManagementService.updateStockBasicInfo()
                            if (result) {
                              message.success('股票基础信息更新已启动')
                              loadData() // 刷新任务列表
                            } else {
                              message.error('启动更新失败')
                            }
                          } catch (error) {
                            message.error('更新失败')
                          } finally {
                            setLoading(false)
                          }
                        }}
                      >
                        更新股票基础信息
                      </Button>
                      <Button
                        type="primary"
                        icon={<BarChartOutlined />}
                        block
                        loading={loading}
                        onClick={async () => {
                          setLoading(true)
                          try {
                            const result = await dataManagementService.updateStockPrices()
                            if (result) {
                              message.success('实时价格数据更新已启动')
                              loadData()
                            } else {
                              message.error('启动更新失败')
                            }
                          } catch (error) {
                            message.error('更新失败')
                          } finally {
                            setLoading(false)
                          }
                        }}
                      >
                        更新实时价格数据
                      </Button>
                      <Button
                        type="primary"
                        icon={<FileTextOutlined />}
                        block
                        loading={loading}
                        onClick={async () => {
                          setLoading(true)
                          try {
                            const result = await dataManagementService.updateFinancialNews()
                            if (result) {
                              message.success('财经新闻更新已启动')
                              loadData()
                            } else {
                              message.error('启动更新失败')
                            }
                          } catch (error) {
                            message.error('更新失败')
                          } finally {
                            setLoading(false)
                          }
                        }}
                      >
                        更新财经新闻
                      </Button>
                      <Button
                        type="primary"
                        icon={<MonitorOutlined />}
                        block
                        loading={loading}
                        onClick={async () => {
                          setLoading(true)
                          try {
                            const result = await dataManagementService.calculateTechnicalIndicators()
                            if (result) {
                              message.success('技术指标计算已启动')
                              loadData()
                            } else {
                              message.error('启动计算失败')
                            }
                          } catch (error) {
                            message.error('计算失败')
                          } finally {
                            setLoading(false)
                          }
                        }}
                      >
                        计算技术指标
                      </Button>
                      <Divider />
                      <Button
                        type="primary"
                        icon={<SyncOutlined />}
                        block
                        size="large"
                        loading={loading}
                        onClick={async () => {
                          setLoading(true)
                          try {
                            const result = await dataManagementService.fullDataUpdate()
                            if (result) {
                              message.success('全量数据更新已启动')
                              loadData()
                            } else {
                              message.error('启动全量更新失败')
                            }
                          } catch (error) {
                            message.error('全量更新失败')
                          } finally {
                            setLoading(false)
                          }
                        }}
                      >
                        全量数据更新
                      </Button>
                    </Space>
                  </Card>

                  <Card title="数据清理" size="small">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        icon={<DeleteOutlined />}
                        block
                        loading={loading}
                        onClick={() => {
                          confirm({
                            title: '确认清理过期数据',
                            content: '这将删除30天前的分钟级数据，确定继续吗？',
                            onOk: async () => {
                              setLoading(true)
                              try {
                                const result = await dataManagementService.cleanExpiredData()
                                if (result) {
                                  message.success('过期数据清理完成')
                                  loadData() // 刷新数据库状态
                                } else {
                                  message.error('清理过期数据失败')
                                }
                              } catch (error) {
                                message.error('清理操作失败')
                              } finally {
                                setLoading(false)
                              }
                            },
                          })
                        }}
                      >
                        清理过期数据
                      </Button>
                      <Button
                        icon={<DatabaseOutlined />}
                        block
                        loading={loading}
                        onClick={() => {
                          confirm({
                            title: '确认优化数据库',
                            content: '这将重建索引并优化数据库性能，可能需要几分钟时间，确定继续吗？',
                            onOk: async () => {
                              setLoading(true)
                              try {
                                const result = await dataManagementService.optimizeDatabase()
                                if (result) {
                                  message.success('数据库优化完成')
                                  loadData() // 刷新数据库状态
                                } else {
                                  message.error('数据库优化失败')
                                }
                              } catch (error) {
                                message.error('优化操作失败')
                              } finally {
                                setLoading(false)
                              }
                            },
                          })
                        }}
                      >
                        优化数据库
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            )
          }
        ]}
      />

      {/* API配置模态框 */}
      <APIConfigModal
        visible={configModalVisible}
        onCancel={handleCloseConfigModal}
        onOk={handleSaveAPIConfig}
        config={editingConfig}
        loading={configLoading}
      />

      {/* 任务详情模态框 */}
      <TaskDetailsModal
        visible={taskDetailsVisible}
        taskId={selectedTaskId}
        onClose={handleCloseTaskDetails}
      />
    </div>
  )
}

export default DataManagement
