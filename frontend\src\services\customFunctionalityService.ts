/**
 * 自定义功能管理服务
 * 提供自定义技术指标、股票形态识别、回测策略相关的API调用
 */

import axios from 'axios'

const API_BASE_URL = 'http://localhost:8000/api/v1/custom-functionality'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`[Custom Functionality API] ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('[Custom Functionality API Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('[Custom Functionality API Response Error]', error)
    if (error.response?.status === 404) {
      throw new Error('请求的资源不存在')
    } else if (error.response?.status === 500) {
      throw new Error('服务器内部错误')
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('请求超时')
    }
    throw error
  }
)

export interface CustomFunction {
  function_id?: string
  function_name: string
  function_type: string
  description?: string
  code: string
  parameters: Record<string, any>
  is_active: boolean
  created_by?: string
  created_at?: string
  updated_at?: string
  version: string
  tags: string[]
}

export interface ExecutionResult {
  execution_id: string
  function_id: string
  status: string
  result_data?: Record<string, any>
  error_message?: string
  execution_time?: number
  started_at: string
  completed_at?: string
}

export interface ValidationResult {
  is_valid: boolean
  error_type?: string
  error_message?: string
  line_number?: number
  message?: string
}

export interface FunctionStatistics {
  total_functions: number
  active_functions: number
  function_types: Record<string, number>
  total_executions: number
  successful_executions: number
  success_rate: number
}

export interface FunctionTemplates {
  function_types: Record<string, any>
  indicator_templates: Record<string, any>
  pattern_templates: Record<string, any>
}

class CustomFunctionalityService {
  /**
   * 获取自定义功能列表
   */
  async getFunctions(
    functionType?: string,
    isActive?: boolean,
    tags?: string
  ): Promise<CustomFunction[]> {
    try {
      const params: any = {}
      if (functionType) params.function_type = functionType
      if (isActive !== undefined) params.is_active = isActive
      if (tags) params.tags = tags

      const response = await apiClient.get('/functions', { params })
      return response.data
    } catch (error) {
      console.error('获取自定义功能失败:', error)
      throw new Error('获取自定义功能失败')
    }
  }

  /**
   * 创建自定义功能
   */
  async createFunction(func: CustomFunction): Promise<CustomFunction> {
    try {
      const response = await apiClient.post('/functions', func)
      return response.data
    } catch (error) {
      console.error('创建自定义功能失败:', error)
      throw new Error('创建自定义功能失败')
    }
  }

  /**
   * 更新自定义功能
   */
  async updateFunction(functionId: string, func: CustomFunction): Promise<CustomFunction> {
    try {
      const response = await apiClient.put(`/functions/${functionId}`, func)
      return response.data
    } catch (error) {
      console.error('更新自定义功能失败:', error)
      throw new Error('更新自定义功能失败')
    }
  }

  /**
   * 删除自定义功能
   */
  async deleteFunction(functionId: string): Promise<void> {
    try {
      await apiClient.delete(`/functions/${functionId}`)
    } catch (error) {
      console.error('删除自定义功能失败:', error)
      throw new Error('删除自定义功能失败')
    }
  }

  /**
   * 执行自定义功能
   */
  async executeFunction(
    functionId: string,
    parameters: Record<string, any>,
    stockCode?: string
  ): Promise<{
    message: string
    execution_id: string
    function_id: string
  }> {
    try {
      const response = await apiClient.post(`/functions/${functionId}/execute`, {
        parameters,
        stock_code: stockCode
      })
      return response.data
    } catch (error) {
      console.error('执行自定义功能失败:', error)
      throw new Error('执行自定义功能失败')
    }
  }

  /**
   * 获取功能执行历史
   */
  async getFunctionExecutions(functionId: string, limit: number = 20): Promise<ExecutionResult[]> {
    try {
      const response = await apiClient.get(`/functions/${functionId}/executions`, {
        params: { limit }
      })
      return response.data
    } catch (error) {
      console.error('获取执行历史失败:', error)
      throw new Error('获取执行历史失败')
    }
  }

  /**
   * 获取执行结果
   */
  async getExecutionResult(executionId: string): Promise<ExecutionResult> {
    try {
      const response = await apiClient.get(`/executions/${executionId}`)
      return response.data
    } catch (error) {
      console.error('获取执行结果失败:', error)
      throw new Error('获取执行结果失败')
    }
  }

  /**
   * 获取功能模板
   */
  async getTemplates(): Promise<FunctionTemplates> {
    try {
      const response = await apiClient.get('/templates')
      return response.data
    } catch (error) {
      console.error('获取功能模板失败:', error)
      throw new Error('获取功能模板失败')
    }
  }

  /**
   * 验证功能代码
   */
  async validateCode(functionId: string): Promise<ValidationResult> {
    try {
      const response = await apiClient.post(`/functions/${functionId}/validate`)
      return response.data
    } catch (error) {
      console.error('验证功能代码失败:', error)
      throw new Error('验证功能代码失败')
    }
  }

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<FunctionStatistics> {
    try {
      const response = await apiClient.get('/statistics')
      return response.data
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw new Error('获取统计信息失败')
    }
  }

  /**
   * 批量执行多个功能
   */
  async batchExecuteFunctions(
    functionIds: string[],
    parameters: Record<string, any> = {},
    stockCode?: string
  ): Promise<Array<{
    function_id: string
    execution_id?: string
    success: boolean
    error?: string
  }>> {
    try {
      const results = []
      
      for (const functionId of functionIds) {
        try {
          const result = await this.executeFunction(functionId, parameters, stockCode)
          results.push({
            function_id: functionId,
            execution_id: result.execution_id,
            success: true
          })
        } catch (error) {
          results.push({
            function_id: functionId,
            success: false,
            error: error instanceof Error ? error.message : '执行失败'
          })
        }
      }
      
      return results
    } catch (error) {
      console.error('批量执行功能失败:', error)
      throw new Error('批量执行功能失败')
    }
  }

  /**
   * 获取功能类型统计
   */
  async getFunctionTypeStats(): Promise<Record<string, number>> {
    try {
      const functions = await this.getFunctions()
      const stats: Record<string, number> = {}
      
      functions.forEach(func => {
        stats[func.function_type] = (stats[func.function_type] || 0) + 1
      })
      
      return stats
    } catch (error) {
      console.error('获取功能类型统计失败:', error)
      throw new Error('获取功能类型统计失败')
    }
  }

  /**
   * 搜索自定义功能
   */
  async searchFunctions(
    keyword: string,
    functionType?: string,
    tags?: string[]
  ): Promise<CustomFunction[]> {
    try {
      const allFunctions = await this.getFunctions(functionType)
      
      return allFunctions.filter(func => {
        // 关键词搜索
        const keywordMatch = !keyword || 
          func.function_name.toLowerCase().includes(keyword.toLowerCase()) ||
          func.description?.toLowerCase().includes(keyword.toLowerCase())
        
        // 标签过滤
        const tagMatch = !tags || tags.length === 0 ||
          tags.some(tag => func.tags.includes(tag))
        
        return keywordMatch && tagMatch
      })
    } catch (error) {
      console.error('搜索自定义功能失败:', error)
      throw new Error('搜索自定义功能失败')
    }
  }

  /**
   * 导出功能配置
   */
  async exportFunction(functionId: string): Promise<string> {
    try {
      const functions = await this.getFunctions()
      const func = functions.find(f => f.function_id === functionId)
      
      if (!func) {
        throw new Error('功能不存在')
      }
      
      // 移除不需要导出的字段
      const exportData = {
        function_name: func.function_name,
        function_type: func.function_type,
        description: func.description,
        code: func.code,
        parameters: func.parameters,
        version: func.version,
        tags: func.tags
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出功能失败:', error)
      throw new Error('导出功能失败')
    }
  }

  /**
   * 导入功能配置
   */
  async importFunction(configJson: string): Promise<CustomFunction> {
    try {
      const config = JSON.parse(configJson)
      
      // 验证必需字段
      if (!config.function_name || !config.function_type || !config.code) {
        throw new Error('配置文件格式不正确，缺少必需字段')
      }
      
      const functionData: CustomFunction = {
        function_name: config.function_name,
        function_type: config.function_type,
        description: config.description || '',
        code: config.code,
        parameters: config.parameters || {},
        is_active: true,
        version: config.version || '1.0',
        tags: config.tags || []
      }
      
      return await this.createFunction(functionData)
    } catch (error) {
      console.error('导入功能失败:', error)
      throw new Error('导入功能失败')
    }
  }

  /**
   * 复制功能
   */
  async cloneFunction(functionId: string, newName?: string): Promise<CustomFunction> {
    try {
      const functions = await this.getFunctions()
      const originalFunc = functions.find(f => f.function_id === functionId)
      
      if (!originalFunc) {
        throw new Error('原功能不存在')
      }
      
      const clonedFunction: CustomFunction = {
        function_name: newName || `${originalFunc.function_name} (副本)`,
        function_type: originalFunc.function_type,
        description: originalFunc.description,
        code: originalFunc.code,
        parameters: { ...originalFunc.parameters },
        is_active: false, // 默认停用
        version: '1.0',
        tags: [...originalFunc.tags]
      }
      
      return await this.createFunction(clonedFunction)
    } catch (error) {
      console.error('复制功能失败:', error)
      throw new Error('复制功能失败')
    }
  }

  /**
   * 获取推荐的功能参数
   */
  async getRecommendedParameters(
    functionType: string,
    stockCode?: string
  ): Promise<Record<string, any>> {
    try {
      // 根据功能类型返回推荐参数
      const recommendations: Record<string, Record<string, any>> = {
        technical_indicator: {
          period: 20,
          source: 'close',
          smoothing: 2
        },
        pattern_recognition: {
          window: 20,
          threshold: 0.02,
          min_pattern_length: 5
        },
        backtesting_strategy: {
          initial_capital: 100000,
          commission: 0.001,
          slippage: 0.001,
          max_position_size: 0.1
        },
        screening_filter: {
          max_results: 50,
          min_market_cap: 1000000000,
          min_volume: 1000000
        }
      }
      
      return recommendations[functionType] || {}
    } catch (error) {
      console.error('获取推荐参数失败:', error)
      throw new Error('获取推荐参数失败')
    }
  }
}

export const customFunctionalityService = new CustomFunctionalityService()
export default customFunctionalityService
