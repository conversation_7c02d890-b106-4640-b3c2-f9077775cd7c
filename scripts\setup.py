#!/usr/bin/env python3
"""
项目初始化设置脚本
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, cwd=None):
    """运行命令"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, cwd=cwd)
    return result.returncode == 0


def setup_backend():
    """设置后端环境"""
    print("🐍 设置Python后端环境...")
    backend_dir = Path(__file__).parent.parent / "backend"
    
    # 创建虚拟环境
    if not (backend_dir / "venv").exists():
        print("创建Python虚拟环境...")
        if not run_command("python -m venv venv", cwd=backend_dir):
            print("❌ 创建虚拟环境失败")
            return False
    
    # 安装依赖
    print("安装Python依赖...")
    if os.name == 'nt':
        pip_cmd = "venv\\Scripts\\pip"
    else:
        pip_cmd = "venv/bin/pip"
    
    if not run_command(f"{pip_cmd} install -r requirements.txt", cwd=backend_dir):
        print("❌ 安装Python依赖失败")
        return False
    
    # 初始化数据库
    print("初始化数据库...")
    if os.name == 'nt':
        python_cmd = "venv\\Scripts\\python"
    else:
        python_cmd = "venv/bin/python"
    
    if not run_command(f"{python_cmd} scripts/init_db.py", cwd=backend_dir):
        print("❌ 初始化数据库失败")
        return False
    
    print("✅ 后端环境设置完成")
    return True


def setup_frontend():
    """设置前端环境"""
    print("⚛️ 设置React前端环境...")
    frontend_dir = Path(__file__).parent.parent / "frontend"
    
    # 检查Node.js
    if not run_command("node --version"):
        print("❌ Node.js未安装，请先安装Node.js")
        return False
    
    # 安装依赖
    print("安装Node.js依赖...")
    if not run_command("npm install", cwd=frontend_dir):
        print("❌ 安装Node.js依赖失败")
        return False
    
    print("✅ 前端环境设置完成")
    return True


def create_directories():
    """创建必要的目录"""
    print("📁 创建项目目录...")
    
    directories = [
        "backend/data",
        "backend/logs",
        "docs/api",
        "tests/integration",
        "tests/unit"
    ]
    
    project_root = Path(__file__).parent.parent
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录创建完成")


def main():
    """主函数"""
    print("🚀 A股智能分析系统 - 项目初始化")
    print("=" * 50)
    
    # 创建目录
    create_directories()
    
    # 设置后端
    if not setup_backend():
        print("❌ 后端设置失败")
        sys.exit(1)
    
    # 设置前端
    if not setup_frontend():
        print("❌ 前端设置失败")
        sys.exit(1)
    
    print("\n🎉 项目初始化完成!")
    print("📝 下一步:")
    print("1. 配置环境变量: 复制 backend/.env.example 到 backend/.env")
    print("2. 启动开发环境: python scripts/dev.py")
    print("3. 访问应用: http://localhost:3000")
    print("4. 查看API文档: http://localhost:8000/docs")


if __name__ == "__main__":
    main()
