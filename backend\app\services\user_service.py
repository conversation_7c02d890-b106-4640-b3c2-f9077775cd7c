"""
用户服务模块
"""

import asyncio
from typing import List, Dict, Any, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func, or_, update
from sqlalchemy.dialects.sqlite import insert

from app.core.logging import get_logger
from app.core.database import Async<PERSON>essionLocal
from app.core.security import (
    verify_password, get_password_hash, create_access_token, create_refresh_token,
    verify_token, verify_refresh_token, generate_api_key, PasswordValidator,
    SecurityUtils, rate_limiter
)
from app.models.user import User, UserProfile, UserSession, UserLoginLog

logger = get_logger(__name__)


class UserAuthService:
    """用户认证服务"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def register_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """用户注册"""
        try:
            logger.info(f"用户注册: {user_data.get('email')}")
            
            # 验证输入数据
            validation_result = await self._validate_registration_data(user_data)
            if not validation_result["valid"]:
                return {"success": False, "message": validation_result["message"]}
            
            # 检查用户是否已存在
            existing_user = await self._get_user_by_email_or_username(
                user_data["email"], user_data.get("username")
            )
            if existing_user:
                return {"success": False, "message": "用户已存在"}
            
            # 验证密码强度
            password_valid, password_message = PasswordValidator.validate_password(user_data["password"])
            if not password_valid:
                return {"success": False, "message": password_message}
            
            # 生成用户名（如果未提供）
            if not user_data.get("username"):
                user_data["username"] = SecurityUtils.generate_username(user_data["email"])
            
            # 创建用户
            hashed_password = get_password_hash(user_data["password"])
            
            user = User(
                username=user_data["username"],
                email=user_data["email"],
                hashed_password=hashed_password,
                full_name=user_data.get("full_name"),
                phone=user_data.get("phone"),
                api_key=generate_api_key()
            )
            
            self.session.add(user)
            await self.session.commit()
            await self.session.refresh(user)
            
            # 创建用户资料
            await self._create_user_profile(user.id)
            
            # 记录注册日志
            await self._log_user_action(user.id, "register", True, user_data.get("ip_address"))
            
            logger.info(f"用户注册成功: {user.email} (ID: {user.id})")
            
            return {
                "success": True,
                "message": "注册成功",
                "user_id": user.id,
                "username": user.username,
                "email": user.email
            }
            
        except Exception as e:
            logger.error(f"用户注册失败: {e}")
            await self.session.rollback()
            return {"success": False, "message": "注册失败，请稍后重试"}
    
    async def authenticate_user(self, login_data: Dict[str, Any]) -> Dict[str, Any]:
        """用户认证"""
        try:
            identifier = login_data.get("email") or login_data.get("username")
            password = login_data.get("password")
            ip_address = login_data.get("ip_address")
            
            logger.info(f"用户登录尝试: {identifier}")
            
            # 速率限制检查
            rate_key = f"login:{ip_address}" if ip_address else f"login:{identifier}"
            if not rate_limiter.is_allowed(rate_key, max_requests=5, window_seconds=300):
                remaining_time = rate_limiter.get_remaining_time(rate_key, 300)
                await self._log_user_action(None, "login", False, ip_address, "rate_limited")
                return {
                    "success": False,
                    "message": f"登录尝试过于频繁，请 {remaining_time} 秒后重试"
                }
            
            # 获取用户
            user = await self._get_user_by_email_or_username(identifier, identifier)
            if not user:
                await self._log_user_action(None, "login", False, ip_address, "user_not_found")
                return {"success": False, "message": "用户名或密码错误"}
            
            # 检查用户状态
            if not user.is_active:
                await self._log_user_action(user.id, "login", False, ip_address, "account_disabled")
                return {"success": False, "message": "账户已被禁用"}
            
            # 检查账户锁定
            if user.locked_until and user.locked_until > datetime.utcnow():
                await self._log_user_action(user.id, "login", False, ip_address, "account_locked")
                return {"success": False, "message": "账户已被锁定，请稍后重试"}
            
            # 验证密码
            if not verify_password(password, user.hashed_password):
                # 增加失败次数
                await self._handle_login_failure(user)
                await self._log_user_action(user.id, "login", False, ip_address, "invalid_password")
                return {"success": False, "message": "用户名或密码错误"}
            
            # 登录成功，重置失败次数
            await self._handle_login_success(user)
            
            # 生成令牌
            access_token = create_access_token(subject=user.id)
            refresh_token = create_refresh_token(subject=user.id)
            
            # 创建会话
            session_id = await self._create_user_session(
                user.id, access_token, refresh_token, login_data
            )
            
            # 记录登录日志
            await self._log_user_action(user.id, "login", True, ip_address)
            
            logger.info(f"用户登录成功: {user.email} (ID: {user.id})")
            
            return {
                "success": True,
                "message": "登录成功",
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": 1800,  # 30分钟
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "full_name": user.full_name,
                    "is_verified": user.is_verified,
                    "is_premium": user.is_premium
                }
            }
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return {"success": False, "message": "登录失败，请稍后重试"}
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新令牌"""
        try:
            # 验证刷新令牌
            user_id = verify_refresh_token(refresh_token)
            if not user_id:
                return {"success": False, "message": "无效的刷新令牌"}
            
            # 检查会话是否存在
            session = await self._get_session_by_refresh_token(refresh_token)
            if not session or not session.is_active:
                return {"success": False, "message": "会话已过期"}
            
            # 获取用户
            user = await self._get_user_by_id(int(user_id))
            if not user or not user.is_active:
                return {"success": False, "message": "用户不存在或已被禁用"}
            
            # 生成新令牌
            new_access_token = create_access_token(subject=user.id)
            new_refresh_token = create_refresh_token(subject=user.id)
            
            # 更新会话
            await self._update_session_tokens(session.id, new_access_token, new_refresh_token)
            
            return {
                "success": True,
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "token_type": "bearer",
                "expires_in": 1800
            }
            
        except Exception as e:
            logger.error(f"刷新令牌失败: {e}")
            return {"success": False, "message": "刷新令牌失败"}
    
    async def logout_user(self, access_token: str) -> Dict[str, Any]:
        """用户登出"""
        try:
            # 验证令牌
            user_id = verify_token(access_token)
            if not user_id:
                return {"success": False, "message": "无效的访问令牌"}
            
            # 停用会话
            await self._deactivate_session_by_token(access_token)
            
            # 记录登出日志
            await self._log_user_action(int(user_id), "logout", True)
            
            return {"success": True, "message": "登出成功"}
            
        except Exception as e:
            logger.error(f"用户登出失败: {e}")
            return {"success": False, "message": "登出失败"}
    
    async def _validate_registration_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证注册数据"""
        required_fields = ["email", "password"]
        for field in required_fields:
            if not data.get(field):
                return {"valid": False, "message": f"缺少必填字段: {field}"}
        
        # 验证邮箱格式
        if not SecurityUtils.validate_email(data["email"]):
            return {"valid": False, "message": "邮箱格式不正确"}
        
        # 验证手机号格式（如果提供）
        if data.get("phone") and not SecurityUtils.validate_phone(data["phone"]):
            return {"valid": False, "message": "手机号格式不正确"}
        
        return {"valid": True, "message": "验证通过"}
    
    async def _get_user_by_email_or_username(self, email: str, username: str) -> Optional[User]:
        """根据邮箱或用户名获取用户"""
        try:
            stmt = select(User).where(
                or_(User.email == email, User.username == username)
            )
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    async def _get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        try:
            stmt = select(User).where(User.id == user_id)
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    async def _create_user_profile(self, user_id: int):
        """创建用户资料"""
        try:
            profile = UserProfile(
                user_id=user_id,
                notification_settings={
                    "email_notifications": True,
                    "push_notifications": True,
                    "sms_notifications": False,
                    "price_alerts": True,
                    "ai_signals": True,
                    "market_news": True
                },
                privacy_settings={
                    "profile_public": False,
                    "show_portfolio": False,
                    "show_trades": False,
                    "allow_friend_requests": True
                }
            )
            
            self.session.add(profile)
            await self.session.commit()
            
        except Exception as e:
            logger.error(f"创建用户资料失败: {e}")
    
    async def _handle_login_failure(self, user: User):
        """处理登录失败"""
        try:
            user.login_attempts += 1
            
            # 如果失败次数达到限制，锁定账户
            if user.login_attempts >= 5:
                user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                logger.warning(f"用户账户被锁定: {user.email}")
            
            await self.session.commit()
            
        except Exception as e:
            logger.error(f"处理登录失败失败: {e}")
    
    async def _handle_login_success(self, user: User):
        """处理登录成功"""
        try:
            user.login_attempts = 0
            user.locked_until = None
            user.last_login = datetime.utcnow()
            user.last_active_at = datetime.utcnow()
            
            await self.session.commit()
            
        except Exception as e:
            logger.error(f"处理登录成功失败: {e}")
    
    async def _create_user_session(
        self, user_id: int, access_token: str, refresh_token: str, login_data: Dict[str, Any]
    ) -> int:
        """创建用户会话"""
        try:
            session = UserSession(
                user_id=user_id,
                session_token=access_token,
                refresh_token=refresh_token,
                ip_address=login_data.get("ip_address"),
                user_agent=login_data.get("user_agent"),
                device_info=login_data.get("device_info"),
                location=login_data.get("location"),
                expires_at=datetime.utcnow() + timedelta(days=7)
            )
            
            self.session.add(session)
            await self.session.commit()
            
            return session.id
            
        except Exception as e:
            logger.error(f"创建用户会话失败: {e}")
            return 0
    
    async def _get_session_by_refresh_token(self, refresh_token: str) -> Optional[UserSession]:
        """根据刷新令牌获取会话"""
        try:
            stmt = select(UserSession).where(
                and_(
                    UserSession.refresh_token == refresh_token,
                    UserSession.is_active == True,
                    UserSession.expires_at > datetime.utcnow()
                )
            )
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取会话失败: {e}")
            return None
    
    async def _update_session_tokens(self, session_id: int, access_token: str, refresh_token: str):
        """更新会话令牌"""
        try:
            stmt = update(UserSession).where(UserSession.id == session_id).values(
                session_token=access_token,
                refresh_token=refresh_token,
                last_used_at=datetime.utcnow()
            )
            await self.session.execute(stmt)
            await self.session.commit()
        except Exception as e:
            logger.error(f"更新会话令牌失败: {e}")
    
    async def _deactivate_session_by_token(self, access_token: str):
        """根据访问令牌停用会话"""
        try:
            stmt = update(UserSession).where(UserSession.session_token == access_token).values(
                is_active=False
            )
            await self.session.execute(stmt)
            await self.session.commit()
        except Exception as e:
            logger.error(f"停用会话失败: {e}")
    
    async def _log_user_action(
        self, user_id: Optional[int], action: str, success: bool, 
        ip_address: Optional[str] = None, failure_reason: Optional[str] = None
    ):
        """记录用户操作日志"""
        try:
            log = UserLoginLog(
                user_id=user_id,
                login_type="password",
                success=success,
                failure_reason=failure_reason,
                ip_address=ip_address
            )
            
            self.session.add(log)
            await self.session.commit()
            
        except Exception as e:
            logger.error(f"记录用户操作日志失败: {e}")
