import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Table,
  Space,
  Typography,
  Tag,
  Switch,
  Modal,
  Badge,
  Tooltip,
  message,
  Tabs,
  List,
  Avatar,
  Divider,
  Alert,
  Progress,
} from 'antd'
import {
  BellOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SoundOutlined,
  MailOutlined,
  MobileOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select

interface AlertRule {
  id: string
  name: string
  symbol: string
  type: 'price' | 'technical' | 'pattern' | 'ai_prediction'
  condition: string
  value: number
  enabled: boolean
  notifications: {
    sound: boolean
    email: boolean
    push: boolean
  }
  createdAt: string
  triggeredCount: number
  lastTriggered?: string
}

interface AlertHistory {
  id: string
  ruleId: string
  ruleName: string
  symbol: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
  triggeredAt: string
  currentValue: number
  targetValue: number
}

const AlertSystem: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null)
  const [activeTab, setActiveTab] = useState('rules')
  
  // 预警规则
  const [alertRules, setAlertRules] = useState<AlertRule[]>([
    {
      id: '1',
      name: 'AAPL价格突破180',
      symbol: 'AAPL',
      type: 'price',
      condition: 'above',
      value: 180,
      enabled: true,
      notifications: { sound: true, email: true, push: false },
      createdAt: '2024-01-15',
      triggeredCount: 3,
      lastTriggered: '2024-01-20 09:30:00'
    },
    {
      id: '2',
      name: 'GOOGL RSI超买',
      symbol: 'GOOGL',
      type: 'technical',
      condition: 'rsi_above',
      value: 80,
      enabled: true,
      notifications: { sound: true, email: false, push: true },
      createdAt: '2024-01-10',
      triggeredCount: 1,
      lastTriggered: '2024-01-18 14:25:00'
    },
    {
      id: '3',
      name: 'MSFT AI预测看跌',
      symbol: 'MSFT',
      type: 'ai_prediction',
      condition: 'bearish',
      value: 70,
      enabled: false,
      notifications: { sound: false, email: true, push: true },
      createdAt: '2024-01-12',
      triggeredCount: 0
    }
  ])

  // 预警历史
  const [alertHistory, setAlertHistory] = useState<AlertHistory[]>([
    {
      id: '1',
      ruleId: '1',
      ruleName: 'AAPL价格突破180',
      symbol: 'AAPL',
      message: 'AAPL价格突破180美元，当前价格182.50美元',
      type: 'success',
      triggeredAt: '2024-01-20 09:30:00',
      currentValue: 182.50,
      targetValue: 180
    },
    {
      id: '2',
      ruleId: '2',
      ruleName: 'GOOGL RSI超买',
      symbol: 'GOOGL',
      message: 'GOOGL RSI指标达到82.5，进入超买区域',
      type: 'warning',
      triggeredAt: '2024-01-18 14:25:00',
      currentValue: 82.5,
      targetValue: 80
    },
    {
      id: '3',
      ruleId: '1',
      ruleName: 'AAPL价格突破180',
      symbol: 'AAPL',
      message: 'AAPL价格突破180美元，当前价格181.20美元',
      type: 'success',
      triggeredAt: '2024-01-19 10:15:00',
      currentValue: 181.20,
      targetValue: 180
    }
  ])

  // 创建/编辑预警规则
  const handleSaveRule = async (values: any) => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newRule: AlertRule = {
        id: editingRule?.id || Date.now().toString(),
        name: values.name,
        symbol: values.symbol,
        type: values.type,
        condition: values.condition,
        value: values.value,
        enabled: values.enabled ?? true,
        notifications: {
          sound: values.sound ?? false,
          email: values.email ?? false,
          push: values.push ?? false
        },
        createdAt: editingRule?.createdAt || new Date().toISOString().split('T')[0],
        triggeredCount: editingRule?.triggeredCount || 0,
        lastTriggered: editingRule?.lastTriggered
      }

      if (editingRule) {
        setAlertRules(rules => rules.map(rule => 
          rule.id === editingRule.id ? newRule : rule
        ))
        message.success('预警规则已更新')
      } else {
        setAlertRules(rules => [...rules, newRule])
        message.success('预警规则已创建')
      }

      setModalVisible(false)
      setEditingRule(null)
      form.resetFields()
    } catch (error) {
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 删除预警规则
  const handleDeleteRule = (ruleId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个预警规则吗？',
      onOk: () => {
        setAlertRules(rules => rules.filter(rule => rule.id !== ruleId))
        message.success('预警规则已删除')
      }
    })
  }

  // 切换规则启用状态
  const handleToggleRule = (ruleId: string, enabled: boolean) => {
    setAlertRules(rules => rules.map(rule => 
      rule.id === ruleId ? { ...rule, enabled } : rule
    ))
    message.success(enabled ? '预警规则已启用' : '预警规则已禁用')
  }

  // 获取预警类型图标
  const getAlertTypeIcon = (type: string) => {
    const icons = {
      info: <CheckCircleOutlined style={{ color: '#1890ff' }} />,
      warning: <WarningOutlined style={{ color: '#faad14' }} />,
      error: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />
    }
    return icons[type as keyof typeof icons] || icons.info
  }

  // 预警规则表格列
  const ruleColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: AlertRule) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{record.symbol}</div>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          price: '价格',
          technical: '技术指标',
          pattern: '形态',
          ai_prediction: 'AI预测'
        }
        return <Tag>{typeMap[type as keyof typeof typeMap] || type}</Tag>
      }
    },
    {
      title: '条件',
      key: 'condition',
      render: (record: AlertRule) => (
        <div>
          <Text>{record.condition} {record.value}</Text>
        </div>
      )
    },
    {
      title: '通知方式',
      key: 'notifications',
      render: (record: AlertRule) => (
        <Space>
          {record.notifications.sound && <SoundOutlined style={{ color: '#52c41a' }} />}
          {record.notifications.email && <MailOutlined style={{ color: '#1890ff' }} />}
          {record.notifications.push && <MobileOutlined style={{ color: '#faad14' }} />}
        </Space>
      )
    },
    {
      title: '触发次数',
      dataIndex: 'triggeredCount',
      key: 'triggeredCount',
      render: (count: number) => <Badge count={count} />
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean, record: AlertRule) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleToggleRule(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: AlertRule) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingRule(record)
              form.setFieldsValue({
                name: record.name,
                symbol: record.symbol,
                type: record.type,
                condition: record.condition,
                value: record.value,
                enabled: record.enabled,
                sound: record.notifications.sound,
                email: record.notifications.email,
                push: record.notifications.push
              })
              setModalVisible(true)
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteRule(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          <BellOutlined /> 实时预警
        </Title>
        <Space>
          <Badge count={alertRules.filter(rule => rule.enabled).length} showZero>
            <Button>活跃规则</Button>
          </Badge>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingRule(null)
              form.resetFields()
              setModalVisible(true)
            }}
          >
            创建预警
          </Button>
        </Space>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        items={[
          {
            key: 'rules',
            label: (
              <span>
                <BellOutlined />
                预警规则
              </span>
            ),
            children: (
              <Card>
                <Table
                  dataSource={alertRules}
                  columns={ruleColumns}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
              </Card>
            )
          },
          {
            key: 'history',
            label: (
              <span>
                <WarningOutlined />
                预警历史
              </span>
            ),
            children: (
              <Card>
                <List
                  dataSource={alertHistory}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={getAlertTypeIcon(item.type)}
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span>{item.ruleName}</span>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {item.triggeredAt}
                            </Text>
                          </div>
                        }
                        description={
                          <div>
                            <div>{item.message}</div>
                            <div style={{ marginTop: '4px' }}>
                              <Tag color="blue">{item.symbol}</Tag>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                当前值: {item.currentValue} | 目标值: {item.targetValue}
                              </Text>
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                  pagination={{ pageSize: 10 }}
                />
              </Card>
            )
          },
          {
            key: 'stats',
            label: (
              <span>
                <RiseOutlined />
                统计分析
              </span>
            ),
            children: (
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="总预警规则"
                      value={alertRules.length}
                      prefix={<BellOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="活跃规则"
                      value={alertRules.filter(rule => rule.enabled).length}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="今日触发"
                      value={alertHistory.filter(alert => 
                        alert.triggeredAt.startsWith(new Date().toISOString().split('T')[0])
                      ).length}
                      prefix={<WarningOutlined />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Card>
                </Col>
                <Col span={24}>
                  <Card title="预警规则分布">
                    <Row gutter={[16, 16]}>
                      <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                            {alertRules.filter(rule => rule.type === 'price').length}
                          </div>
                          <div>价格预警</div>
                        </div>
                      </Col>
                      <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                            {alertRules.filter(rule => rule.type === 'technical').length}
                          </div>
                          <div>技术指标</div>
                        </div>
                      </Col>
                      <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                            {alertRules.filter(rule => rule.type === 'pattern').length}
                          </div>
                          <div>形态识别</div>
                        </div>
                      </Col>
                      <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f5222d' }}>
                            {alertRules.filter(rule => rule.type === 'ai_prediction').length}
                          </div>
                          <div>AI预测</div>
                        </div>
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Row>
            )
          }
        ]}
      />

      {/* 创建/编辑预警规则模态框 */}
      <Modal
        title={editingRule ? '编辑预警规则' : '创建预警规则'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingRule(null)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveRule}
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="例如：000001价格突破15" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="symbol"
                label="股票代码"
                rules={[{ required: true, message: '请输入股票代码' }]}
              >
                <Input placeholder="例如：000001" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="预警类型"
                rules={[{ required: true, message: '请选择预警类型' }]}
              >
                <Select placeholder="选择预警类型">
                  <Option value="price">价格预警</Option>
                  <Option value="technical">技术指标</Option>
                  <Option value="pattern">形态识别</Option>
                  <Option value="ai_prediction">AI预测</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="condition"
                label="触发条件"
                rules={[{ required: true, message: '请选择触发条件' }]}
              >
                <Select placeholder="选择条件">
                  <Option value="above">大于</Option>
                  <Option value="below">小于</Option>
                  <Option value="equal">等于</Option>
                  <Option value="rsi_above">RSI大于</Option>
                  <Option value="rsi_below">RSI小于</Option>
                  <Option value="bullish">AI看涨</Option>
                  <Option value="bearish">AI看跌</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="value"
                label="目标值"
                rules={[{ required: true, message: '请输入目标值' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="输入数值"
                  min={0}
                  step={0.01}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="通知方式">
            <Space>
              <Form.Item name="sound" valuePropName="checked" noStyle>
                <Switch checkedChildren={<SoundOutlined />} unCheckedChildren={<SoundOutlined />} />
              </Form.Item>
              <span>声音</span>
              
              <Form.Item name="email" valuePropName="checked" noStyle>
                <Switch checkedChildren={<MailOutlined />} unCheckedChildren={<MailOutlined />} />
              </Form.Item>
              <span>邮件</span>
              
              <Form.Item name="push" valuePropName="checked" noStyle>
                <Switch checkedChildren={<MobileOutlined />} unCheckedChildren={<MobileOutlined />} />
              </Form.Item>
              <span>推送</span>
            </Space>
          </Form.Item>

          <Form.Item name="enabled" valuePropName="checked" initialValue={true}>
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingRule ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AlertSystem
