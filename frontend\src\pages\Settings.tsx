import React, { useState } from 'react'
import {
  Card,
  Button,
  Form,
  Input,
  Switch,
  Select,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Avatar,
  Upload,
  Tabs,
} from 'antd'
import {
  UserOutlined,
  SettingOutlined,
  BellOutlined,
  SecurityScanOutlined,
  UploadOutlined,
  SaveOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '@/stores/authStore'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { TabPane } = Tabs
const { TextArea } = Input

const Settings: React.FC = () => {
  const { user, updateUser } = useAuthStore()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleSaveProfile = async (values: any) => {
    setLoading(true)
    try {
      // 这里应该调用API更新用户信息
      await new Promise(resolve => setTimeout(resolve, 1000))
      updateUser({ ...user, ...values })
      message.success('个人信息更新成功')
    } catch (error) {
      message.error('更新失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveNotifications = async (values: any) => {
    setLoading(true)
    try {
      // 这里应该调用API更新通知设置
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('通知设置更新成功')
    } catch (error) {
      message.error('更新失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSecurity = async (values: any) => {
    setLoading(true)
    try {
      // 这里应该调用API更新安全设置
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('安全设置更新成功')
    } catch (error) {
      message.error('更新失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <SettingOutlined /> 系统设置
        </Title>
        <Paragraph type="secondary">
          管理您的个人信息、通知偏好和安全设置
        </Paragraph>
      </div>

      <Tabs defaultActiveKey="profile">
        <TabPane tab={<span><UserOutlined />个人信息</span>} key="profile">
          <Card>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSaveProfile}
              initialValues={{
                username: user?.username,
                email: user?.email,
                full_name: user?.full_name,
                phone: user?.phone,
                bio: user?.bio,
                location: user?.location,
              }}
            >
              <Row gutter={24}>
                <Col span={8}>
                  <div style={{ textAlign: 'center', marginBottom: 24 }}>
                    <Avatar size={120} icon={<UserOutlined />} />
                    <div style={{ marginTop: 16 }}>
                      <Upload>
                        <Button icon={<UploadOutlined />}>更换头像</Button>
                      </Upload>
                    </div>
                  </div>
                </Col>
                
                <Col span={16}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="full_name" label="真实姓名">
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="phone" label="手机号码">
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="location" label="所在地区">
                        <Select placeholder="选择地区">
                          <Option value="beijing">北京</Option>
                          <Option value="shanghai">上海</Option>
                          <Option value="guangzhou">广州</Option>
                          <Option value="shenzhen">深圳</Option>
                          <Option value="hangzhou">杭州</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="risk_tolerance" label="风险偏好">
                        <Select placeholder="选择风险偏好">
                          <Option value="conservative">保守型</Option>
                          <Option value="moderate">稳健型</Option>
                          <Option value="aggressive">积极型</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item name="bio" label="个人简介">
                        <TextArea rows={3} placeholder="介绍一下自己..." />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
              </Row>

              <Divider />

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                  保存个人信息
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={<span><BellOutlined />通知设置</span>} key="notifications">
          <Card>
            <Form layout="vertical" onFinish={handleSaveNotifications}>
              <Title level={4}>预警通知</Title>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="price_alerts" label="价格预警" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="technical_alerts" label="技术指标预警" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="ai_alerts" label="AI信号预警" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Title level={4}>通知方式</Title>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="email_notifications" label="邮件通知" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="sms_notifications" label="短信通知" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="app_notifications" label="应用内通知" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Title level={4}>通知时间</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="notification_start" label="开始时间">
                    <Select defaultValue="09:00">
                      <Option value="08:00">08:00</Option>
                      <Option value="09:00">09:00</Option>
                      <Option value="10:00">10:00</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="notification_end" label="结束时间">
                    <Select defaultValue="18:00">
                      <Option value="17:00">17:00</Option>
                      <Option value="18:00">18:00</Option>
                      <Option value="19:00">19:00</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                  保存通知设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={<span><SecurityScanOutlined />安全设置</span>} key="security">
          <Card>
            <Form layout="vertical" onFinish={handleSaveSecurity}>
              <Title level={4}>密码设置</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="current_password"
                    label="当前密码"
                    rules={[{ required: true, message: '请输入当前密码' }]}
                  >
                    <Input.Password />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="new_password"
                    label="新密码"
                    rules={[
                      { required: true, message: '请输入新密码' },
                      { min: 6, message: '密码至少6位' }
                    ]}
                  >
                    <Input.Password />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="confirm_password"
                    label="确认新密码"
                    dependencies={['new_password']}
                    rules={[
                      { required: true, message: '请确认新密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('new_password') === value) {
                            return Promise.resolve()
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'))
                        },
                      }),
                    ]}
                  >
                    <Input.Password />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Title level={4}>安全选项</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="two_factor_auth" label="双因素认证" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                  <Text type="secondary">启用后需要手机验证码才能登录</Text>
                </Col>
                <Col span={12}>
                  <Form.Item name="login_notifications" label="登录通知" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                  <Text type="secondary">新设备登录时发送通知</Text>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                    保存安全设置
                  </Button>
                  <Button danger>
                    注销所有设备
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  )
}

export default Settings
