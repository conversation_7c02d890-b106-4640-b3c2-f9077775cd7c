"""
技术指标计算服务模块
"""

from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, date

from app.core.logging import get_logger
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


class TechnicalIndicators:
    """技术指标计算器"""
    
    def __init__(self):
        pass
    
    @staticmethod
    def sma(data: List[float], period: int) -> List[float]:
        """简单移动平均线 (Simple Moving Average)"""
        if len(data) < period:
            return []
        
        result = []
        for i in range(len(data)):
            if i < period - 1:
                result.append(np.nan)
            else:
                avg = np.mean(data[i - period + 1:i + 1])
                result.append(avg)
        
        return result
    
    @staticmethod
    def ema(data: List[float], period: int) -> List[float]:
        """指数移动平均线 (Exponential Moving Average)"""
        if len(data) < period:
            return []
        
        result = []
        multiplier = 2 / (period + 1)
        
        # 第一个EMA值使用SMA
        sma_first = np.mean(data[:period])
        result.extend([np.nan] * (period - 1))
        result.append(sma_first)
        
        # 计算后续EMA值
        for i in range(period, len(data)):
            ema_value = (data[i] * multiplier) + (result[-1] * (1 - multiplier))
            result.append(ema_value)
        
        return result
    
    @staticmethod
    def rsi(data: List[float], period: int = 14) -> List[float]:
        """相对强弱指数 (Relative Strength Index)"""
        if len(data) < period + 1:
            return []
        
        # 计算价格变化
        changes = []
        for i in range(1, len(data)):
            changes.append(data[i] - data[i-1])
        
        # 分离上涨和下跌
        gains = [max(change, 0) for change in changes]
        losses = [abs(min(change, 0)) for change in changes]
        
        result = [np.nan] * period
        
        # 计算第一个RSI值
        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])
        
        if avg_loss == 0:
            result.append(100)
        else:
            rs = avg_gain / avg_loss
            rsi_value = 100 - (100 / (1 + rs))
            result.append(rsi_value)
        
        # 计算后续RSI值
        for i in range(period, len(changes)):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
            
            if avg_loss == 0:
                result.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi_value = 100 - (100 / (1 + rs))
                result.append(rsi_value)
        
        return result
    
    @staticmethod
    def macd(data: List[float], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, List[float]]:
        """MACD指标 (Moving Average Convergence Divergence)"""
        if len(data) < slow_period:
            return {"macd": [], "signal": [], "histogram": []}
        
        # 计算快线和慢线EMA
        ema_fast = TechnicalIndicators.ema(data, fast_period)
        ema_slow = TechnicalIndicators.ema(data, slow_period)
        
        # 计算MACD线
        macd_line = []
        for i in range(len(data)):
            if i < slow_period - 1 or np.isnan(ema_fast[i]) or np.isnan(ema_slow[i]):
                macd_line.append(np.nan)
            else:
                macd_line.append(ema_fast[i] - ema_slow[i])
        
        # 计算信号线
        macd_values = [x for x in macd_line if not np.isnan(x)]
        if len(macd_values) >= signal_period:
            signal_line = [np.nan] * (len(macd_line) - len(macd_values))
            signal_ema = TechnicalIndicators.ema(macd_values, signal_period)
            signal_line.extend(signal_ema)
        else:
            signal_line = [np.nan] * len(macd_line)
        
        # 计算柱状图
        histogram = []
        for i in range(len(macd_line)):
            if np.isnan(macd_line[i]) or np.isnan(signal_line[i]):
                histogram.append(np.nan)
            else:
                histogram.append(macd_line[i] - signal_line[i])
        
        return {
            "macd": macd_line,
            "signal": signal_line,
            "histogram": histogram
        }
    
    @staticmethod
    def bollinger_bands(data: List[float], period: int = 20, std_dev: float = 2) -> Dict[str, List[float]]:
        """布林带 (Bollinger Bands)"""
        if len(data) < period:
            return {"upper": [], "middle": [], "lower": []}
        
        middle_band = TechnicalIndicators.sma(data, period)
        upper_band = []
        lower_band = []
        
        for i in range(len(data)):
            if i < period - 1:
                upper_band.append(np.nan)
                lower_band.append(np.nan)
            else:
                # 计算标准差
                window_data = data[i - period + 1:i + 1]
                std = np.std(window_data)
                
                upper_band.append(middle_band[i] + (std_dev * std))
                lower_band.append(middle_band[i] - (std_dev * std))
        
        return {
            "upper": upper_band,
            "middle": middle_band,
            "lower": lower_band
        }
    
    @staticmethod
    def kdj(high_data: List[float], low_data: List[float], close_data: List[float], 
            period: int = 9, k_period: int = 3, d_period: int = 3) -> Dict[str, List[float]]:
        """KDJ指标"""
        if len(high_data) < period or len(low_data) < period or len(close_data) < period:
            return {"k": [], "d": [], "j": []}
        
        rsv = []  # 未成熟随机值
        
        for i in range(len(close_data)):
            if i < period - 1:
                rsv.append(np.nan)
            else:
                # 计算周期内的最高价和最低价
                period_high = max(high_data[i - period + 1:i + 1])
                period_low = min(low_data[i - period + 1:i + 1])
                
                if period_high == period_low:
                    rsv.append(50)  # 避免除零
                else:
                    rsv_value = (close_data[i] - period_low) / (period_high - period_low) * 100
                    rsv.append(rsv_value)
        
        # 计算K值
        k_values = []
        k_prev = 50  # K值初始值
        
        for i, rsv_val in enumerate(rsv):
            if np.isnan(rsv_val):
                k_values.append(np.nan)
            else:
                k_value = (rsv_val + (k_period - 1) * k_prev) / k_period
                k_values.append(k_value)
                k_prev = k_value
        
        # 计算D值
        d_values = []
        d_prev = 50  # D值初始值
        
        for k_val in k_values:
            if np.isnan(k_val):
                d_values.append(np.nan)
            else:
                d_value = (k_val + (d_period - 1) * d_prev) / d_period
                d_values.append(d_value)
                d_prev = d_value
        
        # 计算J值
        j_values = []
        for i in range(len(k_values)):
            if np.isnan(k_values[i]) or np.isnan(d_values[i]):
                j_values.append(np.nan)
            else:
                j_value = 3 * k_values[i] - 2 * d_values[i]
                j_values.append(j_value)
        
        return {
            "k": k_values,
            "d": d_values,
            "j": j_values
        }
    
    @staticmethod
    def williams_r(high_data: List[float], low_data: List[float], close_data: List[float], period: int = 14) -> List[float]:
        """威廉指标 (Williams %R)"""
        if len(high_data) < period or len(low_data) < period or len(close_data) < period:
            return []
        
        result = []
        
        for i in range(len(close_data)):
            if i < period - 1:
                result.append(np.nan)
            else:
                period_high = max(high_data[i - period + 1:i + 1])
                period_low = min(low_data[i - period + 1:i + 1])
                
                if period_high == period_low:
                    result.append(-50)  # 避免除零
                else:
                    wr_value = (period_high - close_data[i]) / (period_high - period_low) * (-100)
                    result.append(wr_value)
        
        return result
    
    @staticmethod
    def stochastic(high_data: List[float], low_data: List[float], close_data: List[float], 
                   k_period: int = 14, d_period: int = 3) -> Dict[str, List[float]]:
        """随机指标 (Stochastic Oscillator)"""
        if len(high_data) < k_period or len(low_data) < k_period or len(close_data) < k_period:
            return {"k": [], "d": []}
        
        # 计算%K
        k_values = []
        
        for i in range(len(close_data)):
            if i < k_period - 1:
                k_values.append(np.nan)
            else:
                period_high = max(high_data[i - k_period + 1:i + 1])
                period_low = min(low_data[i - k_period + 1:i + 1])
                
                if period_high == period_low:
                    k_values.append(50)  # 避免除零
                else:
                    k_value = (close_data[i] - period_low) / (period_high - period_low) * 100
                    k_values.append(k_value)
        
        # 计算%D (K值的移动平均)
        d_values = TechnicalIndicators.sma([x for x in k_values if not np.isnan(x)], d_period)
        
        # 对齐D值数组
        nan_count = sum(1 for x in k_values if np.isnan(x))
        d_aligned = [np.nan] * nan_count + [np.nan] * (d_period - 1) + d_values
        
        return {
            "k": k_values,
            "d": d_aligned[:len(k_values)]
        }

    @staticmethod
    def cci(high_data: List[float], low_data: List[float], close_data: List[float], period: int = 20) -> List[float]:
        """顺势指标 (Commodity Channel Index)"""
        if len(high_data) < period or len(low_data) < period or len(close_data) < period:
            return []

        # 计算典型价格
        typical_prices = [(h + l + c) / 3 for h, l, c in zip(high_data, low_data, close_data)]

        result = []

        for i in range(len(typical_prices)):
            if i < period - 1:
                result.append(np.nan)
            else:
                # 计算移动平均
                ma = np.mean(typical_prices[i - period + 1:i + 1])

                # 计算平均绝对偏差
                mad = np.mean([abs(tp - ma) for tp in typical_prices[i - period + 1:i + 1]])

                if mad == 0:
                    result.append(0)
                else:
                    cci_value = (typical_prices[i] - ma) / (0.015 * mad)
                    result.append(cci_value)

        return result

    @staticmethod
    def atr(high_data: List[float], low_data: List[float], close_data: List[float], period: int = 14) -> List[float]:
        """平均真实波幅 (Average True Range)"""
        if len(high_data) < 2 or len(low_data) < 2 or len(close_data) < 2:
            return []

        # 计算真实波幅
        true_ranges = []

        for i in range(1, len(close_data)):
            tr1 = high_data[i] - low_data[i]
            tr2 = abs(high_data[i] - close_data[i-1])
            tr3 = abs(low_data[i] - close_data[i-1])

            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)

        # 计算ATR
        result = [np.nan]  # 第一个值为NaN

        if len(true_ranges) >= period:
            # 第一个ATR值使用简单平均
            first_atr = np.mean(true_ranges[:period])
            result.extend([np.nan] * (period - 1))
            result.append(first_atr)

            # 后续ATR值使用指数平滑
            for i in range(period, len(true_ranges)):
                atr_value = (result[-1] * (period - 1) + true_ranges[i]) / period
                result.append(atr_value)

        return result

    @staticmethod
    def obv(close_data: List[float], volume_data: List[int]) -> List[float]:
        """能量潮 (On Balance Volume)"""
        if len(close_data) < 2 or len(volume_data) < 2:
            return []

        result = [0]  # OBV从0开始

        for i in range(1, len(close_data)):
            if close_data[i] > close_data[i-1]:
                # 价格上涨，加上成交量
                obv_value = result[-1] + volume_data[i]
            elif close_data[i] < close_data[i-1]:
                # 价格下跌，减去成交量
                obv_value = result[-1] - volume_data[i]
            else:
                # 价格不变，OBV不变
                obv_value = result[-1]

            result.append(obv_value)

        return result

    @staticmethod
    def adx(high_data: List[float], low_data: List[float], close_data: List[float], period: int = 14) -> Dict[str, List[float]]:
        """平均趋向指数 (Average Directional Index)"""
        if len(high_data) < period + 1 or len(low_data) < period + 1 or len(close_data) < period + 1:
            return {"adx": [], "di_plus": [], "di_minus": []}

        # 计算真实波幅和方向移动
        tr_list = []
        dm_plus = []
        dm_minus = []

        for i in range(1, len(close_data)):
            # 真实波幅
            tr1 = high_data[i] - low_data[i]
            tr2 = abs(high_data[i] - close_data[i-1])
            tr3 = abs(low_data[i] - close_data[i-1])
            tr = max(tr1, tr2, tr3)
            tr_list.append(tr)

            # 方向移动
            up_move = high_data[i] - high_data[i-1]
            down_move = low_data[i-1] - low_data[i]

            if up_move > down_move and up_move > 0:
                dm_plus.append(up_move)
            else:
                dm_plus.append(0)

            if down_move > up_move and down_move > 0:
                dm_minus.append(down_move)
            else:
                dm_minus.append(0)

        # 计算平滑的TR、DM+、DM-
        atr_values = TechnicalIndicators.ema(tr_list, period)
        adm_plus = TechnicalIndicators.ema(dm_plus, period)
        adm_minus = TechnicalIndicators.ema(dm_minus, period)

        # 计算DI+、DI-
        di_plus = []
        di_minus = []

        for i in range(len(atr_values)):
            if np.isnan(atr_values[i]) or atr_values[i] == 0:
                di_plus.append(np.nan)
                di_minus.append(np.nan)
            else:
                di_plus.append(100 * adm_plus[i] / atr_values[i])
                di_minus.append(100 * adm_minus[i] / atr_values[i])

        # 计算DX和ADX
        dx_values = []
        for i in range(len(di_plus)):
            if np.isnan(di_plus[i]) or np.isnan(di_minus[i]):
                dx_values.append(np.nan)
            else:
                di_sum = di_plus[i] + di_minus[i]
                if di_sum == 0:
                    dx_values.append(0)
                else:
                    dx = 100 * abs(di_plus[i] - di_minus[i]) / di_sum
                    dx_values.append(dx)

        # 计算ADX
        dx_clean = [x for x in dx_values if not np.isnan(x)]
        if len(dx_clean) >= period:
            adx_values = TechnicalIndicators.ema(dx_clean, period)
            # 对齐数组
            nan_count = len(dx_values) - len(dx_clean)
            adx_aligned = [np.nan] * nan_count + [np.nan] * (period - 1) + adx_values
        else:
            adx_aligned = [np.nan] * len(dx_values)

        # 对齐所有数组到原始数据长度
        result_length = len(close_data)

        return {
            "adx": ([np.nan] + adx_aligned)[:result_length],
            "di_plus": ([np.nan] + di_plus)[:result_length],
            "di_minus": ([np.nan] + di_minus)[:result_length]
        }


class IndicatorCalculationService:
    """指标计算服务"""
    
    def __init__(self):
        self.calculator = TechnicalIndicators()
    
    async def calculate_all_indicators(self, stock_code: str, period: str = "daily", limit: int = 250) -> Dict[str, Any]:
        """计算所有技术指标"""
        try:
            logger.info(f"开始计算股票 {stock_code} 的技术指标...")
            
            # 获取K线数据
            async with DataStorageService() as storage:
                klines = await storage.get_kline_data(stock_code, period, limit=limit)
            
            if not klines:
                logger.warning(f"股票 {stock_code} 没有K线数据")
                return {}
            
            # 提取价格数据
            dates = [kline.trade_date for kline in klines]
            opens = [float(kline.open_price) for kline in klines]
            highs = [float(kline.high_price) for kline in klines]
            lows = [float(kline.low_price) for kline in klines]
            closes = [float(kline.close_price) for kline in klines]
            volumes = [kline.volume for kline in klines]
            
            # 计算各种技术指标
            indicators = {}
            
            # 移动平均线
            indicators["ma5"] = self.calculator.sma(closes, 5)
            indicators["ma10"] = self.calculator.sma(closes, 10)
            indicators["ma20"] = self.calculator.sma(closes, 20)
            indicators["ma60"] = self.calculator.sma(closes, 60)
            
            # 指数移动平均线
            indicators["ema12"] = self.calculator.ema(closes, 12)
            indicators["ema26"] = self.calculator.ema(closes, 26)
            
            # RSI
            indicators["rsi"] = self.calculator.rsi(closes, 14)
            
            # MACD
            macd_result = self.calculator.macd(closes)
            indicators["macd"] = macd_result["macd"]
            indicators["macd_signal"] = macd_result["signal"]
            indicators["macd_histogram"] = macd_result["histogram"]
            
            # 布林带
            boll_result = self.calculator.bollinger_bands(closes)
            indicators["boll_upper"] = boll_result["upper"]
            indicators["boll_middle"] = boll_result["middle"]
            indicators["boll_lower"] = boll_result["lower"]
            
            # KDJ
            kdj_result = self.calculator.kdj(highs, lows, closes)
            indicators["kdj_k"] = kdj_result["k"]
            indicators["kdj_d"] = kdj_result["d"]
            indicators["kdj_j"] = kdj_result["j"]
            
            # 威廉指标
            indicators["williams_r"] = self.calculator.williams_r(highs, lows, closes)
            
            # 随机指标
            stoch_result = self.calculator.stochastic(highs, lows, closes)
            indicators["stoch_k"] = stoch_result["k"]
            indicators["stoch_d"] = stoch_result["d"]

            # CCI指标
            indicators["cci"] = self.calculator.cci(highs, lows, closes)

            # ATR指标
            indicators["atr"] = self.calculator.atr(highs, lows, closes)

            # OBV指标
            indicators["obv"] = self.calculator.obv(closes, volumes)

            # ADX指标
            adx_result = self.calculator.adx(highs, lows, closes)
            indicators["adx"] = adx_result["adx"]
            indicators["di_plus"] = adx_result["di_plus"]
            indicators["di_minus"] = adx_result["di_minus"]
            
            # 格式化结果
            result = {
                "stock_code": stock_code,
                "period": period,
                "data_count": len(klines),
                "calculated_at": datetime.now().isoformat(),
                "indicators": {}
            }
            
            # 将指标数据与日期对应
            for indicator_name, values in indicators.items():
                result["indicators"][indicator_name] = []
                for i, date in enumerate(dates):
                    if i < len(values):
                        value = values[i]
                        if not np.isnan(value):
                            result["indicators"][indicator_name].append({
                                "date": date.isoformat(),
                                "value": round(float(value), 4)
                            })
            
            logger.info(f"股票 {stock_code} 技术指标计算完成，共计算 {len(indicators)} 个指标")
            return result
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}
