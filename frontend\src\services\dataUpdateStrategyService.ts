/**
 * 数据更新策略管理服务
 * 提供数据更新策略相关的API调用
 */

import axios from 'axios'

const API_BASE_URL = 'http://localhost:8000/api/v1/data-update-strategy'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`[Data Update Strategy API] ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('[Data Update Strategy API Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('[Data Update Strategy API Response Error]', error)
    if (error.response?.status === 404) {
      throw new Error('请求的资源不存在')
    } else if (error.response?.status === 500) {
      throw new Error('服务器内部错误')
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('请求超时')
    }
    throw error
  }
)

export interface UpdateStrategy {
  strategy_id?: string
  strategy_name: string
  strategy_type: string
  data_source: string
  target_tables: string[]
  config: Record<string, any>
  is_active: boolean
  created_at?: string
  last_run?: string
  next_run?: string
}

export interface UpdateTask {
  task_id?: string
  strategy_id: string
  task_name: string
  status: string
  progress: number
  total_records: number
  processed_records: number
  error_message?: string
  started_at?: string
  completed_at?: string
}

export interface UpdateStatistics {
  total_strategies: number
  active_strategies: number
  total_tasks: number
  task_statistics: Record<string, number>
  recent_tasks_24h: number
  success_rate: number
}

export interface DataSources {
  data_sources: Record<string, any>
  update_strategies: Record<string, any>
}

class DataUpdateStrategyService {
  /**
   * 获取所有更新策略
   */
  async getStrategies(): Promise<UpdateStrategy[]> {
    try {
      const response = await apiClient.get('/strategies')
      return response.data
    } catch (error) {
      console.error('获取更新策略失败:', error)
      throw new Error('获取更新策略失败')
    }
  }

  /**
   * 创建新的更新策略
   */
  async createStrategy(strategy: UpdateStrategy): Promise<UpdateStrategy> {
    try {
      const response = await apiClient.post('/strategies', strategy)
      return response.data
    } catch (error) {
      console.error('创建更新策略失败:', error)
      throw new Error('创建更新策略失败')
    }
  }

  /**
   * 更新策略配置
   */
  async updateStrategy(strategyId: string, strategy: UpdateStrategy): Promise<UpdateStrategy> {
    try {
      const response = await apiClient.put(`/strategies/${strategyId}`, strategy)
      return response.data
    } catch (error) {
      console.error('更新策略失败:', error)
      throw new Error('更新策略失败')
    }
  }

  /**
   * 删除更新策略
   */
  async deleteStrategy(strategyId: string): Promise<void> {
    try {
      await apiClient.delete(`/strategies/${strategyId}`)
    } catch (error) {
      console.error('删除策略失败:', error)
      throw new Error('删除策略失败')
    }
  }

  /**
   * 执行更新策略
   */
  async executeStrategy(strategyId: string, force: boolean = false): Promise<{
    message: string
    task_id: string
    strategy_id: string
  }> {
    try {
      const response = await apiClient.post(`/strategies/${strategyId}/execute`, { force })
      return response.data
    } catch (error) {
      console.error('执行策略失败:', error)
      throw new Error('执行策略失败')
    }
  }

  /**
   * 获取更新任务列表
   */
  async getTasks(
    strategyId?: string,
    status?: string,
    limit: number = 50
  ): Promise<UpdateTask[]> {
    try {
      const params: any = { limit }
      if (strategyId) params.strategy_id = strategyId
      if (status) params.status = status

      const response = await apiClient.get('/tasks', { params })
      return response.data
    } catch (error) {
      console.error('获取更新任务失败:', error)
      throw new Error('获取更新任务失败')
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<UpdateTask> {
    try {
      const response = await apiClient.get(`/tasks/${taskId}`)
      return response.data
    } catch (error) {
      console.error('获取任务状态失败:', error)
      throw new Error('获取任务状态失败')
    }
  }

  /**
   * 取消更新任务
   */
  async cancelTask(taskId: string): Promise<void> {
    try {
      await apiClient.post(`/tasks/${taskId}/cancel`)
    } catch (error) {
      console.error('取消任务失败:', error)
      throw new Error('取消任务失败')
    }
  }

  /**
   * 获取可用的数据源
   */
  async getDataSources(): Promise<DataSources> {
    try {
      const response = await apiClient.get('/data-sources')
      return response.data
    } catch (error) {
      console.error('获取数据源失败:', error)
      throw new Error('获取数据源失败')
    }
  }

  /**
   * 获取更新统计信息
   */
  async getStatistics(): Promise<UpdateStatistics> {
    try {
      const response = await apiClient.get('/statistics')
      return response.data
    } catch (error) {
      console.error('获取更新统计失败:', error)
      throw new Error('获取更新统计失败')
    }
  }

  /**
   * 批量执行多个策略
   */
  async batchExecuteStrategies(strategyIds: string[]): Promise<Array<{
    strategy_id: string
    task_id?: string
    success: boolean
    error?: string
  }>> {
    try {
      const results = []
      
      for (const strategyId of strategyIds) {
        try {
          const result = await this.executeStrategy(strategyId)
          results.push({
            strategy_id: strategyId,
            task_id: result.task_id,
            success: true
          })
        } catch (error) {
          results.push({
            strategy_id: strategyId,
            success: false,
            error: error instanceof Error ? error.message : '执行失败'
          })
        }
      }
      
      return results
    } catch (error) {
      console.error('批量执行策略失败:', error)
      throw new Error('批量执行策略失败')
    }
  }

  /**
   * 获取策略执行历史
   */
  async getStrategyHistory(strategyId: string, limit: number = 20): Promise<UpdateTask[]> {
    try {
      return await this.getTasks(strategyId, undefined, limit)
    } catch (error) {
      console.error('获取策略历史失败:', error)
      throw new Error('获取策略历史失败')
    }
  }

  /**
   * 验证策略配置
   */
  async validateStrategyConfig(strategy: UpdateStrategy): Promise<{
    is_valid: boolean
    errors: string[]
    warnings: string[]
  }> {
    try {
      const errors: string[] = []
      const warnings: string[] = []

      // 基本验证
      if (!strategy.strategy_name?.trim()) {
        errors.push('策略名称不能为空')
      }

      if (!strategy.strategy_type) {
        errors.push('必须选择策略类型')
      }

      if (!strategy.data_source) {
        errors.push('必须选择数据源')
      }

      if (!strategy.target_tables || strategy.target_tables.length === 0) {
        errors.push('必须选择至少一个目标表')
      }

      // 策略类型特定验证
      if (strategy.strategy_type === 'scheduled') {
        const intervalHours = strategy.config?.interval_hours
        if (!intervalHours || intervalHours < 1 || intervalHours > 168) {
          errors.push('定时更新间隔必须在1-168小时之间')
        }

        if (intervalHours && intervalHours < 6) {
          warnings.push('更新间隔过短可能影响系统性能')
        }
      }

      if (strategy.strategy_type === 'incremental') {
        const checkInterval = strategy.config?.check_interval_minutes
        if (!checkInterval || checkInterval < 1) {
          errors.push('增量更新检查间隔必须大于0')
        }

        const batchSize = strategy.config?.batch_size
        if (!batchSize || batchSize < 100) {
          errors.push('批次大小必须至少为100')
        }
      }

      // 数据源验证
      if (strategy.data_source === 'akshare' && strategy.target_tables.length > 5) {
        warnings.push('AKShare数据源建议限制目标表数量以避免频率限制')
      }

      return {
        is_valid: errors.length === 0,
        errors,
        warnings
      }
    } catch (error) {
      console.error('验证策略配置失败:', error)
      throw new Error('验证策略配置失败')
    }
  }

  /**
   * 获取推荐的策略配置
   */
  async getRecommendedConfig(strategyType: string, dataSource: string): Promise<Record<string, any>> {
    try {
      const recommendations: Record<string, Record<string, any>> = {
        scheduled: {
          akshare: { interval_hours: 24, start_time: '09:00', end_time: '15:00', weekdays: [1, 2, 3, 4, 5] },
          tushare: { interval_hours: 12, start_time: '08:00', end_time: '16:00', weekdays: [1, 2, 3, 4, 5] },
          manual: { interval_hours: 168 }
        },
        incremental: {
          akshare: { check_interval_minutes: 60, batch_size: 500, max_records_per_batch: 2000 },
          tushare: { check_interval_minutes: 30, batch_size: 1000, max_records_per_batch: 5000 },
          manual: { check_interval_minutes: 120, batch_size: 1000, max_records_per_batch: 10000 }
        },
        on_demand: {
          akshare: { priority: 'normal', timeout_minutes: 30 },
          tushare: { priority: 'normal', timeout_minutes: 60 },
          manual: { priority: 'low', timeout_minutes: 120 }
        },
        real_time: {
          akshare: { retry_attempts: 3, buffer_size: 1000 },
          tushare: { retry_attempts: 5, buffer_size: 2000 },
          manual: { retry_attempts: 1, buffer_size: 100 }
        }
      }

      return recommendations[strategyType]?.[dataSource] || {}
    } catch (error) {
      console.error('获取推荐配置失败:', error)
      throw new Error('获取推荐配置失败')
    }
  }
}

export const dataUpdateStrategyService = new DataUpdateStrategyService()
export default dataUpdateStrategyService
