#!/usr/bin/env python3
"""
直接测试AKShare API调用
"""

import akshare as ak
import pandas as pd
import time

def test_akshare_spot_data():
    """测试AKShare实时行情数据接口"""
    print("🚀 开始测试AKShare实时行情数据接口...")
    
    try:
        # 调用AKShare接口获取实时行情数据
        print("📡 调用 ak.stock_zh_a_spot_em() ...")
        df = ak.stock_zh_a_spot_em()
        
        if df is not None and not df.empty:
            print(f"✅ 成功获取数据! 数据形状: {df.shape}")
            print(f"📊 列名: {list(df.columns)}")
            
            # 显示前3条数据
            print("\n📈 前3条数据:")
            print(df.head(3).to_string())
            
            # 检查数据类型
            print(f"\n🔍 数据类型:")
            for col in df.columns[:10]:  # 只显示前10列的类型
                print(f"  {col}: {df[col].dtype}")
                
            return True
        else:
            print("❌ 获取到空数据")
            return False
            
    except Exception as e:
        print(f"❌ AKShare API调用失败: {e}")
        return False

def test_akshare_hist_data():
    """测试AKShare历史K线数据接口"""
    print("\n🚀 开始测试AKShare历史K线数据接口...")
    
    try:
        # 测试获取平安银行的日线数据
        stock_code = "000001"
        print(f"📡 调用 ak.stock_zh_a_hist(symbol={stock_code}, period='daily') ...")
        
        df = ak.stock_zh_a_hist(
            symbol=stock_code,
            period="daily",
            start_date="20240701",
            end_date="20240801",
            adjust="qfq"
        )
        
        if df is not None and not df.empty:
            print(f"✅ 成功获取K线数据! 数据形状: {df.shape}")
            print(f"📊 列名: {list(df.columns)}")
            
            # 显示前3条数据
            print("\n📈 前3条K线数据:")
            print(df.head(3).to_string())
            
            return True
        else:
            print("❌ 获取到空K线数据")
            return False
            
    except Exception as e:
        print(f"❌ AKShare K线API调用失败: {e}")
        return False

def test_akshare_individual_info():
    """测试AKShare个股信息接口"""
    print("\n🚀 开始测试AKShare个股信息接口...")
    
    try:
        # 测试获取平安银行的个股信息
        stock_code = "000001"
        print(f"📡 调用 ak.stock_individual_info_em(symbol={stock_code}) ...")
        
        df = ak.stock_individual_info_em(symbol=stock_code)
        
        if df is not None and not df.empty:
            print(f"✅ 成功获取个股信息! 数据形状: {df.shape}")
            print(f"📊 列名: {list(df.columns)}")
            
            # 显示所有数据
            print("\n📈 个股信息:")
            print(df.to_string())
            
            return True
        else:
            print("❌ 获取到空个股信息")
            return False
            
    except Exception as e:
        print(f"❌ AKShare 个股信息API调用失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔬 AKShare API 直接测试")
    print("=" * 80)
    
    # 测试结果统计
    results = []
    
    # 1. 测试实时行情数据
    results.append(("实时行情数据", test_akshare_spot_data()))
    
    # 等待一下避免频率限制
    print("\n⏳ 等待3秒避免频率限制...")
    time.sleep(3)
    
    # 2. 测试历史K线数据
    results.append(("历史K线数据", test_akshare_hist_data()))
    
    # 等待一下避免频率限制
    print("\n⏳ 等待3秒避免频率限制...")
    time.sleep(3)
    
    # 3. 测试个股信息
    results.append(("个股信息", test_akshare_individual_info()))
    
    # 输出测试结果汇总
    print("\n" + "=" * 80)
    print("📊 测试结果汇总:")
    print("=" * 80)
    
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 个接口测试成功")
    
    if success_count == total_count:
        print("🎉 所有AKShare接口都可以正常使用!")
    elif success_count > 0:
        print("⚠️  部分AKShare接口可以使用，建议检查失败的接口")
    else:
        print("❌ 所有AKShare接口都无法使用，请检查网络连接和AKShare版本")

if __name__ == "__main__":
    main()
