import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Table,
  Space,
  Typography,
  Row,
  Col,
  Form,
  InputNumber,
  Select,
  Slider,
  Switch,
  Tag,
  message,
  Tabs,
  Statistic,
  Progress,
  Divider,
} from 'antd'
import {
  SearchOutlined,
  FilterOutlined,
  StarOutlined,
  TrophyOutlined,
  RocketOutlined,
  SafetyOutlined,
} from '@ant-design/icons'
import { screenerService } from '@/services/apiService'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface ScreenerFilter {
  market_cap_min?: number
  market_cap_max?: number
  pe_ratio_min?: number
  pe_ratio_max?: number
  pb_ratio_min?: number
  pb_ratio_max?: number
  roe_min?: number
  revenue_growth_min?: number
  profit_growth_min?: number
  debt_ratio_max?: number
  current_ratio_min?: number
  sectors?: string[]
  price_min?: number
  price_max?: number
  volume_min?: number
}

interface ScreenerResult {
  stock_code: string
  stock_name: string
  current_price: number
  change_percent: number
  market_cap: number
  pe_ratio: number
  pb_ratio: number
  roe: number
  revenue_growth: number
  profit_growth: number
  sector: string
  score: number
  rank: number
  reasons: string[]
}

interface Strategy {
  name: string
  display_name: string
  description: string
  risk_level: string
  expected_return: string
  filters: ScreenerFilter
}

const Screener: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<ScreenerResult[]>([])
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [selectedStrategy, setSelectedStrategy] = useState<string>('')
  const [customFilters, setCustomFilters] = useState<ScreenerFilter>({})

  useEffect(() => {
    loadStrategies()
  }, [])

  const loadStrategies = async () => {
    try {
      // 模拟策略数据
      const mockStrategies: Strategy[] = [
        {
          name: 'value_investing',
          display_name: '价值投资策略',
          description: '寻找被低估的优质股票，适合长期投资',
          risk_level: '低风险',
          expected_return: '8-15%',
          filters: {
            pe_ratio_max: 15,
            pb_ratio_max: 2,
            roe_min: 10,
            debt_ratio_max: 50,
            market_cap_min: 10000000000
          }
        },
        {
          name: 'growth_investing',
          display_name: '成长投资策略',
          description: '专注高成长性公司，追求资本增值',
          risk_level: '中等风险',
          expected_return: '15-25%',
          filters: {
            revenue_growth_min: 20,
            profit_growth_min: 15,
            roe_min: 15,
            pe_ratio_max: 30
          }
        },
        {
          name: 'dividend_strategy',
          display_name: '股息策略',
          description: '选择高股息率的稳定公司',
          risk_level: '低风险',
          expected_return: '6-12%',
          filters: {
            pe_ratio_max: 20,
            debt_ratio_max: 40,
            current_ratio_min: 1.5,
            market_cap_min: 5000000000
          }
        },
        {
          name: 'momentum_strategy',
          display_name: '动量策略',
          description: '捕捉市场热点和趋势',
          risk_level: '高风险',
          expected_return: '20-40%',
          filters: {
            volume_min: 100000000,
            revenue_growth_min: 10,
            profit_growth_min: 10
          }
        }
      ]
      setStrategies(mockStrategies)
    } catch (error) {
      message.error('加载策略失败')
    }
  }

  const handleStrategySelect = (strategyName: string) => {
    const strategy = strategies.find(s => s.name === strategyName)
    if (strategy) {
      setSelectedStrategy(strategyName)
      setCustomFilters(strategy.filters)
      form.setFieldsValue(strategy.filters)
    }
  }

  const handleRunScreener = async () => {
    setLoading(true)
    try {
      const filters = form.getFieldsValue()

      // 模拟选股结果
      const mockResults: ScreenerResult[] = [
        {
          stock_code: '000001',
          stock_name: '平安银行',
          current_price: 12.45,
          change_percent: 1.88,
          market_cap: 241000000000,
          pe_ratio: 5.2,
          pb_ratio: 0.8,
          roe: 12.5,
          revenue_growth: 8.5,
          profit_growth: 15.2,
          sector: '银行',
          score: 85,
          rank: 1,
          reasons: ['低估值', '高ROE', '稳定增长']
        },
        {
          stock_code: '600036',
          stock_name: '招商银行',
          current_price: 45.67,
          change_percent: 2.77,
          market_cap: 1200000000000,
          pe_ratio: 6.5,
          pb_ratio: 1.1,
          roe: 16.8,
          revenue_growth: 12.3,
          profit_growth: 18.7,
          sector: '银行',
          score: 92,
          rank: 2,
          reasons: ['优质银行', '高ROE', '强劲增长']
        },
        {
          stock_code: '000858',
          stock_name: '五粮液',
          current_price: 156.78,
          change_percent: -1.47,
          market_cap: 607000000000,
          pe_ratio: 28.5,
          pb_ratio: 4.2,
          roe: 22.1,
          revenue_growth: 15.6,
          profit_growth: 12.8,
          sector: '食品饮料',
          score: 78,
          rank: 3,
          reasons: ['品牌价值', '高毛利', '消费升级']
        }
      ]

      setResults(mockResults)
      message.success(`筛选完成，找到 ${mockResults.length} 只符合条件的股票`)
    } catch (error) {
      message.error('选股失败')
    } finally {
      setLoading(false)
    }
  }

  const resultColumns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      render: (rank: number) => (
        <div style={{ textAlign: 'center' }}>
          {rank <= 3 ? (
            <TrophyOutlined style={{ color: rank === 1 ? '#ffd700' : rank === 2 ? '#c0c0c0' : '#cd7f32' }} />
          ) : (
            <Text strong>{rank}</Text>
          )}
        </div>
      ),
    },
    {
      title: '股票',
      key: 'stock',
      render: (record: ScreenerResult) => (
        <Space>
          <Text strong>{record.stock_code}</Text>
          <Text>{record.stock_name}</Text>
        </Space>
      ),
    },
    {
      title: '现价',
      dataIndex: 'current_price',
      key: 'current_price',
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '涨跌幅',
      dataIndex: 'change_percent',
      key: 'change_percent',
      render: (value: number) => (
        <Text type={value >= 0 ? 'success' : 'danger'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Text>
      ),
    },
    {
      title: '市值(亿)',
      dataIndex: 'market_cap',
      key: 'market_cap',
      render: (value: number) => (value / 100000000).toFixed(0),
    },
    {
      title: 'PE',
      dataIndex: 'pe_ratio',
      key: 'pe_ratio',
      render: (value: number) => value.toFixed(1),
    },
    {
      title: 'PB',
      dataIndex: 'pb_ratio',
      key: 'pb_ratio',
      render: (value: number) => value.toFixed(1),
    },
    {
      title: 'ROE(%)',
      dataIndex: 'roe',
      key: 'roe',
      render: (value: number) => value.toFixed(1),
    },
    {
      title: '评分',
      dataIndex: 'score',
      key: 'score',
      render: (value: number) => (
        <Progress
          percent={value}
          size="small"
          strokeColor={value >= 80 ? '#52c41a' : value >= 60 ? '#faad14' : '#ff4d4f'}
          format={(percent) => `${percent}`}
        />
      ),
    },
    {
      title: '板块',
      dataIndex: 'sector',
      key: 'sector',
      render: (value: string) => <Tag>{value}</Tag>,
    },
    {
      title: '选股理由',
      dataIndex: 'reasons',
      key: 'reasons',
      render: (reasons: string[]) => (
        <Space wrap>
          {reasons.map((reason, index) => (
            <Tag key={index} color="blue" style={{ fontSize: 11 }}>
              {reason}
            </Tag>
          ))}
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <FilterOutlined /> 智能选股
        </Title>
        <Paragraph type="secondary">
          基于多维度指标筛选优质股票，提供专业的投资建议
        </Paragraph>
      </div>

      <Tabs defaultActiveKey="strategies">
        <TabPane tab="策略选股" key="strategies">
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            {strategies.map(strategy => (
              <Col span={6} key={strategy.name}>
                <Card
                  hoverable
                  style={{
                    border: selectedStrategy === strategy.name ? '2px solid #1890ff' : '1px solid #d9d9d9'
                  }}
                  onClick={() => handleStrategySelect(strategy.name)}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text strong>{strategy.display_name}</Text>
                      {strategy.risk_level === '低风险' && <SafetyOutlined style={{ color: '#52c41a' }} />}
                      {strategy.risk_level === '中等风险' && <StarOutlined style={{ color: '#faad14' }} />}
                      {strategy.risk_level === '高风险' && <RocketOutlined style={{ color: '#ff4d4f' }} />}
                    </div>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {strategy.description}
                    </Text>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Tag color={
                        strategy.risk_level === '低风险' ? 'green' :
                        strategy.risk_level === '中等风险' ? 'orange' : 'red'
                      }>
                        {strategy.risk_level}
                      </Tag>
                      <Text style={{ fontSize: 12 }}>预期收益: {strategy.expected_return}</Text>
                    </div>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="自定义筛选" key="custom">
          <Card title="筛选条件">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleRunScreener}
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="市值范围(亿元)">
                    <Row gutter={8}>
                      <Col span={12}>
                        <Form.Item name="market_cap_min" noStyle>
                          <InputNumber placeholder="最小值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item name="market_cap_max" noStyle>
                          <InputNumber placeholder="最大值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item label="市盈率(PE)">
                    <Row gutter={8}>
                      <Col span={12}>
                        <Form.Item name="pe_ratio_min" noStyle>
                          <InputNumber placeholder="最小值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item name="pe_ratio_max" noStyle>
                          <InputNumber placeholder="最大值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item label="市净率(PB)">
                    <Row gutter={8}>
                      <Col span={12}>
                        <Form.Item name="pb_ratio_min" noStyle>
                          <InputNumber placeholder="最小值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item name="pb_ratio_max" noStyle>
                          <InputNumber placeholder="最大值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item name="roe_min" label="ROE最小值(%)">
                    <InputNumber placeholder="如: 10" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item name="revenue_growth_min" label="营收增长率最小值(%)">
                    <InputNumber placeholder="如: 10" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item name="profit_growth_min" label="利润增长率最小值(%)">
                    <InputNumber placeholder="如: 15" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item name="sectors" label="行业板块">
                    <Select mode="multiple" placeholder="选择行业">
                      <Option value="银行">银行</Option>
                      <Option value="房地产">房地产</Option>
                      <Option value="食品饮料">食品饮料</Option>
                      <Option value="医药生物">医药生物</Option>
                      <Option value="电子">电子</Option>
                      <Option value="计算机">计算机</Option>
                      <Option value="汽车">汽车</Option>
                      <Option value="化工">化工</Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item label="股价范围(元)">
                    <Row gutter={8}>
                      <Col span={12}>
                        <Form.Item name="price_min" noStyle>
                          <InputNumber placeholder="最小值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item name="price_max" noStyle>
                          <InputNumber placeholder="最大值" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item name="volume_min" label="最小成交量">
                    <InputNumber placeholder="最小成交量" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Row justify="center">
                <Col>
                  <Space>
                    <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />}>
                      开始筛选
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      重置条件
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </Card>
        </TabPane>
      </Tabs>

      {results.length > 0 && (
        <Card title={`筛选结果 (${results.length}只股票)`} style={{ marginTop: 24 }}>
          <Table
            dataSource={results}
            columns={resultColumns}
            rowKey="stock_code"
            pagination={{ pageSize: 20 }}
            scroll={{ x: 1200 }}
          />
        </Card>
      )}
    </div>
  )
}

export default Screener
