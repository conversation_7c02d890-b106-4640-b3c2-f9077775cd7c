"""
用户认证相关API端点
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Form
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, EmailStr

from app.core.database import get_db
from app.core.logging import get_logger
from app.core.security import verify_token, PasswordValidator, SecurityUtils
from app.services.user_service import UserAuthService
from app.models.user import User

router = APIRouter()
logger = get_logger(__name__)
security = HTTPBearer()


class UserRegister(BaseModel):
    """用户注册请求模型"""
    username: Optional[str] = None
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    phone: Optional[str] = None


class UserLogin(BaseModel):
    """用户登录请求模型"""
    email: Optional[str] = None
    username: Optional[str] = None
    password: str


class TokenRefresh(BaseModel):
    """令牌刷新请求模型"""
    refresh_token: str


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """获取当前用户"""
    token = credentials.credentials
    user_id = verify_token(token)

    if not user_id:
        raise HTTPException(status_code=401, detail="无效的访问令牌")

    from sqlalchemy import select
    stmt = select(User).where(User.id == int(user_id))
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=401, detail="用户不存在")

    if not user.is_active:
        raise HTTPException(status_code=401, detail="用户已被禁用")

    return user


@router.post("/register")
async def register(user_data: UserRegister, request: Request):
    """用户注册"""
    try:
        # 获取客户端信息
        client_info = {
            "ip_address": request.client.host,
            "user_agent": request.headers.get("user-agent"),
            "device_info": request.headers.get("x-device-info")
        }

        # 合并用户数据和客户端信息
        registration_data = {**user_data.dict(), **client_info}

        async with UserAuthService() as auth_service:
            result = await auth_service.register_user(registration_data)

        if result["success"]:
            return {
                "code": 200,
                "message": result["message"],
                "data": {
                    "user_id": result["user_id"],
                    "username": result["username"],
                    "email": result["email"]
                }
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(status_code=500, detail="注册失败，请稍后重试")


@router.post("/login")
async def login(user_data: UserLogin, request: Request):
    """用户登录"""
    try:
        # 获取客户端信息
        client_info = {
            "ip_address": request.client.host,
            "user_agent": request.headers.get("user-agent"),
            "device_info": request.headers.get("x-device-info"),
            "location": request.headers.get("x-location")
        }

        # 合并登录数据和客户端信息
        login_data = {**user_data.dict(), **client_info}

        async with UserAuthService() as auth_service:
            result = await auth_service.authenticate_user(login_data)

        if result["success"]:
            return {
                "code": 200,
                "message": result["message"],
                "data": {
                    "access_token": result["access_token"],
                    "refresh_token": result["refresh_token"],
                    "token_type": result["token_type"],
                    "expires_in": result["expires_in"],
                    "user": result["user"]
                }
            }
        else:
            raise HTTPException(status_code=401, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(status_code=500, detail="登录失败，请稍后重试")


@router.post("/refresh")
async def refresh_token(token_data: TokenRefresh):
    """刷新访问令牌"""
    try:
        async with UserAuthService() as auth_service:
            result = await auth_service.refresh_token(token_data.refresh_token)

        if result["success"]:
            return {
                "code": 200,
                "message": "令牌刷新成功",
                "data": {
                    "access_token": result["access_token"],
                    "refresh_token": result["refresh_token"],
                    "token_type": result["token_type"],
                    "expires_in": result["expires_in"]
                }
            }
        else:
            raise HTTPException(status_code=401, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新令牌失败: {e}")
        raise HTTPException(status_code=500, detail="刷新令牌失败")


@router.post("/logout")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出"""
    try:
        access_token = credentials.credentials

        async with UserAuthService() as auth_service:
            result = await auth_service.logout_user(access_token)

        if result["success"]:
            return {
                "code": 200,
                "message": result["message"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登出失败: {e}")
        raise HTTPException(status_code=500, detail="登出失败")


@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    try:
        return {
            "code": 200,
            "message": "success",
            "data": {
                "id": current_user.id,
                "username": current_user.username,
                "email": SecurityUtils.mask_email(current_user.email),
                "full_name": current_user.full_name,
                "phone": SecurityUtils.mask_phone(current_user.phone) if current_user.phone else None,
                "avatar": current_user.avatar,
                "bio": current_user.bio,
                "location": current_user.location,
                "is_verified": current_user.is_verified,
                "email_verified": current_user.email_verified,
                "phone_verified": current_user.phone_verified,
                "is_premium": current_user.is_premium,
                "two_factor_enabled": current_user.two_factor_enabled,
                "api_calls_count": current_user.api_calls_count,
                "api_calls_limit": current_user.api_calls_limit,
                "created_at": current_user.created_at.isoformat(),
                "last_login": current_user.last_login.isoformat() if current_user.last_login else None
            }
        }
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户信息失败")


@router.post("/check-password")
async def check_password_strength(password: str = Form(...)):
    """检查密码强度"""
    try:
        strength_info = PasswordValidator.get_password_strength(password)
        is_valid, message = PasswordValidator.validate_password(password)

        return {
            "code": 200,
            "message": "success",
            "data": {
                "is_valid": is_valid,
                "validation_message": message,
                "strength": strength_info
            }
        }
    except Exception as e:
        logger.error(f"检查密码强度失败: {e}")
        raise HTTPException(status_code=500, detail="检查密码强度失败")


@router.get("/validate-email/{email}")
async def validate_email(email: str):
    """验证邮箱格式"""
    try:
        is_valid = SecurityUtils.validate_email(email)

        return {
            "code": 200,
            "message": "success",
            "data": {
                "email": email,
                "is_valid": is_valid,
                "masked_email": SecurityUtils.mask_email(email) if is_valid else None
            }
        }
    except Exception as e:
        logger.error(f"验证邮箱失败: {e}")
        raise HTTPException(status_code=500, detail="验证邮箱失败")


@router.get("/validate-username/{username}")
async def validate_username(username: str, db: AsyncSession = Depends(get_db)):
    """验证用户名是否可用"""
    try:
        from sqlalchemy import select

        # 检查用户名格式
        if len(username) < 3 or len(username) > 20:
            return {
                "code": 200,
                "message": "success",
                "data": {
                    "username": username,
                    "is_available": False,
                    "reason": "用户名长度必须在3-20个字符之间"
                }
            }

        # 检查用户名是否已存在
        stmt = select(User).where(User.username == username)
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()

        is_available = existing_user is None
        reason = None if is_available else "用户名已被使用"

        return {
            "code": 200,
            "message": "success",
            "data": {
                "username": username,
                "is_available": is_available,
                "reason": reason
            }
        }
    except Exception as e:
        logger.error(f"验证用户名失败: {e}")
        raise HTTPException(status_code=500, detail="验证用户名失败")
