#!/usr/bin/env python3
"""
重构后的数据库初始化脚本
创建新的数据库架构和表结构
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from loguru import logger

from app.core.config import settings
from app.models.stock_redesign import (
    Base, StockBasicInfo, StockKlineDaily, StockRealtimeData,
    StockFinancialData, DataUpdateLog, CustomIndicator, 
    StockPattern, BacktestStrategy
)
from app.services.akshare_data_manager import AKShareDataManager


async def create_database_tables():
    """创建数据库表"""
    try:
        logger.info("开始创建数据库表...")
        
        # 创建异步引擎
        engine = create_async_engine(
            settings.SQLALCHEMY_DATABASE_URI,
            echo=True,  # 显示SQL语句
            future=True
        )
        
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("✅ 数据库表创建成功")
        return engine
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        raise


async def initialize_sample_data(engine):
    """初始化示例数据"""
    try:
        logger.info("开始初始化示例数据...")
        
        # 创建会话
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as session:
            data_manager = AKShareDataManager(session)
            
            # 1. 更新股票基础信息（获取前50只股票）
            logger.info("1. 更新股票基础信息...")
            success_count, error_count = await data_manager.update_stock_basic_info()
            logger.info(f"股票基础信息更新完成: 成功 {success_count}, 失败 {error_count}")
            
            # 2. 获取前10只股票的K线数据
            logger.info("2. 更新K线数据...")
            stocks = await data_manager.get_stock_list(limit=10)
            
            for i, stock in enumerate(stocks[:10]):
                stock_code = stock['stock_code']
                logger.info(f"更新 {stock_code} ({stock['stock_name']}) K线数据... ({i+1}/10)")
                
                try:
                    success, error = await data_manager.update_kline_data(stock_code, days=30)
                    logger.info(f"{stock_code} K线更新: 成功 {success}, 失败 {error}")
                except Exception as e:
                    logger.error(f"{stock_code} K线更新失败: {e}")
                    continue
            
            # 3. 更新实时行情数据
            logger.info("3. 更新实时行情数据...")
            success_count, error_count = await data_manager.update_realtime_data()
            logger.info(f"实时行情更新完成: 成功 {success_count}, 失败 {error_count}")
            
            # 4. 创建示例自定义指标
            logger.info("4. 创建示例自定义指标...")
            await create_sample_indicators(session)
            
            # 5. 创建示例股票形态
            logger.info("5. 创建示例股票形态...")
            await create_sample_patterns(session)
            
            # 6. 创建示例回测策略
            logger.info("6. 创建示例回测策略...")
            await create_sample_strategies(session)
            
        logger.info("✅ 示例数据初始化完成")
        
    except Exception as e:
        logger.error(f"❌ 初始化示例数据失败: {e}")
        raise


async def create_sample_indicators(session: AsyncSession):
    """创建示例自定义指标"""
    try:
        indicators = [
            {
                "name": "自定义RSI",
                "description": "基于相对强弱指数的自定义指标",
                "formula": "RSI = 100 - (100 / (1 + RS))",
                "parameters": {"period": 14, "overbought": 70, "oversold": 30},
                "data_requirements": {"fields": ["close"], "min_periods": 14},
                "chart_config": {"type": "line", "range": [0, 100]},
                "color_scheme": {"line": "#1890ff", "overbought": "#ff4d4f", "oversold": "#52c41a"},
                "category": "momentum"
            },
            {
                "name": "动态支撑阻力",
                "description": "基于价格波动的动态支撑阻力位",
                "formula": "Support = MIN(low, period) * 0.98; Resistance = MAX(high, period) * 1.02",
                "parameters": {"period": 20, "support_factor": 0.98, "resistance_factor": 1.02},
                "data_requirements": {"fields": ["high", "low"], "min_periods": 20},
                "chart_config": {"type": "band", "opacity": 0.3},
                "color_scheme": {"support": "#52c41a", "resistance": "#ff4d4f"},
                "category": "support_resistance"
            }
        ]
        
        for indicator_data in indicators:
            indicator = CustomIndicator(**indicator_data)
            session.add(indicator)
        
        await session.commit()
        logger.info(f"创建了 {len(indicators)} 个示例自定义指标")
        
    except Exception as e:
        logger.error(f"创建示例指标失败: {e}")
        raise


async def create_sample_patterns(session: AsyncSession):
    """创建示例股票形态"""
    try:
        patterns = [
            {
                "pattern_name": "双底形态",
                "description": "价格形成两个相近的低点，预示反转上涨",
                "recognition_rules": {
                    "min_distance": 10,
                    "max_price_diff": 0.03,
                    "volume_confirmation": True
                },
                "time_window": 30,
                "confidence_threshold": 0.75,
                "signal_type": "buy",
                "success_rate": 0.68,
                "category": "reversal"
            },
            {
                "pattern_name": "突破形态",
                "description": "价格突破重要阻力位，预示继续上涨",
                "recognition_rules": {
                    "resistance_period": 20,
                    "breakout_volume": 1.5,
                    "confirmation_days": 3
                },
                "time_window": 25,
                "confidence_threshold": 0.80,
                "signal_type": "buy",
                "success_rate": 0.72,
                "category": "breakout"
            }
        ]
        
        for pattern_data in patterns:
            pattern = StockPattern(**pattern_data)
            session.add(pattern)
        
        await session.commit()
        logger.info(f"创建了 {len(patterns)} 个示例股票形态")
        
    except Exception as e:
        logger.error(f"创建示例形态失败: {e}")
        raise


async def create_sample_strategies(session: AsyncSession):
    """创建示例回测策略"""
    try:
        from decimal import Decimal
        
        strategies = [
            {
                "strategy_name": "均线金叉策略",
                "description": "基于5日和20日均线金叉的买入策略",
                "entry_rules": {
                    "ma5_cross_ma20": True,
                    "volume_increase": 1.2,
                    "rsi_range": [30, 70]
                },
                "exit_rules": {
                    "profit_target": 0.10,
                    "stop_loss": 0.05,
                    "ma5_cross_down_ma20": True
                },
                "risk_management": {
                    "max_position_size": 0.1,
                    "max_drawdown": 0.15,
                    "position_sizing": "fixed"
                },
                "initial_capital": Decimal("100000.00"),
                "commission_rate": Decimal("0.0003"),
                "slippage": Decimal("0.001"),
                "total_return": Decimal("0.1250"),
                "annual_return": Decimal("0.0850"),
                "max_drawdown": Decimal("0.0680"),
                "sharpe_ratio": Decimal("1.2500"),
                "win_rate": Decimal("0.6200")
            }
        ]
        
        for strategy_data in strategies:
            strategy = BacktestStrategy(**strategy_data)
            session.add(strategy)
        
        await session.commit()
        logger.info(f"创建了 {len(strategies)} 个示例回测策略")
        
    except Exception as e:
        logger.error(f"创建示例策略失败: {e}")
        raise


async def verify_database():
    """验证数据库初始化结果"""
    try:
        logger.info("开始验证数据库...")
        
        engine = create_async_engine(settings.SQLALCHEMY_DATABASE_URI)
        async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        
        async with async_session() as session:
            data_manager = AKShareDataManager(session)
            
            # 检查股票数量
            stocks = await data_manager.get_stock_list(limit=10)
            logger.info(f"✅ 股票基础信息: {len(stocks)} 条记录")
            
            # 检查K线数据
            if stocks:
                kline_data = await data_manager.get_kline_data(stocks[0]['stock_code'], days=5)
                logger.info(f"✅ K线数据示例: {len(kline_data)} 条记录")
            
            # 检查实时数据
            if stocks:
                realtime_data = await data_manager.get_realtime_data(stocks[0]['stock_code'])
                logger.info(f"✅ 实时数据: {'有数据' if realtime_data else '无数据'}")
            
            # 检查自定义指标
            from sqlalchemy import select, func
            result = await session.execute(select(func.count(CustomIndicator.id)))
            indicator_count = result.scalar()
            logger.info(f"✅ 自定义指标: {indicator_count} 个")
            
            # 检查股票形态
            result = await session.execute(select(func.count(StockPattern.id)))
            pattern_count = result.scalar()
            logger.info(f"✅ 股票形态: {pattern_count} 个")
            
            # 检查回测策略
            result = await session.execute(select(func.count(BacktestStrategy.id)))
            strategy_count = result.scalar()
            logger.info(f"✅ 回测策略: {strategy_count} 个")
        
        logger.info("✅ 数据库验证完成")
        
    except Exception as e:
        logger.error(f"❌ 数据库验证失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        logger.info("🚀 开始初始化重构后的股票数据库...")
        
        # 1. 创建数据库表
        engine = await create_database_tables()
        
        # 2. 初始化示例数据
        await initialize_sample_data(engine)
        
        # 3. 验证数据库
        await verify_database()
        
        logger.info("🎉 股票数据库初始化完成!")
        
    except Exception as e:
        logger.error(f"💥 数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
