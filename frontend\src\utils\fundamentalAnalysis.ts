/**
 * 基本面分析系统
 * 实现专业的财务质量评估和估值模型
 */

export interface FinancialData {
  // 资产负债表数据
  totalAssets: number
  totalLiabilities: number
  shareholdersEquity: number
  currentAssets: number
  currentLiabilities: number
  cash: number
  inventory: number
  accountsReceivable: number
  longTermDebt: number
  
  // 利润表数据
  revenue: number
  grossProfit: number
  operatingIncome: number
  netIncome: number
  ebitda: number
  interestExpense: number
  
  // 现金流量表数据
  operatingCashFlow: number
  investingCashFlow: number
  financingCashFlow: number
  freeCashFlow: number
  
  // 市场数据
  marketCap: number
  sharesOutstanding: number
  stockPrice: number
  
  // 时间标识
  period: string
  year: number
  quarter?: number
}

export interface FinancialQuality {
  pitroskiScore: number
  altmanZScore: number
  benishMScore: number
  cashConversionCycle: number
  earningsQuality: number
  debtToEquity: number
  currentRatio: number
  quickRatio: number
  roaQuality: number
  roeQuality: number
}

export interface ValuationMetrics {
  pe: number
  pb: number
  ps: number
  peg: number
  ev: number
  evEbitda: number
  evRevenue: number
  priceToFreeCashFlow: number
  dividendYield: number
  bookValuePerShare: number
}

export interface DCFValuation {
  intrinsicValue: number
  currentPrice: number
  upside: number
  wacc: number
  terminalGrowthRate: number
  projectedCashFlows: number[]
  terminalValue: number
  presentValue: number
}

export interface ComparableAnalysis {
  industryAvgPE: number
  industryAvgPB: number
  industryAvgROE: number
  industryAvgROA: number
  relativeValuation: 'undervalued' | 'fairly_valued' | 'overvalued'
  percentileRanking: number
}

/**
 * 基本面分析计算器
 */
export class FundamentalAnalysis {

  /**
   * 计算Piotroski F-Score
   * 评估公司财务健康状况的9项指标
   */
  static calculatePitroskiScore(data: FinancialData, prevData?: FinancialData): number {
    let score = 0
    
    // 盈利能力指标 (4分)
    if (data.netIncome > 0) score += 1 // 正净利润
    if (data.operatingCashFlow > 0) score += 1 // 正经营现金流
    if (data.operatingCashFlow > data.netIncome) score += 1 // 经营现金流 > 净利润
    
    if (prevData) {
      const currentROA = data.netIncome / data.totalAssets
      const prevROA = prevData.netIncome / prevData.totalAssets
      if (currentROA > prevROA) score += 1 // ROA改善
    }
    
    // 杠杆、流动性和资金来源指标 (3分)
    if (prevData) {
      const currentDebtRatio = data.longTermDebt / data.totalAssets
      const prevDebtRatio = prevData.longTermDebt / prevData.totalAssets
      if (currentDebtRatio < prevDebtRatio) score += 1 // 杠杆率下降
      
      const currentRatio = data.currentAssets / data.currentLiabilities
      const prevCurrentRatio = prevData.currentAssets / prevData.currentLiabilities
      if (currentRatio > prevCurrentRatio) score += 1 // 流动比率改善
      
      if (data.sharesOutstanding <= prevData.sharesOutstanding) score += 1 // 股本未稀释
    }
    
    // 运营效率指标 (2分)
    if (prevData) {
      const currentGrossMargin = data.grossProfit / data.revenue
      const prevGrossMargin = prevData.grossProfit / prevData.revenue
      if (currentGrossMargin > prevGrossMargin) score += 1 // 毛利率改善
      
      const currentAssetTurnover = data.revenue / data.totalAssets
      const prevAssetTurnover = prevData.revenue / prevData.totalAssets
      if (currentAssetTurnover > prevAssetTurnover) score += 1 // 资产周转率改善
    }
    
    return score
  }

  /**
   * 计算Altman Z-Score
   * 预测企业破产风险
   */
  static calculateAltmanZScore(data: FinancialData): number {
    const workingCapital = data.currentAssets - data.currentLiabilities
    const retainedEarnings = data.shareholdersEquity - (data.sharesOutstanding * 10) // 假设面值10元
    
    const z1 = (workingCapital / data.totalAssets) * 1.2
    const z2 = (retainedEarnings / data.totalAssets) * 1.4
    const z3 = (data.ebitda / data.totalAssets) * 3.3
    const z4 = (data.marketCap / data.totalLiabilities) * 0.6
    const z5 = (data.revenue / data.totalAssets) * 1.0
    
    return z1 + z2 + z3 + z4 + z5
  }

  /**
   * 计算Beneish M-Score
   * 检测财务造假风险
   */
  static calculateBenishMScore(data: FinancialData, prevData: FinancialData): number {
    // 应收账款指数
    const dsr = (data.accountsReceivable / data.revenue) / (prevData.accountsReceivable / prevData.revenue)
    
    // 毛利率指数
    const gmi = (prevData.grossProfit / prevData.revenue) / (data.grossProfit / data.revenue)
    
    // 资产质量指数
    const aqi = (data.totalAssets - data.currentAssets - data.cash) / data.totalAssets
    
    // 销售增长指数
    const sgi = data.revenue / prevData.revenue
    
    // 折旧指数 (简化计算)
    const depi = 1.0 // 需要更详细的折旧数据
    
    // 销售管理费用指数 (简化)
    const sgai = 1.0 // 需要更详细的费用数据
    
    // 杠杆指数
    const lvgi = (data.totalLiabilities / data.totalAssets) / (prevData.totalLiabilities / prevData.totalAssets)
    
    // 总应计项目与总资产比
    const tata = (data.netIncome - data.operatingCashFlow) / data.totalAssets
    
    const mScore = -4.84 + 0.92 * dsr + 0.528 * gmi + 0.404 * aqi + 
                   0.892 * sgi + 0.115 * depi - 0.172 * sgai + 
                   4.679 * tata - 0.327 * lvgi
    
    return mScore
  }

  /**
   * 计算现金转换周期
   */
  static calculateCashConversionCycle(data: FinancialData): number {
    const daysInYear = 365
    
    // 存货周转天数
    const inventoryDays = (data.inventory / data.revenue) * daysInYear
    
    // 应收账款周转天数
    const receivablesDays = (data.accountsReceivable / data.revenue) * daysInYear
    
    // 应付账款周转天数 (简化计算)
    const payablesDays = (data.currentLiabilities * 0.6 / data.revenue) * daysInYear
    
    return inventoryDays + receivablesDays - payablesDays
  }

  /**
   * 计算估值指标
   */
  static calculateValuationMetrics(data: FinancialData): ValuationMetrics {
    const pe = data.stockPrice / (data.netIncome / data.sharesOutstanding)
    const pb = data.stockPrice / (data.shareholdersEquity / data.sharesOutstanding)
    const ps = data.marketCap / data.revenue
    
    // 简化的PEG计算 (需要增长率数据)
    const estimatedGrowthRate = 0.15 // 假设15%增长率
    const peg = pe / (estimatedGrowthRate * 100)
    
    const ev = data.marketCap + data.longTermDebt - data.cash
    const evEbitda = ev / data.ebitda
    const evRevenue = ev / data.revenue
    
    const priceToFreeCashFlow = data.marketCap / data.freeCashFlow
    const dividendYield = 0.02 // 假设2%股息率
    const bookValuePerShare = data.shareholdersEquity / data.sharesOutstanding
    
    return {
      pe,
      pb,
      ps,
      peg,
      ev,
      evEbitda,
      evRevenue,
      priceToFreeCashFlow,
      dividendYield,
      bookValuePerShare
    }
  }

  /**
   * DCF估值模型
   */
  static calculateDCFValuation(
    data: FinancialData,
    growthRates: number[],
    terminalGrowthRate: number = 0.03,
    wacc: number = 0.10
  ): DCFValuation {
    const projectedCashFlows: number[] = []
    let currentFCF = data.freeCashFlow
    
    // 预测未来现金流
    for (let i = 0; i < growthRates.length; i++) {
      currentFCF = currentFCF * (1 + growthRates[i])
      projectedCashFlows.push(currentFCF)
    }
    
    // 计算终值
    const terminalFCF = currentFCF * (1 + terminalGrowthRate)
    const terminalValue = terminalFCF / (wacc - terminalGrowthRate)
    
    // 计算现值
    let presentValue = 0
    for (let i = 0; i < projectedCashFlows.length; i++) {
      presentValue += projectedCashFlows[i] / Math.pow(1 + wacc, i + 1)
    }
    
    const terminalPresentValue = terminalValue / Math.pow(1 + wacc, projectedCashFlows.length)
    const totalPresentValue = presentValue + terminalPresentValue
    
    const intrinsicValue = (totalPresentValue - data.longTermDebt + data.cash) / data.sharesOutstanding
    const upside = (intrinsicValue - data.stockPrice) / data.stockPrice
    
    return {
      intrinsicValue,
      currentPrice: data.stockPrice,
      upside,
      wacc,
      terminalGrowthRate,
      projectedCashFlows,
      terminalValue,
      presentValue: totalPresentValue
    }
  }

  /**
   * 综合财务质量评估
   */
  static assessFinancialQuality(data: FinancialData, prevData?: FinancialData): FinancialQuality {
    const pitroskiScore = prevData ? this.calculatePitroskiScore(data, prevData) : 0
    const altmanZScore = this.calculateAltmanZScore(data)
    const benishMScore = prevData ? this.calculateBenishMScore(data, prevData) : 0
    const cashConversionCycle = this.calculateCashConversionCycle(data)
    
    // 盈利质量评估
    const earningsQuality = data.operatingCashFlow / data.netIncome
    
    // 财务比率
    const debtToEquity = data.totalLiabilities / data.shareholdersEquity
    const currentRatio = data.currentAssets / data.currentLiabilities
    const quickRatio = (data.currentAssets - data.inventory) / data.currentLiabilities
    
    // ROA和ROE质量
    const roaQuality = data.operatingCashFlow / data.totalAssets
    const roeQuality = data.operatingCashFlow / data.shareholdersEquity
    
    return {
      pitroskiScore,
      altmanZScore,
      benishMScore,
      cashConversionCycle,
      earningsQuality,
      debtToEquity,
      currentRatio,
      quickRatio,
      roaQuality,
      roeQuality
    }
  }

  /**
   * 行业对比分析
   */
  static performComparableAnalysis(
    data: FinancialData,
    industryData: FinancialData[]
  ): ComparableAnalysis {
    const valuationMetrics = this.calculateValuationMetrics(data)
    
    // 计算行业平均值
    const industryAvgPE = industryData.reduce((sum, company) => {
      const pe = company.stockPrice / (company.netIncome / company.sharesOutstanding)
      return sum + pe
    }, 0) / industryData.length
    
    const industryAvgPB = industryData.reduce((sum, company) => {
      const pb = company.stockPrice / (company.shareholdersEquity / company.sharesOutstanding)
      return sum + pb
    }, 0) / industryData.length
    
    const industryAvgROE = industryData.reduce((sum, company) => {
      const roe = company.netIncome / company.shareholdersEquity
      return sum + roe
    }, 0) / industryData.length
    
    const industryAvgROA = industryData.reduce((sum, company) => {
      const roa = company.netIncome / company.totalAssets
      return sum + roa
    }, 0) / industryData.length
    
    // 相对估值判断
    let relativeValuation: 'undervalued' | 'fairly_valued' | 'overvalued' = 'fairly_valued'
    if (valuationMetrics.pe < industryAvgPE * 0.8 && valuationMetrics.pb < industryAvgPB * 0.8) {
      relativeValuation = 'undervalued'
    } else if (valuationMetrics.pe > industryAvgPE * 1.2 && valuationMetrics.pb > industryAvgPB * 1.2) {
      relativeValuation = 'overvalued'
    }
    
    // 百分位排名 (简化计算)
    const percentileRanking = Math.random() * 100 // 实际应基于真实排名计算
    
    return {
      industryAvgPE,
      industryAvgPB,
      industryAvgROE,
      industryAvgROA,
      relativeValuation,
      percentileRanking
    }
  }
}

export default FundamentalAnalysis
