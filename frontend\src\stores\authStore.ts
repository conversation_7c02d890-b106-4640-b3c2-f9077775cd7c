import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, authService } from '@/services/authService'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  loading: boolean
  
  // Actions
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  setLoading: (loading: boolean) => void
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  refreshUser: () => Promise<void>
  clearAuth: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,

      setUser: (user) => {
        set({ 
          user, 
          isAuthenticated: !!user 
        })
        if (user) {
          authService.saveUser(user)
        }
      },

      setToken: (token) => {
        set({ 
          token, 
          isAuthenticated: !!token 
        })
        if (token) {
          localStorage.setItem('access_token', token)
        } else {
          localStorage.removeItem('access_token')
        }
      },

      setLoading: (loading) => {
        set({ loading })
      },

      login: async (email: string, password: string) => {
        set({ loading: true })
        try {
          const response = await authService.login({ email, password })
          if (response.code === 200) {
            const { access_token, refresh_token, user } = response.data
            
            // 保存tokens
            authService.saveTokens(access_token, refresh_token)
            
            // 更新状态
            set({
              user,
              token: access_token,
              isAuthenticated: true,
              loading: false
            })
            
            return true
          }
          return false
        } catch (error) {
          set({ loading: false })
          return false
        }
      },

      logout: async () => {
        try {
          await authService.logout()
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          // 清除本地状态
          authService.clearStorage()
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            loading: false
          })
        }
      },

      refreshUser: async () => {
        try {
          const response = await authService.getCurrentUser()
          if (response.code === 200) {
            set({ user: response.data })
            authService.saveUser(response.data)
          }
        } catch (error) {
          console.error('Refresh user error:', error)
          // 如果获取用户信息失败，可能token已过期
          get().logout()
        }
      },

      clearAuth: () => {
        authService.clearStorage()
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          loading: false
        })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // 从localStorage恢复状态后的处理
        if (state) {
          const token = authService.getAccessToken()
          const user = authService.getUser()
          
          if (token && user) {
            state.token = token
            state.user = user
            state.isAuthenticated = true
          } else {
            state.clearAuth()
          }
        }
      },
    }
  )
)
