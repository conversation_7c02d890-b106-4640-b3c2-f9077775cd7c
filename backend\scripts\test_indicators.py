#!/usr/bin/env python3
"""
技术指标计算功能测试脚本
"""

import asyncio
import sys
import os
from datetime import date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.logging import setup_logging, get_logger
from app.services.technical_indicators import TechnicalIndicators, IndicatorCalculationService
from app.services.indicator_storage import IndicatorStorageService
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


async def test_basic_indicators():
    """测试基础指标计算"""
    logger.info("=== 测试基础指标计算 ===")
    
    # 模拟价格数据
    test_prices = [10.0, 10.5, 11.0, 10.8, 11.2, 11.5, 11.3, 12.0, 11.8, 12.2, 12.5, 12.3, 12.8, 13.0, 12.7]
    test_highs = [p + 0.2 for p in test_prices]
    test_lows = [p - 0.2 for p in test_prices]
    test_volumes = [1000000 + i * 10000 for i in range(len(test_prices))]
    
    calculator = TechnicalIndicators()
    
    # 测试SMA
    sma5 = calculator.sma(test_prices, 5)
    logger.info(f"SMA5 计算结果: {[round(x, 4) if not pd.isna(x) else None for x in sma5[-5:]]}")
    
    # 测试EMA
    ema5 = calculator.ema(test_prices, 5)
    logger.info(f"EMA5 计算结果: {[round(x, 4) if not pd.isna(x) else None for x in ema5[-5:]]}")
    
    # 测试RSI
    rsi = calculator.rsi(test_prices, 14)
    logger.info(f"RSI 计算结果: {[round(x, 4) if not pd.isna(x) else None for x in rsi[-3:]]}")
    
    # 测试MACD
    macd_result = calculator.macd(test_prices)
    logger.info(f"MACD 计算结果: MACD={round(macd_result['macd'][-1], 6) if macd_result['macd'] and not pd.isna(macd_result['macd'][-1]) else None}")
    
    # 测试布林带
    boll_result = calculator.bollinger_bands(test_prices, 10)
    if boll_result['middle']:
        logger.info(f"布林带计算结果: 上轨={round(boll_result['upper'][-1], 4) if not pd.isna(boll_result['upper'][-1]) else None}")
    
    # 测试KDJ
    kdj_result = calculator.kdj(test_highs, test_lows, test_prices, 9)
    if kdj_result['k']:
        logger.info(f"KDJ 计算结果: K={round(kdj_result['k'][-1], 4) if not pd.isna(kdj_result['k'][-1]) else None}")
    
    logger.info("✅ 基础指标计算测试完成")


async def test_indicator_calculation_service():
    """测试指标计算服务"""
    logger.info("=== 测试指标计算服务 ===")
    
    # 首先确保有测试数据
    test_stock = "000001"
    
    # 检查是否有K线数据
    async with DataStorageService() as storage:
        klines = await storage.get_kline_data(test_stock, "daily", limit=10)
    
    if not klines:
        logger.warning(f"股票 {test_stock} 没有K线数据，跳过指标计算测试")
        return
    
    # 测试指标计算服务
    calculation_service = IndicatorCalculationService()
    
    try:
        indicators_data = await calculation_service.calculate_all_indicators(test_stock, "daily", 100)
        
        if indicators_data:
            logger.info(f"✅ 成功计算股票 {test_stock} 的技术指标")
            logger.info(f"数据量: {indicators_data.get('data_count', 0)} 条")
            logger.info(f"指标数量: {len(indicators_data.get('indicators', {}))}")
            
            # 显示部分指标
            indicators = indicators_data.get("indicators", {})
            for indicator_name in ["ma5", "ma20", "rsi", "macd"]:
                if indicator_name in indicators and indicators[indicator_name]:
                    latest_value = indicators[indicator_name][-1]
                    logger.info(f"{indicator_name.upper()}: {latest_value['date']} = {latest_value['value']}")
        else:
            logger.warning("指标计算返回空结果")
            
    except Exception as e:
        logger.error(f"指标计算服务测试失败: {e}")


async def test_indicator_storage():
    """测试指标存储服务"""
    logger.info("=== 测试指标存储服务 ===")
    
    test_stock = "000001"
    
    # 先计算指标
    calculation_service = IndicatorCalculationService()
    
    try:
        indicators_data = await calculation_service.calculate_all_indicators(test_stock, "daily", 50)
        
        if not indicators_data:
            logger.warning("没有指标数据可以存储")
            return
        
        # 测试存储
        async with IndicatorStorageService() as storage:
            saved_count = await storage.save_indicators(test_stock, indicators_data)
            logger.info(f"✅ 成功保存 {saved_count} 条指标数据")
            
            # 测试查询
            stored_indicators = await storage.get_indicators(test_stock, "daily", limit=10)
            logger.info(f"✅ 成功查询到 {len(stored_indicators)} 条指标数据")
            
            if stored_indicators:
                latest = stored_indicators[-1]
                logger.info(f"最新指标数据: {latest.trade_date}")
                logger.info(f"  MA5: {latest.ma5}")
                logger.info(f"  RSI: {latest.rsi}")
                logger.info(f"  MACD: {latest.macd}")
            
            # 测试统计信息
            stats = await storage.get_indicator_statistics(test_stock)
            logger.info(f"✅ 指标统计信息: {stats}")
            
    except Exception as e:
        logger.error(f"指标存储测试失败: {e}")


async def test_indicator_api_simulation():
    """模拟API调用测试"""
    logger.info("=== 模拟API调用测试 ===")
    
    test_stock = "000001"
    
    try:
        # 模拟计算指标API调用
        calculation_service = IndicatorCalculationService()
        indicators_data = await calculation_service.calculate_all_indicators(test_stock, "daily", 100)
        
        if indicators_data:
            # 模拟保存
            async with IndicatorStorageService() as storage:
                await storage.save_indicators(test_stock, indicators_data)
                
                # 模拟查询MA指标
                indicators = await storage.get_indicators(test_stock, "daily", limit=20)
                ma20_data = []
                for indicator in indicators:
                    if indicator.ma20 is not None:
                        ma20_data.append({
                            "date": indicator.trade_date.isoformat(),
                            "value": round(float(indicator.ma20), 4)
                        })
                
                logger.info(f"✅ MA20 数据查询成功，共 {len(ma20_data)} 条")
                if ma20_data:
                    logger.info(f"最新MA20: {ma20_data[-1]}")
                
                # 模拟查询RSI指标
                rsi_data = []
                for indicator in indicators:
                    if indicator.rsi is not None:
                        rsi_data.append({
                            "date": indicator.trade_date.isoformat(),
                            "value": round(float(indicator.rsi), 4)
                        })
                
                logger.info(f"✅ RSI 数据查询成功，共 {len(rsi_data)} 条")
                if rsi_data:
                    logger.info(f"最新RSI: {rsi_data[-1]}")
        
    except Exception as e:
        logger.error(f"API模拟测试失败: {e}")


async def test_performance():
    """测试性能"""
    logger.info("=== 性能测试 ===")
    
    import time
    
    # 测试大量数据的指标计算性能
    large_data = list(range(1000))
    large_data = [10 + (i % 100) * 0.1 for i in large_data]  # 模拟价格数据
    
    calculator = TechnicalIndicators()
    
    start_time = time.time()
    
    # 计算多个指标
    sma20 = calculator.sma(large_data, 20)
    ema12 = calculator.ema(large_data, 12)
    rsi = calculator.rsi(large_data, 14)
    macd_result = calculator.macd(large_data)
    
    end_time = time.time()
    
    logger.info(f"✅ 1000条数据计算4个指标耗时: {end_time - start_time:.4f} 秒")
    logger.info(f"SMA20 有效值: {len([x for x in sma20 if not pd.isna(x)])}")
    logger.info(f"RSI 有效值: {len([x for x in rsi if not pd.isna(x)])}")


async def main():
    """主测试函数"""
    setup_logging()
    logger.info("🧮 开始技术指标计算功能测试")
    
    try:
        # 导入pandas用于NaN检查
        global pd
        import pandas as pd
        
        # 1. 测试基础指标计算
        await test_basic_indicators()
        
        # 2. 测试指标计算服务
        await test_indicator_calculation_service()
        
        # 3. 测试指标存储服务
        await test_indicator_storage()
        
        # 4. 模拟API调用测试
        await test_indicator_api_simulation()
        
        # 5. 性能测试
        await test_performance()
        
        logger.info("✅ 所有技术指标测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
