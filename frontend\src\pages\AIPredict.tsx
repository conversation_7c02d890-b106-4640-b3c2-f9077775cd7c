import React, { useState, useEffect } from 'react'
import {
  Card,
  Input,
  Button,
  Table,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Progress,
  Select,
  message,
  Tabs,
  List,
  Avatar,
} from 'antd'
import {
  SearchOutlined,
  RobotOutlined,
  <PERSON>UpOutlined,
  <PERSON>DownOutlined,
  <PERSON><PERSON>Outlined,
  BulbOutlined,
  WarningOutlined,
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface AIPrediction {
  id: number
  stock_code: string
  stock_name: string
  prediction_type: string
  trend_direction: 'up' | 'down' | 'sideways'
  confidence_score: number
  target_price: number
  current_price: number
  time_horizon: number
  predicted_return: number
  risk_level: 'low' | 'medium' | 'high'
  created_at: string
  reasoning: string[]
}

interface AISignal {
  id: number
  stock_code: string
  stock_name: string
  signal_type: 'buy' | 'sell' | 'hold'
  strength: number
  confidence: number
  factors: string[]
  generated_at: string
}

const AIPredict: React.FC = () => {
  const [searchValue, setSearchValue] = useState('')
  const [predictions, setPredictions] = useState<AIPrediction[]>([])
  const [signals, setSignals] = useState<AISignal[]>([])
  const [loading, setLoading] = useState(false)
  const [generateLoading, setGenerateLoading] = useState(false)
  const [selectedPrediction, setSelectedPrediction] = useState<AIPrediction | null>(null)

  useEffect(() => {
    loadPredictions()
    loadSignals()
  }, [])

  const loadPredictions = async () => {
    setLoading(true)
    try {
      // 模拟AI预测数据
      const mockPredictions: AIPrediction[] = [
        {
          id: 1,
          stock_code: '000001',
          stock_name: '平安银行',
          prediction_type: 'price_target',
          trend_direction: 'up',
          confidence_score: 0.85,
          target_price: 13.50,
          current_price: 12.45,
          time_horizon: 30,
          predicted_return: 8.43,
          risk_level: 'medium',
          created_at: '2025-07-27 10:30:00',
          reasoning: [
            '技术指标显示突破关键阻力位',
            '基本面改善，ROE持续提升',
            '行业景气度回升，估值修复空间大',
            '资金流入明显，机构持仓增加'
          ]
        },
        {
          id: 2,
          stock_code: '600036',
          stock_name: '招商银行',
          prediction_type: 'trend_analysis',
          trend_direction: 'up',
          confidence_score: 0.92,
          target_price: 48.50,
          current_price: 45.67,
          time_horizon: 60,
          predicted_return: 6.20,
          risk_level: 'low',
          created_at: '2025-07-27 09:15:00',
          reasoning: [
            '财报超预期，净利润增长稳健',
            '息差企稳回升，资产质量优良',
            '数字化转型成效显著',
            '分红政策稳定，投资价值凸显'
          ]
        },
        {
          id: 3,
          stock_code: '000002',
          stock_name: '万科A',
          prediction_type: 'risk_assessment',
          trend_direction: 'down',
          confidence_score: 0.78,
          target_price: 16.80,
          current_price: 18.67,
          time_horizon: 45,
          predicted_return: -10.02,
          risk_level: 'high',
          created_at: '2025-07-27 08:45:00',
          reasoning: [
            '房地产政策收紧，销售压力增大',
            '债务负担较重，现金流紧张',
            '行业整体下行，估值承压',
            '技术面显示下跌趋势确立'
          ]
        }
      ]
      setPredictions(mockPredictions)
    } catch (error) {
      message.error('加载AI预测失败')
    } finally {
      setLoading(false)
    }
  }

  const loadSignals = async () => {
    try {
      // 模拟AI信号数据
      const mockSignals: AISignal[] = [
        {
          id: 1,
          stock_code: '000858',
          stock_name: '五粮液',
          signal_type: 'buy',
          strength: 85,
          confidence: 78,
          factors: ['技术突破', '基本面改善', '资金流入'],
          generated_at: '2025-07-27 11:20:00'
        },
        {
          id: 2,
          stock_code: '600000',
          stock_name: '浦发银行',
          signal_type: 'hold',
          strength: 65,
          confidence: 72,
          factors: ['震荡整理', '等待催化剂', '风险可控'],
          generated_at: '2025-07-27 10:45:00'
        },
        {
          id: 3,
          stock_code: '000001',
          stock_name: '平安银行',
          signal_type: 'buy',
          strength: 78,
          confidence: 85,
          factors: ['估值修复', '业绩预期', '政策利好'],
          generated_at: '2025-07-27 09:30:00'
        }
      ]
      setSignals(mockSignals)
    } catch (error) {
      message.error('加载AI信号失败')
    }
  }

  const handleGeneratePrediction = async () => {
    if (!searchValue.trim()) {
      message.warning('请输入股票代码')
      return
    }

    setGenerateLoading(true)
    try {
      // 模拟生成预测
      await new Promise(resolve => setTimeout(resolve, 2000))
      message.success('AI预测生成成功')
      loadPredictions()
    } catch (error) {
      message.error('生成预测失败')
    } finally {
      setGenerateLoading(false)
    }
  }

  const predictionColumns = [
    {
      title: '股票',
      key: 'stock',
      render: (record: AIPrediction) => (
        <Space>
          <Text strong>{record.stock_code}</Text>
          <Text>{record.stock_name}</Text>
        </Space>
      ),
    },
    {
      title: '预测方向',
      dataIndex: 'trend_direction',
      key: 'trend_direction',
      render: (value: string) => {
        const config = {
          up: { color: 'green', icon: <ArrowUpOutlined />, text: '看涨' },
          down: { color: 'red', icon: <ArrowDownOutlined />, text: '看跌' },
          sideways: { color: 'orange', icon: <ThunderboltOutlined />, text: '震荡' }
        }
        const item = config[value as keyof typeof config]
        return (
          <Tag color={item.color} icon={item.icon}>
            {item.text}
          </Tag>
        )
      },
    },
    {
      title: '目标价',
      key: 'target',
      render: (record: AIPrediction) => (
        <Space direction="vertical" size="small">
          <Text strong>¥{record.target_price.toFixed(2)}</Text>
          <Text type={record.predicted_return >= 0 ? 'success' : 'danger'} style={{ fontSize: 12 }}>
            {record.predicted_return >= 0 ? '+' : ''}{record.predicted_return.toFixed(2)}%
          </Text>
        </Space>
      ),
    },
    {
      title: '置信度',
      dataIndex: 'confidence_score',
      key: 'confidence_score',
      render: (value: number) => (
        <Progress
          percent={Math.round(value * 100)}
          size="small"
          strokeColor={value >= 0.8 ? '#52c41a' : value >= 0.6 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      key: 'risk_level',
      render: (value: string) => {
        const config = {
          low: { color: 'green', text: '低风险' },
          medium: { color: 'orange', text: '中风险' },
          high: { color: 'red', text: '高风险' }
        }
        const item = config[value as keyof typeof config]
        return <Tag color={item.color}>{item.text}</Tag>
      },
    },
    {
      title: '时间周期',
      dataIndex: 'time_horizon',
      key: 'time_horizon',
      render: (value: number) => `${value}天`,
    },
    {
      title: '生成时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (value: string) => value.split(' ')[1],
    },
    {
      title: '操作',
      key: 'action',
      render: (record: AIPrediction) => (
        <Button
          type="link"
          onClick={() => setSelectedPrediction(record)}
        >
          查看详情
        </Button>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <RobotOutlined /> AI智能预测
        </Title>
        <Paragraph type="secondary">
          基于深度学习和大数据分析的股票价格预测和投资建议
        </Paragraph>
      </div>

      {/* 生成预测工具 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space.Compact style={{ width: '100%', maxWidth: 400 }}>
              <Input
                placeholder="输入股票代码生成AI预测"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onPressEnter={handleGeneratePrediction}
              />
              <Button
                type="primary"
                icon={<RobotOutlined />}
                loading={generateLoading}
                onClick={handleGeneratePrediction}
              >
                生成预测
              </Button>
            </Space.Compact>
          </Col>
          <Col>
            <Space>
              <Select defaultValue="price_target" style={{ width: 120 }}>
                <Option value="price_target">价格预测</Option>
                <Option value="trend_analysis">趋势分析</Option>
                <Option value="risk_assessment">风险评估</Option>
              </Select>
              <Select defaultValue="30" style={{ width: 100 }}>
                <Option value="7">7天</Option>
                <Option value="30">30天</Option>
                <Option value="60">60天</Option>
                <Option value="90">90天</Option>
              </Select>
            </Space>
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="predictions">
        <TabPane tab="AI预测" key="predictions">
          <Card>
            <Table
              dataSource={predictions}
              columns={predictionColumns}
              loading={loading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="实时信号" key="signals">
          <Row gutter={[16, 16]}>
            {signals.map(signal => (
              <Col span={8} key={signal.id}>
                <Card size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Space>
                        <Text strong>{signal.stock_code}</Text>
                        <Text>{signal.stock_name}</Text>
                      </Space>
                      <Tag
                        color={
                          signal.signal_type === 'buy' ? 'green' :
                          signal.signal_type === 'sell' ? 'red' : 'orange'
                        }
                      >
                        {signal.signal_type === 'buy' ? '买入' :
                         signal.signal_type === 'sell' ? '卖出' : '持有'}
                      </Tag>
                    </div>

                    <div>
                      <Text type="secondary">信号强度: </Text>
                      <Progress
                        percent={signal.strength}
                        size="small"
                        strokeColor={signal.strength >= 80 ? '#52c41a' : signal.strength >= 60 ? '#faad14' : '#ff4d4f'}
                      />
                    </div>

                    <div>
                      <Text type="secondary">置信度: {signal.confidence}%</Text>
                    </div>

                    <div>
                      <Text type="secondary">关键因素:</Text>
                      <div style={{ marginTop: 4 }}>
                        {signal.factors.map((factor, index) => (
                          <Tag key={index}>{factor}</Tag>
                        ))}
                      </div>
                    </div>

                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {signal.generated_at}
                    </Text>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>
      </Tabs>

      {/* 预测详情模态框 */}
      {selectedPrediction && (
        <Card
          style={{ marginTop: 24 }}
          title={`${selectedPrediction.stock_code} - ${selectedPrediction.stock_name} 预测详情`}
          extra={
            <Button onClick={() => setSelectedPrediction(null)}>
              关闭
            </Button>
          }
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card size="small" title="预测结果">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Statistic
                    title="目标价格"
                    value={selectedPrediction.target_price}
                    precision={2}
                    prefix="¥"
                    valueStyle={{
                      color: selectedPrediction.predicted_return >= 0 ? '#3f8600' : '#cf1322'
                    }}
                  />
                  <Statistic
                    title="预期收益"
                    value={selectedPrediction.predicted_return}
                    precision={2}
                    suffix="%"
                    valueStyle={{
                      color: selectedPrediction.predicted_return >= 0 ? '#3f8600' : '#cf1322'
                    }}
                  />
                  <div>
                    <Text>置信度: </Text>
                    <Progress
                      percent={Math.round(selectedPrediction.confidence_score * 100)}
                      size="small"
                      strokeColor={
                        selectedPrediction.confidence_score >= 0.8 ? '#52c41a' :
                        selectedPrediction.confidence_score >= 0.6 ? '#faad14' : '#ff4d4f'
                      }
                    />
                  </div>
                </Space>
              </Card>
            </Col>

            <Col span={12}>
              <Card size="small" title="AI分析逻辑">
                <List
                  dataSource={selectedPrediction.reasoning}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<Avatar size="small" icon={<BulbOutlined />} />}
                        description={item}
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  )
}

export default AIPredict
