"""
技术指标存储服务模块
"""

import json
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.dialects.sqlite import insert

from app.core.logging import get_logger
from app.models.stock import TechnicalIndicator
from app.core.database import AsyncSessionLocal

logger = get_logger(__name__)


class IndicatorStorageService:
    """技术指标存储服务"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def save_indicators(self, stock_code: str, indicators_data: Dict[str, Any]) -> int:
        """保存技术指标数据"""
        try:
            logger.info(f"开始保存股票 {stock_code} 的技术指标数据...")
            
            if not indicators_data.get("indicators"):
                logger.warning("没有指标数据需要保存")
                return 0
            
            saved_count = 0
            period = indicators_data.get("period", "daily")
            
            # 获取所有日期
            all_dates = set()
            for indicator_name, values in indicators_data["indicators"].items():
                for item in values:
                    all_dates.add(item["date"])
            
            # 按日期组织数据
            date_indicators = {}
            for trade_date in all_dates:
                date_indicators[trade_date] = {}
            
            # 填充指标数据
            for indicator_name, values in indicators_data["indicators"].items():
                for item in values:
                    trade_date = item["date"]
                    date_indicators[trade_date][indicator_name] = item["value"]
            
            # 保存每日指标数据
            for trade_date_str, daily_indicators in date_indicators.items():
                try:
                    trade_date = datetime.fromisoformat(trade_date_str).date()
                    
                    # 检查是否已存在
                    stmt = select(TechnicalIndicator).where(
                        and_(
                            TechnicalIndicator.stock_code == stock_code,
                            TechnicalIndicator.trade_date == trade_date,
                            TechnicalIndicator.period == period
                        )
                    )
                    result = await self.session.execute(stmt)
                    existing_indicator = result.scalar_one_or_none()
                    
                    if existing_indicator:
                        # 更新现有记录
                        for field_name, value in daily_indicators.items():
                            if hasattr(existing_indicator, field_name):
                                setattr(existing_indicator, field_name, value)
                        existing_indicator.calculated_at = datetime.utcnow()
                        existing_indicator.updated_at = datetime.utcnow()
                    else:
                        # 创建新记录
                        indicator_data = {
                            "stock_code": stock_code,
                            "trade_date": trade_date,
                            "period": period,
                            "calculated_at": datetime.utcnow()
                        }
                        
                        # 添加指标值
                        for field_name, value in daily_indicators.items():
                            if hasattr(TechnicalIndicator, field_name):
                                indicator_data[field_name] = value
                        
                        new_indicator = TechnicalIndicator(**indicator_data)
                        self.session.add(new_indicator)
                    
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"保存日期 {trade_date_str} 的指标数据失败: {e}")
                    continue
            
            await self.session.commit()
            logger.info(f"成功保存 {saved_count} 条技术指标数据")
            return saved_count
            
        except Exception as e:
            logger.error(f"批量保存技术指标数据失败: {e}")
            await self.session.rollback()
            return 0
    
    async def get_indicators(
        self,
        stock_code: str,
        period: str = "daily",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        indicators: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[TechnicalIndicator]:
        """查询技术指标数据"""
        try:
            stmt = select(TechnicalIndicator).where(
                and_(
                    TechnicalIndicator.stock_code == stock_code,
                    TechnicalIndicator.period == period
                )
            )
            
            if start_date:
                stmt = stmt.where(TechnicalIndicator.trade_date >= start_date)
            if end_date:
                stmt = stmt.where(TechnicalIndicator.trade_date <= end_date)
            
            stmt = stmt.order_by(desc(TechnicalIndicator.trade_date)).limit(limit)
            
            result = await self.session.execute(stmt)
            indicators_data = result.scalars().all()
            
            return list(reversed(indicators_data))  # 按日期正序返回
            
        except Exception as e:
            logger.error(f"查询技术指标数据失败: {e}")
            return []
    
    async def get_latest_indicators(self, stock_code: str, period: str = "daily") -> Optional[TechnicalIndicator]:
        """获取最新的技术指标数据"""
        try:
            stmt = select(TechnicalIndicator).where(
                and_(
                    TechnicalIndicator.stock_code == stock_code,
                    TechnicalIndicator.period == period
                )
            ).order_by(desc(TechnicalIndicator.trade_date)).limit(1)
            
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取最新技术指标数据失败: {e}")
            return None
    
    # async def save_signals(self, signals_data: List[Dict[str, Any]]) -> int:
        # """保存指标信号"""
        # 暂时注释掉，因为IndicatorSignal模型已删除
        return 0
    
    # async def get_signals(
    #     self,
    #     stock_code: Optional[str] = None,
    #     signal_type: Optional[str] = None,
    #     signal_direction: Optional[str] = None,
    #     start_date: Optional[date] = None,
    #     end_date: Optional[date] = None,
    #     limit: int = 100
    # ) -> List[IndicatorSignal]:
        # """查询指标信号"""
        # 暂时注释掉，因为IndicatorSignal模型已删除
        return []
    
    async def get_indicator_statistics(self, stock_code: str, period: str = "daily") -> Dict[str, Any]:
        """获取指标统计信息"""
        try:
            # 查询指标数据数量
            count_stmt = select(func.count(TechnicalIndicator.id)).where(
                and_(
                    TechnicalIndicator.stock_code == stock_code,
                    TechnicalIndicator.period == period
                )
            )
            count_result = await self.session.execute(count_stmt)
            total_count = count_result.scalar()
            
            # 查询最新和最早日期
            date_stmt = select(
                func.min(TechnicalIndicator.trade_date).label('earliest_date'),
                func.max(TechnicalIndicator.trade_date).label('latest_date')
            ).where(
                and_(
                    TechnicalIndicator.stock_code == stock_code,
                    TechnicalIndicator.period == period
                )
            )
            date_result = await self.session.execute(date_stmt)
            date_info = date_result.first()
            
            # 查询信号统计 (暂时注释掉，因为IndicatorSignal模型已删除)
            # signal_count_stmt = select(func.count(IndicatorSignal.id)).where(
            #     IndicatorSignal.stock_code == stock_code
            # )
            # signal_count_result = await self.session.execute(signal_count_stmt)
            signal_count = 0  # signal_count_result.scalar()
            
            return {
                "stock_code": stock_code,
                "period": period,
                "total_indicators": total_count or 0,
                "total_signals": signal_count or 0,
                "earliest_date": date_info.earliest_date.isoformat() if date_info.earliest_date else None,
                "latest_date": date_info.latest_date.isoformat() if date_info.latest_date else None,
                "data_coverage_days": (date_info.latest_date - date_info.earliest_date).days if date_info.earliest_date and date_info.latest_date else 0
            }
            
        except Exception as e:
            logger.error(f"获取指标统计信息失败: {e}")
            return {}
    
    async def delete_old_indicators(self, days_to_keep: int = 365) -> int:
        """删除过期的指标数据"""
        try:
            cutoff_date = date.today() - timedelta(days=days_to_keep)
            
            from sqlalchemy import delete
            delete_stmt = delete(TechnicalIndicator).where(
                TechnicalIndicator.trade_date < cutoff_date
            )
            result = await self.session.execute(delete_stmt)
            deleted_count = result.rowcount
            
            await self.session.commit()
            
            logger.info(f"删除了 {deleted_count} 条过期指标数据")
            return deleted_count
            
        except Exception as e:
            logger.error(f"删除过期指标数据失败: {e}")
            await self.session.rollback()
            return 0
