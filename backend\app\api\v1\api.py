"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import stocks, indicators, ai, screener, alerts, analytics, auth, data_quality, tasks, selection, watchlist, data_management
from app.api import data_management as data_mgmt_api, akshare_data, stock_data_management, professional_db_management, data_classification, data_update_strategy, custom_functionality

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(stocks.router, prefix="/stocks", tags=["股票数据"])
api_router.include_router(indicators.router, prefix="/indicators", tags=["技术指标"])
api_router.include_router(ai.router, prefix="/ai", tags=["AI预测"])
api_router.include_router(screener.router, prefix="/screener", tags=["选股系统"])
api_router.include_router(selection.router, prefix="/selection", tags=["智能选股"])
api_router.include_router(alerts.router, prefix="/alerts", tags=["预警系统"])
api_router.include_router(watchlist.router, prefix="/watchlist", tags=["自选股管理"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["统计分析与回测"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["统计分析"])
api_router.include_router(data_quality.router, prefix="/data-quality", tags=["数据质量"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(data_management.router, prefix="/data-management", tags=["数据管理"])
api_router.include_router(data_mgmt_api.router, prefix="/data-management", tags=["数据管理API"])
api_router.include_router(akshare_data.router, prefix="/akshare", tags=["AKShare数据"])
api_router.include_router(stock_data_management.router, prefix="/stock-data", tags=["股票数据管理"])
api_router.include_router(professional_db_management.router, prefix="/professional-db", tags=["专业数据库管理"])
api_router.include_router(data_classification.router, prefix="/data-classification", tags=["数据分类管理"])
api_router.include_router(data_update_strategy.router, prefix="/data-update-strategy", tags=["数据更新策略"])
api_router.include_router(custom_functionality.router, prefix="/custom-functionality", tags=["自定义功能管理"])
