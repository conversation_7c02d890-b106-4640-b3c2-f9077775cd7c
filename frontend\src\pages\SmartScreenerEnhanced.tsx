import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Form,
  Select,
  InputNumber,
  Button,
  Table,
  Space,
  Typography,
  Tag,
  Progress,
  Tabs,
  Slider,
  Switch,
  Badge,
  Tooltip,
  message,
  Spin,
  Empty,
  Divider,
  theme,
  Radio,
  Checkbox,
  DatePicker,
  Statistic,
  Alert
} from 'antd'
import {
  SearchOutlined,
  StarOutlined,
  TrophyOutlined,
  RobotOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON>hartOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
  SaveOutlined,
  HistoryOutlined,
  BulbOutlined,
  ThunderboltOutlined,
  RiseOutlined,
  FallOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface ScreenerCriteria {
  // 基础指标
  marketCap: [number, number]
  peRatio: [number, number]
  pbRatio: [number, number]
  roe: [number, number]
  roa: [number, number]
  debtRatio: [number, number]
  
  // 技术指标
  rsi: [number, number]
  macd: 'bullish' | 'bearish' | 'any'
  volume: 'increasing' | 'decreasing' | 'any'
  ma: {
    ma5: 'above' | 'below' | 'any'
    ma20: 'above' | 'below' | 'any'
    ma60: 'above' | 'below' | 'any'
  }
  
  // AI预测
  aiPrediction: 'bullish' | 'bearish' | 'any'
  aiConfidence: [number, number]
  
  // K线形态
  patterns: string[]
  
  // 财务指标
  revenue: 'increasing' | 'decreasing' | 'any'
  profit: 'increasing' | 'decreasing' | 'any'
  
  // 其他条件
  sector: string[]
  priceRange: [number, number]
  volumeRange: [number, number]
  
  // 时间范围
  timeRange: string
}

interface ScreenerResult {
  code: string
  name: string
  currentPrice: number
  change: number
  changePercent: number
  marketCap: number
  peRatio: number
  pbRatio: number
  roe: number
  roa: number
  
  // 技术评分
  technicalScore: number
  fundamentalScore: number
  aiScore: number
  overallScore: number
  
  // AI预测
  aiPrediction: 'bullish' | 'bearish'
  aiConfidence: number
  aiTargetPrice: number
  aiTimeframe: string
  
  // 技术指标
  rsi: number
  macd: number
  volumeRatio: number
  
  // 形态识别
  patterns: string[]
  
  sector: string
  recommendation: 'strong_buy' | 'buy' | 'hold' | 'sell' | 'strong_sell'
  
  // 风险评级
  riskLevel: 'low' | 'medium' | 'high'
  
  // 匹配度
  matchScore: number
}

const SmartScreenerEnhanced: React.FC = () => {
  const { token } = theme.useToken()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<ScreenerResult[]>([])
  const [activeTab, setActiveTab] = useState('criteria')
  const [selectedCriteria, setSelectedCriteria] = useState<ScreenerCriteria>({
    marketCap: [1000000000, 1000000000000], // 10亿到1万亿
    peRatio: [0, 50],
    pbRatio: [0, 10],
    roe: [5, 50],
    roa: [2, 30],
    debtRatio: [0, 80],
    rsi: [30, 70],
    macd: 'any',
    volume: 'any',
    ma: {
      ma5: 'any',
      ma20: 'any',
      ma60: 'any'
    },
    aiPrediction: 'any',
    aiConfidence: [60, 100],
    patterns: [],
    revenue: 'any',
    profit: 'any',
    sector: [],
    priceRange: [1, 1000],
    volumeRange: [1000000, 1000000000],
    timeRange: '1M'
  })

  // 预设筛选策略
  const presetStrategies = [
    {
      name: '价值投资',
      description: '低估值、高ROE、稳定增长的股票',
      criteria: {
        peRatio: [0, 20],
        pbRatio: [0, 3],
        roe: [15, 50],
        debtRatio: [0, 50],
        revenue: 'increasing',
        profit: 'increasing'
      }
    },
    {
      name: '成长投资',
      description: '高成长性、创新能力强的股票',
      criteria: {
        revenue: 'increasing',
        profit: 'increasing',
        sector: ['科技', '医药', '新能源'],
        aiPrediction: 'bullish',
        aiConfidence: [70, 100]
      }
    },
    {
      name: '技术突破',
      description: '技术指标强势、有突破信号的股票',
      criteria: {
        rsi: [50, 80],
        macd: 'bullish',
        volume: 'increasing',
        ma: {
          ma5: 'above',
          ma20: 'above',
          ma60: 'above'
        }
      }
    },
    {
      name: 'AI推荐',
      description: 'AI算法高置信度推荐的股票',
      criteria: {
        aiPrediction: 'bullish',
        aiConfidence: [80, 100],
        overallScore: [80, 100]
      }
    }
  ]

  // 模拟选股结果
  const mockResults: ScreenerResult[] = [
    {
      code: '000001',
      name: '平安银行',
      currentPrice: 12.45,
      change: 0.23,
      changePercent: 1.88,
      marketCap: 241000000000,
      peRatio: 5.2,
      pbRatio: 0.8,
      roe: 12.5,
      roa: 1.2,
      technicalScore: 85,
      fundamentalScore: 78,
      aiScore: 92,
      overallScore: 85,
      aiPrediction: 'bullish',
      aiConfidence: 87,
      aiTargetPrice: 14.50,
      aiTimeframe: '3个月',
      rsi: 65,
      macd: 1.2,
      volumeRatio: 1.15,
      patterns: ['突破上升三角形', '金叉'],
      sector: '银行',
      recommendation: 'strong_buy',
      riskLevel: 'low',
      matchScore: 92
    },
    {
      code: '000002',
      name: '万科A',
      currentPrice: 18.67,
      change: -0.45,
      changePercent: -2.35,
      marketCap: 206000000000,
      peRatio: 8.9,
      pbRatio: 1.2,
      roe: 15.8,
      roa: 3.2,
      technicalScore: 72,
      fundamentalScore: 85,
      aiScore: 78,
      overallScore: 78,
      aiPrediction: 'bullish',
      aiConfidence: 75,
      aiTargetPrice: 22.00,
      aiTimeframe: '6个月',
      rsi: 45,
      macd: -0.3,
      volumeRatio: 0.95,
      patterns: ['底部双重底', '超跌反弹'],
      sector: '房地产',
      recommendation: 'buy',
      riskLevel: 'medium',
      matchScore: 78
    },
    {
      code: '600036',
      name: '招商银行',
      currentPrice: 45.67,
      change: 1.23,
      changePercent: 2.77,
      marketCap: 1200000000000,
      peRatio: 6.5,
      pbRatio: 1.1,
      roe: 16.2,
      roa: 1.8,
      technicalScore: 88,
      fundamentalScore: 92,
      aiScore: 95,
      overallScore: 92,
      aiPrediction: 'bullish',
      aiConfidence: 93,
      aiTargetPrice: 52.00,
      aiTimeframe: '3个月',
      rsi: 72,
      macd: 2.1,
      volumeRatio: 1.35,
      patterns: ['向上突破', '多头排列'],
      sector: '银行',
      recommendation: 'strong_buy',
      riskLevel: 'low',
      matchScore: 95
    },
    {
      code: '000858',
      name: '五粮液',
      currentPrice: 156.78,
      change: -2.34,
      changePercent: -1.47,
      marketCap: 567000000000,
      peRatio: 23.5,
      pbRatio: 4.2,
      roe: 22.8,
      roa: 8.5,
      technicalScore: 65,
      fundamentalScore: 88,
      aiScore: 82,
      overallScore: 78,
      aiPrediction: 'bullish',
      aiConfidence: 68,
      aiTargetPrice: 175.00,
      aiTimeframe: '6个月',
      rsi: 38,
      macd: -1.5,
      volumeRatio: 0.85,
      patterns: ['调整中', '支撑位'],
      sector: '食品饮料',
      recommendation: 'hold',
      riskLevel: 'medium',
      matchScore: 72
    },
    {
      code: '300750',
      name: '宁德时代',
      currentPrice: 425.60,
      change: 8.90,
      changePercent: 2.14,
      marketCap: 1980000000000,
      peRatio: 45.2,
      pbRatio: 8.5,
      roe: 18.5,
      roa: 6.8,
      technicalScore: 92,
      fundamentalScore: 85,
      aiScore: 98,
      overallScore: 92,
      aiPrediction: 'bullish',
      aiConfidence: 96,
      aiTargetPrice: 480.00,
      aiTimeframe: '3个月',
      rsi: 78,
      macd: 3.2,
      volumeRatio: 1.65,
      patterns: ['强势突破', '新高'],
      sector: '新能源',
      recommendation: 'strong_buy',
      riskLevel: 'high',
      matchScore: 96
    }
  ]

  const runScreener = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 根据筛选条件过滤结果
      const filteredResults = mockResults.filter(stock => {
        // 这里可以添加实际的筛选逻辑
        return true
      })
      
      setResults(filteredResults)
      setActiveTab('results')
      message.success(`找到 ${filteredResults.length} 只符合条件的股票`)
    } catch (error) {
      message.error('筛选失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const applyPresetStrategy = (strategy: any) => {
    setSelectedCriteria({
      ...selectedCriteria,
      ...strategy.criteria
    })
    form.setFieldsValue(strategy.criteria)
    message.success(`已应用 ${strategy.name} 策略`)
  }

  return (
    <div style={{ padding: '24px', background: token.colorBgContainer }}>
      {/* 顶部标题和统计 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space size="large">
              <div>
                <Title level={3} style={{ margin: 0 }}>
                  <RobotOutlined style={{ color: token.colorPrimary, marginRight: 8 }} />
                  智能选股系统
                </Title>
                <Text type="secondary">基于AI算法的多维度股票筛选工具</Text>
              </div>
            </Space>
          </Col>
          
          <Col>
            <Space size="large">
              <Statistic
                title="可选股票"
                value={4567}
                prefix={<BarChartOutlined />}
                valueStyle={{ fontSize: '18px' }}
              />
              <Statistic
                title="今日推荐"
                value={23}
                prefix={<ThunderboltOutlined />}
                valueStyle={{ fontSize: '18px', color: token.colorSuccess }}
              />
              <Statistic
                title="AI置信度"
                value={87}
                suffix="%"
                prefix={<BulbOutlined />}
                valueStyle={{ fontSize: '18px', color: token.colorWarning }}
              />
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 主要内容区域 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'criteria',
            label: (
              <span>
                <FilterOutlined />
                筛选条件
              </span>
            ),
            children: (
              <Row gutter={[16, 16]}>
                {/* 预设策略 */}
                <Col span={24}>
                  <Card title="预设策略" size="small">
                    <Row gutter={[8, 8]}>
                      {presetStrategies.map((strategy, index) => (
                        <Col xs={24} sm={12} md={6} key={index}>
                          <Card
                            size="small"
                            hoverable
                            onClick={() => applyPresetStrategy(strategy)}
                            style={{
                              cursor: 'pointer',
                              border: `1px solid ${token.colorBorder}`,
                              transition: 'all 0.3s'
                            }}
                            styles={{ body: { padding: '12px' } }}
                          >
                            <Space direction="vertical" size="small" style={{ width: '100%' }}>
                              <Text strong style={{ color: token.colorPrimary }}>
                                {strategy.name}
                              </Text>
                              <Text style={{ fontSize: '12px' }} type="secondary">
                                {strategy.description}
                              </Text>
                            </Space>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </Card>
                </Col>

                {/* 基础指标 */}
                <Col xs={24} lg={12}>
                  <Card title="基础指标" size="small">
                    <Form form={form} layout="vertical">
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item label="市值范围 (亿元)">
                            <Slider
                              range
                              min={10}
                              max={50000}
                              step={10}
                              value={[
                                selectedCriteria.marketCap[0] / 100000000,
                                selectedCriteria.marketCap[1] / 100000000
                              ]}
                              onChange={(value) => {
                                setSelectedCriteria({
                                  ...selectedCriteria,
                                  marketCap: [value[0] * 100000000, value[1] * 100000000]
                                })
                              }}
                              tooltip={{
                                formatter: (value) => `${value}亿`
                              }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="PE范围">
                            <Slider
                              range
                              min={0}
                              max={100}
                              step={1}
                              value={selectedCriteria.peRatio}
                              onChange={(value) => {
                                setSelectedCriteria({
                                  ...selectedCriteria,
                                  peRatio: value
                                })
                              }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="PB范围">
                            <Slider
                              range
                              min={0}
                              max={20}
                              step={0.1}
                              value={selectedCriteria.pbRatio}
                              onChange={(value) => {
                                setSelectedCriteria({
                                  ...selectedCriteria,
                                  pbRatio: value
                                })
                              }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="ROE范围 (%)">
                            <Slider
                              range
                              min={0}
                              max={50}
                              step={1}
                              value={selectedCriteria.roe}
                              onChange={(value) => {
                                setSelectedCriteria({
                                  ...selectedCriteria,
                                  roe: value
                                })
                              }}
                              tooltip={{
                                formatter: (value) => `${value}%`
                              }}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                  </Card>
                </Col>

                {/* 技术指标 */}
                <Col xs={24} lg={12}>
                  <Card title="技术指标" size="small">
                    <Space direction="vertical" style={{ width: '100%' }} size="middle">
                      <div>
                        <Text strong>RSI范围</Text>
                        <Slider
                          range
                          min={0}
                          max={100}
                          step={1}
                          value={selectedCriteria.rsi}
                          onChange={(value) => {
                            setSelectedCriteria({
                              ...selectedCriteria,
                              rsi: value
                            })
                          }}
                          marks={{
                            20: '超卖',
                            50: '中性',
                            80: '超买'
                          }}
                        />
                      </div>

                      <Row gutter={16}>
                        <Col span={8}>
                          <Text strong>MACD信号</Text>
                          <br />
                          <Radio.Group
                            value={selectedCriteria.macd}
                            onChange={(e) => {
                              setSelectedCriteria({
                                ...selectedCriteria,
                                macd: e.target.value
                              })
                            }}
                            size="small"
                          >
                            <Radio.Button value="any">任意</Radio.Button>
                            <Radio.Button value="bullish">看涨</Radio.Button>
                            <Radio.Button value="bearish">看跌</Radio.Button>
                          </Radio.Group>
                        </Col>

                        <Col span={8}>
                          <Text strong>成交量</Text>
                          <br />
                          <Radio.Group
                            value={selectedCriteria.volume}
                            onChange={(e) => {
                              setSelectedCriteria({
                                ...selectedCriteria,
                                volume: e.target.value
                              })
                            }}
                            size="small"
                          >
                            <Radio.Button value="any">任意</Radio.Button>
                            <Radio.Button value="increasing">放量</Radio.Button>
                            <Radio.Button value="decreasing">缩量</Radio.Button>
                          </Radio.Group>
                        </Col>

                        <Col span={8}>
                          <Text strong>均线位置</Text>
                          <br />
                          <Space direction="vertical" size="small">
                            <div>
                              <Text style={{ fontSize: '12px' }}>MA5: </Text>
                              <Radio.Group
                                value={selectedCriteria.ma.ma5}
                                onChange={(e) => {
                                  setSelectedCriteria({
                                    ...selectedCriteria,
                                    ma: {
                                      ...selectedCriteria.ma,
                                      ma5: e.target.value
                                    }
                                  })
                                }}
                                size="small"
                              >
                                <Radio.Button value="any">任意</Radio.Button>
                                <Radio.Button value="above">上方</Radio.Button>
                                <Radio.Button value="below">下方</Radio.Button>
                              </Radio.Group>
                            </div>
                          </Space>
                        </Col>
                      </Row>
                    </Space>
                  </Card>
                </Col>

                {/* AI预测条件 */}
                <Col xs={24} lg={12}>
                  <Card title="AI预测条件" size="small">
                    <Space direction="vertical" style={{ width: '100%' }} size="middle">
                      <div>
                        <Text strong>AI预测方向</Text>
                        <br />
                        <Radio.Group
                          value={selectedCriteria.aiPrediction}
                          onChange={(e) => {
                            setSelectedCriteria({
                              ...selectedCriteria,
                              aiPrediction: e.target.value
                            })
                          }}
                        >
                          <Radio.Button value="any">任意</Radio.Button>
                          <Radio.Button value="bullish">看涨</Radio.Button>
                          <Radio.Button value="bearish">看跌</Radio.Button>
                        </Radio.Group>
                      </div>

                      <div>
                        <Text strong>AI置信度范围</Text>
                        <Slider
                          range
                          min={50}
                          max={100}
                          step={1}
                          value={selectedCriteria.aiConfidence}
                          onChange={(value) => {
                            setSelectedCriteria({
                              ...selectedCriteria,
                              aiConfidence: value
                            })
                          }}
                          tooltip={{
                            formatter: (value) => `${value}%`
                          }}
                          marks={{
                            60: '60%',
                            80: '80%',
                            95: '95%'
                          }}
                        />
                      </div>
                    </Space>
                  </Card>
                </Col>

                {/* 行业和其他条件 */}
                <Col xs={24} lg={12}>
                  <Card title="行业和其他条件" size="small">
                    <Space direction="vertical" style={{ width: '100%' }} size="middle">
                      <div>
                        <Text strong>行业板块</Text>
                        <br />
                        <Select
                          mode="multiple"
                          placeholder="选择行业板块"
                          style={{ width: '100%' }}
                          value={selectedCriteria.sector}
                          onChange={(value) => {
                            setSelectedCriteria({
                              ...selectedCriteria,
                              sector: value
                            })
                          }}
                        >
                          <Option value="银行">银行</Option>
                          <Option value="科技">科技</Option>
                          <Option value="医药">医药</Option>
                          <Option value="新能源">新能源</Option>
                          <Option value="房地产">房地产</Option>
                          <Option value="食品饮料">食品饮料</Option>
                          <Option value="制造业">制造业</Option>
                          <Option value="消费">消费</Option>
                        </Select>
                      </div>

                      <div>
                        <Text strong>价格范围 (元)</Text>
                        <Slider
                          range
                          min={1}
                          max={1000}
                          step={1}
                          value={selectedCriteria.priceRange}
                          onChange={(value) => {
                            setSelectedCriteria({
                              ...selectedCriteria,
                              priceRange: value
                            })
                          }}
                          tooltip={{
                            formatter: (value) => `¥${value}`
                          }}
                        />
                      </div>
                    </Space>
                  </Card>
                </Col>

                {/* 操作按钮 */}
                <Col span={24}>
                  <Card size="small">
                    <Space>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        loading={loading}
                        onClick={runScreener}
                        size="large"
                      >
                        开始筛选
                      </Button>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          form.resetFields()
                          setSelectedCriteria({
                            marketCap: [1000000000, 1000000000000],
                            peRatio: [0, 50],
                            pbRatio: [0, 10],
                            roe: [5, 50],
                            roa: [2, 30],
                            debtRatio: [0, 80],
                            rsi: [30, 70],
                            macd: 'any',
                            volume: 'any',
                            ma: { ma5: 'any', ma20: 'any', ma60: 'any' },
                            aiPrediction: 'any',
                            aiConfidence: [60, 100],
                            patterns: [],
                            revenue: 'any',
                            profit: 'any',
                            sector: [],
                            priceRange: [1, 1000],
                            volumeRange: [1000000, 1000000000],
                            timeRange: '1M'
                          })
                        }}
                      >
                        重置条件
                      </Button>
                      <Button icon={<SaveOutlined />}>
                        保存策略
                      </Button>
                      <Button icon={<HistoryOutlined />}>
                        历史记录
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            )
          },
          {
            key: 'results',
            label: (
              <span>
                <TrophyOutlined />
                筛选结果 {results.length > 0 && <Badge count={results.length} />}
              </span>
            ),
            children: (
              <div>
                {results.length > 0 ? (
                  <Text strong style={{ fontSize: '18px', color: token.colorText }}>
                    📊 筛选结果展示区域开发中...
                  </Text>
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="暂无筛选结果，请先设置筛选条件"
                  />
                )}
              </div>
            )
          }
        ]}
      />
    </div>
  )
}

export default SmartScreenerEnhanced
