"""
股票相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, DateTime, Date, Numeric, Integer, Text, Boolean, Index, JSON, Float
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func

from app.core.database import Base


class Stock(Base):
    """股票基本信息表"""
    __tablename__ = "stocks"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), unique=True, index=True, comment="股票代码")
    stock_name: Mapped[str] = mapped_column(String(50), comment="股票名称")
    market: Mapped[str] = mapped_column(String(10), comment="市场类型")
    industry: Mapped[Optional[str]] = mapped_column(String(50), comment="所属行业")
    concept: Mapped[Optional[str]] = mapped_column(Text, comment="概念板块")
    list_date: Mapped[Optional[date]] = mapped_column(Date, comment="上市日期")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否活跃")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class KlineData(Base):
    """K线数据表"""
    __tablename__ = "kline_data"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    trade_date: Mapped[date] = mapped_column(Date, index=True, comment="交易日期")
    period: Mapped[str] = mapped_column(String(10), comment="周期类型")
    open_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="开盘价")
    high_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最高价")
    low_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最低价")
    close_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="收盘价")
    volume: Mapped[int] = mapped_column(Integer, comment="成交量")
    turnover: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="成交额")
    change: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="涨跌额")
    change_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="涨跌幅")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_kline_stock_date_period', 'stock_code', 'trade_date', 'period'),
    )


class RealtimeData(Base):
    """实时行情数据表"""
    __tablename__ = "realtime_data"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    current_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="当前价格")
    change: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="涨跌额")
    change_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="涨跌幅")
    volume: Mapped[int] = mapped_column(Integer, comment="成交量")
    turnover: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="成交额")
    bid_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="买一价")
    ask_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="卖一价")
    bid_volume: Mapped[int] = mapped_column(Integer, comment="买一量")
    ask_volume: Mapped[int] = mapped_column(Integer, comment="卖一量")
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="时间戳")
    
    __table_args__ = (
        Index('ix_realtime_stock_timestamp', 'stock_code', 'timestamp'),
    )


class FinancialNews(Base):
    """财经新闻表"""
    __tablename__ = "financial_news"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String(200), comment="新闻标题")
    content: Mapped[Optional[str]] = mapped_column(Text, comment="新闻内容")
    summary: Mapped[Optional[str]] = mapped_column(Text, comment="新闻摘要")

    # 来源信息
    source: Mapped[Optional[str]] = mapped_column(String(50), comment="新闻来源")
    url: Mapped[Optional[str]] = mapped_column(String(500), comment="原文链接")
    author: Mapped[Optional[str]] = mapped_column(String(50), comment="作者")

    # 分类和标签
    category: Mapped[Optional[str]] = mapped_column(String(50), comment="新闻分类")
    keywords: Mapped[Optional[dict]] = mapped_column(JSON, comment="关键词列表")
    related_stocks: Mapped[Optional[dict]] = mapped_column(JSON, comment="相关股票代码")

    # 时间信息
    publish_time: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="发布时间")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('ix_news_title', 'title'),
        Index('ix_news_source', 'source'),
        Index('ix_news_publish_time', 'publish_time'),
    )


class TechnicalIndicator(Base):
    """技术指标表"""
    __tablename__ = "technical_indicators"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), comment="股票代码")
    trade_date: Mapped[date] = mapped_column(Date, comment="计算日期")

    # 移动平均线
    ma5: Mapped[Optional[float]] = mapped_column(Float, comment="5日均线")
    ma10: Mapped[Optional[float]] = mapped_column(Float, comment="10日均线")
    ma20: Mapped[Optional[float]] = mapped_column(Float, comment="20日均线")
    ma30: Mapped[Optional[float]] = mapped_column(Float, comment="30日均线")
    ma60: Mapped[Optional[float]] = mapped_column(Float, comment="60日均线")

    # MACD指标
    macd_dif: Mapped[Optional[float]] = mapped_column(Float, comment="MACD DIF")
    macd_dea: Mapped[Optional[float]] = mapped_column(Float, comment="MACD DEA")
    macd_histogram: Mapped[Optional[float]] = mapped_column(Float, comment="MACD柱状图")

    # RSI指标
    rsi6: Mapped[Optional[float]] = mapped_column(Float, comment="6日RSI")
    rsi12: Mapped[Optional[float]] = mapped_column(Float, comment="12日RSI")
    rsi24: Mapped[Optional[float]] = mapped_column(Float, comment="24日RSI")

    # KDJ指标
    kdj_k: Mapped[Optional[float]] = mapped_column(Float, comment="KDJ K值")
    kdj_d: Mapped[Optional[float]] = mapped_column(Float, comment="KDJ D值")
    kdj_j: Mapped[Optional[float]] = mapped_column(Float, comment="KDJ J值")

    # 布林带
    boll_upper: Mapped[Optional[float]] = mapped_column(Float, comment="布林带上轨")
    boll_mid: Mapped[Optional[float]] = mapped_column(Float, comment="布林带中轨")
    boll_lower: Mapped[Optional[float]] = mapped_column(Float, comment="布林带下轨")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('ix_tech_stock_date', 'stock_code', 'trade_date'),
    )


class DataUpdateLog(Base):
    """数据更新日志表"""
    __tablename__ = "data_update_logs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_id: Mapped[str] = mapped_column(String(50), comment="任务ID")
    task_name: Mapped[str] = mapped_column(String(100), comment="任务名称")
    task_type: Mapped[str] = mapped_column(String(50), comment="任务类型")

    # 执行状态
    status: Mapped[str] = mapped_column(String(20), comment="执行状态")
    progress: Mapped[int] = mapped_column(Integer, default=0, comment="执行进度")

    # 处理统计
    total_records: Mapped[Optional[int]] = mapped_column(Integer, comment="总记录数")
    processed_records: Mapped[Optional[int]] = mapped_column(Integer, comment="已处理记录数")
    success_records: Mapped[Optional[int]] = mapped_column(Integer, comment="成功记录数")
    failed_records: Mapped[Optional[int]] = mapped_column(Integer, comment="失败记录数")

    # 时间信息
    start_time: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="开始时间")
    end_time: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="结束时间")
    duration: Mapped[Optional[int]] = mapped_column(Integer, comment="执行时长(秒)")

    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(Text, comment="错误信息")
    error_details: Mapped[Optional[dict]] = mapped_column(JSON, comment="错误详情")

    # 数据源信息
    data_source: Mapped[Optional[str]] = mapped_column(String(50), comment="数据来源")
    api_calls: Mapped[Optional[int]] = mapped_column(Integer, comment="API调用次数")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())

    __table_args__ = (
        Index('ix_log_task_id', 'task_id'),
        Index('ix_log_task_type', 'task_type'),
        Index('ix_log_status', 'status'),
    )


class APIConfiguration(Base):
    """API配置表"""
    __tablename__ = "api_configurations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    api_id: Mapped[str] = mapped_column(String(50), unique=True, comment="API标识")
    name: Mapped[str] = mapped_column(String(100), comment="API名称")
    api_type: Mapped[str] = mapped_column(String(50), comment="API类型")

    # 连接配置
    endpoint: Mapped[Optional[str]] = mapped_column(String(200), comment="API端点")
    api_key: Mapped[Optional[str]] = mapped_column(String(200), comment="API密钥")
    secret_key: Mapped[Optional[str]] = mapped_column(String(200), comment="API密钥")

    # 限制配置
    rate_limit: Mapped[int] = mapped_column(Integer, default=100, comment="速率限制(次/分钟)")
    timeout: Mapped[int] = mapped_column(Integer, default=30000, comment="超时时间(毫秒)")
    retry_count: Mapped[int] = mapped_column(Integer, default=3, comment="重试次数")

    # 优先级和状态
    priority: Mapped[int] = mapped_column(Integer, default=1, comment="优先级")
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    status: Mapped[str] = mapped_column(String(20), default='disconnected', comment="连接状态")

    # 统计信息
    total_calls: Mapped[int] = mapped_column(Integer, default=0, comment="总调用次数")
    success_calls: Mapped[int] = mapped_column(Integer, default=0, comment="成功调用次数")
    failed_calls: Mapped[int] = mapped_column(Integer, default=0, comment="失败调用次数")
    last_call_time: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后调用时间")

    # 配置信息
    config_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="额外配置数据")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('ix_api_config_api_id', 'api_id'),
        Index('ix_api_config_type', 'api_type'),
    )


class StockDetailInfo(Base):
    """个股详细信息表 - 基于AKShare个股信息查询"""
    __tablename__ = "stock_detail_info"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), unique=True, index=True, comment="股票代码")

    # 基本信息
    stock_name: Mapped[Optional[str]] = mapped_column(String(100), comment="股票简称")
    full_name: Mapped[Optional[str]] = mapped_column(String(200), comment="公司全称")
    english_name: Mapped[Optional[str]] = mapped_column(String(200), comment="英文名称")

    # 市场信息
    market: Mapped[Optional[str]] = mapped_column(String(20), comment="市场类型")
    exchange: Mapped[Optional[str]] = mapped_column(String(50), comment="交易所")
    list_date: Mapped[Optional[date]] = mapped_column(Date, comment="上市日期")

    # 股本信息
    total_shares: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="总股本")
    float_shares: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="流通股本")

    # 市值信息
    total_market_cap: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="总市值")
    float_market_cap: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="流通市值")

    # 行业分类
    industry: Mapped[Optional[str]] = mapped_column(String(100), comment="所属行业")
    sector: Mapped[Optional[str]] = mapped_column(String(100), comment="所属板块")
    concept: Mapped[Optional[str]] = mapped_column(Text, comment="概念板块")

    # 财务指标
    pe_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="市盈率")
    pb_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="市净率")
    roe: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="净资产收益率")
    roa: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="总资产收益率")

    # 公司信息
    legal_representative: Mapped[Optional[str]] = mapped_column(String(100), comment="法定代表人")
    general_manager: Mapped[Optional[str]] = mapped_column(String(100), comment="总经理")
    secretary: Mapped[Optional[str]] = mapped_column(String(100), comment="董事会秘书")

    # 联系信息
    phone: Mapped[Optional[str]] = mapped_column(String(50), comment="联系电话")
    fax: Mapped[Optional[str]] = mapped_column(String(50), comment="传真")
    email: Mapped[Optional[str]] = mapped_column(String(100), comment="邮箱")
    website: Mapped[Optional[str]] = mapped_column(String(200), comment="公司网站")

    # 地址信息
    registered_address: Mapped[Optional[str]] = mapped_column(Text, comment="注册地址")
    office_address: Mapped[Optional[str]] = mapped_column(Text, comment="办公地址")

    # 业务信息
    main_business: Mapped[Optional[str]] = mapped_column(Text, comment="主营业务")
    business_scope: Mapped[Optional[str]] = mapped_column(Text, comment="经营范围")
    company_profile: Mapped[Optional[str]] = mapped_column(Text, comment="公司简介")

    # 其他信息
    employee_count: Mapped[Optional[int]] = mapped_column(Integer, comment="员工人数")
    registered_capital: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="注册资本")
    currency: Mapped[Optional[str]] = mapped_column(String(10), comment="货币单位")

    # 控制人信息
    actual_controller: Mapped[Optional[str]] = mapped_column(String(200), comment="实际控制人")
    controller_type: Mapped[Optional[str]] = mapped_column(String(50), comment="控制人类型")

    # 时间戳
    data_date: Mapped[Optional[date]] = mapped_column(Date, comment="数据日期")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (
        Index('ix_stock_detail_code', 'stock_code'),
        Index('ix_stock_detail_industry', 'industry'),
        Index('ix_stock_detail_sector', 'sector'),
    )


class SectorData(Base):
    """板块数据表 - 基于AKShare板块数据"""
    __tablename__ = "sector_data"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    sector_code: Mapped[str] = mapped_column(String(20), index=True, comment="板块代码")
    sector_name: Mapped[str] = mapped_column(String(100), index=True, comment="板块名称")
    sector_type: Mapped[str] = mapped_column(String(50), comment="板块类型: industry/concept/region")

    # 统计信息
    stock_count: Mapped[int] = mapped_column(Integer, default=0, comment="股票数量")
    total_market_cap: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="总市值")
    avg_pe_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="平均市盈率")
    avg_pb_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="平均市净率")

    # 涨跌统计
    up_count: Mapped[int] = mapped_column(Integer, default=0, comment="上涨股票数")
    down_count: Mapped[int] = mapped_column(Integer, default=0, comment="下跌股票数")
    flat_count: Mapped[int] = mapped_column(Integer, default=0, comment="平盘股票数")

    # 成交统计
    total_volume: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="总成交量")
    total_turnover: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="总成交额")
    avg_turnover_rate: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="平均换手率")

    # 板块表现
    sector_change: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="板块涨跌幅")
    sector_change_amount: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="板块涨跌额")

    # 时间信息
    trade_date: Mapped[date] = mapped_column(Date, index=True, comment="交易日期")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (
        Index('ix_sector_code_date', 'sector_code', 'trade_date'),
        Index('ix_sector_type_date', 'sector_type', 'trade_date'),
    )


class SectorStock(Base):
    """板块股票关联表"""
    __tablename__ = "sector_stocks"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    sector_code: Mapped[str] = mapped_column(String(20), index=True, comment="板块代码")
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")

    # 权重信息
    weight: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 6), comment="在板块中的权重")
    market_cap_weight: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 6), comment="市值权重")

    # 时间信息
    effective_date: Mapped[date] = mapped_column(Date, comment="生效日期")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('ix_sector_stock_unique', 'sector_code', 'stock_code', 'effective_date', unique=True),
    )


class StockSpotData(Base):
    """股票实时行情数据表 - 基于AKShare实时行情"""
    __tablename__ = "stock_spot_data"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    stock_name: Mapped[str] = mapped_column(String(50), comment="股票名称")

    # 价格信息
    current_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最新价")
    open_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="今开")
    high_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最高")
    low_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最低")
    pre_close: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="昨收")

    # 涨跌信息
    change_amount: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="涨跌额")
    change_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="涨跌幅")
    amplitude: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="振幅")

    # 成交信息
    volume: Mapped[int] = mapped_column(Integer, comment="成交量(手)")
    turnover: Mapped[Decimal] = mapped_column(Numeric(20, 2), comment="成交额(元)")
    turnover_rate: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="换手率")
    volume_ratio: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="量比")

    # 估值信息
    pe_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="市盈率-动态")
    pb_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="市净率")
    total_market_cap: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="总市值")
    float_market_cap: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="流通市值")

    # 涨速和短期表现
    speed: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="涨速")
    change_5min: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="5分钟涨跌")
    change_60day: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="60日涨跌幅")
    change_ytd: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="年初至今涨跌幅")

    # 买卖盘信息
    bid1_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="买一价")
    bid1_volume: Mapped[Optional[int]] = mapped_column(Integer, comment="买一量")
    ask1_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="卖一价")
    ask1_volume: Mapped[Optional[int]] = mapped_column(Integer, comment="卖一量")

    # 时间信息
    trade_date: Mapped[date] = mapped_column(Date, index=True, comment="交易日期")
    update_time: Mapped[datetime] = mapped_column(DateTime, index=True, comment="更新时间")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('ix_spot_stock_date', 'stock_code', 'trade_date'),
        Index('ix_spot_update_time', 'update_time'),
    )


class MarketSummary(Base):
    """市场总貌数据表 - 基于AKShare市场总貌"""
    __tablename__ = "market_summary"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    exchange: Mapped[str] = mapped_column(String(20), index=True, comment="交易所: SSE/SZSE")
    market_type: Mapped[str] = mapped_column(String(20), comment="市场类型: 主板/科创板/创业板等")

    # 股票统计
    listed_count: Mapped[int] = mapped_column(Integer, comment="上市公司数量")
    stock_count: Mapped[int] = mapped_column(Integer, comment="上市股票数量")

    # 股本统计(亿股)
    total_shares: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="总股本")
    float_shares: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="流通股本")

    # 市值统计(亿元)
    total_market_cap: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="总市值")
    float_market_cap: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="流通市值")

    # 估值指标
    avg_pe_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), comment="平均市盈率")

    # 成交统计
    turnover_amount: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="成交金额")
    turnover_volume: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="成交量")

    # 时间信息
    report_date: Mapped[date] = mapped_column(Date, index=True, comment="报告日期")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('ix_market_exchange_date', 'exchange', 'report_date'),
        Index('ix_market_type_date', 'market_type', 'report_date'),
    )


class RegionTradingData(Base):
    """地区交易数据表 - 基于AKShare地区交易排序"""
    __tablename__ = "region_trading_data"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    region_name: Mapped[str] = mapped_column(String(50), index=True, comment="地区名称")
    rank: Mapped[int] = mapped_column(Integer, comment="排名")

    # 交易统计(元)
    total_turnover: Mapped[Decimal] = mapped_column(Numeric(20, 2), comment="总交易额")
    market_share: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="占市场比例")
    stock_turnover: Mapped[Decimal] = mapped_column(Numeric(20, 2), comment="股票交易额")
    fund_turnover: Mapped[Decimal] = mapped_column(Numeric(20, 2), comment="基金交易额")
    bond_turnover: Mapped[Decimal] = mapped_column(Numeric(20, 2), comment="债券交易额")

    # 时间信息
    trade_month: Mapped[str] = mapped_column(String(6), index=True, comment="交易月份(YYYYMM)")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('ix_region_month_rank', 'trade_month', 'rank'),
        Index('ix_region_name_month', 'region_name', 'trade_month'),
    )


class IndustryTradingData(Base):
    """行业成交数据表 - 基于AKShare股票行业成交"""
    __tablename__ = "industry_trading_data"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    industry_code: Mapped[str] = mapped_column(String(50), index=True, comment="行业代码")
    industry_name: Mapped[str] = mapped_column(String(100), comment="行业名称")
    industry_name_cn: Mapped[str] = mapped_column(String(100), comment="行业中文名称")

    # 交易统计
    trading_days: Mapped[int] = mapped_column(Integer, comment="交易天数")

    # 成交金额统计
    turnover_amount: Mapped[int] = mapped_column(Integer, comment="成交金额(人民币元)")
    turnover_amount_ratio: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="成交金额占总计比例")

    # 成交股数统计
    turnover_volume: Mapped[int] = mapped_column(Integer, comment="成交股数")
    turnover_volume_ratio: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="成交股数占总计比例")

    # 成交笔数统计
    trade_count: Mapped[int] = mapped_column(Integer, comment="成交笔数")
    trade_count_ratio: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="成交笔数占总计比例")

    # 时间信息
    period_type: Mapped[str] = mapped_column(String(10), comment="统计周期: 当月/当年")
    trade_period: Mapped[str] = mapped_column(String(6), index=True, comment="交易周期(YYYYMM)")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('ix_industry_period_type', 'trade_period', 'period_type'),
        Index('ix_industry_code_period', 'industry_code', 'trade_period'),
    )


class StockBidAsk(Base):
    """股票买卖盘数据表 - 基于AKShare行情报价"""
    __tablename__ = "stock_bid_ask"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")

    # 卖盘数据
    sell_5_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="卖五价")
    sell_5_volume: Mapped[int] = mapped_column(Integer, comment="卖五量")
    sell_4_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="卖四价")
    sell_4_volume: Mapped[int] = mapped_column(Integer, comment="卖四量")
    sell_3_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="卖三价")
    sell_3_volume: Mapped[int] = mapped_column(Integer, comment="卖三量")
    sell_2_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="卖二价")
    sell_2_volume: Mapped[int] = mapped_column(Integer, comment="卖二量")
    sell_1_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="卖一价")
    sell_1_volume: Mapped[int] = mapped_column(Integer, comment="卖一量")

    # 买盘数据
    buy_1_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="买一价")
    buy_1_volume: Mapped[int] = mapped_column(Integer, comment="买一量")
    buy_2_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="买二价")
    buy_2_volume: Mapped[int] = mapped_column(Integer, comment="买二量")
    buy_3_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="买三价")
    buy_3_volume: Mapped[int] = mapped_column(Integer, comment="买三量")
    buy_4_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="买四价")
    buy_4_volume: Mapped[int] = mapped_column(Integer, comment="买四量")
    buy_5_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="买五价")
    buy_5_volume: Mapped[int] = mapped_column(Integer, comment="买五量")

    # 当前行情
    current_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最新价")
    avg_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="均价")
    change_amount: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="涨跌额")
    change_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="涨跌幅")

    # 成交统计
    total_volume: Mapped[int] = mapped_column(Integer, comment="总手")
    turnover_amount: Mapped[Decimal] = mapped_column(Numeric(20, 2), comment="成交额")
    turnover_rate: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="换手率")
    volume_ratio: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="量比")

    # 价格区间
    high_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最高价")
    low_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="最低价")
    open_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="今开价")
    pre_close: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="昨收价")

    # 涨跌停价格
    limit_up: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="涨停价")
    limit_down: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="跌停价")

    # 内外盘
    outer_volume: Mapped[int] = mapped_column(Integer, comment="外盘")
    inner_volume: Mapped[int] = mapped_column(Integer, comment="内盘")

    # 时间信息
    quote_time: Mapped[datetime] = mapped_column(DateTime, index=True, comment="报价时间")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('ix_bid_ask_stock_time', 'stock_code', 'quote_time'),
    )


class SystemConfiguration(Base):
    """系统配置表"""
    __tablename__ = "system_configurations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    config_key: Mapped[str] = mapped_column(String(100), unique=True, index=True, comment="配置键")
    config_value: Mapped[str] = mapped_column(Text, comment="配置值(JSON格式)")
    config_type: Mapped[str] = mapped_column(String(50), comment="配置类型")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="配置描述")

    # 状态信息
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('ix_system_config_key', 'config_key'),
        Index('ix_system_config_type', 'config_type'),
    )
