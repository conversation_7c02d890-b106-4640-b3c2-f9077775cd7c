#!/usr/bin/env python3
"""
修复SQLAlchemy Decimal导入问题
"""

import re

def fix_decimal_imports():
    """修复Decimal导入"""
    file_path = "backend/app/models/stock_redesign.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换所有的 Decimal( 为 DECIMAL(
    content = re.sub(r'Decimal\(', 'DECIMAL(', content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复所有Decimal导入问题")

if __name__ == "__main__":
    fix_decimal_imports()
