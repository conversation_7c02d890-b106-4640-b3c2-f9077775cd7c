import React, { useMemo } from 'react'
import { Card, Tag, Space, Typography, Alert, Tooltip } from 'antd'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  InfoCircleOutlined,
  TrophyOutlined,
  WarningOutlined
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { KellerChannel, TradingSignal, PriceData } from '@/utils/advancedIndicators'

const { Text } = Typography

interface KeltnerChannelChartProps {
  priceData: PriceData[]
  keltnerData: KellerChannel[]
  tradingSignals: TradingSignal[]
  height?: number
}

const KeltnerChannelChart: React.FC<KeltnerChannelChartProps> = ({
  priceData,
  keltnerData,
  tradingSignals,
  height = 400
}) => {

  // 准备图表数据
  const chartData = useMemo(() => {
    if (!priceData || priceData.length === 0) {
      return {
        candlestickData: [],
        volumeData: [],
        keltnerUpper: [],
        keltnerMiddle: [],
        keltnerLower: [],
        buySignals: [],
        sellSignals: []
      }
    }

    console.log('=== PREPARING CHART DATA ===')
    console.log('priceData length:', priceData.length)

    // 准备K线数据 - ECharts candlestick格式: [open, close, low, high]
    const candlestickData = priceData.map(item => [
      item.timestamp,
      item.open,
      item.close,
      item.low,
      item.high
    ])

    // 准备成交量数据
    const volumeData = priceData.map(item => [
      item.timestamp,
      item.volume
    ])

    // 生成Keltner通道数据（简化版本）
    const keltnerUpper = priceData.map(item => [
      item.timestamp,
      item.close * 1.02  // 上轨比收盘价高2%
    ])

    const keltnerMiddle = priceData.map(item => [
      item.timestamp,
      item.close  // 中轨等于收盘价
    ])

    const keltnerLower = priceData.map(item => [
      item.timestamp,
      item.close * 0.98  // 下轨比收盘价低2%
    ])

    // 生成交易信号
    const buySignals = []
    const sellSignals = []

    for (let i = 10; i < priceData.length; i += 15) {
      const item = priceData[i]
      if (Math.random() > 0.7) {
        if (Math.random() > 0.5) {
          buySignals.push([item.timestamp, item.low * 0.995])
        } else {
          sellSignals.push([item.timestamp, item.high * 1.005])
        }
      }
    }

    console.log('Candlestick data length:', candlestickData.length)
    console.log('Volume data length:', volumeData.length)
    console.log('Buy signals:', buySignals.length)
    console.log('Sell signals:', sellSignals.length)

    return {
      candlestickData,
      volumeData,
      keltnerUpper,
      keltnerMiddle,
      keltnerLower,
      buySignals,
      sellSignals
    }
  }, [priceData])

  const getSignalColor = (type: string) => {
    return type === 'buy' ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
  }

  const getSignalIcon = (type: string, strength: string) => {
    const IconComponent = type === 'buy' ? ArrowUpOutlined : ArrowDownOutlined
    const size = strength === 'strong' ? 16 : strength === 'medium' ? 14 : 12
    return <IconComponent style={{ fontSize: size }} />
  }

  const getStrengthTag = (strength: string) => {
    const colors = { strong: 'red', medium: 'orange', weak: 'blue' }
    const labels = { strong: '强', medium: '中', weak: '弱' }
    return <Tag color={colors[strength as keyof typeof colors]} size="small">
      {labels[strength as keyof typeof labels]}
    </Tag>
  }

  // 准备ECharts数据
  const echartsData = useMemo(() => {
    console.log('=== PREPARING ECHARTS DATA ===')
    console.log('priceData length:', priceData?.length || 0)
    console.log('keltnerData length:', keltnerData?.length || 0)
    console.log('First priceData item:', priceData?.[0])

    const groupedData: { [key: string]: Array<[string, number]> } = {}
    const candlestickData: Array<[string, number, number, number, number, number]> = []

    // 处理Keltner通道数据
    if (keltnerData && keltnerData.length > 0) {
      groupedData['upper'] = keltnerData.map(item => [item.timestamp, item.upper])
      groupedData['middle'] = keltnerData.map(item => [item.timestamp, item.middle])
      groupedData['lower'] = keltnerData.map(item => [item.timestamp, item.lower])
    }

    // 处理K线数据 - ECharts candlestick需要 [open, close, low, high] 格式
    priceData.forEach((item, index) => {
      if (index < 3) {
        console.log('Price data item:', item)
        console.log('Available fields:', Object.keys(item))
        console.log('OHLC values:', {
          open: item.open,
          close: item.close,
          low: item.low,
          high: item.high,
          date: item.date,
          timestamp: item.timestamp
        })
      }

      // 使用timestamp字段作为日期
      const dateField = item.timestamp

      // 确保所有数值都存在且为数字
      if (typeof item.open === 'number' &&
          typeof item.close === 'number' &&
          typeof item.low === 'number' &&
          typeof item.high === 'number') {
        candlestickData.push([
          dateField,
          item.open,
          item.close,
          item.low,
          item.high
        ])
      } else {
        console.warn('Invalid price data item (missing OHLC):', item)
      }
    })

    console.log('ECharts data prepared for', Object.keys(groupedData))
    console.log('Candlestick data length:', candlestickData.length)
    console.log('First candlestick item:', candlestickData[0])
    console.log('Sample candlestick data:', candlestickData.slice(0, 3))

    return { groupedData, candlestickData }
  }, [keltnerData, priceData])

  const echartsOption = {
    backgroundColor: '#ffffff',  // 纯白色背景
    animation: false, // 禁用动画提高性能和稳定性
    title: {
      text: 'Keltner通道 + K线图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any[]) => {
        if (!params || params.length === 0) return ''

        const date = params[0].axisValue
        let content = `<div style="margin-bottom: 4px; font-weight: bold;">${date}</div>`

        params.forEach(param => {
          console.log('Tooltip param:', param)
          console.log('Param value:', param.value)
          console.log('Param seriesType:', param.seriesType)

          if (param.seriesType === 'candlestick') {
            // K线数据格式: [date, open, close, low, high]
            if (param.value && Array.isArray(param.value) && param.value.length >= 5) {
              const [, open, close, low, high] = param.value
              if (typeof open === 'number' && typeof close === 'number' &&
                  typeof low === 'number' && typeof high === 'number') {
                const color = close >= open ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                content += `<div style="margin: 4px 0; padding: 4px; border-left: 3px solid ${color};">
                  <div><strong>K线</strong></div>
                  <div>开盘: ¥${open.toFixed(2)}</div>
                  <div>收盘: ¥${close.toFixed(2)}</div>
                  <div>最低: ¥${low.toFixed(2)}</div>
                  <div>最高: ¥${high.toFixed(2)}</div>
                </div>`
              } else {
                content += `<div style="margin: 4px 0;">K线数据格式错误</div>`
              }
            } else {
              content += `<div style="margin: 4px 0;">K线数据不完整</div>`
            }
          } else {
            // 线条数据
            const value = (param.value && typeof param.value[1] === 'number') ? param.value[1].toFixed(2) : 'N/A'
            content += `<div style="margin: 2px 0;">
              <span style="display: inline-block; width: 8px; height: 8px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              ${param.seriesName}: ¥${value}
            </div>`
          }
        })

        return content
      }
    },
    legend: {
      data: ['K线', '上轨', '中轨', '下轨'],
      bottom: 10
    },
    grid: [
      {
        id: 'main',
        left: '3%',
        right: '4%',
        top: '10%',
        height: '65%',
        containLabel: true
      },
      {
        id: 'volume',
        left: '3%',
        right: '4%',
        top: '80%',
        height: '15%',
        containLabel: true
      }
    ],
    xAxis: [
      {
        type: 'category',
        gridIndex: 0,
        boundaryGap: true,  // K线图需要boundaryGap为true
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      },
      {
        type: 'category',
        gridIndex: 1,
        boundaryGap: true,
        axisLabel: {
          rotate: 45,
          fontSize: 10,
          color: '#8392A5'
        },
        axisLine: {
          lineStyle: {
            color: '#8392A5'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        gridIndex: 0,
        scale: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E6E8EB',
            type: 'dashed'
          }
        },
        axisLabel: {
          formatter: '¥{value}',
          color: '#8392A5'
        },
        axisLine: { show: false },
        axisTick: { show: false }
      },
      {
        type: 'value',
        gridIndex: 1,
        scale: true,
        splitLine: { show: false },
        axisLabel: {
          formatter: '{value}',
          color: '#8392A5'
        },
        axisLine: { show: false },
        axisTick: { show: false }
      }
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100,
        filterMode: 'none', // 防止数据过滤导致的重置
        throttle: 50, // 节流，提高性能
        zoomLock: false, // 允许缩放
        moveOnMouseMove: true, // 鼠标移动时平移
        moveOnMouseWheel: false, // 禁用鼠标滚轮平移，避免冲突
        preventDefaultMouseMove: false
      },
      {
        type: 'slider',
        show: true,
        start: 70,
        end: 100,
        height: 20,
        bottom: 40,
        filterMode: 'none',
        throttle: 50,
        brushSelect: false, // 禁用刷选功能，避免意外重置
        zoomLock: false
      }
    ],
    series: [
      {
        name: 'K线',
        type: 'candlestick',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: echartsData.candlestickData,
        itemStyle: {
          color: 'rgb(214, 10, 34)',        // 阳线颜色 (红色) - 上涨
          color0: 'rgb(3, 123, 102)',       // 阴线颜色 (绿色) - 下跌
          borderColor: 'rgb(214, 10, 34)',  // 阳线边框
          borderColor0: 'rgb(3, 123, 102)'  // 阴线边框
        },
        emphasis: {
          itemStyle: {
            color: 'rgb(214, 10, 34)',
            color0: 'rgb(3, 123, 102)',
            borderColor: 'rgb(214, 10, 34)',
            borderColor0: 'rgb(3, 123, 102)'
          }
        }
      },
      {
        name: '上轨',
        type: 'line',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: echartsData.groupedData['上轨'] || [],
        lineStyle: { color: '#ff4d4f', width: 2, type: 'dashed' },
        itemStyle: { color: '#ff4d4f' },
        smooth: true,
        symbol: 'none',
        z: 10
      },
      {
        name: '中轨',
        type: 'line',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: echartsData.groupedData['中轨'] || [],
        lineStyle: { color: '#1890ff', width: 2 },
        itemStyle: { color: '#1890ff' },
        smooth: true,
        symbol: 'none',
        z: 10
      },
      {
        name: '下轨',
        type: 'line',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: echartsData.groupedData['下轨'] || [],
        lineStyle: { color: '#52c41a', width: 2, type: 'dashed' },
        itemStyle: { color: '#52c41a' },
        smooth: true,
        symbol: 'none',
        z: 10
      },
      // 成交量柱状图
      {
        name: '成交量',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        data: priceData.map(item => [item.timestamp, item.volume]),
        itemStyle: {
          color: function(params: any) {
            const dataIndex = params.dataIndex
            if (dataIndex < priceData.length) {
              const current = priceData[dataIndex]
              return current.close >= current.open ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
            }
            return '#ccc'
          }
        },
        barWidth: '60%'
      },
      // 买入信号
      {
        name: '买入信号',
        type: 'scatter',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: mockTradingSignals
          .filter(signal => signal.type === 'buy')
          .map(signal => {
            const priceItem = priceData.find(p => p.timestamp === signal.timestamp)
            return [signal.timestamp, priceItem?.low || signal.price, signal]
          }),
        symbol: 'triangle',
        symbolSize: function(data: any) {
          const signal = data[2]
          return signal.strength === 'strong' ? 15 : signal.strength === 'medium' ? 12 : 8
        },
        itemStyle: {
          color: '#52c41a',
          borderColor: '#389e0d',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'bottom',
          formatter: 'Buy',
          color: '#52c41a',
          fontSize: 10,
          fontWeight: 'bold'
        },
        z: 20
      },
      // 卖出信号
      {
        name: '卖出信号',
        type: 'scatter',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: mockTradingSignals
          .filter(signal => signal.type === 'sell')
          .map(signal => {
            const priceItem = priceData.find(p => p.timestamp === signal.timestamp)
            return [signal.timestamp, priceItem?.high || signal.price, signal]
          }),
        symbol: 'triangle',
        symbolRotate: 180,
        symbolSize: function(data: any) {
          const signal = data[2]
          return signal.strength === 'strong' ? 15 : signal.strength === 'medium' ? 12 : 8
        },
        itemStyle: {
          color: '#ff4d4f',
          borderColor: '#cf1322',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'top',
          formatter: 'Sell',
          color: '#ff4d4f',
          fontSize: 10,
          fontWeight: 'bold'
        },
        z: 20
      }
    ]
  }

  // 获取最新信号分析
  const latestKeltner = keltnerData[keltnerData.length - 1]
  const latestPrice = priceData[priceData.length - 1]?.close
  const recentSignals = tradingSignals.slice(-3) // 最近3个信号

  return (
    <Card 
      title={
        <Space>
          <span>Keltner通道 + K线图</span>
          <Tooltip title="Keltner通道结合K线图，显示价格走势、通道边界和交易信号">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        </Space>
      }
      size="small"
      extra={
        latestKeltner && (
          <Space>
            <Tag color={latestKeltner.signal === 'buy' ? 'green' : latestKeltner.signal === 'sell' ? 'red' : 'blue'}>
              {latestKeltner.signal === 'buy' ? '买入' : latestKeltner.signal === 'sell' ? '卖出' : '中性'}
            </Tag>
            {latestKeltner.signalStrength && getStrengthTag(latestKeltner.signalStrength)}
          </Space>
        )
      }
    >
      {/* 图表 */}
      <ReactECharts
        option={echartsOption}
        style={{ height: height || 400 }}
        opts={{
          renderer: 'canvas',
          useDirtyRect: true // 启用脏矩形优化
        }}
        notMerge={true} // 防止配置合并导致的问题
        lazyUpdate={true} // 延迟更新提高性能
      />
      
      {/* 当前状态分析 */}
      {latestKeltner && latestPrice && (
        <Alert
          message={latestKeltner.signalDescription || '通道分析'}
          type={latestKeltner.signal === 'buy' ? 'success' : latestKeltner.signal === 'sell' ? 'error' : 'info'}
          size="small"
          style={{ marginTop: 12 }}
          showIcon
        />
      )}

      {/* 通道数据 */}
      {latestKeltner && latestPrice && (
        <div style={{ marginTop: 12, padding: '8px', background: '#fafafa', borderRadius: '4px' }}>
          <Space direction="vertical" style={{ width: '100%' }} size="small">
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text strong>当前价格:</Text>
              <Text>{latestPrice ? latestPrice.toFixed(2) : 'N/A'}</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>上轨:</Text>
              <Text style={{ color: '#ff4d4f' }}>
                {latestKeltner.upper !== null && latestKeltner.upper !== undefined ? latestKeltner.upper.toFixed(2) : 'N/A'}
              </Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>中轨:</Text>
              <Text style={{ color: '#1890ff' }}>
                {latestKeltner.middle !== null && latestKeltner.middle !== undefined ? latestKeltner.middle.toFixed(2) : 'N/A'}
              </Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>下轨:</Text>
              <Text style={{ color: '#52c41a' }}>
                {latestKeltner.lower !== null && latestKeltner.lower !== undefined ? latestKeltner.lower.toFixed(2) : 'N/A'}
              </Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>通道宽度:</Text>
              <Text>
                {(latestKeltner.upper !== null && latestKeltner.lower !== null) ?
                  (latestKeltner.upper - latestKeltner.lower).toFixed(2) : 'N/A'}
              </Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>价格位置:</Text>
              <Text>
                {(latestPrice && latestKeltner.upper !== null && latestKeltner.lower !== null) ?
                  (((latestPrice - latestKeltner.lower) / (latestKeltner.upper - latestKeltner.lower)) * 100).toFixed(1) + '%' : 'N/A'}
              </Text>
            </div>
          </Space>
        </div>
      )}

      {/* 最近交易信号 */}
      {recentSignals.length > 0 && (
        <div style={{ marginTop: 12 }}>
          <Text strong style={{ marginBottom: 8, display: 'block' }}>
            <TrophyOutlined style={{ marginRight: 4 }} />
            最近交易信号:
          </Text>
          <Space direction="vertical" style={{ width: '100%' }} size="small">
            {recentSignals.map((signal, index) => (
              <div key={index} style={{ 
                padding: '6px 8px', 
                border: `1px solid ${getSignalColor(signal.type)}20`,
                borderRadius: '4px',
                background: `${getSignalColor(signal.type)}08`
              }}>
                <Space>
                  <span style={{ color: getSignalColor(signal.type) }}>
                    {getSignalIcon(signal.type, signal.strength)}
                  </span>
                  <Text strong style={{ color: getSignalColor(signal.type) }}>
                    {signal.type === 'buy' ? '买入' : '卖出'}
                  </Text>
                  <Text>¥{signal.price.toFixed(2)}</Text>
                  {getStrengthTag(signal.strength)}
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {signal.reason}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    置信度: {(signal.confidence * 100).toFixed(0)}%
                  </Text>
                </Space>
              </div>
            ))}
          </Space>
        </div>
      )}

      {/* 使用说明 */}
      <div style={{ marginTop: 12, fontSize: '12px', color: '#666' }}>
        <Space direction="vertical" size="small">
          <Text>
            <WarningOutlined style={{ marginRight: 4, color: '#faad14' }} />
            使用说明: 突破上轨为买入信号，跌破下轨为卖出信号，中轨为趋势参考线
          </Text>
          <Text>
            信号强度: 强信号 - 中信号 - 弱信号，结合成交量确认
          </Text>
        </Space>
      </div>
    </Card>
  )
}

export default KeltnerChannelChart
