"""
股票数据管理API
统一的数据管理接口
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import date, datetime
from loguru import logger

from app.core.database import get_db
from app.services.akshare_data_manager import AKShareDataManager
from app.models.stock_redesign import DataUpdateLog, StockBasicInfo

router = APIRouter()

# ==================== 响应模型 ====================

class StockBasicResponse(BaseModel):
    stock_code: str
    stock_name: str
    exchange: str
    industry: Optional[str] = None
    status: str
    list_date: Optional[str] = None
    updated_at: str

class KlineDataResponse(BaseModel):
    trade_date: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    turnover: float
    change_percent: Optional[float] = None

class RealtimeDataResponse(BaseModel):
    stock_code: str
    current_price: float
    change_amount: float
    change_percent: float
    open_price: float
    high_price: float
    low_price: float
    pre_close: float
    volume: int
    turnover: float
    pe_ratio: Optional[float] = None
    pb_ratio: Optional[float] = None
    total_market_cap: Optional[float] = None
    update_time: str

class UpdateResultResponse(BaseModel):
    success: bool
    message: str
    success_count: int
    error_count: int
    execution_time: Optional[int] = None

class BatchUpdateResponse(BaseModel):
    total_success: int
    total_error: int
    details: Dict[str, Dict[str, int]]

class DataStatsResponse(BaseModel):
    total_stocks: int
    active_stocks: int
    last_update_time: Optional[str] = None
    kline_data_count: int
    realtime_data_count: int

# ==================== 数据查询接口 ====================

@router.get("/stocks", response_model=List[StockBasicResponse])
async def get_stocks(
    limit: int = Query(100, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    exchange: Optional[str] = Query(None, description="交易所筛选: SH/SZ"),
    status: str = Query("active", description="状态筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取股票列表"""
    try:
        data_manager = AKShareDataManager(db)
        stocks = await data_manager.get_stock_list(limit=limit, offset=offset)
        
        # 应用筛选条件
        if exchange:
            stocks = [s for s in stocks if s.get('exchange') == exchange.upper()]
        if status:
            stocks = [s for s in stocks if s.get('status') == status]
        
        return stocks
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票列表失败")

@router.get("/stocks/{stock_code}/kline", response_model=List[KlineDataResponse])
async def get_stock_kline(
    stock_code: str,
    days: int = Query(30, description="获取天数"),
    db: AsyncSession = Depends(get_db)
):
    """获取股票K线数据"""
    try:
        data_manager = AKShareDataManager(db)
        kline_data = await data_manager.get_kline_data(stock_code, days=days)
        return kline_data
    except Exception as e:
        logger.error(f"获取K线数据失败 {stock_code}: {e}")
        raise HTTPException(status_code=500, detail=f"获取K线数据失败: {stock_code}")

@router.get("/stocks/{stock_code}/realtime", response_model=Optional[RealtimeDataResponse])
async def get_stock_realtime(
    stock_code: str,
    db: AsyncSession = Depends(get_db)
):
    """获取股票实时行情"""
    try:
        data_manager = AKShareDataManager(db)
        realtime_data = await data_manager.get_realtime_data(stock_code)
        
        if not realtime_data:
            raise HTTPException(status_code=404, detail=f"未找到股票 {stock_code} 的实时数据")
        
        return realtime_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实时数据失败 {stock_code}: {e}")
        raise HTTPException(status_code=500, detail=f"获取实时数据失败: {stock_code}")

# ==================== 数据更新接口 ====================

@router.post("/update/basic-info", response_model=UpdateResultResponse)
async def update_basic_info(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """更新股票基础信息"""
    try:
        data_manager = AKShareDataManager(db)
        
        # 在后台任务中执行更新
        def run_update():
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success_count, error_count = loop.run_until_complete(
                    data_manager.update_stock_basic_info()
                )
                return success_count, error_count
            finally:
                loop.close()
        
        background_tasks.add_task(run_update)
        
        return UpdateResultResponse(
            success=True,
            message="股票基础信息更新任务已启动",
            success_count=0,
            error_count=0
        )
    except Exception as e:
        logger.error(f"启动基础信息更新失败: {e}")
        raise HTTPException(status_code=500, detail="启动基础信息更新失败")

@router.post("/update/kline/{stock_code}", response_model=UpdateResultResponse)
async def update_stock_kline(
    stock_code: str,
    days: int = Query(30, description="更新天数"),
    db: AsyncSession = Depends(get_db)
):
    """更新单只股票K线数据"""
    try:
        start_time = datetime.now()
        data_manager = AKShareDataManager(db)
        success_count, error_count = await data_manager.update_kline_data(stock_code, days=days)
        execution_time = int((datetime.now() - start_time).total_seconds())
        
        return UpdateResultResponse(
            success=error_count == 0,
            message=f"K线数据更新完成: 成功 {success_count}, 失败 {error_count}",
            success_count=success_count,
            error_count=error_count,
            execution_time=execution_time
        )
    except Exception as e:
        logger.error(f"更新K线数据失败 {stock_code}: {e}")
        raise HTTPException(status_code=500, detail=f"更新K线数据失败: {stock_code}")

@router.post("/update/realtime", response_model=UpdateResultResponse)
async def update_realtime_data(
    stock_codes: Optional[str] = Query(None, description="股票代码列表，逗号分隔"),
    db: AsyncSession = Depends(get_db)
):
    """更新实时行情数据"""
    try:
        start_time = datetime.now()
        data_manager = AKShareDataManager(db)
        
        codes_list = None
        if stock_codes:
            codes_list = [code.strip() for code in stock_codes.split(',')]
        
        success_count, error_count = await data_manager.update_realtime_data(codes_list)
        execution_time = int((datetime.now() - start_time).total_seconds())
        
        return UpdateResultResponse(
            success=error_count == 0,
            message=f"实时行情更新完成: 成功 {success_count}, 失败 {error_count}",
            success_count=success_count,
            error_count=error_count,
            execution_time=execution_time
        )
    except Exception as e:
        logger.error(f"更新实时行情失败: {e}")
        raise HTTPException(status_code=500, detail="更新实时行情失败")

@router.post("/update/batch-kline", response_model=BatchUpdateResponse)
async def batch_update_kline(
    background_tasks: BackgroundTasks,
    stock_codes: str = Query(..., description="股票代码列表，逗号分隔"),
    days: int = Query(30, description="更新天数"),
    db: AsyncSession = Depends(get_db)
):
    """批量更新K线数据"""
    try:
        codes_list = [code.strip() for code in stock_codes.split(',')]
        
        if len(codes_list) > 50:
            raise HTTPException(status_code=400, detail="批量更新股票数量不能超过50只")
        
        data_manager = AKShareDataManager(db)
        
        # 在后台任务中执行批量更新
        def run_batch_update():
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    data_manager.batch_update_kline(codes_list, days=days)
                )
                return result
            finally:
                loop.close()
        
        background_tasks.add_task(run_batch_update)
        
        return BatchUpdateResponse(
            total_success=0,
            total_error=0,
            details={code: {"success": 0, "error": 0} for code in codes_list}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动批量K线更新失败: {e}")
        raise HTTPException(status_code=500, detail="启动批量K线更新失败")

# ==================== 数据统计接口 ====================

@router.get("/stats", response_model=DataStatsResponse)
async def get_data_stats(db: AsyncSession = Depends(get_db)):
    """获取数据统计信息"""
    try:
        from sqlalchemy import select, func
        from app.models.stock_redesign import StockKlineDaily, StockRealtimeData
        
        # 统计股票数量
        total_stocks_result = await db.execute(
            select(func.count(StockBasicInfo.id))
        )
        total_stocks = total_stocks_result.scalar()
        
        active_stocks_result = await db.execute(
            select(func.count(StockBasicInfo.id))
            .where(StockBasicInfo.status == "active")
        )
        active_stocks = active_stocks_result.scalar()
        
        # 统计K线数据数量
        kline_count_result = await db.execute(
            select(func.count(StockKlineDaily.id))
        )
        kline_data_count = kline_count_result.scalar()
        
        # 统计实时数据数量
        realtime_count_result = await db.execute(
            select(func.count(StockRealtimeData.id))
        )
        realtime_data_count = realtime_count_result.scalar()
        
        # 获取最后更新时间
        last_update_result = await db.execute(
            select(DataUpdateLog.created_at)
            .order_by(DataUpdateLog.created_at.desc())
            .limit(1)
        )
        last_update = last_update_result.scalar_one_or_none()
        
        return DataStatsResponse(
            total_stocks=total_stocks or 0,
            active_stocks=active_stocks or 0,
            last_update_time=last_update.isoformat() if last_update else None,
            kline_data_count=kline_data_count or 0,
            realtime_data_count=realtime_data_count or 0
        )
    except Exception as e:
        logger.error(f"获取数据统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据统计失败")
