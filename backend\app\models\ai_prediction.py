"""
AI预测相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, DateTime, Date, Numeric, Integer, Text, JSON, Index
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class AIPrediction(Base):
    """AI预测结果表"""
    __tablename__ = "ai_predictions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    prediction_date: Mapped[date] = mapped_column(Date, index=True, comment="预测日期")
    target_date: Mapped[date] = mapped_column(Date, comment="目标日期")
    model_version: Mapped[str] = mapped_column(String(20), comment="模型版本")
    
    # 预测结果
    predicted_price: Mapped[Decimal] = mapped_column(Numeric(10, 3), comment="预测价格")
    predicted_change: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="预测涨跌幅")
    confidence_score: Mapped[Decimal] = mapped_column(Numeric(5, 4), comment="置信度")
    trend_direction: Mapped[str] = mapped_column(String(10), comment="趋势方向: up/down/sideways")
    
    # 预测区间
    price_upper: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="价格上限")
    price_lower: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="价格下限")
    
    # 特征重要性
    feature_importance: Mapped[Optional[dict]] = mapped_column(JSON, comment="特征重要性")
    
    # 预测依据
    prediction_basis: Mapped[Optional[str]] = mapped_column(Text, comment="预测依据")
    risk_factors: Mapped[Optional[str]] = mapped_column(Text, comment="风险因素")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_prediction_stock_date', 'stock_code', 'prediction_date'),
        Index('ix_prediction_target_date', 'target_date'),
    )


class PatternRecognition(Base):
    """形态识别结果表"""
    __tablename__ = "pattern_recognitions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    recognition_date: Mapped[date] = mapped_column(Date, index=True, comment="识别日期")
    pattern_type: Mapped[str] = mapped_column(String(30), comment="形态类型")
    pattern_name: Mapped[str] = mapped_column(String(50), comment="形态名称")
    
    # 形态特征
    start_date: Mapped[date] = mapped_column(Date, comment="形态开始日期")
    end_date: Mapped[Optional[date]] = mapped_column(Date, comment="形态结束日期")
    confidence: Mapped[Decimal] = mapped_column(Numeric(5, 4), comment="识别置信度")
    completion_rate: Mapped[Decimal] = mapped_column(Numeric(5, 4), comment="形态完成度")
    
    # 关键价位
    key_prices: Mapped[Optional[dict]] = mapped_column(JSON, comment="关键价位")
    support_level: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="支撑位")
    resistance_level: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="阻力位")
    
    # 预期目标
    target_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="目标价位")
    stop_loss: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="止损价位")
    
    # 形态描述
    description: Mapped[Optional[str]] = mapped_column(Text, comment="形态描述")
    trading_suggestion: Mapped[Optional[str]] = mapped_column(Text, comment="交易建议")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_pattern_stock_date', 'stock_code', 'recognition_date'),
        Index('ix_pattern_type_date', 'pattern_type', 'recognition_date'),
    )
