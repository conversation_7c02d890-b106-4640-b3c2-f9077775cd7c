"""
API路由初始化
"""

from fastapi import APIRouter
from app.api import data_management, akshare_data, stock_data_management

# 创建主路由器
api_router = APIRouter()

# 包含数据管理路由
api_router.include_router(
    data_management.router,
    prefix="/data-management",
    tags=["data-management"]
)

# 包含AKShare数据路由
api_router.include_router(
    akshare_data.router,
    prefix="/akshare",
    tags=["akshare-data"]
)

# 包含重构后的股票数据管理路由
api_router.include_router(
    stock_data_management.router,
    prefix="/stock-data",
    tags=["stock-data-management"]
)
