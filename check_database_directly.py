#!/usr/bin/env python3
"""
直接检查数据库中的数据
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import AsyncSessionLocal
from app.models.stock import StockSpotData
from sqlalchemy import select
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(name)s | %(message)s')
logger = logging.getLogger(__name__)

async def check_database_data():
    """直接检查数据库中的数据"""
    try:
        logger.info("=" * 80)
        logger.info("🔍 直接检查数据库中的StockSpotData表")
        logger.info("=" * 80)
        
        async with AsyncSessionLocal() as session:
            # 查询所有数据
            logger.info("📊 查询StockSpotData表中的所有数据")
            result = await session.execute(select(StockSpotData))
            all_data = result.scalars().all()
            
            if all_data:
                logger.info(f"✅ 数据库中有 {len(all_data)} 条记录")
                
                for i, record in enumerate(all_data):
                    logger.info(f"  {i+1}. {record.stock_code} {record.stock_name}: ¥{record.current_price} ({record.change_percent:+.2f}%)")
                    logger.info(f"      交易日期: {record.trade_date}, 更新时间: {record.update_time}")
                
                # 检查是否是真实数据
                first_price = float(all_data[0].current_price)
                if first_price != 10.5:
                    logger.info("🎉 数据库包含真实数据!")
                    return True, all_data
                else:
                    logger.warning("⚠️  数据库仍然是模拟数据")
                    return False, all_data
            else:
                logger.error("❌ 数据库中没有数据")
                return False, []
                
    except Exception as e:
        logger.error(f"❌ 检查数据库失败: {e}", exc_info=True)
        return False, []

async def check_api_response():
    """检查API响应"""
    try:
        logger.info("\n" + "=" * 40)
        logger.info("🌐 检查API响应")
        logger.info("=" * 40)
        
        import requests
        
        response = requests.get("http://localhost:8000/api/v1/akshare/spot-data?limit=10", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ API返回 {len(data)} 条数据")
            
            for i, stock in enumerate(data):
                logger.info(f"  {i+1}. {stock['stock_name']} ({stock['stock_code']}): ¥{stock['current_price']} ({stock['change_percent']:+.2f}%)")
            
            # 检查是否是真实数据
            if data and float(data[0]['current_price']) != 10.5:
                logger.info("🎉 API返回真实数据!")
                return True
            else:
                logger.warning("⚠️  API返回模拟数据")
                return False
        else:
            logger.error(f"❌ API调用失败: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查API失败: {e}")
        return False

async def main():
    """主函数"""
    # 1. 检查数据库
    db_has_real_data, db_records = await check_database_data()
    
    # 2. 检查API
    api_has_real_data = await check_api_response()
    
    logger.info("\n" + "=" * 80)
    logger.info("📋 检查结果总结")
    logger.info("=" * 80)
    logger.info(f"数据库状态: {'✅ 真实数据' if db_has_real_data else '❌ 模拟数据'} ({len(db_records)} 条记录)")
    logger.info(f"API状态: {'✅ 真实数据' if api_has_real_data else '❌ 模拟数据'}")
    
    if db_has_real_data and not api_has_real_data:
        logger.warning("⚠️  数据库有真实数据，但API返回模拟数据 - 可能存在缓存或查询问题")
    elif not db_has_real_data and api_has_real_data:
        logger.warning("⚠️  数据库是模拟数据，但API返回真实数据 - 数据可能来自其他源")
    elif db_has_real_data and api_has_real_data:
        logger.info("🎉 数据库和API都返回真实数据 - 系统正常!")
    else:
        logger.error("❌ 数据库和API都返回模拟数据")
    
    logger.info("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
