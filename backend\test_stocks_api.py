"""
测试股票API端点
"""

import requests
import json

def test_stocks_api():
    base_url = "http://localhost:8000"
    
    print("🚀 开始测试股票API端点")
    
    # 测试根路径
    print("\n1. 测试根路径")
    try:
        response = requests.get(f"{base_url}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试股票列表API
    print("\n2. 测试股票列表API")
    try:
        response = requests.get(f"{base_url}/api/v1/stocks/list?limit=5")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试健康检查
    print("\n3. 测试健康检查")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"健康状态: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试股票详情API
    print("\n4. 测试股票详情API")
    try:
        response = requests.get(f"{base_url}/api/v1/stocks/000001/info")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"股票详情: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"错误: {e}")

    # 测试实时数据API
    print("\n4.5. 测试实时数据API")
    try:
        response = requests.get(f"{base_url}/api/v1/stocks/000001/realtime")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"实时数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试K线数据API
    print("\n5. 测试K线数据API")
    try:
        response = requests.get(f"{base_url}/api/v1/stocks/000001/kline?period=daily&limit=10")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"K线数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_stocks_api()
