#!/usr/bin/env python3
import requests
import json

try:
    print("🔍 测试API连接 (使用localhost)...")
    response = requests.get("http://localhost:8000/api/v1/akshare/spot-data?limit=3", timeout=10)
    print(f"✅ 状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"📊 数据条数: {len(data)}")
        for i, item in enumerate(data):
            print(f"  {i+1}. {item['stock_name']} ({item['stock_code']}): ¥{item['current_price']}")

        # 检查是否是真实数据
        if data and float(data[0]['current_price']) != 10.5:
            print("🎉 API返回真实数据!")
        else:
            print("⚠️  API返回模拟数据")
    else:
        print(f"❌ 错误: {response.text}")
except Exception as e:
    print(f"❌ 连接失败: {e}")
