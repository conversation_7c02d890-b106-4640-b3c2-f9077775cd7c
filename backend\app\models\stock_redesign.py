"""
重构后的股票数据模型
统一的数据架构设计
"""

from sqlalchemy import Column, Integer, String, Date, DateTime, Text, Boolean, JSON, BigInteger, Index, UniqueConstraint, DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Mapped, mapped_column
from datetime import datetime, date
from typing import Optional, Dict, Any
from decimal import Decimal as PyDecimal

from app.core.database import Base

class StockBasicInfo(Base):
    """股票基础信息表 - 核心主表"""
    __tablename__ = "stock_basic_info"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), unique=True, index=True, comment="股票代码")
    stock_name: Mapped[str] = mapped_column(String(50), comment="股票名称")
    exchange: Mapped[str] = mapped_column(String(10), comment="交易所: SH/SZ")
    
    # 分类信息
    industry: Mapped[Optional[str]] = mapped_column(String(50), comment="所属行业")
    concept_sectors: Mapped[Optional[Dict]] = mapped_column(JSON, comment="概念板块")
    
    # 上市信息
    list_date: Mapped[Optional[date]] = mapped_column(Date, comment="上市日期")
    delist_date: Mapped[Optional[date]] = mapped_column(Date, comment="退市日期")
    
    # 股本信息
    total_shares: Mapped[Optional[int]] = mapped_column(BigInteger, comment="总股本")
    float_shares: Mapped[Optional[int]] = mapped_column(BigInteger, comment="流通股本")
    market_cap: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="总市值")
    
    # 状态信息
    status: Mapped[str] = mapped_column(String(20), default="active", comment="状态: active/suspended/delisted")
    is_st: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否ST股票")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    __table_args__ = (
        Index('ix_stock_basic_exchange', 'exchange'),
        Index('ix_stock_basic_industry', 'industry'),
        Index('ix_stock_basic_status', 'status'),
    )


class StockKlineDaily(Base):
    """日线K线数据表"""
    __tablename__ = "stock_kline_daily"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    trade_date: Mapped[date] = mapped_column(Date, comment="交易日期")
    
    # OHLC数据
    open_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="开盘价")
    high_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="最高价")
    low_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="最低价")
    close_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="收盘价")

    # 成交数据
    volume: Mapped[int] = mapped_column(BigInteger, comment="成交量")
    turnover: Mapped[PyDecimal] = mapped_column(DECIMAL(15, 2), comment="成交额")

    # 计算指标
    change_amount: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(10, 3), comment="涨跌额")
    change_percent: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="涨跌幅")
    turnover_rate: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="换手率")
    amplitude: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="振幅")
    
    # 复权信息
    adj_factor: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(10, 6), comment="复权因子")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")

    __table_args__ = (
        UniqueConstraint('stock_code', 'trade_date', name='uk_stock_date'),
        Index('ix_kline_daily_date', 'trade_date'),
        Index('ix_kline_daily_code_date', 'stock_code', 'trade_date'),
    )


class StockRealtimeData(Base):
    """实时行情数据表"""
    __tablename__ = "stock_realtime_data"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), unique=True, index=True, comment="股票代码")
    
    # 价格信息
    current_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="最新价")
    change_amount: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="涨跌额")
    change_percent: Mapped[PyDecimal] = mapped_column(DECIMAL(8, 4), comment="涨跌幅")
    
    # 当日价格
    open_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="今开")
    high_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="最高")
    low_price: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="最低")
    pre_close: Mapped[PyDecimal] = mapped_column(DECIMAL(10, 3), comment="昨收")
    
    # 成交数据
    volume: Mapped[int] = mapped_column(BigInteger, comment="成交量")
    turnover: Mapped[PyDecimal] = mapped_column(DECIMAL(15, 2), comment="成交额")
    turnover_rate: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="换手率")
    
    # 盘口数据
    bid_prices: Mapped[Optional[Dict]] = mapped_column(JSON, comment="五档买盘")
    ask_prices: Mapped[Optional[Dict]] = mapped_column(JSON, comment="五档卖盘")
    
    # 市场指标
    pe_ratio: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 2), comment="市盈率")
    pb_ratio: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 2), comment="市净率")
    total_market_cap: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="总市值")
    float_market_cap: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="流通市值")
    
    # 时间信息
    trade_date: Mapped[date] = mapped_column(Date, comment="交易日期")
    update_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="更新时间")

    __table_args__ = (
        Index('ix_realtime_update_time', 'update_time'),
        Index('ix_realtime_trade_date', 'trade_date'),
    )


class StockFinancialData(Base):
    """股票财务数据表"""
    __tablename__ = "stock_financial_data"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    report_date: Mapped[date] = mapped_column(Date, comment="报告期")
    report_type: Mapped[str] = mapped_column(String(20), comment="报告类型: Q1/Q2/Q3/annual")
    
    # 盈利能力
    revenue: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="营业收入")
    net_profit: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="净利润")
    eps: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="每股收益")
    roe: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="净资产收益率")
    
    # 财务状况
    total_assets: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="总资产")
    total_liabilities: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="总负债")
    shareholders_equity: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="股东权益")
    
    # 现金流
    operating_cash_flow: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="经营现金流")
    free_cash_flow: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(15, 2), comment="自由现金流")
    
    # 财务比率
    debt_ratio: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="资产负债率")
    current_ratio: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="流动比率")
    quick_ratio: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="速动比率")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")

    __table_args__ = (
        UniqueConstraint('stock_code', 'report_date', 'report_type', name='uk_financial_report'),
        Index('ix_financial_report_date', 'report_date'),
        Index('ix_financial_code_date', 'stock_code', 'report_date'),
    )


class DataUpdateLog(Base):
    """数据更新日志表"""
    __tablename__ = "data_update_log"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    data_type: Mapped[str] = mapped_column(String(50), comment="数据类型")
    stock_code: Mapped[Optional[str]] = mapped_column(String(10), comment="股票代码")
    update_date: Mapped[date] = mapped_column(Date, comment="更新日期")
    
    # 更新结果
    records_count: Mapped[Optional[int]] = mapped_column(Integer, comment="记录数量")
    status: Mapped[str] = mapped_column(String(20), comment="状态: success/failed/partial")
    error_message: Mapped[Optional[str]] = mapped_column(Text, comment="错误信息")
    execution_time: Mapped[Optional[int]] = mapped_column(Integer, comment="执行时间(秒)")
    
    # 详细信息
    source_api: Mapped[Optional[str]] = mapped_column(String(50), comment="数据源API")
    update_params: Mapped[Optional[Dict]] = mapped_column(JSON, comment="更新参数")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")

    __table_args__ = (
        Index('ix_update_log_type_date', 'data_type', 'update_date'),
        Index('ix_update_log_status', 'status'),
        Index('ix_update_log_created', 'created_at'),
    )


class CustomIndicator(Base):
    """自定义技术指标表"""
    __tablename__ = "custom_indicators"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(50), comment="指标名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="指标描述")
    
    # 指标定义
    formula: Mapped[str] = mapped_column(Text, comment="计算公式")
    parameters: Mapped[Dict] = mapped_column(JSON, comment="参数配置")
    data_requirements: Mapped[Dict] = mapped_column(JSON, comment="数据需求")
    
    # 显示配置
    chart_config: Mapped[Optional[Dict]] = mapped_column(JSON, comment="图表配置")
    color_scheme: Mapped[Optional[Dict]] = mapped_column(JSON, comment="颜色方案")
    
    # 元信息
    category: Mapped[str] = mapped_column(String(30), comment="指标分类")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    created_by: Mapped[Optional[str]] = mapped_column(String(50), comment="创建者")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    __table_args__ = (
        Index('ix_custom_indicator_category', 'category'),
        Index('ix_custom_indicator_active', 'is_active'),
    )


class StockPattern(Base):
    """股票形态识别表"""
    __tablename__ = "stock_patterns"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    pattern_name: Mapped[str] = mapped_column(String(50), comment="形态名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="形态描述")
    
    # 识别规则
    recognition_rules: Mapped[Dict] = mapped_column(JSON, comment="识别规则")
    time_window: Mapped[int] = mapped_column(Integer, comment="时间窗口(天)")
    confidence_threshold: Mapped[PyDecimal] = mapped_column(DECIMAL(5, 4), comment="置信度阈值")
    
    # 交易信号
    signal_type: Mapped[str] = mapped_column(String(20), comment="信号类型: buy/sell/hold")
    success_rate: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(5, 4), comment="历史成功率")
    
    # 配置信息
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    category: Mapped[str] = mapped_column(String(30), comment="形态分类")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    __table_args__ = (
        Index('ix_pattern_category', 'category'),
        Index('ix_pattern_signal', 'signal_type'),
        Index('ix_pattern_active', 'is_active'),
    )


class BacktestStrategy(Base):
    """回测策略表"""
    __tablename__ = "backtest_strategies"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    strategy_name: Mapped[str] = mapped_column(String(50), comment="策略名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="策略描述")
    
    # 策略配置
    entry_rules: Mapped[Dict] = mapped_column(JSON, comment="入场规则")
    exit_rules: Mapped[Dict] = mapped_column(JSON, comment="出场规则")
    risk_management: Mapped[Dict] = mapped_column(JSON, comment="风险管理")
    
    # 回测参数
    initial_capital: Mapped[PyDecimal] = mapped_column(DECIMAL(15, 2), comment="初始资金")
    commission_rate: Mapped[PyDecimal] = mapped_column(DECIMAL(6, 4), comment="手续费率")
    slippage: Mapped[PyDecimal] = mapped_column(DECIMAL(6, 4), comment="滑点")
    
    # 回测结果
    total_return: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="总收益率")
    annual_return: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="年化收益率")
    max_drawdown: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="最大回撤")
    sharpe_ratio: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(8, 4), comment="夏普比率")
    win_rate: Mapped[Optional[PyDecimal]] = mapped_column(DECIMAL(5, 4), comment="胜率")
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    last_backtest_date: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后回测时间")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    __table_args__ = (
        Index('ix_strategy_active', 'is_active'),
        Index('ix_strategy_return', 'total_return'),
    )
