import React, { useState, useEffect, useMemo } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Button,
  Table,
  Tabs,
  Typography,
  Space,
  Tag,
  Statistic,
  Progress,
  Alert,
  Spin,
  Tooltip,
} from 'antd'
import {
  Line<PERSON>hartOutlined,
  Bar<PERSON>hartOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons'
import { Line, Bar, Scatter } from '@ant-design/plots'
import AdvancedTechnicalIndicators, { PriceData, VolumeProfile, IndicatorInfo, TradingSignal } from '@/utils/advancedIndicators'
import VolumeAnalysis, { LargeOrder, VolumeAnalysisResult } from '@/utils/volumeAnalysis'
import TechnicalIndicatorConfig, { IndicatorConfig } from '@/components/TechnicalIndicatorConfig'
import IndicatorDetailModal from '@/components/IndicatorDetailModal'
import KeltnerChannelChart from '@/components/KeltnerChannelChartNew'

const { Title, Text } = Typography
const { Option } = Select

interface AdvancedAnalysisData {
  technicalIndicators: {
    kaufmanAMA: any[]
    hullMA: any[]
    stochasticRSI: any[]
    williamsVIX: any[]
    kellerChannels: any[]
    atr: any[]
  }
  volumeAnalysis: VolumeAnalysisResult
  volumeProfile: VolumeProfile
  largeOrders: LargeOrder[]
  riskMetrics: {
    volatility: number
    sharpeRatio: number
    maxDrawdown: number
    beta: number
  }
  tradingSignals: {
    keltnerSignals: TradingSignal[]
  }
}

const StockAnalysisAdvanced: React.FC = () => {
  const [selectedStock, setSelectedStock] = useState('000001')
  const [timeframe, setTimeframe] = useState('1D')
  const [loading, setLoading] = useState(false)
  const [analysisData, setAnalysisData] = useState<AdvancedAnalysisData | null>(null)
  const [activeTab, setActiveTab] = useState('technical')
  const [configVisible, setConfigVisible] = useState(false)
  const [indicatorConfig, setIndicatorConfig] = useState<IndicatorConfig | null>(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedIndicator, setSelectedIndicator] = useState<{
    key: string
    info: any
    value?: number
    signal?: any
  } | null>(null)

  // 模拟股票数据
  const mockPriceData: PriceData[] = useMemo(() => {
    const data: PriceData[] = []
    let basePrice = 12.5
    
    for (let i = 0; i < 100; i++) {
      const date = new Date()
      date.setDate(date.getDate() - (100 - i))
      
      const change = (Math.random() - 0.5) * 0.5
      basePrice += change
      
      const open = basePrice
      const high = open + Math.random() * 0.3
      const low = open - Math.random() * 0.3
      const close = low + Math.random() * (high - low)
      const volume = Math.floor(Math.random() * 1000000 + 500000)
      
      data.push({
        open,
        high,
        low,
        close,
        volume,
        timestamp: date.toISOString().split('T')[0]
      })
      
      basePrice = close
    }
    
    return data
  }, [selectedStock, timeframe])

  useEffect(() => {
    loadAdvancedAnalysis()
  }, [selectedStock, timeframe, mockPriceData])

  const loadAdvancedAnalysis = async () => {
    setLoading(true)
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 计算高级技术指标
      const kaufmanAMA = AdvancedTechnicalIndicators.kaufmanAMA(mockPriceData, 14, 2, 30)
      const hullMA = AdvancedTechnicalIndicators.hullMA(mockPriceData, 20)
      const stochasticRSI = AdvancedTechnicalIndicators.stochasticRSI(mockPriceData, 14, 14)
      const williamsVIX = AdvancedTechnicalIndicators.williamsVIX(mockPriceData, 14)
      const kellerChannels = AdvancedTechnicalIndicators.kellerChannels(mockPriceData, 20, 2)
      const atr = AdvancedTechnicalIndicators.averageTrueRange(mockPriceData, 14)

      // 生成交易信号
      const keltnerSignals = AdvancedTechnicalIndicators.generateKeltnerTradingSignals(mockPriceData, kellerChannels)
      
      // 计算量价分析
      const volumeAnalysis = VolumeAnalysis.comprehensiveVolumeAnalysis(mockPriceData, 20)
      const volumeProfile = AdvancedTechnicalIndicators.volumeProfile(mockPriceData, 50)
      const largeOrders = VolumeAnalysis.detectLargeOrders(mockPriceData, 800000)
      
      // 计算风险指标
      const riskMetrics = {
        volatility: 0.234,
        sharpeRatio: 1.45,
        maxDrawdown: -0.156,
        beta: 1.12
      }
      
      setAnalysisData({
        technicalIndicators: {
          kaufmanAMA: kaufmanAMA.map(item => ({ date: item.timestamp, value: item.value })),
          hullMA: hullMA.map(item => ({ date: item.timestamp, value: item.value })),
          stochasticRSI: stochasticRSI.map(item => ({ 
            date: item.timestamp, 
            k: item.k, 
            d: item.d, 
            rsi: item.rsi 
          })),
          williamsVIX: williamsVIX.map(item => ({ date: item.timestamp, value: item.value })),
          kellerChannels: kellerChannels.map(item => {
            const mapped = {
              date: item.timestamp,
              upper: typeof item.upper === 'number' && !isNaN(item.upper) ? item.upper : 0,
              middle: typeof item.middle === 'number' && !isNaN(item.middle) ? item.middle : 0,
              lower: typeof item.lower === 'number' && !isNaN(item.lower) ? item.lower : 0
            }

            return mapped
          }),
          atr: atr.map(item => ({ date: item.timestamp, value: item.value }))
        },
        volumeAnalysis,
        volumeProfile,
        largeOrders,
        riskMetrics,
        tradingSignals: {
          keltnerSignals
        }
      })
    } catch (error) {
      console.error('Failed to load advanced analysis:', error)
    } finally {
      setLoading(false)
    }
  }

  const renderTechnicalIndicators = () => {
    if (!analysisData) return null

    const { technicalIndicators } = analysisData
    const indicatorInfo = AdvancedTechnicalIndicators.getIndicatorInfo()

    // 获取最新指标值和信号分析
    const getLatestAnalysis = (indicatorName: string, data: any[]) => {
      if (!data || data.length === 0) return null
      const latestItem = data[data.length - 1]
      if (!latestItem) return null

      const latestValue = latestItem.value
      if (latestValue === undefined || latestValue === null || isNaN(latestValue)) return null

      return AdvancedTechnicalIndicators.analyzeIndicatorSignal(indicatorName, latestValue)
    }

    const amaAnalysis = getLatestAnalysis('kaufmanAMA', technicalIndicators.kaufmanAMA)
    const hullAnalysis = getLatestAnalysis('hullMA', technicalIndicators.hullMA)

    // 随机RSI需要特殊处理，使用k值
    const getStochRSIAnalysis = () => {
      if (!technicalIndicators.stochasticRSI || technicalIndicators.stochasticRSI.length === 0) return null
      const latestItem = technicalIndicators.stochasticRSI[technicalIndicators.stochasticRSI.length - 1]
      if (!latestItem || latestItem.k === undefined || latestItem.k === null || isNaN(latestItem.k)) return null
      return AdvancedTechnicalIndicators.analyzeIndicatorSignal('stochasticRSI', latestItem.k)
    }
    const stochRSIAnalysis = getStochRSIAnalysis()

    const vixAnalysis = getLatestAnalysis('williamsVIX', technicalIndicators.williamsVIX)
    const atrAnalysis = getLatestAnalysis('atr', technicalIndicators.atr)

    const getSignalColor = (signal: string) => {
      switch (signal) {
        case 'buy': return 'rgb(214, 10, 34)'  // 买入信号用红色
        case 'sell': return 'rgb(3, 123, 102)' // 卖出信号用绿色
        default: return '#faad14'
      }
    }

    const getStrengthTag = (strength: string) => {
      const colors = { strong: 'red', medium: 'orange', weak: 'blue' }
      const labels = { strong: '强', medium: '中', weak: '弱' }
      return <Tag color={colors[strength as keyof typeof colors]}>{labels[strength as keyof typeof labels]}</Tag>
    }

    const showIndicatorDetail = (indicatorKey: string, value?: number, signal?: any) => {
      const info = indicatorInfo[indicatorKey]
      if (info) {
        setSelectedIndicator({ key: indicatorKey, info, value, signal })
        setDetailModalVisible(true)
      }
    }

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <span>自适应移动平均线 (Kaufman AMA)</span>
                <Tooltip title="点击查看详细说明">
                  <InfoCircleOutlined
                    style={{ color: '#1890ff', cursor: 'pointer' }}
                    onClick={() => showIndicatorDetail(
                      'kaufmanAMA',
                      technicalIndicators.kaufmanAMA[technicalIndicators.kaufmanAMA.length - 1]?.value,
                      amaAnalysis
                    )}
                  />
                </Tooltip>
              </Space>
            }
            size="small"
            extra={
              amaAnalysis && (
                <Space>
                  <Tag color={getSignalColor(amaAnalysis.signal)}>
                    {amaAnalysis.signal === 'buy' ? '买入' : amaAnalysis.signal === 'sell' ? '卖出' : '中性'}
                  </Tag>
                  {getStrengthTag(amaAnalysis.strength)}
                </Space>
              )
            }
          >
            <Line
              data={technicalIndicators.kaufmanAMA}
              xField="date"
              yField="value"
              height={200}
              smooth={true}
              color="#1890ff"
            />
            {amaAnalysis && (
              <Alert
                message={amaAnalysis.description}
                type={amaAnalysis.signal === 'buy' ? 'success' : amaAnalysis.signal === 'sell' ? 'error' : 'info'}
                size="small"
                style={{ marginTop: 8 }}
              />
            )}
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              <Text>安全范围: {(indicatorInfo.kaufmanAMA.safeRange.min * 100).toFixed(1)}% ~ {(indicatorInfo.kaufmanAMA.safeRange.max * 100).toFixed(1)}%</Text>
              <br />
              <Text>极值范围: {(indicatorInfo.kaufmanAMA.extremeRange.min * 100).toFixed(1)}% ~ {(indicatorInfo.kaufmanAMA.extremeRange.max * 100).toFixed(1)}%</Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <span>Hull移动平均线</span>
                <Tooltip title="点击查看详细说明">
                  <InfoCircleOutlined
                    style={{ color: '#1890ff', cursor: 'pointer' }}
                    onClick={() => showIndicatorDetail(
                      'hullMA',
                      technicalIndicators.hullMA[technicalIndicators.hullMA.length - 1]?.value,
                      hullAnalysis
                    )}
                  />
                </Tooltip>
              </Space>
            }
            size="small"
            extra={
              hullAnalysis && (
                <Space>
                  <Tag color={getSignalColor(hullAnalysis.signal)}>
                    {hullAnalysis.signal === 'buy' ? '买入' : hullAnalysis.signal === 'sell' ? '卖出' : '中性'}
                  </Tag>
                  {getStrengthTag(hullAnalysis.strength)}
                </Space>
              )
            }
          >
            <Line
              data={technicalIndicators.hullMA}
              xField="date"
              yField="value"
              height={200}
              smooth={true}
              color="#52c41a"
            />
            {hullAnalysis && (
              <Alert
                message={hullAnalysis.description}
                type={hullAnalysis.signal === 'buy' ? 'success' : hullAnalysis.signal === 'sell' ? 'error' : 'info'}
                size="small"
                style={{ marginTop: 8 }}
              />
            )}
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              <Text>安全范围: {(indicatorInfo.hullMA.safeRange.min * 100).toFixed(1)}% ~ {(indicatorInfo.hullMA.safeRange.max * 100).toFixed(1)}%</Text>
              <br />
              <Text>适用时间: {indicatorInfo.hullMA.timeframe} | 可靠性: {indicatorInfo.hullMA.reliability === 'high' ? '高' : '中'}</Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <span>随机RSI</span>
                <Tooltip title="点击查看详细说明">
                  <InfoCircleOutlined
                    style={{ color: '#1890ff', cursor: 'pointer' }}
                    onClick={() => showIndicatorDetail(
                      'stochasticRSI',
                      technicalIndicators.stochasticRSI[technicalIndicators.stochasticRSI.length - 1]?.k,
                      stochRSIAnalysis
                    )}
                  />
                </Tooltip>
              </Space>
            }
            size="small"
            extra={
              stochRSIAnalysis && (
                <Space>
                  <Tag color={getSignalColor(stochRSIAnalysis.signal)}>
                    {stochRSIAnalysis.signal === 'buy' ? '买入' : stochRSIAnalysis.signal === 'sell' ? '卖出' : '中性'}
                  </Tag>
                  {getStrengthTag(stochRSIAnalysis.strength)}
                </Space>
              )
            }
          >
            <Line
              data={[
                ...technicalIndicators.stochasticRSI.map(item => ({ ...item, type: 'K线', value: item.k })),
                ...technicalIndicators.stochasticRSI.map(item => ({ ...item, type: 'D线', value: item.d }))
              ]}
              xField="date"
              yField="value"
              seriesField="type"
              height={200}
              color={['#ff4d4f', '#faad14']}
            />
            {stochRSIAnalysis && (
              <Alert
                message={stochRSIAnalysis.description}
                type={stochRSIAnalysis.signal === 'buy' ? 'success' : stochRSIAnalysis.signal === 'sell' ? 'error' : 'info'}
                size="small"
                style={{ marginTop: 8 }}
              />
            )}
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              <Text>超买线: 80 | 超卖线: 20 | 安全区间: {indicatorInfo.stochasticRSI.safeRange.min}-{indicatorInfo.stochasticRSI.safeRange.max}</Text>
              <br />
              <Text>买入信号: {indicatorInfo.stochasticRSI.buySignal}</Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <span>威廉波动率指数</span>
                <Tooltip title="点击查看详细说明">
                  <InfoCircleOutlined
                    style={{ color: '#1890ff', cursor: 'pointer' }}
                    onClick={() => showIndicatorDetail(
                      'williamsVIX',
                      technicalIndicators.williamsVIX[technicalIndicators.williamsVIX.length - 1]?.value,
                      vixAnalysis
                    )}
                  />
                </Tooltip>
              </Space>
            }
            size="small"
            extra={
              vixAnalysis && (
                <Space>
                  <Tag color={getSignalColor(vixAnalysis.signal)}>
                    {vixAnalysis.signal === 'buy' ? '低风险' : vixAnalysis.signal === 'sell' ? '高风险' : '正常'}
                  </Tag>
                  {getStrengthTag(vixAnalysis.strength)}
                </Space>
              )
            }
          >
            <Line
              data={technicalIndicators.williamsVIX}
              xField="date"
              yField="value"
              height={200}
              color="#722ed1"
            />
            {vixAnalysis && (
              <Alert
                message={vixAnalysis.description}
                type={vixAnalysis.signal === 'buy' ? 'success' : vixAnalysis.signal === 'sell' ? 'warning' : 'info'}
                size="small"
                style={{ marginTop: 8 }}
              />
            )}
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              <Text>低风险: &lt;15 | 正常: 15-25 | 高风险: &gt;25</Text>
              <br />
              <Text>用途: {indicatorInfo.williamsVIX.usage}</Text>
            </div>
          </Card>
        </Col>

        <Col xs={24}>
          <KeltnerChannelChart
            priceData={mockPriceData}
            keltnerData={analysisData.technicalIndicators.kellerChannels.map((item, index) => {
              console.log(`Final keltner mapping ${index}:`, item)

              // 如果数据无效，直接使用基于价格的简单计算
              const basePrice = mockPriceData[Math.min(index, mockPriceData.length - 1)]?.close || 12

              return {
                timestamp: item.date,
                upper: (typeof item.upper === 'number' && !isNaN(item.upper)) ? item.upper : basePrice * 1.02,
                middle: (typeof item.middle === 'number' && !isNaN(item.middle)) ? item.middle : basePrice,
                lower: (typeof item.lower === 'number' && !isNaN(item.lower)) ? item.lower : basePrice * 0.98,
                signal: 'neutral' as const,
                signalStrength: 'weak' as const,
                signalDescription: '通道分析'
              }
            })}
            tradingSignals={analysisData.tradingSignals?.keltnerSignals || []}
            height={400}
          />
        </Col>

        {/* 指标汇总表格 */}
        <Col xs={24}>
          <Card title="指标信号汇总" size="small">
            <Table
              dataSource={[
                {
                  key: 'kaufmanAMA',
                  indicator: 'Kaufman自适应移动平均线',
                  value: technicalIndicators.kaufmanAMA?.length > 0 ? technicalIndicators.kaufmanAMA[technicalIndicators.kaufmanAMA.length - 1]?.value : undefined,
                  signal: amaAnalysis?.signal,
                  strength: amaAnalysis?.strength,
                  description: amaAnalysis?.description,
                  reliability: 'high'
                },
                {
                  key: 'hullMA',
                  indicator: 'Hull移动平均线',
                  value: technicalIndicators.hullMA?.length > 0 ? technicalIndicators.hullMA[technicalIndicators.hullMA.length - 1]?.value : undefined,
                  signal: hullAnalysis?.signal,
                  strength: hullAnalysis?.strength,
                  description: hullAnalysis?.description,
                  reliability: 'high'
                },
                {
                  key: 'stochasticRSI',
                  indicator: '随机RSI',
                  value: technicalIndicators.stochasticRSI?.length > 0 ? technicalIndicators.stochasticRSI[technicalIndicators.stochasticRSI.length - 1]?.k : undefined,
                  signal: stochRSIAnalysis?.signal,
                  strength: stochRSIAnalysis?.strength,
                  description: stochRSIAnalysis?.description,
                  reliability: 'medium'
                },
                {
                  key: 'williamsVIX',
                  indicator: '威廉波动率指数',
                  value: technicalIndicators.williamsVIX?.length > 0 ? technicalIndicators.williamsVIX[technicalIndicators.williamsVIX.length - 1]?.value : undefined,
                  signal: vixAnalysis?.signal,
                  strength: vixAnalysis?.strength,
                  description: vixAnalysis?.description,
                  reliability: 'medium'
                },
                {
                  key: 'atr',
                  indicator: '平均真实波动范围',
                  value: technicalIndicators.atr?.length > 0 ? technicalIndicators.atr[technicalIndicators.atr.length - 1]?.value : undefined,
                  signal: atrAnalysis?.signal,
                  strength: atrAnalysis?.strength,
                  description: atrAnalysis?.description,
                  reliability: 'high'
                }
              ]}
              size="small"
              pagination={false}
              columns={[
                {
                  title: '技术指标',
                  dataIndex: 'indicator',
                  key: 'indicator',
                  width: 200,
                  render: (text: string, record: any) => (
                    <Space>
                      <Text strong>{text}</Text>
                      <Tag color={record.reliability === 'high' ? 'green' : 'orange'}>
                        {record.reliability === 'high' ? '高可靠性' : '中可靠性'}
                      </Tag>
                    </Space>
                  )
                },
                {
                  title: '当前数值',
                  dataIndex: 'value',
                  key: 'value',
                  width: 100,
                  render: (value: number) => {
                    if (value === undefined || value === null || isNaN(value)) {
                      return 'N/A'
                    }
                    return value.toFixed(3)
                  }
                },
                {
                  title: '信号',
                  dataIndex: 'signal',
                  key: 'signal',
                  width: 80,
                  render: (signal: string) => (
                    <Tag color={getSignalColor(signal)}>
                      {signal === 'buy' ? '买入' : signal === 'sell' ? '卖出' : '中性'}
                    </Tag>
                  )
                },
                {
                  title: '强度',
                  dataIndex: 'strength',
                  key: 'strength',
                  width: 60,
                  render: (strength: string) => strength ? getStrengthTag(strength) : null
                },
                {
                  title: '分析描述',
                  dataIndex: 'description',
                  key: 'description',
                  render: (description: string) => (
                    <Text style={{ fontSize: '12px' }}>{description}</Text>
                  )
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 80,
                  render: (_, record: any) => (
                    <Button
                      type="link"
                      size="small"
                      onClick={() => showIndicatorDetail(record.key, record.value, {
                        signal: record.signal,
                        strength: record.strength,
                        description: record.description
                      })}
                    >
                      详情
                    </Button>
                  )
                }
              ]}
            />
          </Card>
        </Col>
      </Row>
    )
  }

  const renderVolumeAnalysis = () => {
    if (!analysisData) return null

    const { volumeAnalysis, volumeProfile, largeOrders } = analysisData

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="成交量分布" size="small">
            <Bar
              data={volumeProfile.volumeNodes.slice(0, 20)}
              xField="volume"
              yField="price"
              height={300}
              color="#1890ff"
            />
            <div style={{ marginTop: 16 }}>
              <Space direction="vertical" size="small">
                <Text><strong>POC价格:</strong> ¥{volumeProfile.pointOfControl.toFixed(2)}</Text>
                <Text><strong>价值区域:</strong> ¥{volumeProfile.valueAreaLow.toFixed(2)} - ¥{volumeProfile.valueAreaHigh.toFixed(2)}</Text>
              </Space>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="资金流向指标" size="small">
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Statistic
                  title="Chaikin资金流"
                  value={volumeAnalysis.chaikinMoneyFlow[volumeAnalysis.chaikinMoneyFlow.length - 1]?.value || 0}
                  precision={4}
                  valueStyle={{ 
                    color: (volumeAnalysis.chaikinMoneyFlow[volumeAnalysis.chaikinMoneyFlow.length - 1]?.value || 0) >= 0 ? '#3f8600' : '#cf1322' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="资金流量指数"
                  value={volumeAnalysis.moneyFlowIndex[volumeAnalysis.moneyFlowIndex.length - 1]?.value || 0}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        
        <Col xs={24}>
          <Card title="大单交易监控" size="small">
            <Table
              dataSource={largeOrders.slice(0, 10)}
              size="small"
              pagination={false}
              columns={[
                {
                  title: '时间',
                  dataIndex: 'timestamp',
                  key: 'timestamp',
                  width: 100,
                },
                {
                  title: '价格',
                  dataIndex: 'price',
                  key: 'price',
                  width: 80,
                  render: (value: number) => `¥${value.toFixed(2)}`,
                },
                {
                  title: '成交量',
                  dataIndex: 'volume',
                  key: 'volume',
                  width: 100,
                  render: (value: number) => value.toLocaleString(),
                },
                {
                  title: '方向',
                  dataIndex: 'direction',
                  key: 'direction',
                  width: 60,
                  render: (direction: string) => (
                    <Tag color={direction === 'buy' ? 'green' : 'red'}>
                      {direction === 'buy' ? '买入' : '卖出'}
                    </Tag>
                  ),
                },
                {
                  title: '类型',
                  dataIndex: 'type',
                  key: 'type',
                  width: 80,
                  render: (type: string) => {
                    const colorMap = {
                      large: 'blue',
                      super: 'orange',
                      institutional: 'purple'
                    }
                    const labelMap = {
                      large: '大单',
                      super: '超大单',
                      institutional: '机构单'
                    }
                    return <Tag color={colorMap[type as keyof typeof colorMap]}>{labelMap[type as keyof typeof labelMap]}</Tag>
                  },
                },
                {
                  title: '金额',
                  dataIndex: 'amount',
                  key: 'amount',
                  width: 100,
                  render: (value: number) => `¥${(value / 10000).toFixed(1)}万`,
                },
              ]}
            />
          </Card>
        </Col>
      </Row>
    )
  }

  const renderRiskAnalysis = () => {
    if (!analysisData) return null

    const { riskMetrics } = analysisData

    return (
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="波动率"
              value={riskMetrics.volatility * 100}
              precision={2}
              suffix="%"
              valueStyle={{ color: riskMetrics.volatility > 0.3 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="夏普比率"
              value={riskMetrics.sharpeRatio}
              precision={2}
              valueStyle={{ color: riskMetrics.sharpeRatio > 1 ? '#3f8600' : '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="最大回撤"
              value={riskMetrics.maxDrawdown * 100}
              precision={2}
              suffix="%"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="Beta系数"
              value={riskMetrics.beta}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24}>
          <Alert
            message="风险评估"
            description={`当前股票的波动率为${(riskMetrics.volatility * 100).toFixed(2)}%，${riskMetrics.volatility > 0.3 ? '属于高风险股票' : '风险水平适中'}。夏普比率${riskMetrics.sharpeRatio.toFixed(2)}${riskMetrics.sharpeRatio > 1 ? '表现良好' : '需要关注'}。`}
            type={riskMetrics.volatility > 0.3 ? 'warning' : 'info'}
            showIcon
          />
        </Col>
      </Row>
    )
  }

  const tabItems = [
    {
      key: 'technical',
      label: (
        <span>
          <LineChartOutlined />
          高级技术指标
        </span>
      ),
      children: renderTechnicalIndicators(),
    },
    {
      key: 'volume',
      label: (
        <span>
          <BarChartOutlined />
          量价分析
        </span>
      ),
      children: renderVolumeAnalysis(),
    },
    {
      key: 'risk',
      label: (
        <span>
          <InfoCircleOutlined />
          风险分析
        </span>
      ),
      children: renderRiskAnalysis(),
    },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>高级股票分析</Title>
        <Space>
          <Select
            value={selectedStock}
            onChange={setSelectedStock}
            style={{ width: 120 }}
          >
            <Option value="000001">平安银行</Option>
            <Option value="600519">贵州茅台</Option>
            <Option value="300750">宁德时代</Option>
            <Option value="002594">比亚迪</Option>
          </Select>
          <Select
            value={timeframe}
            onChange={setTimeframe}
            style={{ width: 100 }}
          >
            <Option value="1D">日线</Option>
            <Option value="1W">周线</Option>
            <Option value="1M">月线</Option>
          </Select>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setConfigVisible(true)}
          >
            指标配置
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadAdvancedAnalysis}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在计算高级技术指标...</Text>
          </div>
        </div>
      ) : (
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      )}

      <TechnicalIndicatorConfig
        visible={configVisible}
        onClose={() => setConfigVisible(false)}
        onSave={(config) => {
          setIndicatorConfig(config)
          loadAdvancedAnalysis()
        }}
        initialConfig={indicatorConfig || undefined}
      />

      {selectedIndicator?.info && (
        <IndicatorDetailModal
          visible={detailModalVisible}
          onClose={() => setDetailModalVisible(false)}
          indicatorInfo={selectedIndicator.info}
          currentValue={selectedIndicator.value}
          signal={selectedIndicator.signal}
        />
      )}
    </div>
  )
}

export default StockAnalysisAdvanced
