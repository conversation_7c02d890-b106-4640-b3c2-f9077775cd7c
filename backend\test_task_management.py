#!/usr/bin/env python3
"""
测试任务管理功能
"""

import asyncio
import requests
import json
from datetime import datetime

API_BASE = "http://localhost:8001/api/v1/data-management"

async def test_task_management():
    """测试任务管理功能"""
    print("🚀 开始测试任务管理功能\n")
    
    # 1. 获取当前任务列表
    print("1. 获取当前任务列表")
    try:
        response = requests.get(f"{API_BASE}/update-tasks")
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 获取任务列表成功，当前任务数量: {len(tasks)}")
            for task in tasks:
                print(f"  - {task['name']} ({task['status']}) - {task['progress']}%")
        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取任务列表错误: {e}")
    
    print()
    
    # 2. 启动股票基础信息更新任务
    print("2. 启动股票基础信息更新任务")
    try:
        response = requests.post(f"{API_BASE}/update-tasks/start", 
                               json={"type": "stock_basic"})
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功: {result['message']}")
            print(f"  任务ID: {result['task_id']}")
        else:
            print(f"❌ 启动任务失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 启动任务错误: {e}")
    
    print()
    
    # 3. 启动股票价格更新任务
    print("3. 启动股票价格更新任务")
    try:
        response = requests.post(f"{API_BASE}/update-tasks/start", 
                               json={"type": "stock_price"})
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功: {result['message']}")
            print(f"  任务ID: {result['task_id']}")
        else:
            print(f"❌ 启动任务失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 启动任务错误: {e}")
    
    print()
    
    # 4. 等待一段时间让任务运行
    print("4. 等待任务运行...")
    await asyncio.sleep(3)
    
    # 5. 再次获取任务列表查看状态
    print("5. 获取更新后的任务列表")
    try:
        response = requests.get(f"{API_BASE}/update-tasks")
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 获取任务列表成功，当前任务数量: {len(tasks)}")
            for task in tasks:
                print(f"  - {task['name']} ({task['status']}) - {task['progress']}%")
                if task.get('start_time'):
                    print(f"    开始时间: {task['start_time']}")
                if task.get('records_processed') and task.get('total_records'):
                    print(f"    处理进度: {task['records_processed']}/{task['total_records']}")
        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取任务列表错误: {e}")
    
    print()
    
    # 6. 测试获取数据库统计
    print("6. 测试获取数据库统计")
    try:
        response = requests.get(f"{API_BASE}/database-stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 获取数据库统计成功")
            print(f"  股票总数: {stats.get('total_stocks', 0)}")
            print(f"  今日更新: {stats.get('today_updates', 0)}")
            print(f"  新闻数量: {stats.get('news_count', 0)}")
            print(f"  数据大小: {stats.get('data_size', 'N/A')}")
        else:
            print(f"❌ 获取数据库统计失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取数据库统计错误: {e}")
    
    print()
    
    # 7. 测试获取自动更新设置
    print("7. 测试获取自动更新设置")
    try:
        response = requests.get(f"{API_BASE}/auto-update/settings")
        if response.status_code == 200:
            settings = response.json()
            print(f"✅ 获取自动更新设置成功")
            print(f"  启用状态: {settings.get('enabled', False)}")
            print(f"  股票数据间隔: {settings.get('stock_data_interval', 0)}分钟")
            print(f"  新闻间隔: {settings.get('news_interval', 0)}分钟")
        else:
            print(f"❌ 获取自动更新设置失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取自动更新设置错误: {e}")
    
    print("\n✅ 任务管理功能测试完成！")

if __name__ == "__main__":
    asyncio.run(test_task_management())
