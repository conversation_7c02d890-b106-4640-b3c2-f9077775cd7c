#!/usr/bin/env python3
"""
检查数据库表结构
"""

import sqlite3
import os

def check_database():
    """检查数据库表"""
    db_path = 'backend/data/stock_analyzer.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 数据库文件: {db_path}")
        print(f"📋 数据库表数量: {len(tables)}")
        print("📝 表列表:")
        
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            # 获取表的记录数
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"    记录数: {count}")
            except Exception as e:
                print(f"    记录数: 查询失败 ({e})")
        
        # 检查重构后的表是否存在
        redesign_tables = [
            'stock_basic_info',
            'stock_kline_daily', 
            'stock_realtime_data',
            'stock_financial_data',
            'custom_indicators',
            'stock_patterns',
            'backtest_strategies',
            'data_update_logs'
        ]
        
        print("\n🔍 重构表检查:")
        for table_name in redesign_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            exists = cursor.fetchone() is not None
            status = "✅ 存在" if exists else "❌ 不存在"
            print(f"  {table_name}: {status}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

if __name__ == "__main__":
    check_database()
