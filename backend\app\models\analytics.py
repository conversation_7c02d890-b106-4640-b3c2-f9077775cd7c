"""
统计分析相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, DateTime, <PERSON>olean, Integer, Text, Date, Numeric, JSON, Index
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class Portfolio(Base):
    """投资组合表"""
    __tablename__ = "portfolios"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    
    # 组合信息
    name: Mapped[str] = mapped_column(String(100), comment="组合名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="组合描述")
    portfolio_type: Mapped[str] = mapped_column(String(20), comment="组合类型: real/virtual/backtest")
    
    # 基础数据
    initial_capital: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="初始资金")
    current_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="当前市值")
    cash_balance: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="现金余额")
    
    # 收益统计
    total_return: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="总收益")
    total_return_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="总收益率")
    daily_return_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="日收益率")
    
    # 风险指标
    volatility: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="波动率")
    sharpe_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="夏普比率")
    max_drawdown: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="最大回撤")
    beta: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="贝塔系数")
    
    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否活跃")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    __table_args__ = (
        Index('ix_portfolio_user_type', 'user_id', 'portfolio_type'),
        Index('ix_portfolio_user_active', 'user_id', 'is_active'),
    )


class PortfolioHolding(Base):
    """投资组合持仓表"""
    __tablename__ = "portfolio_holdings"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    portfolio_id: Mapped[int] = mapped_column(Integer, index=True, comment="组合ID")
    stock_code: Mapped[str] = mapped_column(String(20), index=True, comment="股票代码")
    
    # 持仓信息
    shares: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="持股数量")
    avg_cost: Mapped[Decimal] = mapped_column(Numeric(10, 4), comment="平均成本")
    current_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="当前价格")
    market_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="市值")
    
    # 收益信息
    unrealized_pnl: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="浮动盈亏")
    unrealized_pnl_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="浮动盈亏率")
    realized_pnl: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="已实现盈亏")
    
    # 权重
    weight: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="持仓权重")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_holding_portfolio_stock', 'portfolio_id', 'stock_code'),
    )


class PortfolioTransaction(Base):
    """投资组合交易记录表"""
    __tablename__ = "portfolio_transactions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    portfolio_id: Mapped[int] = mapped_column(Integer, index=True, comment="组合ID")
    stock_code: Mapped[str] = mapped_column(String(20), index=True, comment="股票代码")
    
    # 交易信息
    transaction_type: Mapped[str] = mapped_column(String(10), comment="交易类型: buy/sell")
    shares: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="交易数量")
    price: Mapped[Decimal] = mapped_column(Numeric(10, 4), comment="交易价格")
    amount: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="交易金额")
    
    # 费用
    commission: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), comment="佣金")
    tax: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), comment="税费")
    total_cost: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="总成本")
    
    # 交易原因
    reason: Mapped[Optional[str]] = mapped_column(Text, comment="交易原因")
    strategy: Mapped[Optional[str]] = mapped_column(String(50), comment="交易策略")
    
    # 时间
    transaction_date: Mapped[date] = mapped_column(Date, comment="交易日期")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_transaction_portfolio_date', 'portfolio_id', 'transaction_date'),
        Index('ix_transaction_stock_date', 'stock_code', 'transaction_date'),
    )


class PortfolioPerformance(Base):
    """投资组合表现记录表"""
    __tablename__ = "portfolio_performance"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    portfolio_id: Mapped[int] = mapped_column(Integer, index=True, comment="组合ID")
    
    # 日期
    performance_date: Mapped[date] = mapped_column(Date, comment="表现日期")
    
    # 价值数据
    total_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="总价值")
    cash_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="现金价值")
    stock_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="股票价值")
    
    # 收益数据
    daily_return: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="日收益")
    daily_return_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="日收益率")
    cumulative_return: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="累计收益")
    cumulative_return_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="累计收益率")
    
    # 基准比较
    benchmark_return_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="基准收益率")
    excess_return_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="超额收益率")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_performance_portfolio_date', 'portfolio_id', 'performance_date'),
    )


class BacktestResult(Base):
    """回测结果表"""
    __tablename__ = "portfolio_backtest_results"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    
    # 回测信息
    strategy_name: Mapped[str] = mapped_column(String(100), comment="策略名称")
    strategy_config: Mapped[dict] = mapped_column(JSON, comment="策略配置")
    
    # 回测参数
    start_date: Mapped[date] = mapped_column(Date, comment="开始日期")
    end_date: Mapped[date] = mapped_column(Date, comment="结束日期")
    initial_capital: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="初始资金")
    
    # 回测结果
    final_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="最终价值")
    total_return: Mapped[Decimal] = mapped_column(Numeric(15, 2), comment="总收益")
    total_return_pct: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="总收益率")
    annualized_return: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="年化收益率")
    
    # 风险指标
    volatility: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="波动率")
    sharpe_ratio: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="夏普比率")
    max_drawdown: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="最大回撤")
    calmar_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="卡玛比率")
    
    # 交易统计
    total_trades: Mapped[int] = mapped_column(Integer, comment="总交易次数")
    winning_trades: Mapped[int] = mapped_column(Integer, comment="盈利交易次数")
    losing_trades: Mapped[int] = mapped_column(Integer, comment="亏损交易次数")
    win_rate: Mapped[Decimal] = mapped_column(Numeric(8, 4), comment="胜率")
    
    # 基准比较
    benchmark_return: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="基准收益率")
    alpha: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="阿尔法")
    beta: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="贝塔")
    
    # 详细数据
    performance_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="详细表现数据")
    trade_data: Mapped[Optional[dict]] = mapped_column(JSON, comment="交易详情数据")
    
    # 状态
    status: Mapped[str] = mapped_column(String(20), default="completed", comment="状态: running/completed/failed")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_backtest_user_strategy', 'user_id', 'strategy_name'),
        Index('ix_backtest_period_range', 'start_date', 'end_date'),
    )


class RiskMetrics(Base):
    """风险指标表"""
    __tablename__ = "risk_metrics"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    portfolio_id: Mapped[Optional[int]] = mapped_column(Integer, index=True, comment="组合ID")
    stock_code: Mapped[Optional[str]] = mapped_column(String(20), index=True, comment="股票代码")
    
    # 计算参数
    calculation_date: Mapped[date] = mapped_column(Date, comment="计算日期")
    period_days: Mapped[int] = mapped_column(Integer, comment="计算周期天数")
    
    # 收益风险指标
    mean_return: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 6), comment="平均收益率")
    volatility: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="波动率")
    skewness: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="偏度")
    kurtosis: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="峰度")
    
    # 风险价值
    var_95: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="95% VaR")
    var_99: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="99% VaR")
    cvar_95: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="95% CVaR")
    cvar_99: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="99% CVaR")
    
    # 回撤指标
    max_drawdown: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="最大回撤")
    avg_drawdown: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="平均回撤")
    drawdown_duration: Mapped[Optional[int]] = mapped_column(Integer, comment="回撤持续天数")
    
    # 相关性指标
    correlation_market: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="与市场相关性")
    beta_market: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="市场贝塔")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_risk_portfolio_date', 'portfolio_id', 'calculation_date'),
        Index('ix_risk_stock_date', 'stock_code', 'calculation_date'),
    )


class MarketBenchmark(Base):
    """市场基准表"""
    __tablename__ = "market_benchmarks"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # 基准信息
    benchmark_code: Mapped[str] = mapped_column(String(20), unique=True, index=True, comment="基准代码")
    benchmark_name: Mapped[str] = mapped_column(String(100), comment="基准名称")
    benchmark_type: Mapped[str] = mapped_column(String(20), comment="基准类型: index/etf")
    
    # 价格数据
    trade_date: Mapped[date] = mapped_column(Date, comment="交易日期")
    open_price: Mapped[Decimal] = mapped_column(Numeric(10, 4), comment="开盘价")
    high_price: Mapped[Decimal] = mapped_column(Numeric(10, 4), comment="最高价")
    low_price: Mapped[Decimal] = mapped_column(Numeric(10, 4), comment="最低价")
    close_price: Mapped[Decimal] = mapped_column(Numeric(10, 4), comment="收盘价")
    
    # 收益率
    daily_return: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="日收益率")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_benchmark_code_date', 'benchmark_code', 'trade_date'),
    )
