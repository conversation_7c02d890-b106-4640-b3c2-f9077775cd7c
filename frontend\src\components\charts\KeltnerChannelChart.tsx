/**
 * 肯特纳通道图表组件
 * 包含K线蜡烛图和肯特纳通道上下轨线
 */

import React, { useMemo } from 'react'
import ReactECharts from 'echarts-for-react'
import { STOCK_COLORS } from '../../config/colors'

interface PriceData {
  timestamp: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

interface KeltnerChannelChartProps {
  data: PriceData[]
  height?: number
  timeFrame?: string
}

// 计算EMA
const calculateEMA = (data: number[], period: number): number[] => {
  const ema: number[] = []
  const multiplier = 2 / (period + 1)
  
  if (data.length === 0) return ema
  
  ema[0] = data[0]
  
  for (let i = 1; i < data.length; i++) {
    ema[i] = (data[i] - ema[i - 1]) * multiplier + ema[i - 1]
  }
  
  return ema
}

// 计算ATR (Average True Range)
const calculateATR = (data: PriceData[], period: number): number[] => {
  const atr: number[] = []
  const trueRanges: number[] = []
  
  for (let i = 1; i < data.length; i++) {
    const high = data[i].high
    const low = data[i].low
    const prevClose = data[i - 1].close
    
    const tr = Math.max(
      high - low,
      Math.abs(high - prevClose),
      Math.abs(low - prevClose)
    )
    
    trueRanges.push(tr)
  }
  
  // 计算ATR的EMA
  const atrEma = calculateEMA(trueRanges, period)
  return [0, ...atrEma] // 第一个值为0，因为无法计算
}

// 计算肯特纳通道
const calculateKeltnerChannel = (data: PriceData[], emaPeriod: number = 20, atrPeriod: number = 10, multiplier: number = 2) => {
  const closes = data.map(item => item.close)
  const ema = calculateEMA(closes, emaPeriod)
  const atr = calculateATR(data, atrPeriod)
  
  const upperBand = ema.map((value, index) => value + (atr[index] * multiplier))
  const lowerBand = ema.map((value, index) => value - (atr[index] * multiplier))
  
  return {
    middle: ema,
    upper: upperBand,
    lower: lowerBand
  }
}

const KeltnerChannelChart: React.FC<KeltnerChannelChartProps> = ({
  data,
  height = 400,
  timeFrame = '1D'
}) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null

    // 计算肯特纳通道
    const keltner = calculateKeltnerChannel(data)
    
    // 准备K线数据
    const candlestickData = data.map(item => [
      item.open,
      item.close,
      item.low,
      item.high
    ])

    // 准备时间轴
    const timeAxis = data.map(item => {
      const date = new Date(item.timestamp)
      if (timeFrame === '1m' || timeFrame === '5m' || timeFrame === '15m' || timeFrame === '30m' || timeFrame === '1h') {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    })

    return {
      candlestickData,
      timeAxis,
      keltner
    }
  }, [data, timeFrame])

  const option = useMemo(() => {
    if (!chartData) return {}

    return {
      backgroundColor: '#ffffff',
      grid: {
        left: '8%',
        right: '8%',
        top: '10%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.timeAxis,
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        min: 'dataMin',
        max: 'dataMax',
        axisLabel: {
          fontSize: 10
        }
      },
      yAxis: {
        scale: true,
        splitArea: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          fontSize: 10
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        textStyle: {
          color: '#000',
          fontSize: 12
        }
      },
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: chartData.candlestickData,
          itemStyle: {
            color: STOCK_COLORS.UP,
            color0: STOCK_COLORS.DOWN,
            borderColor: STOCK_COLORS.UP,
            borderColor0: STOCK_COLORS.DOWN
          },
          emphasis: {
            itemStyle: {
              color: STOCK_COLORS.UP,
              color0: STOCK_COLORS.DOWN,
              borderColor: STOCK_COLORS.UP,
              borderColor0: STOCK_COLORS.DOWN
            }
          }
        },
        {
          name: '肯特纳上轨',
          type: 'line',
          data: chartData.keltner.upper,
          smooth: true,
          lineStyle: {
            color: '#ff6b6b',
            width: 1
          },
          symbol: 'none'
        },
        {
          name: '肯特纳中轨',
          type: 'line',
          data: chartData.keltner.middle,
          smooth: true,
          lineStyle: {
            color: '#4ecdc4',
            width: 1
          },
          symbol: 'none'
        },
        {
          name: '肯特纳下轨',
          type: 'line',
          data: chartData.keltner.lower,
          smooth: true,
          lineStyle: {
            color: '#45b7d1',
            width: 1
          },
          symbol: 'none'
        }
      ]
    }
  }, [chartData])

  if (!chartData) {
    return (
      <div style={{
        height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>暂无数据</div>
      </div>
    )
  }

  return (
    <ReactECharts
      option={option}
      style={{ height, width: '100%' }}
      opts={{ renderer: 'canvas' }}
    />
  )
}

export default KeltnerChannelChart
