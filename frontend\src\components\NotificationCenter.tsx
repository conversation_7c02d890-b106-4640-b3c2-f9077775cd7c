import React, { useEffect, useState } from 'react'
import { notification, Badge, Drawer, List, Avatar, Typography, Button, Space, Tag } from 'antd'
import {
  BellOutlined,
  WarningOutlined,
  RobotOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  DeleteOutlined,
  CheckOutlined,
} from '@ant-design/icons'
import { realtimeService, StockPriceUpdate, AISignal, AlertNotification } from '@/services/realtimeService'

const { Text, Title } = Typography

interface NotificationItem {
  id: string
  type: 'stock_price' | 'ai_signal' | 'alert'
  title: string
  description: string
  timestamp: string
  read: boolean
  data: any
}

interface NotificationCenterProps {
  visible: boolean
  onClose: () => void
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ visible, onClose }) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    // 订阅实时数据更新
    const handleStockPriceUpdate = (data: { data: StockPriceUpdate }) => {
      const { stock_code, price_change, change_percent, timestamp } = data.data
      
      // 只有显著变化才推送通知
      if (Math.abs(change_percent) > 3) {
        addNotification({
          type: 'stock_price',
          title: `${stock_code} 价格异动`,
          description: `股价${change_percent >= 0 ? '上涨' : '下跌'} ${Math.abs(change_percent).toFixed(2)}%`,
          data: data.data,
          timestamp
        })
      }
    }

    const handleAISignal = (data: { data: AISignal }) => {
      const { stock_code, signal_type, confidence, timestamp } = data.data
      
      addNotification({
        type: 'ai_signal',
        title: `AI信号: ${stock_code}`,
        description: `${signal_type === 'buy' ? '买入' : signal_type === 'sell' ? '卖出' : '持有'}信号 (置信度: ${confidence}%)`,
        data: data.data,
        timestamp
      })
    }

    const handleAlert = (data: { data: AlertNotification }) => {
      const { stock_code, message, timestamp } = data.data
      
      addNotification({
        type: 'alert',
        title: `预警通知: ${stock_code}`,
        description: message,
        data: data.data,
        timestamp
      })

      // 显示系统通知
      notification.warning({
        message: `预警通知: ${stock_code}`,
        description: message,
        placement: 'topRight',
        duration: 5,
      })
    }

    // 订阅事件
    realtimeService.on('stock_price_update', handleStockPriceUpdate)
    realtimeService.on('ai_signal', handleAISignal)
    realtimeService.on('alert', handleAlert)

    return () => {
      // 取消订阅
      realtimeService.off('stock_price_update', handleStockPriceUpdate)
      realtimeService.off('ai_signal', handleAISignal)
      realtimeService.off('alert', handleAlert)
    }
  }, [])

  const addNotification = (notificationData: Omit<NotificationItem, 'id' | 'read'>) => {
    const newNotification: NotificationItem = {
      ...notificationData,
      id: Date.now().toString(),
      read: false,
    }

    setNotifications(prev => [newNotification, ...prev.slice(0, 49)]) // 保持最多50条通知
    setUnreadCount(prev => prev + 1)
  }

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(item =>
        item.id === id ? { ...item, read: true } : item
      )
    )
    setUnreadCount(prev => Math.max(0, prev - 1))
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(item => ({ ...item, read: true }))
    )
    setUnreadCount(0)
  }

  const deleteNotification = (id: string) => {
    setNotifications(prev => {
      const item = prev.find(n => n.id === id)
      if (item && !item.read) {
        setUnreadCount(count => Math.max(0, count - 1))
      }
      return prev.filter(n => n.id !== id)
    })
  }

  const clearAll = () => {
    setNotifications([])
    setUnreadCount(0)
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'stock_price':
        return <ArrowUpOutlined style={{ color: '#1890ff' }} />
      case 'ai_signal':
        return <RobotOutlined style={{ color: '#52c41a' }} />
      case 'alert':
        return <WarningOutlined style={{ color: '#faad14' }} />
      default:
        return <BellOutlined />
    }
  }

  const getNotificationTag = (type: string) => {
    switch (type) {
      case 'stock_price':
        return <Tag color="blue">价格异动</Tag>
      case 'ai_signal':
        return <Tag color="green">AI信号</Tag>
      case 'alert':
        return <Tag color="orange">预警</Tag>
      default:
        return <Tag>通知</Tag>
    }
  }

  return (
    <Drawer
      title={
        <Space>
          <BellOutlined />
          <span>通知中心</span>
          {unreadCount > 0 && <Badge count={unreadCount} />}
        </Space>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={400}
      extra={
        <Space>
          {unreadCount > 0 && (
            <Button size="small" onClick={markAllAsRead}>
              全部已读
            </Button>
          )}
          {notifications.length > 0 && (
            <Button size="small" danger onClick={clearAll}>
              清空全部
            </Button>
          )}
        </Space>
      }
    >
      {notifications.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '50px 0', color: '#999' }}>
          <BellOutlined style={{ fontSize: 48, marginBottom: 16 }} />
          <div>暂无通知</div>
        </div>
      ) : (
        <List
          dataSource={notifications}
          renderItem={(item) => (
            <List.Item
              style={{
                backgroundColor: item.read ? 'transparent' : '#f6ffed',
                padding: '12px',
                borderRadius: 6,
                marginBottom: 8,
                border: item.read ? '1px solid #f0f0f0' : '1px solid #b7eb8f'
              }}
              actions={[
                !item.read && (
                  <Button
                    type="text"
                    size="small"
                    icon={<CheckOutlined />}
                    onClick={() => markAsRead(item.id)}
                  />
                ),
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => deleteNotification(item.id)}
                />
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={getNotificationIcon(item.type)}
                    style={{ backgroundColor: '#f0f0f0' }}
                  />
                }
                title={
                  <Space>
                    <Text strong={!item.read}>{item.title}</Text>
                    {getNotificationTag(item.type)}
                  </Space>
                }
                description={
                  <div>
                    <div style={{ marginBottom: 4 }}>{item.description}</div>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {new Date(item.timestamp).toLocaleString()}
                    </Text>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Drawer>
  )
}

export default NotificationCenter
