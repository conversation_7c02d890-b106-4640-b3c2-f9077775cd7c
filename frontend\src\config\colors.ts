/**
 * 全局颜色配置
 * 统一管理股票分析系统中的所有颜色
 */

// 股票涨跌颜色配置
export const STOCK_COLORS = {
  // 上涨颜色 (红色)
  UP: 'rgb(214, 10, 34)',
  UP_HEX: '#d60a22',
  
  // 下跌颜色 (绿色)  
  DOWN: 'rgb(3, 123, 102)',
  DOWN_HEX: '#037b66',
  
  // 中性颜色
  NEUTRAL: '#faad14',
  
  // 背景颜色
  BACKGROUND: '#ffffff',
  
  // 边框颜色
  BORDER: '#d9d9d9'
} as const

// K线图颜色配置
export const CANDLESTICK_COLORS = {
  // 阳线 (上涨)
  UP_COLOR: STOCK_COLORS.UP,
  UP_BORDER: STOCK_COLORS.UP,
  
  // 阴线 (下跌)
  DOWN_COLOR: STOCK_COLORS.DOWN,
  DOWN_BORDER: STOCK_COLORS.DOWN,
  
  // 背景色
  BACKGROUND: STOCK_COLORS.BACKGROUND,
  
  // 边框宽度
  BORDER_WIDTH: 1
} as const

// 技术指标颜色
export const INDICATOR_COLORS = {
  MA: '#1890ff',
  EMA: '#52c41a', 
  BOLLINGER_UPPER: STOCK_COLORS.UP,
  BOLLINGER_LOWER: STOCK_COLORS.DOWN,
  BOLLINGER_MIDDLE: '#1890ff',
  RSI: '#faad14',
  MACD: '#722ed1',
  KELTNER: '#4A90E2'
} as const

// 信号颜色
export const SIGNAL_COLORS = {
  BUY: STOCK_COLORS.UP,    // 买入信号用红色
  SELL: STOCK_COLORS.DOWN, // 卖出信号用绿色
  HOLD: STOCK_COLORS.NEUTRAL
} as const

// Ant Design 主题颜色映射
export const ANTD_COLORS = {
  SUCCESS: STOCK_COLORS.UP,   // 成功状态用红色 (上涨)
  ERROR: STOCK_COLORS.DOWN,   // 错误状态用绿色 (下跌)
  WARNING: STOCK_COLORS.NEUTRAL,
  INFO: '#1890ff'
} as const

// 图表主题配置
export const CHART_THEME = {
  backgroundColor: STOCK_COLORS.BACKGROUND,
  textStyle: {
    color: '#333'
  },
  grid: {
    backgroundColor: STOCK_COLORS.BACKGROUND
  }
} as const

// 获取涨跌颜色的工具函数
export const getChangeColor = (change: number): string => {
  return change >= 0 ? STOCK_COLORS.UP : STOCK_COLORS.DOWN
}

// 获取涨跌状态的工具函数
export const getChangeStatus = (change: number): 'up' | 'down' => {
  return change >= 0 ? 'up' : 'down'
}

// 获取Ant Design状态的工具函数
export const getAntdStatus = (change: number): 'success' | 'error' => {
  return change >= 0 ? 'success' : 'error'
}

// 导出默认配置
export default {
  STOCK_COLORS,
  CANDLESTICK_COLORS,
  INDICATOR_COLORS,
  SIGNAL_COLORS,
  ANTD_COLORS,
  CHART_THEME,
  getChangeColor,
  getChangeStatus,
  getAntdStatus
}
