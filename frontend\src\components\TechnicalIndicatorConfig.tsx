import React, { useState } from 'react'
import {
  Modal,
  Form,
  InputNumber,
  Select,
  Switch,
  Button,
  Space,
  Tabs,
  Card,
  Row,
  Col,
  Typo<PERSON>,
  Di<PERSON>r,
  <PERSON><PERSON><PERSON>,
  Al<PERSON>,
} from 'antd'
import {
  SettingOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  SaveOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select

export interface IndicatorConfig {
  // 移动平均线配置
  movingAverages: {
    kaufmanAMA: {
      enabled: boolean
      period: number
      fastSC: number
      slowSC: number
    }
    hullMA: {
      enabled: boolean
      period: number
    }
    exponentialMA: {
      enabled: boolean
      period: number
    }
  }
  
  // 动量指标配置
  momentum: {
    stochasticRSI: {
      enabled: boolean
      rsiPeriod: number
      stochPeriod: number
    }
    williamsVIX: {
      enabled: boolean
      period: number
    }
    rsi: {
      enabled: boolean
      period: number
      overbought: number
      oversold: number
    }
  }
  
  // 波动率指标配置
  volatility: {
    atr: {
      enabled: boolean
      period: number
    }
    kellerChannels: {
      enabled: boolean
      period: number
      atrMultiplier: number
    }
    bollingerBands: {
      enabled: boolean
      period: number
      stdDev: number
    }
  }
  
  // 量价指标配置
  volume: {
    onBalanceVolume: {
      enabled: boolean
    }
    chaikinMoneyFlow: {
      enabled: boolean
      period: number
    }
    volumeProfile: {
      enabled: boolean
      bins: number
    }
    moneyFlowIndex: {
      enabled: boolean
      period: number
    }
  }
}

interface TechnicalIndicatorConfigProps {
  visible: boolean
  onClose: () => void
  onSave: (config: IndicatorConfig) => void
  initialConfig?: IndicatorConfig
}

const defaultConfig: IndicatorConfig = {
  movingAverages: {
    kaufmanAMA: {
      enabled: true,
      period: 14,
      fastSC: 2,
      slowSC: 30
    },
    hullMA: {
      enabled: true,
      period: 20
    },
    exponentialMA: {
      enabled: false,
      period: 12
    }
  },
  momentum: {
    stochasticRSI: {
      enabled: true,
      rsiPeriod: 14,
      stochPeriod: 14
    },
    williamsVIX: {
      enabled: true,
      period: 14
    },
    rsi: {
      enabled: false,
      period: 14,
      overbought: 70,
      oversold: 30
    }
  },
  volatility: {
    atr: {
      enabled: true,
      period: 14
    },
    kellerChannels: {
      enabled: true,
      period: 20,
      atrMultiplier: 2
    },
    bollingerBands: {
      enabled: false,
      period: 20,
      stdDev: 2
    }
  },
  volume: {
    onBalanceVolume: {
      enabled: true
    },
    chaikinMoneyFlow: {
      enabled: true,
      period: 20
    },
    volumeProfile: {
      enabled: true,
      bins: 50
    },
    moneyFlowIndex: {
      enabled: true,
      period: 14
    }
  }
}

const TechnicalIndicatorConfig: React.FC<TechnicalIndicatorConfigProps> = ({
  visible,
  onClose,
  onSave,
  initialConfig
}) => {
  const [form] = Form.useForm()
  const [config, setConfig] = useState<IndicatorConfig>(initialConfig || defaultConfig)
  const [activeTab, setActiveTab] = useState('movingAverages')

  const handleSave = () => {
    form.validateFields().then(() => {
      onSave(config)
      onClose()
    })
  }

  const handleReset = () => {
    setConfig(defaultConfig)
    form.setFieldsValue(defaultConfig)
  }

  const updateConfig = (path: string[], value: any) => {
    const newConfig = { ...config }
    let current: any = newConfig
    
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]]
    }
    current[path[path.length - 1]] = value
    
    setConfig(newConfig)
  }

  const renderMovingAveragesTab = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={12}>
        <Card title="Kaufman自适应移动平均线" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.movingAverages.kaufmanAMA.enabled}
              onChange={(checked) => updateConfig(['movingAverages', 'kaufmanAMA', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="周期">
            <InputNumber
              value={config.movingAverages.kaufmanAMA.period}
              onChange={(value) => updateConfig(['movingAverages', 'kaufmanAMA', 'period'], value)}
              min={5}
              max={100}
              disabled={!config.movingAverages.kaufmanAMA.enabled}
            />
          </Form.Item>
          <Form.Item label="快速平滑常数">
            <InputNumber
              value={config.movingAverages.kaufmanAMA.fastSC}
              onChange={(value) => updateConfig(['movingAverages', 'kaufmanAMA', 'fastSC'], value)}
              min={1}
              max={10}
              disabled={!config.movingAverages.kaufmanAMA.enabled}
            />
          </Form.Item>
          <Form.Item label="慢速平滑常数">
            <InputNumber
              value={config.movingAverages.kaufmanAMA.slowSC}
              onChange={(value) => updateConfig(['movingAverages', 'kaufmanAMA', 'slowSC'], value)}
              min={10}
              max={100}
              disabled={!config.movingAverages.kaufmanAMA.enabled}
            />
          </Form.Item>
        </Card>
      </Col>
      
      <Col xs={24} md={12}>
        <Card title="Hull移动平均线" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.movingAverages.hullMA.enabled}
              onChange={(checked) => updateConfig(['movingAverages', 'hullMA', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="周期">
            <InputNumber
              value={config.movingAverages.hullMA.period}
              onChange={(value) => updateConfig(['movingAverages', 'hullMA', 'period'], value)}
              min={5}
              max={100}
              disabled={!config.movingAverages.hullMA.enabled}
            />
          </Form.Item>
          <Alert
            message="Hull MA特点"
            description="Hull移动平均线减少了滞后性，能更快地响应价格变化，适合短期交易。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        </Card>
      </Col>
    </Row>
  )

  const renderMomentumTab = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={12}>
        <Card title="随机RSI" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.momentum.stochasticRSI.enabled}
              onChange={(checked) => updateConfig(['momentum', 'stochasticRSI', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="RSI周期">
            <InputNumber
              value={config.momentum.stochasticRSI.rsiPeriod}
              onChange={(value) => updateConfig(['momentum', 'stochasticRSI', 'rsiPeriod'], value)}
              min={5}
              max={50}
              disabled={!config.momentum.stochasticRSI.enabled}
            />
          </Form.Item>
          <Form.Item label="随机周期">
            <InputNumber
              value={config.momentum.stochasticRSI.stochPeriod}
              onChange={(value) => updateConfig(['momentum', 'stochasticRSI', 'stochPeriod'], value)}
              min={5}
              max={50}
              disabled={!config.momentum.stochasticRSI.enabled}
            />
          </Form.Item>
        </Card>
      </Col>
      
      <Col xs={24} md={12}>
        <Card title="威廉波动率指数" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.momentum.williamsVIX.enabled}
              onChange={(checked) => updateConfig(['momentum', 'williamsVIX', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="周期">
            <InputNumber
              value={config.momentum.williamsVIX.period}
              onChange={(value) => updateConfig(['momentum', 'williamsVIX', 'period'], value)}
              min={5}
              max={50}
              disabled={!config.momentum.williamsVIX.enabled}
            />
          </Form.Item>
          <Alert
            message="Williams VIX说明"
            description="威廉波动率指数衡量价格波动的强度，数值越高表示波动越大。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        </Card>
      </Col>
    </Row>
  )

  const renderVolatilityTab = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={12}>
        <Card title="平均真实波动范围 (ATR)" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.volatility.atr.enabled}
              onChange={(checked) => updateConfig(['volatility', 'atr', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="周期">
            <InputNumber
              value={config.volatility.atr.period}
              onChange={(value) => updateConfig(['volatility', 'atr', 'period'], value)}
              min={5}
              max={50}
              disabled={!config.volatility.atr.enabled}
            />
          </Form.Item>
        </Card>
      </Col>
      
      <Col xs={24} md={12}>
        <Card title="Keltner通道" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.volatility.kellerChannels.enabled}
              onChange={(checked) => updateConfig(['volatility', 'kellerChannels', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="周期">
            <InputNumber
              value={config.volatility.kellerChannels.period}
              onChange={(value) => updateConfig(['volatility', 'kellerChannels', 'period'], value)}
              min={5}
              max={50}
              disabled={!config.volatility.kellerChannels.enabled}
            />
          </Form.Item>
          <Form.Item label="ATR倍数">
            <InputNumber
              value={config.volatility.kellerChannels.atrMultiplier}
              onChange={(value) => updateConfig(['volatility', 'kellerChannels', 'atrMultiplier'], value)}
              min={0.5}
              max={5}
              step={0.1}
              disabled={!config.volatility.kellerChannels.enabled}
            />
          </Form.Item>
        </Card>
      </Col>
    </Row>
  )

  const renderVolumeTab = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={12}>
        <Card title="Chaikin资金流量" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.volume.chaikinMoneyFlow.enabled}
              onChange={(checked) => updateConfig(['volume', 'chaikinMoneyFlow', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="周期">
            <InputNumber
              value={config.volume.chaikinMoneyFlow.period}
              onChange={(value) => updateConfig(['volume', 'chaikinMoneyFlow', 'period'], value)}
              min={5}
              max={50}
              disabled={!config.volume.chaikinMoneyFlow.enabled}
            />
          </Form.Item>
        </Card>
      </Col>
      
      <Col xs={24} md={12}>
        <Card title="成交量分布" size="small">
          <Form.Item label="启用">
            <Switch
              checked={config.volume.volumeProfile.enabled}
              onChange={(checked) => updateConfig(['volume', 'volumeProfile', 'enabled'], checked)}
            />
          </Form.Item>
          <Form.Item label="价格区间数">
            <InputNumber
              value={config.volume.volumeProfile.bins}
              onChange={(value) => updateConfig(['volume', 'volumeProfile', 'bins'], value)}
              min={20}
              max={100}
              disabled={!config.volume.volumeProfile.enabled}
            />
          </Form.Item>
        </Card>
      </Col>
    </Row>
  )

  const tabItems = [
    {
      key: 'movingAverages',
      label: '移动平均线',
      children: renderMovingAveragesTab(),
    },
    {
      key: 'momentum',
      label: '动量指标',
      children: renderMomentumTab(),
    },
    {
      key: 'volatility',
      label: '波动率指标',
      children: renderVolatilityTab(),
    },
    {
      key: 'volume',
      label: '量价指标',
      children: renderVolumeTab(),
    },
  ]

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          技术指标配置
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={
        <Space>
          <Button onClick={handleReset} icon={<ReloadOutlined />}>
            重置默认
          </Button>
          <Button onClick={onClose}>
            取消
          </Button>
          <Button type="primary" onClick={handleSave} icon={<SaveOutlined />}>
            保存配置
          </Button>
        </Space>
      }
    >
      <Alert
        message="配置说明"
        description="调整技术指标参数以适应不同的市场环境和交易策略。建议在历史数据上测试参数效果。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      
      <Form form={form} layout="vertical">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      </Form>
    </Modal>
  )
}

export default TechnicalIndicatorConfig
