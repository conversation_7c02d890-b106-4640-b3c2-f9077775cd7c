/**
 * 股票数据服务 - 统一的数据接口层
 * 支持真实API和模拟数据的切换
 */

import { stockService } from './apiService'
import { PriceData } from '@/utils/advancedIndicators'
import { DATA_SOURCE_CONFIG, CACHE_CONFIG } from '@/config/dataConfig'

export interface StockInfo {
  code: string
  name: string
  current_price: number
  change: number
  change_percent: number
  volume: number
  market_cap: number
  pe_ratio: number
  pb_ratio: number
  sector: string
  is_watched?: boolean
  high: number
  low: number
  open: number
  close: number
  turnover_rate?: number
  amplitude?: number
  high_52w?: number
  low_52w?: number
  avg_volume?: number
  dividend_yield?: number
  eps?: number
  beta?: number
}

export interface StockDataResponse {
  stock: StockInfo
  priceData: PriceData[]
  success: boolean
  message?: string
}

class StockDataService {
  private cache = new Map<string, { data: StockDataResponse; timestamp: number }>()
  private readonly CACHE_DURATION = CACHE_CONFIG.STOCK_DATA_TTL

  /**
   * 获取股票数据（统一接口）
   */
  async getStockData(symbol: string): Promise<StockDataResponse> {
    try {
      // 检查缓存
      const cached = this.getCachedData(symbol)
      if (cached) {
        return cached
      }

      let result: StockDataResponse

      if (DATA_SOURCE_CONFIG.USE_REAL_API) {
        result = await this.fetchRealData(symbol)
      } else {
        result = await this.generateMockData(symbol)
      }

      // 缓存结果
      this.setCachedData(symbol, result)
      return result

    } catch (error) {
      console.error('获取股票数据失败:', error)
      return {
        stock: this.getDefaultStockInfo(symbol),
        priceData: [],
        success: false,
        message: '数据获取失败，请稍后重试'
      }
    }
  }

  /**
   * 从真实API获取数据
   */
  private async fetchRealData(symbol: string): Promise<StockDataResponse> {
    try {
      // 获取股票基本信息
      const stockResponse = await stockService.getStockDetail(symbol)

      // 如果股票不存在，返回友好的错误信息
      if (stockResponse.code !== 200) {
        if (stockResponse.code === 404) {
          return {
            stock: this.getDefaultStockInfo(symbol),
            priceData: [],
            success: false,
            message: `股票代码 ${symbol} 不存在，请检查代码是否正确`
          }
        }
        throw new Error(stockResponse.message || 'API请求失败')
      }

      // 获取K线数据
      const klineResponse = await stockService.getKlineData(symbol, 'daily', undefined, undefined, 100)

      if (klineResponse.code !== 200) {
        throw new Error(klineResponse.message || 'K线数据获取失败')
      }

      // 尝试获取实时数据
      let realtimeData = null
      try {
        const realtimeResponse = await stockService.getRealtimeData(symbol)
        if (realtimeResponse.code === 200) {
          realtimeData = realtimeResponse.data
        }
      } catch (error) {
        console.warn('获取实时数据失败，使用默认值:', error)
      }

      // 从K线数据中获取最新的开高低收数据
      const latestKline = (klineResponse.data.klines || []).slice(-1)[0]

      const stock: StockInfo = {
        code: stockResponse.data.stock_code,
        name: stockResponse.data.stock_name,
        current_price: realtimeData?.current_price || latestKline?.close_price || 0,
        change: realtimeData?.change || latestKline?.change || 0,
        change_percent: realtimeData?.change_percent || latestKline?.change_percent || 0,
        volume: realtimeData?.volume || latestKline?.volume || 0,
        market_cap: 0, // 暂时没有市值数据
        pe_ratio: 0, // 暂时没有PE数据
        pb_ratio: 0, // 暂时没有PB数据
        sector: stockResponse.data.industry || '未知',
        is_watched: false,
        high: latestKline?.high_price || 0,
        low: latestKline?.low_price || 0,
        open: latestKline?.open_price || 0,
        close: latestKline?.close_price || 0,
        turnover_rate: 0,
        amplitude: 0,
        high_52w: 0, // 暂时没有52周高点数据
        low_52w: 0, // 暂时没有52周低点数据
        avg_volume: 0, // 暂时没有平均成交量数据
        dividend_yield: 0, // 暂时没有股息率数据
        eps: 0, // 暂时没有EPS数据
        beta: 0 // 暂时没有Beta数据
      }

      const priceData: PriceData[] = (klineResponse.data.klines || []).map((item: any) => ({
        timestamp: item.trade_date,
        date: item.trade_date,
        open: item.open_price,
        high: item.high_price,
        low: item.low_price,
        close: item.close_price,
        volume: item.volume,
        adj_close: item.close_price // 使用收盘价作为调整后收盘价
      }))

      return {
        stock,
        priceData,
        success: true
      }

    } catch (error) {
      console.error('真实API数据获取失败:', error)
      throw error
    }
  }

  /**
   * 生成模拟数据
   */
  private async generateMockData(symbol: string): Promise<StockDataResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, DATA_SOURCE_CONFIG.MOCK_CONFIG.NETWORK_DELAY))

    const stock = this.getMockStockInfo(symbol)
    const priceData = this.generateMockPriceData(symbol, stock.current_price)

    return {
      stock,
      priceData,
      success: true
    }
  }

  /**
   * 获取模拟股票信息
   */
  private getMockStockInfo(symbol: string): StockInfo {
    const mockStocks: { [key: string]: StockInfo } = {
      'AAPL': {
        code: 'AAPL',
        name: '苹果公司',
        current_price: 175.43,
        change: 2.15,
        change_percent: 1.24,
        volume: 45678900,
        market_cap: 2800000000000,
        pe_ratio: 28.5,
        pb_ratio: 12.3,
        sector: '科技',
        high_52w: 198.23,
        low_52w: 124.17,
        avg_volume: 52000000,
        dividend_yield: 0.52,
        eps: 6.15,
        beta: 1.21
      },
      'GOOGL': {
        code: 'GOOGL',
        name: '谷歌',
        current_price: 2847.63,
        change: -15.42,
        change_percent: -0.54,
        volume: 1234567,
        market_cap: 1900000000000,
        pe_ratio: 25.8,
        pb_ratio: 5.2,
        sector: '科技',
        high_52w: 3030.93,
        low_52w: 2193.62,
        avg_volume: 1500000,
        dividend_yield: 0.0,
        eps: 110.25,
        beta: 1.05
      },
      'MSFT': {
        code: 'MSFT',
        name: '微软',
        current_price: 378.85,
        change: 4.23,
        change_percent: 1.13,
        volume: 23456789,
        market_cap: 2820000000000,
        pe_ratio: 32.1,
        pb_ratio: 11.8,
        sector: '科技',
        high_52w: 384.30,
        low_52w: 213.43,
        avg_volume: 25000000,
        dividend_yield: 0.72,
        eps: 11.79,
        beta: 0.89
      }
    }

    return mockStocks[symbol.toUpperCase()] || this.getDefaultStockInfo(symbol)
  }

  /**
   * 生成模拟价格数据
   */
  private generateMockPriceData(symbol: string, basePrice: number): PriceData[] {
    const data: PriceData[] = []
    let currentPrice = basePrice * 0.9
    
    // 使用股票代码作为种子，确保数据稳定
    const seed = symbol.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)

    for (let i = 0; i < 100; i++) {
      const date = new Date()
      date.setDate(date.getDate() - (100 - i))
      
      // 使用确定性的伪随机数生成器
      const pseudoRandom1 = Math.sin(seed + i * 1.1) * 0.5
      const pseudoRandom2 = Math.sin(seed + i * 2.3) * 0.5
      const pseudoRandom3 = Math.sin(seed + i * 3.7) * 0.5
      const pseudoRandom4 = Math.sin(seed + i * 5.1) * 0.5
      
      const change = pseudoRandom1 * 0.04
      const open = currentPrice
      const close = currentPrice * (1 + change)
      const high = Math.max(open, close) * (1 + Math.abs(pseudoRandom2) * 0.02)
      const low = Math.min(open, close) * (1 - Math.abs(pseudoRandom3) * 0.02)
      const volume = Math.floor(Math.abs(pseudoRandom4) * 50000000) + 10000000

      data.push({
        timestamp: date.toISOString().split('T')[0],
        date: date.toISOString().split('T')[0],
        open,
        high,
        low,
        close,
        volume,
        adj_close: close
      })

      currentPrice = close
    }

    return data
  }

  /**
   * 获取默认股票信息
   */
  private getDefaultStockInfo(symbol: string): StockInfo {
    const basePrice = 100 + (symbol.charCodeAt(0) % 100)
    return {
      code: symbol.toUpperCase(),
      name: `${symbol.toUpperCase()} 公司`,
      current_price: basePrice,
      change: (symbol.charCodeAt(1) % 10) - 5,
      change_percent: ((symbol.charCodeAt(1) % 10) - 5) / 100,
      volume: (symbol.charCodeAt(2) % 50) * 1000000,
      market_cap: (symbol.charCodeAt(0) % 1000) * 1000000000,
      pe_ratio: 15 + (symbol.charCodeAt(1) % 30),
      pb_ratio: 1 + (symbol.charCodeAt(2) % 10),
      sector: '未知',
      is_watched: false,
      high: basePrice + (symbol.charCodeAt(0) % 10),
      low: basePrice - (symbol.charCodeAt(1) % 10),
      open: basePrice - (symbol.charCodeAt(2) % 5),
      close: basePrice,
      turnover_rate: (symbol.charCodeAt(0) % 10) / 100,
      amplitude: (symbol.charCodeAt(1) % 20) / 100,
      high_52w: 150 + (symbol.charCodeAt(0) % 100),
      low_52w: 50 + (symbol.charCodeAt(1) % 50),
      avg_volume: (symbol.charCodeAt(2) % 30) * 1000000,
      dividend_yield: (symbol.charCodeAt(0) % 3),
      eps: (symbol.charCodeAt(1) % 20),
      beta: 0.5 + (symbol.charCodeAt(2) % 150) / 100
    }
  }

  /**
   * 缓存管理
   */
  private getCachedData(symbol: string): StockDataResponse | null {
    const cached = this.cache.get(symbol.toUpperCase())
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data
    }
    return null
  }

  private setCachedData(symbol: string, data: StockDataResponse): void {
    this.cache.set(symbol.toUpperCase(), {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * 切换数据源
   */
  static setUseRealAPI(useReal: boolean): void {
    // 这里可以动态切换数据源
    console.log(`数据源已切换为: ${useReal ? '真实API' : '模拟数据'}`)
  }
}

// 导出单例
export const stockDataService = new StockDataService()
export default stockDataService
