#!/usr/bin/env python3
"""
检查backend目录下的数据库文件
"""

import sqlite3
from datetime import datetime

def check_backend_database():
    """检查backend目录下的数据库文件"""
    try:
        print("🔍 检查backend目录下的数据库文件...")
        
        # 连接数据库
        conn = sqlite3.connect('backend/data/stock_analyzer.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_spot_data'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ stock_spot_data表不存在")
            return
        
        print("✅ stock_spot_data表存在")
        
        # 检查数据总数
        cursor.execute("SELECT COUNT(*) FROM stock_spot_data")
        total_count = cursor.fetchone()[0]
        print(f"📊 表中共有 {total_count} 条记录")
        
        if total_count == 0:
            print("⚠️  表中没有数据")
            return
        
        # 查看所有数据，按更新时间排序
        cursor.execute("""
            SELECT stock_code, stock_name, current_price, change_percent, update_time 
            FROM stock_spot_data 
            ORDER BY update_time DESC
        """)
        
        records = cursor.fetchall()
        print(f"📈 所有 {len(records)} 条数据 (按更新时间倒序):")
        
        for i, record in enumerate(records):
            stock_code, stock_name, current_price, change_percent, update_time = record
            print(f"  {i+1}. {stock_code} {stock_name}: ¥{current_price} ({change_percent:+.2f}%) - {update_time}")
        
        # 检查是否是真实数据
        if records:
            first_price = float(records[0][2])
            if first_price != 10.5:
                print("🎉 backend数据库包含真实数据!")
                return True
            else:
                print("⚠️  backend数据库包含模拟数据")
                return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 80)
    print("🗄️  Backend数据库检查工具")
    print("=" * 80)
    
    result = check_backend_database()
    
    print("\n" + "=" * 80)
    print(f"📋 检查结果: {'✅ 真实数据' if result else '❌ 模拟数据'}")
    print("=" * 80)
