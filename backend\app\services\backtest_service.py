"""
回测系统服务模块
"""

import asyncio
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func

from app.core.logging import get_logger
from app.core.database import AsyncSessionLocal
from app.models.analytics import BacktestResult
from app.services.data_storage import DataStorageService
from app.services.indicator_storage import IndicatorStorageService
from app.services.ai_storage import AIPredictionStorageService

logger = get_logger(__name__)


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def run_backtest(self, user_id: int, backtest_config: Dict[str, Any]) -> int:
        """运行回测"""
        try:
            logger.info(f"用户 {user_id} 开始回测: {backtest_config.get('strategy_name')}")
            
            # 验证回测配置
            if not self._validate_config(backtest_config):
                raise ValueError("回测配置无效")
            
            # 获取股票池
            stock_pool = backtest_config.get("stock_pool", ["000001", "000002", "600000"])
            start_date = datetime.strptime(backtest_config["start_date"], "%Y-%m-%d").date()
            end_date = datetime.strptime(backtest_config["end_date"], "%Y-%m-%d").date()
            initial_capital = Decimal(str(backtest_config["initial_capital"]))
            
            # 执行回测
            backtest_results = await self._execute_backtest(
                stock_pool, start_date, end_date, initial_capital, backtest_config
            )
            
            # 计算回测指标
            performance_metrics = self._calculate_performance_metrics(backtest_results)
            
            # 保存回测结果
            backtest_id = await self._save_backtest_result(
                user_id, backtest_config, performance_metrics, backtest_results
            )
            
            logger.info(f"回测完成，结果ID: {backtest_id}")
            return backtest_id
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            return 0
    
    async def _execute_backtest(self, stock_pool: List[str], start_date: date, end_date: date,
                              initial_capital: Decimal, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行回测逻辑"""
        try:
            strategy_name = config["strategy_name"]
            
            # 初始化回测状态
            portfolio = {
                "cash": float(initial_capital),
                "holdings": {},  # {stock_code: {"shares": 100, "avg_cost": 10.0}}
                "total_value": float(initial_capital),
                "daily_values": [],
                "transactions": []
            }
            
            # 获取交易日历
            trading_days = await self._get_trading_days(start_date, end_date)
            
            # 按日期执行回测
            for trading_day in trading_days:
                # 获取当日市场数据
                market_data = await self._get_market_data(stock_pool, trading_day)
                
                # 执行策略逻辑
                signals = await self._generate_signals(strategy_name, stock_pool, trading_day, config)
                
                # 执行交易
                transactions = await self._execute_trades(portfolio, signals, market_data, trading_day)
                
                # 更新组合价值
                portfolio_value = self._calculate_portfolio_value(portfolio, market_data)
                portfolio["daily_values"].append({
                    "date": trading_day.isoformat(),
                    "total_value": portfolio_value,
                    "cash": portfolio["cash"],
                    "stock_value": portfolio_value - portfolio["cash"]
                })
                
                portfolio["transactions"].extend(transactions)
            
            return portfolio
            
        except Exception as e:
            logger.error(f"执行回测逻辑失败: {e}")
            return {}
    
    async def _generate_signals(self, strategy_name: str, stock_pool: List[str], 
                              trading_day: date, config: Dict[str, Any]) -> Dict[str, str]:
        """生成交易信号"""
        try:
            signals = {}
            
            if strategy_name == "ma_crossover":
                # 移动平均线交叉策略
                signals = await self._ma_crossover_strategy(stock_pool, trading_day, config)
            
            elif strategy_name == "rsi_reversal":
                # RSI反转策略
                signals = await self._rsi_reversal_strategy(stock_pool, trading_day, config)
            
            elif strategy_name == "ai_momentum":
                # AI动量策略
                signals = await self._ai_momentum_strategy(stock_pool, trading_day, config)
            
            elif strategy_name == "value_investing":
                # 价值投资策略
                signals = await self._value_investing_strategy(stock_pool, trading_day, config)
            
            return signals
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return {}
    
    async def _ma_crossover_strategy(self, stock_pool: List[str], trading_day: date, 
                                   config: Dict[str, Any]) -> Dict[str, str]:
        """移动平均线交叉策略"""
        try:
            signals = {}
            short_period = config.get("short_ma", 5)
            long_period = config.get("long_ma", 20)
            
            async with IndicatorStorageService() as indicator_storage:
                for stock_code in stock_pool:
                    # 获取技术指标数据
                    indicators = await indicator_storage.get_indicators(
                        stock_code, "daily", limit=long_period + 5
                    )
                    
                    if len(indicators) < long_period:
                        continue
                    
                    # 计算移动平均线
                    prices = [float(ind.close_price) for ind in indicators[-long_period:]]
                    short_ma = np.mean(prices[-short_period:])
                    long_ma = np.mean(prices[-long_period:])
                    prev_short_ma = np.mean(prices[-(short_period+1):-1])
                    prev_long_ma = np.mean(prices[-(long_period+1):-1])
                    
                    # 判断交叉信号
                    if prev_short_ma <= prev_long_ma and short_ma > long_ma:
                        signals[stock_code] = "buy"
                    elif prev_short_ma >= prev_long_ma and short_ma < long_ma:
                        signals[stock_code] = "sell"
            
            return signals
            
        except Exception as e:
            logger.error(f"移动平均线策略失败: {e}")
            return {}
    
    async def _rsi_reversal_strategy(self, stock_pool: List[str], trading_day: date,
                                   config: Dict[str, Any]) -> Dict[str, str]:
        """RSI反转策略"""
        try:
            signals = {}
            oversold_threshold = config.get("oversold", 30)
            overbought_threshold = config.get("overbought", 70)
            
            async with IndicatorStorageService() as indicator_storage:
                for stock_code in stock_pool:
                    indicators = await indicator_storage.get_indicators(
                        stock_code, "daily", limit=2
                    )
                    
                    if len(indicators) < 2:
                        continue
                    
                    current_rsi = float(indicators[-1].rsi) if indicators[-1].rsi else 50
                    prev_rsi = float(indicators[-2].rsi) if indicators[-2].rsi else 50
                    
                    # RSI反转信号
                    if prev_rsi <= oversold_threshold and current_rsi > oversold_threshold:
                        signals[stock_code] = "buy"
                    elif prev_rsi >= overbought_threshold and current_rsi < overbought_threshold:
                        signals[stock_code] = "sell"
            
            return signals
            
        except Exception as e:
            logger.error(f"RSI反转策略失败: {e}")
            return {}
    
    async def _ai_momentum_strategy(self, stock_pool: List[str], trading_day: date,
                                  config: Dict[str, Any]) -> Dict[str, str]:
        """AI动量策略"""
        try:
            signals = {}
            confidence_threshold = config.get("confidence_threshold", 0.7)
            
            async with AIPredictionStorageService() as ai_storage:
                for stock_code in stock_pool:
                    predictions = await ai_storage.get_predictions(
                        stock_code=stock_code, limit=1
                    )
                    
                    if not predictions:
                        continue
                    
                    prediction = predictions[0]
                    confidence = float(prediction.confidence_score) if prediction.confidence_score else 0
                    
                    if confidence >= confidence_threshold:
                        if prediction.trend_direction == "up":
                            signals[stock_code] = "buy"
                        elif prediction.trend_direction == "down":
                            signals[stock_code] = "sell"
            
            return signals
            
        except Exception as e:
            logger.error(f"AI动量策略失败: {e}")
            return {}
    
    async def _value_investing_strategy(self, stock_pool: List[str], trading_day: date,
                                      config: Dict[str, Any]) -> Dict[str, str]:
        """价值投资策略"""
        try:
            signals = {}
            pe_threshold = config.get("max_pe", 15)
            pb_threshold = config.get("max_pb", 2)
            
            async with DataStorageService() as data_storage:
                for stock_code in stock_pool:
                    # 获取基本面数据
                    stock_info = await data_storage.get_stock_info(stock_code)
                    
                    if not stock_info:
                        continue
                    
                    pe_ratio = stock_info.pe_ratio or 999
                    pb_ratio = stock_info.pb_ratio or 999
                    
                    # 价值投资筛选
                    if pe_ratio <= pe_threshold and pb_ratio <= pb_threshold:
                        signals[stock_code] = "buy"
                    elif pe_ratio > pe_threshold * 2 or pb_ratio > pb_threshold * 2:
                        signals[stock_code] = "sell"
            
            return signals
            
        except Exception as e:
            logger.error(f"价值投资策略失败: {e}")
            return {}
    
    async def _execute_trades(self, portfolio: Dict, signals: Dict[str, str], 
                            market_data: Dict, trading_day: date) -> List[Dict]:
        """执行交易"""
        try:
            transactions = []
            position_size = 0.1  # 每次交易占组合的10%
            
            for stock_code, signal in signals.items():
                if stock_code not in market_data:
                    continue
                
                price = market_data[stock_code]["close"]
                
                if signal == "buy" and portfolio["cash"] > 0:
                    # 买入
                    trade_amount = portfolio["cash"] * position_size
                    shares = int(trade_amount / price / 100) * 100  # 整手交易
                    
                    if shares > 0:
                        cost = shares * price
                        portfolio["cash"] -= cost
                        
                        if stock_code in portfolio["holdings"]:
                            # 更新持仓
                            old_shares = portfolio["holdings"][stock_code]["shares"]
                            old_cost = portfolio["holdings"][stock_code]["avg_cost"]
                            new_avg_cost = (old_shares * old_cost + cost) / (old_shares + shares)
                            portfolio["holdings"][stock_code] = {
                                "shares": old_shares + shares,
                                "avg_cost": new_avg_cost
                            }
                        else:
                            # 新建持仓
                            portfolio["holdings"][stock_code] = {
                                "shares": shares,
                                "avg_cost": price
                            }
                        
                        transactions.append({
                            "date": trading_day.isoformat(),
                            "stock_code": stock_code,
                            "action": "buy",
                            "shares": shares,
                            "price": price,
                            "amount": cost
                        })
                
                elif signal == "sell" and stock_code in portfolio["holdings"]:
                    # 卖出
                    holding = portfolio["holdings"][stock_code]
                    shares_to_sell = holding["shares"]
                    
                    if shares_to_sell > 0:
                        proceeds = shares_to_sell * price
                        portfolio["cash"] += proceeds
                        del portfolio["holdings"][stock_code]
                        
                        transactions.append({
                            "date": trading_day.isoformat(),
                            "stock_code": stock_code,
                            "action": "sell",
                            "shares": shares_to_sell,
                            "price": price,
                            "amount": proceeds
                        })
            
            return transactions
            
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return []
    
    def _calculate_portfolio_value(self, portfolio: Dict, market_data: Dict) -> float:
        """计算组合价值"""
        try:
            stock_value = 0
            for stock_code, holding in portfolio["holdings"].items():
                if stock_code in market_data:
                    current_price = market_data[stock_code]["close"]
                    stock_value += holding["shares"] * current_price
            
            return portfolio["cash"] + stock_value
            
        except Exception as e:
            logger.error(f"计算组合价值失败: {e}")
            return portfolio["cash"]
    
    def _calculate_performance_metrics(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算回测表现指标"""
        try:
            daily_values = backtest_results.get("daily_values", [])
            transactions = backtest_results.get("transactions", [])
            
            if len(daily_values) < 2:
                return {}
            
            # 基础指标
            initial_value = daily_values[0]["total_value"]
            final_value = daily_values[-1]["total_value"]
            total_return = final_value - initial_value
            total_return_pct = (total_return / initial_value) * 100
            
            # 计算日收益率
            daily_returns = []
            for i in range(1, len(daily_values)):
                prev_value = daily_values[i-1]["total_value"]
                curr_value = daily_values[i]["total_value"]
                daily_return = (curr_value - prev_value) / prev_value
                daily_returns.append(daily_return)
            
            # 年化收益率
            trading_days = len(daily_values)
            years = trading_days / 252
            annualized_return = ((final_value / initial_value) ** (1/years) - 1) * 100 if years > 0 else 0
            
            # 波动率
            volatility = np.std(daily_returns) * np.sqrt(252) * 100 if daily_returns else 0
            
            # 夏普比率
            risk_free_rate = 0.03
            excess_returns = np.array(daily_returns) - risk_free_rate/252
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if len(excess_returns) > 0 and np.std(excess_returns) > 0 else 0
            
            # 最大回撤
            cumulative_values = [dv["total_value"] for dv in daily_values]
            peak = cumulative_values[0]
            max_drawdown = 0
            for value in cumulative_values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            max_drawdown_pct = max_drawdown * 100
            
            # 交易统计
            buy_trades = [t for t in transactions if t["action"] == "buy"]
            sell_trades = [t for t in transactions if t["action"] == "sell"]
            total_trades = len(buy_trades) + len(sell_trades)
            
            # 计算盈亏交易
            winning_trades = 0
            losing_trades = 0
            
            # 简化的盈亏计算（实际应该配对买卖交易）
            for sell_trade in sell_trades:
                # 这里简化处理，实际应该找到对应的买入交易
                profit = sell_trade["amount"] - (sell_trade["shares"] * 10)  # 假设平均成本10元
                if profit > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
            
            win_rate = (winning_trades / (winning_trades + losing_trades)) * 100 if (winning_trades + losing_trades) > 0 else 0
            
            return {
                "initial_value": initial_value,
                "final_value": final_value,
                "total_return": total_return,
                "total_return_pct": total_return_pct,
                "annualized_return": annualized_return,
                "volatility": volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown_pct,
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "trading_days": trading_days
            }
            
        except Exception as e:
            logger.error(f"计算回测指标失败: {e}")
            return {}
    
    async def _save_backtest_result(self, user_id: int, config: Dict[str, Any], 
                                  metrics: Dict[str, Any], results: Dict[str, Any]) -> int:
        """保存回测结果"""
        try:
            backtest_result = BacktestResult(
                user_id=user_id,
                strategy_name=config["strategy_name"],
                strategy_config=config,
                start_date=datetime.strptime(config["start_date"], "%Y-%m-%d").date(),
                end_date=datetime.strptime(config["end_date"], "%Y-%m-%d").date(),
                initial_capital=Decimal(str(config["initial_capital"])),
                final_value=Decimal(str(metrics.get("final_value", 0))),
                total_return=Decimal(str(metrics.get("total_return", 0))),
                total_return_pct=Decimal(str(metrics.get("total_return_pct", 0))),
                annualized_return=Decimal(str(metrics.get("annualized_return", 0))),
                volatility=Decimal(str(metrics.get("volatility", 0))),
                sharpe_ratio=Decimal(str(metrics.get("sharpe_ratio", 0))),
                max_drawdown=Decimal(str(metrics.get("max_drawdown", 0))),
                total_trades=metrics.get("total_trades", 0),
                winning_trades=metrics.get("winning_trades", 0),
                losing_trades=metrics.get("losing_trades", 0),
                win_rate=Decimal(str(metrics.get("win_rate", 0))),
                performance_data=results.get("daily_values", []),
                trade_data=results.get("transactions", [])
            )
            
            self.session.add(backtest_result)
            await self.session.commit()
            await self.session.refresh(backtest_result)
            
            return backtest_result.id
            
        except Exception as e:
            logger.error(f"保存回测结果失败: {e}")
            await self.session.rollback()
            return 0
    
    async def _get_trading_days(self, start_date: date, end_date: date) -> List[date]:
        """获取交易日历"""
        try:
            # 简化实现：生成工作日列表
            trading_days = []
            current_date = start_date
            
            while current_date <= end_date:
                # 排除周末
                if current_date.weekday() < 5:
                    trading_days.append(current_date)
                current_date += timedelta(days=1)
            
            return trading_days
            
        except Exception as e:
            logger.error(f"获取交易日历失败: {e}")
            return []
    
    async def _get_market_data(self, stock_pool: List[str], trading_day: date) -> Dict[str, Dict]:
        """获取市场数据"""
        try:
            market_data = {}
            
            async with DataStorageService() as data_storage:
                for stock_code in stock_pool:
                    # 获取K线数据
                    klines = await data_storage.get_kline_data(
                        stock_code, "daily", start_date=trading_day, end_date=trading_day
                    )
                    
                    if klines:
                        kline = klines[0]
                        market_data[stock_code] = {
                            "open": float(kline.open_price),
                            "high": float(kline.high_price),
                            "low": float(kline.low_price),
                            "close": float(kline.close_price),
                            "volume": kline.volume
                        }
            
            return market_data
            
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return {}
    
    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """验证回测配置"""
        required_fields = ["strategy_name", "start_date", "end_date", "initial_capital"]
        
        for field in required_fields:
            if field not in config:
                logger.error(f"回测配置缺少必填字段: {field}")
                return False
        
        try:
            start_date = datetime.strptime(config["start_date"], "%Y-%m-%d").date()
            end_date = datetime.strptime(config["end_date"], "%Y-%m-%d").date()
            
            if start_date >= end_date:
                logger.error("开始日期必须早于结束日期")
                return False
            
            if float(config["initial_capital"]) <= 0:
                logger.error("初始资金必须大于0")
                return False
            
            return True
            
        except (ValueError, TypeError) as e:
            logger.error(f"回测配置格式错误: {e}")
            return False
