import React, { useState, useEffect } from 'react'
import {
  Modal,
  Descriptions,
  Timeline,
  Tag,
  Progress,
  Spin,
  Alert,
  Card,
  Row,
  Col,
  Typography,
  Space,
  Divider,
  List
} from 'antd'
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined
} from '@ant-design/icons'
import { TaskDetails, TaskLog, dataManagementService } from '../services/dataManagementService'

const { Title, Text, Paragraph } = Typography

interface TaskDetailsModalProps {
  visible: boolean
  taskId: string | null
  onClose: () => void
}

const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
  visible,
  taskId,
  onClose
}) => {
  const [loading, setLoading] = useState(false)
  const [taskDetails, setTaskDetails] = useState<TaskDetails | null>(null)
  const [error, setError] = useState<string | null>(null)

  const dataService = dataManagementService

  useEffect(() => {
    if (visible && taskId) {
      loadTaskDetails()
    }
  }, [visible, taskId])

  const loadTaskDetails = async () => {
    if (!taskId) return

    setLoading(true)
    setError(null)
    try {
      const details = await dataService.getTaskDetails(taskId)
      setTaskDetails(details)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取任务详情失败')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'processing'
      case 'completed':
        return 'success'
      case 'failed':
        return 'error'
      case 'pending':
        return 'default'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running':
        return '运行中'
      case 'completed':
        return '已完成'
      case 'failed':
        return '失败'
      case 'pending':
        return '等待中'
      default:
        return status
    }
  }

  const getLogIcon = (level: string) => {
    switch (level) {
      case 'INFO':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />
      case 'WARNING':
        return <WarningOutlined style={{ color: '#faad14' }} />
      case 'ERROR':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      default:
        return <InfoCircleOutlined />
    }
  }

  const renderTaskInfo = () => {
    if (!taskDetails) return null

    const { task, execution_info } = taskDetails

    return (
      <Card title="任务基本信息" size="small" style={{ marginBottom: 16 }}>
        <Descriptions column={2} size="small">
          <Descriptions.Item label="任务ID">{task.id}</Descriptions.Item>
          <Descriptions.Item label="任务名称">{task.name}</Descriptions.Item>
          <Descriptions.Item label="任务类型">{task.type}</Descriptions.Item>
          <Descriptions.Item label="任务状态">
            <Tag color={getStatusColor(task.status)}>
              {getStatusText(task.status)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="开始时间">
            {task.start_time || '未开始'}
          </Descriptions.Item>
          <Descriptions.Item label="结束时间">
            {task.end_time || '未结束'}
          </Descriptions.Item>
          <Descriptions.Item label="执行时长">
            {execution_info?.duration || '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="任务进度">
            <Progress percent={task.progress} size="small" />
          </Descriptions.Item>
        </Descriptions>

        {task.records_processed && task.total_records && (
          <div style={{ marginTop: 12 }}>
            <Text type="secondary">
              处理进度: {task.records_processed}/{task.total_records} 条记录
            </Text>
          </div>
        )}

        {task.error_message && (
          <Alert
            message="错误信息"
            description={task.error_message}
            type="error"
            showIcon
            style={{ marginTop: 12 }}
          />
        )}
      </Card>
    )
  }

  const renderDataSourceInfo = () => {
    if (!taskDetails?.execution_info?.data_source) return null

    const { data_source } = taskDetails.execution_info

    return (
      <Card title="数据源信息" size="small" style={{ marginBottom: 16 }}>
        <Descriptions column={1} size="small">
          <Descriptions.Item label="数据源">{data_source.source}</Descriptions.Item>
          <Descriptions.Item label="API接口">{data_source.api}</Descriptions.Item>
          <Descriptions.Item label="更新频率">{data_source.update_frequency}</Descriptions.Item>
          <Descriptions.Item label="描述">
            <Paragraph style={{ margin: 0 }}>{data_source.description}</Paragraph>
          </Descriptions.Item>
          <Descriptions.Item label="数据字段">
            <Space wrap>
              {(data_source.data_fields || []).map((field, index) => (
                <Tag key={index} color="blue">{field}</Tag>
              ))}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    )
  }

  const renderUpdateDetails = () => {
    if (!taskDetails?.update_details) return null

    const { update_details } = taskDetails

    return (
      <Card title="数据更新详情" size="small" style={{ marginBottom: 16 }}>
        <Alert
          message={update_details.update_type}
          description={update_details.data_summary}
          type="info"
          showIcon
          style={{ marginBottom: 12 }}
        />

        <Row gutter={16}>
          {update_details.total_stocks && (
            <Col span={8}>
              <Card size="small">
                <div style={{ textAlign: 'center' }}>
                  <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                    {update_details.total_stocks}
                  </Title>
                  <Text type="secondary">总股票数</Text>
                </div>
              </Card>
            </Col>
          )}
          {update_details.updated_today && (
            <Col span={8}>
              <Card size="small">
                <div style={{ textAlign: 'center' }}>
                  <Title level={4} style={{ margin: 0, color: '#52c41a' }}>
                    {update_details.updated_today}
                  </Title>
                  <Text type="secondary">今日更新</Text>
                </div>
              </Card>
            </Col>
          )}
          {update_details.today_records && (
            <Col span={8}>
              <Card size="small">
                <div style={{ textAlign: 'center' }}>
                  <Title level={4} style={{ margin: 0, color: '#722ed1' }}>
                    {update_details.today_records}
                  </Title>
                  <Text type="secondary">今日记录</Text>
                </div>
              </Card>
            </Col>
          )}
        </Row>

        {update_details.error && (
          <Alert
            message="获取详情时出错"
            description={update_details.error}
            type="warning"
            showIcon
            style={{ marginTop: 12 }}
          />
        )}
      </Card>
    )
  }

  const renderExecutionLogs = () => {
    if (!taskDetails?.logs || taskDetails.logs.length === 0) {
      return (
        <Card title="执行日志" size="small">
          <Text type="secondary">暂无执行日志</Text>
        </Card>
      )
    }

    return (
      <Card title="执行日志" size="small">
        <List
          size="small"
          dataSource={taskDetails.logs}
          renderItem={(log: TaskLog) => (
            <List.Item>
              <Space>
                {getLogIcon(log.level)}
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {log.timestamp}
                </Text>
                <Text>{log.message}</Text>
              </Space>
            </List.Item>
          )}
        />
      </Card>
    )
  }

  return (
    <Modal
      title="任务详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      style={{ top: 20 }}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在加载任务详情...</Text>
          </div>
        </div>
      ) : error ? (
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
        />
      ) : taskDetails ? (
        <div>
          {renderTaskInfo()}
          {renderDataSourceInfo()}
          {renderUpdateDetails()}
          {renderExecutionLogs()}
        </div>
      ) : null}
    </Modal>
  )
}

export default TaskDetailsModal
