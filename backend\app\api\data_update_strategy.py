"""
数据更新策略管理API
提供定时批量更新、按需更新、增量更新机制
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
from loguru import logger
import asyncio
import json

from app.api.professional_db_management import get_professional_db
from app.models.professional_stock_db import DataUpdateLog

router = APIRouter()

# 更新策略类型
UPDATE_STRATEGIES = {
    "scheduled": {
        "name": "定时更新",
        "description": "按照预设时间间隔自动更新数据",
        "config_fields": ["interval_hours", "start_time", "end_time", "weekdays"]
    },
    "on_demand": {
        "name": "按需更新", 
        "description": "手动触发或API调用时更新数据",
        "config_fields": ["priority", "timeout_minutes"]
    },
    "incremental": {
        "name": "增量更新",
        "description": "只更新变化的数据，提高效率",
        "config_fields": ["check_interval_minutes", "batch_size", "max_records_per_batch"]
    },
    "real_time": {
        "name": "实时更新",
        "description": "监听数据源变化，实时同步数据",
        "config_fields": ["websocket_url", "retry_attempts", "buffer_size"]
    }
}

# 数据源配置
DATA_SOURCES = {
    "akshare": {
        "name": "AKShare",
        "description": "AKShare数据接口",
        "supported_data": ["stock_basic", "kline", "realtime", "financial"],
        "rate_limit": "100/minute"
    },
    "tushare": {
        "name": "Tushare",
        "description": "Tushare数据接口", 
        "supported_data": ["stock_basic", "kline", "financial", "indicators"],
        "rate_limit": "200/minute"
    },
    "manual": {
        "name": "手动导入",
        "description": "手动上传CSV/Excel文件",
        "supported_data": ["all"],
        "rate_limit": "unlimited"
    }
}

class UpdateStrategy(BaseModel):
    """更新策略配置"""
    strategy_id: Optional[str] = None
    strategy_name: str
    strategy_type: str  # scheduled, on_demand, incremental, real_time
    data_source: str    # akshare, tushare, manual
    target_tables: List[str]
    config: Dict[str, Any]
    is_active: bool = True
    created_at: Optional[datetime] = None
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None

class UpdateTask(BaseModel):
    """更新任务"""
    task_id: Optional[str] = None
    strategy_id: str
    task_name: str
    status: str  # pending, running, completed, failed
    progress: float = 0.0
    total_records: int = 0
    processed_records: int = 0
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

class UpdateResult(BaseModel):
    """更新结果"""
    task_id: str
    success: bool
    records_updated: int
    records_inserted: int
    records_failed: int
    execution_time_seconds: float
    error_details: Optional[List[str]] = None

# 内存中的策略和任务存储（生产环境应使用数据库）
strategies_store: Dict[str, UpdateStrategy] = {}
tasks_store: Dict[str, UpdateTask] = {}

@router.get("/strategies", response_model=List[UpdateStrategy])
async def get_update_strategies():
    """获取所有更新策略"""
    try:
        return list(strategies_store.values())
    except Exception as e:
        logger.error(f"获取更新策略失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取更新策略失败: {str(e)}")

@router.post("/strategies", response_model=UpdateStrategy)
async def create_update_strategy(strategy: UpdateStrategy):
    """创建新的更新策略"""
    try:
        # 生成策略ID
        strategy_id = f"strategy_{len(strategies_store) + 1}_{int(datetime.now().timestamp())}"
        strategy.strategy_id = strategy_id
        strategy.created_at = datetime.now()
        
        # 计算下次运行时间
        if strategy.strategy_type == "scheduled":
            interval_hours = strategy.config.get("interval_hours", 24)
            strategy.next_run = datetime.now() + timedelta(hours=interval_hours)
        
        strategies_store[strategy_id] = strategy
        
        logger.info(f"创建更新策略成功: {strategy_id}")
        return strategy
        
    except Exception as e:
        logger.error(f"创建更新策略失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建更新策略失败: {str(e)}")

@router.put("/strategies/{strategy_id}", response_model=UpdateStrategy)
async def update_strategy(strategy_id: str, strategy: UpdateStrategy):
    """更新策略配置"""
    try:
        if strategy_id not in strategies_store:
            raise HTTPException(status_code=404, detail="策略不存在")
        
        strategy.strategy_id = strategy_id
        strategies_store[strategy_id] = strategy
        
        logger.info(f"更新策略成功: {strategy_id}")
        return strategy
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新策略失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新策略失败: {str(e)}")

@router.delete("/strategies/{strategy_id}")
async def delete_strategy(strategy_id: str):
    """删除更新策略"""
    try:
        if strategy_id not in strategies_store:
            raise HTTPException(status_code=404, detail="策略不存在")
        
        del strategies_store[strategy_id]
        
        logger.info(f"删除策略成功: {strategy_id}")
        return {"message": "策略删除成功", "strategy_id": strategy_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除策略失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除策略失败: {str(e)}")

@router.post("/strategies/{strategy_id}/execute")
async def execute_strategy(
    strategy_id: str, 
    background_tasks: BackgroundTasks,
    force: bool = False
):
    """执行更新策略"""
    try:
        if strategy_id not in strategies_store:
            raise HTTPException(status_code=404, detail="策略不存在")
        
        strategy = strategies_store[strategy_id]
        
        if not strategy.is_active and not force:
            raise HTTPException(status_code=400, detail="策略未激活")
        
        # 创建更新任务
        task_id = f"task_{strategy_id}_{int(datetime.now().timestamp())}"
        task = UpdateTask(
            task_id=task_id,
            strategy_id=strategy_id,
            task_name=f"执行策略: {strategy.strategy_name}",
            status="pending",
            started_at=datetime.now()
        )
        
        tasks_store[task_id] = task
        
        # 在后台执行更新任务
        background_tasks.add_task(execute_update_task, task_id, strategy)
        
        return {
            "message": "更新任务已启动",
            "task_id": task_id,
            "strategy_id": strategy_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行策略失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行策略失败: {str(e)}")

@router.get("/tasks", response_model=List[UpdateTask])
async def get_update_tasks(
    strategy_id: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 50
):
    """获取更新任务列表"""
    try:
        tasks = list(tasks_store.values())
        
        # 过滤条件
        if strategy_id:
            tasks = [task for task in tasks if task.strategy_id == strategy_id]
        
        if status:
            tasks = [task for task in tasks if task.status == status]
        
        # 按创建时间倒序排列
        tasks.sort(key=lambda x: x.started_at or datetime.min, reverse=True)
        
        return tasks[:limit]
        
    except Exception as e:
        logger.error(f"获取更新任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取更新任务失败: {str(e)}")

@router.get("/tasks/{task_id}", response_model=UpdateTask)
async def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        if task_id not in tasks_store:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return tasks_store[task_id]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.post("/tasks/{task_id}/cancel")
async def cancel_task(task_id: str):
    """取消更新任务"""
    try:
        if task_id not in tasks_store:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        task = tasks_store[task_id]
        
        if task.status in ["completed", "failed"]:
            raise HTTPException(status_code=400, detail="任务已完成，无法取消")
        
        task.status = "cancelled"
        task.completed_at = datetime.now()
        
        return {"message": "任务已取消", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.get("/data-sources")
async def get_data_sources():
    """获取可用的数据源"""
    return {
        "data_sources": DATA_SOURCES,
        "update_strategies": UPDATE_STRATEGIES
    }

@router.get("/statistics")
async def get_update_statistics():
    """获取更新统计信息"""
    try:
        total_strategies = len(strategies_store)
        active_strategies = len([s for s in strategies_store.values() if s.is_active])
        total_tasks = len(tasks_store)
        
        # 统计任务状态
        task_stats = {}
        for task in tasks_store.values():
            task_stats[task.status] = task_stats.get(task.status, 0) + 1
        
        # 最近24小时的任务
        recent_tasks = [
            task for task in tasks_store.values()
            if task.started_at and task.started_at > datetime.now() - timedelta(hours=24)
        ]
        
        return {
            "total_strategies": total_strategies,
            "active_strategies": active_strategies,
            "total_tasks": total_tasks,
            "task_statistics": task_stats,
            "recent_tasks_24h": len(recent_tasks),
            "success_rate": (
                task_stats.get("completed", 0) / max(total_tasks, 1) * 100
                if total_tasks > 0 else 0
            )
        }
        
    except Exception as e:
        logger.error(f"获取更新统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取更新统计失败: {str(e)}")

async def execute_update_task(task_id: str, strategy: UpdateStrategy):
    """执行更新任务的后台函数"""
    try:
        task = tasks_store[task_id]
        task.status = "running"
        task.progress = 0.0
        
        logger.info(f"开始执行更新任务: {task_id}")
        
        # 模拟数据更新过程
        total_steps = len(strategy.target_tables) * 10
        task.total_records = total_steps
        
        for i, table_name in enumerate(strategy.target_tables):
            for step in range(10):
                # 模拟处理过程
                await asyncio.sleep(0.1)
                
                task.processed_records += 1
                task.progress = (task.processed_records / total_steps) * 100
                
                logger.debug(f"处理表 {table_name}, 步骤 {step + 1}/10")
        
        # 更新策略的最后运行时间
        strategy.last_run = datetime.now()
        if strategy.strategy_type == "scheduled":
            interval_hours = strategy.config.get("interval_hours", 24)
            strategy.next_run = datetime.now() + timedelta(hours=interval_hours)
        
        task.status = "completed"
        task.completed_at = datetime.now()
        task.progress = 100.0
        
        logger.info(f"更新任务完成: {task_id}")
        
    except Exception as e:
        logger.error(f"执行更新任务失败: {e}")
        task.status = "failed"
        task.error_message = str(e)
        task.completed_at = datetime.now()
