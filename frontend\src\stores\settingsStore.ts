/**
 * 设置状态管理
 * 管理用户的数据源、缓存、性能等设置
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { CONFIG } from '@/config/dataConfig'

export interface DataSourceSettings {
  useRealAPI: boolean
  apiBaseUrl: string
  wsUrl: string
  timeout: number
  retryCount: number
}

export interface CacheSettings {
  stockDataTTL: number // 分钟
  priceDataTTL: number // 分钟
  indicatorsTTL: number // 分钟
  maxCacheSize: number
  enableCache: boolean
}

export interface MockDataSettings {
  networkDelay: number
  simulateErrors: boolean
  errorProbability: number // 百分比
  defaultStocks: string[]
}

export interface PerformanceSettings {
  debounceDelay: number
  throttleDelay: number
  autoRefreshInterval: number // 分钟
  maxSearchHistory: number
  enableVirtualScroll: boolean
}

export interface UISettings {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  chartTheme: 'default' | 'dark' | 'colorful'
  showWelcomeGuide: boolean
  compactMode: boolean
}

export interface NotificationSettings {
  enablePriceAlerts: boolean
  enableSystemNotifications: boolean
  alertSound: boolean
  emailNotifications: boolean
  pushNotifications: boolean
}

export interface SettingsState {
  dataSource: DataSourceSettings
  cache: CacheSettings
  mockData: MockDataSettings
  performance: PerformanceSettings
  ui: UISettings
  notifications: NotificationSettings
  lastUpdated: number | null
}

export interface SettingsActions {
  // 更新设置
  updateDataSourceSettings: (settings: Partial<DataSourceSettings>) => void
  updateCacheSettings: (settings: Partial<CacheSettings>) => void
  updateMockDataSettings: (settings: Partial<MockDataSettings>) => void
  updatePerformanceSettings: (settings: Partial<PerformanceSettings>) => void
  updateUISettings: (settings: Partial<UISettings>) => void
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void
  
  // 重置设置
  resetToDefaults: () => void
  resetDataSourceSettings: () => void
  resetCacheSettings: () => void
  
  // 导入导出设置
  exportSettings: () => string
  importSettings: (settingsJson: string) => boolean
  
  // 验证设置
  validateSettings: () => { isValid: boolean; errors: string[] }
}

type SettingsStore = SettingsState & SettingsActions

// 默认设置
const getDefaultSettings = (): SettingsState => ({
  dataSource: {
    useRealAPI: CONFIG.DATA_SOURCE.USE_REAL_API,
    apiBaseUrl: CONFIG.API.BASE_URL,
    wsUrl: CONFIG.REALTIME.WS_URL,
    timeout: CONFIG.API.TIMEOUT,
    retryCount: CONFIG.API.RETRY_COUNT,
  },
  cache: {
    stockDataTTL: CONFIG.CACHE.STOCK_DATA_TTL / 1000 / 60, // 转换为分钟
    priceDataTTL: CONFIG.CACHE.PRICE_DATA_TTL / 1000 / 60,
    indicatorsTTL: CONFIG.CACHE.INDICATORS_TTL / 1000 / 60,
    maxCacheSize: CONFIG.CACHE.MAX_CACHE_SIZE,
    enableCache: true,
  },
  mockData: {
    networkDelay: CONFIG.DATA_SOURCE.MOCK_CONFIG.NETWORK_DELAY,
    simulateErrors: CONFIG.DATA_SOURCE.MOCK_CONFIG.SIMULATE_ERRORS,
    errorProbability: CONFIG.DATA_SOURCE.MOCK_CONFIG.ERROR_PROBABILITY * 100,
    defaultStocks: [...CONFIG.DATA_SOURCE.MOCK_CONFIG.DEFAULT_STOCKS],
  },
  performance: {
    debounceDelay: CONFIG.PERFORMANCE.DEBOUNCE_DELAY,
    throttleDelay: CONFIG.PERFORMANCE.THROTTLE_DELAY,
    autoRefreshInterval: CONFIG.UX.AUTO_REFRESH_INTERVAL / 1000 / 60,
    maxSearchHistory: CONFIG.UX.MAX_SEARCH_HISTORY,
    enableVirtualScroll: true,
  },
  ui: {
    theme: 'light',
    language: 'zh-CN',
    chartTheme: 'default',
    showWelcomeGuide: true,
    compactMode: false,
  },
  notifications: {
    enablePriceAlerts: true,
    enableSystemNotifications: true,
    alertSound: true,
    emailNotifications: false,
    pushNotifications: false,
  },
  lastUpdated: null,
})

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      ...getDefaultSettings(),

      // 更新数据源设置
      updateDataSourceSettings: (settings) => {
        set((state) => ({
          dataSource: { ...state.dataSource, ...settings },
          lastUpdated: Date.now(),
        }))
      },

      // 更新缓存设置
      updateCacheSettings: (settings) => {
        set((state) => ({
          cache: { ...state.cache, ...settings },
          lastUpdated: Date.now(),
        }))
      },

      // 更新模拟数据设置
      updateMockDataSettings: (settings) => {
        set((state) => ({
          mockData: { ...state.mockData, ...settings },
          lastUpdated: Date.now(),
        }))
      },

      // 更新性能设置
      updatePerformanceSettings: (settings) => {
        set((state) => ({
          performance: { ...state.performance, ...settings },
          lastUpdated: Date.now(),
        }))
      },

      // 更新UI设置
      updateUISettings: (settings) => {
        set((state) => ({
          ui: { ...state.ui, ...settings },
          lastUpdated: Date.now(),
        }))
      },

      // 更新通知设置
      updateNotificationSettings: (settings) => {
        set((state) => ({
          notifications: { ...state.notifications, ...settings },
          lastUpdated: Date.now(),
        }))
      },

      // 重置所有设置
      resetToDefaults: () => {
        set(getDefaultSettings())
      },

      // 重置数据源设置
      resetDataSourceSettings: () => {
        const defaults = getDefaultSettings()
        set((state) => ({
          dataSource: defaults.dataSource,
          lastUpdated: Date.now(),
        }))
      },

      // 重置缓存设置
      resetCacheSettings: () => {
        const defaults = getDefaultSettings()
        set((state) => ({
          cache: defaults.cache,
          lastUpdated: Date.now(),
        }))
      },

      // 导出设置
      exportSettings: () => {
        const state = get()
        const exportData = {
          dataSource: state.dataSource,
          cache: state.cache,
          mockData: state.mockData,
          performance: state.performance,
          ui: state.ui,
          notifications: state.notifications,
          exportedAt: new Date().toISOString(),
          version: '1.0.0',
        }
        return JSON.stringify(exportData, null, 2)
      },

      // 导入设置
      importSettings: (settingsJson) => {
        try {
          const importData = JSON.parse(settingsJson)
          
          // 验证导入数据的结构
          if (!importData.dataSource || !importData.cache) {
            return false
          }

          set({
            dataSource: { ...getDefaultSettings().dataSource, ...importData.dataSource },
            cache: { ...getDefaultSettings().cache, ...importData.cache },
            mockData: { ...getDefaultSettings().mockData, ...importData.mockData },
            performance: { ...getDefaultSettings().performance, ...importData.performance },
            ui: { ...getDefaultSettings().ui, ...importData.ui },
            notifications: { ...getDefaultSettings().notifications, ...importData.notifications },
            lastUpdated: Date.now(),
          })

          return true
        } catch (error) {
          console.error('导入设置失败:', error)
          return false
        }
      },

      // 验证设置
      validateSettings: () => {
        const state = get()
        const errors: string[] = []

        // 验证数据源设置
        if (state.dataSource.useRealAPI) {
          if (!state.dataSource.apiBaseUrl) {
            errors.push('API基础URL不能为空')
          }
          if (!state.dataSource.wsUrl) {
            errors.push('WebSocket URL不能为空')
          }
          if (state.dataSource.timeout < 1000) {
            errors.push('请求超时时间不能少于1秒')
          }
        }

        // 验证缓存设置
        if (state.cache.enableCache) {
          if (state.cache.stockDataTTL < 1) {
            errors.push('股票数据缓存时间不能少于1分钟')
          }
          if (state.cache.maxCacheSize < 10) {
            errors.push('最大缓存条目数不能少于10')
          }
        }

        // 验证性能设置
        if (state.performance.debounceDelay < 0) {
          errors.push('防抖延迟不能为负数')
        }
        if (state.performance.maxSearchHistory < 1) {
          errors.push('搜索历史条目数不能少于1')
        }

        return {
          isValid: errors.length === 0,
          errors,
        }
      },
    }),
    {
      name: 'app-settings-store',
      storage: createJSONStorage(() => localStorage),
      // 只持久化设置数据，不包括临时状态
      partialize: (state) => ({
        dataSource: state.dataSource,
        cache: state.cache,
        mockData: state.mockData,
        performance: state.performance,
        ui: state.ui,
        notifications: state.notifications,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
)

// 导出选择器hooks
export const useDataSourceSettings = () => useSettingsStore(state => state.dataSource)
export const useCacheSettings = () => useSettingsStore(state => state.cache)
export const useMockDataSettings = () => useSettingsStore(state => state.mockData)
export const usePerformanceSettings = () => useSettingsStore(state => state.performance)
export const useUISettings = () => useSettingsStore(state => state.ui)
export const useNotificationSettings = () => useSettingsStore(state => state.notifications)

// 导出actions
export const useSettingsActions = () => useSettingsStore(state => ({
  updateDataSourceSettings: state.updateDataSourceSettings,
  updateCacheSettings: state.updateCacheSettings,
  updateMockDataSettings: state.updateMockDataSettings,
  updatePerformanceSettings: state.updatePerformanceSettings,
  updateUISettings: state.updateUISettings,
  updateNotificationSettings: state.updateNotificationSettings,
  resetToDefaults: state.resetToDefaults,
  resetDataSourceSettings: state.resetDataSourceSettings,
  resetCacheSettings: state.resetCacheSettings,
  exportSettings: state.exportSettings,
  importSettings: state.importSettings,
  validateSettings: state.validateSettings,
}))

export default useSettingsStore
