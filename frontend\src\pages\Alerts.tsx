import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Table,
  Space,
  Typography,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Tag,
  message,
  Modal,
  Badge,
  List,
  Avatar,
  Tabs,
  Statistic,
  Divider,
} from 'antd'
import {
  BellOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SoundOutlined,
  MailOutlined,
  MessageOutlined,
} from '@ant-design/icons'
import { alertService } from '@/services/apiService'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface AlertRule {
  id: number
  rule_name: string
  rule_type: string
  stock_code: string
  stock_name: string
  conditions: any
  notification_methods: string[]
  is_active: boolean
  created_at: string
  triggered_count: number
  last_triggered?: string
}

interface AlertHistory {
  id: number
  rule_id: number
  rule_name: string
  stock_code: string
  stock_name: string
  alert_type: string
  message: string
  triggered_at: string
  is_read: boolean
  trigger_value: number
  threshold_value: number
}

const Alerts: React.FC = () => {
  const [form] = Form.useForm()
  const [rules, setRules] = useState<AlertRule[]>([])
  const [history, setHistory] = useState<AlertHistory[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null)

  useEffect(() => {
    loadAlertRules()
    loadAlertHistory()
  }, [])

  const loadAlertRules = async () => {
    setLoading(true)
    try {
      // 模拟预警规则数据
      const mockRules: AlertRule[] = [
        {
          id: 1,
          rule_name: '平安银行价格预警',
          rule_type: 'price_alert',
          stock_code: '000001',
          stock_name: '平安银行',
          conditions: {
            price_above: 13.0,
            price_below: 11.5
          },
          notification_methods: ['email', 'sms'],
          is_active: true,
          created_at: '2025-07-27 09:00:00',
          triggered_count: 3,
          last_triggered: '2025-07-27 10:30:00'
        },
        {
          id: 2,
          rule_name: '招商银行RSI预警',
          rule_type: 'technical_alert',
          stock_code: '600036',
          stock_name: '招商银行',
          conditions: {
            indicator: 'rsi',
            above: 70,
            below: 30
          },
          notification_methods: ['app', 'email'],
          is_active: true,
          created_at: '2025-07-27 08:30:00',
          triggered_count: 1,
          last_triggered: '2025-07-27 11:15:00'
        },
        {
          id: 3,
          rule_name: '万科A成交量预警',
          rule_type: 'volume_alert',
          stock_code: '000002',
          stock_name: '万科A',
          conditions: {
            volume_ratio: 2.0
          },
          notification_methods: ['app'],
          is_active: false,
          created_at: '2025-07-26 15:20:00',
          triggered_count: 0
        }
      ]
      setRules(mockRules)
    } catch (error) {
      message.error('加载预警规则失败')
    } finally {
      setLoading(false)
    }
  }

  const loadAlertHistory = async () => {
    try {
      // 模拟预警历史数据
      const mockHistory: AlertHistory[] = [
        {
          id: 1,
          rule_id: 1,
          rule_name: '平安银行价格预警',
          stock_code: '000001',
          stock_name: '平安银行',
          alert_type: 'price_above',
          message: '股价突破上限 13.00 元，当前价格 13.15 元',
          triggered_at: '2025-07-27 10:30:00',
          is_read: false,
          trigger_value: 13.15,
          threshold_value: 13.0
        },
        {
          id: 2,
          rule_id: 2,
          rule_name: '招商银行RSI预警',
          stock_code: '600036',
          stock_name: '招商银行',
          alert_type: 'rsi_overbought',
          message: 'RSI指标超买，当前值 72.5，建议关注回调风险',
          triggered_at: '2025-07-27 11:15:00',
          is_read: true,
          trigger_value: 72.5,
          threshold_value: 70
        },
        {
          id: 3,
          rule_id: 1,
          rule_name: '平安银行价格预警',
          stock_code: '000001',
          stock_name: '平安银行',
          alert_type: 'price_below',
          message: '股价跌破下限 11.50 元，当前价格 11.35 元',
          triggered_at: '2025-07-26 14:20:00',
          is_read: true,
          trigger_value: 11.35,
          threshold_value: 11.5
        }
      ]
      setHistory(mockHistory)
    } catch (error) {
      message.error('加载预警历史失败')
    }
  }

  const handleCreateRule = async (values: any) => {
    try {
      // 这里应该调用API创建预警规则
      message.success('预警规则创建成功')
      setModalVisible(false)
      form.resetFields()
      loadAlertRules()
    } catch (error) {
      message.error('创建预警规则失败')
    }
  }

  const handleToggleRule = async (rule: AlertRule) => {
    try {
      // 这里应该调用API切换规则状态
      setRules(prev =>
        prev.map(r =>
          r.id === rule.id
            ? { ...r, is_active: !r.is_active }
            : r
        )
      )
      message.success(rule.is_active ? '预警规则已停用' : '预警规则已启用')
    } catch (error) {
      message.error('操作失败')
    }
  }

  const handleDeleteRule = async (ruleId: number) => {
    try {
      // 这里应该调用API删除预警规则
      setRules(prev => prev.filter(r => r.id !== ruleId))
      message.success('预警规则已删除')
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleMarkAsRead = async (alertId: number) => {
    try {
      setHistory(prev =>
        prev.map(h =>
          h.id === alertId
            ? { ...h, is_read: true }
            : h
        )
      )
    } catch (error) {
      message.error('操作失败')
    }
  }

  const ruleColumns = [
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      key: 'rule_name',
    },
    {
      title: '股票',
      key: 'stock',
      render: (record: AlertRule) => (
        <Space>
          <Text strong>{record.stock_code}</Text>
          <Text>{record.stock_name}</Text>
        </Space>
      ),
    },
    {
      title: '预警类型',
      dataIndex: 'rule_type',
      key: 'rule_type',
      render: (value: string) => {
        const typeMap = {
          price_alert: '价格预警',
          technical_alert: '技术指标',
          volume_alert: '成交量预警',
          ai_alert: 'AI信号预警'
        }
        return <Tag>{typeMap[value as keyof typeof typeMap] || value}</Tag>
      },
    },
    {
      title: '通知方式',
      dataIndex: 'notification_methods',
      key: 'notification_methods',
      render: (methods: string[]) => (
        <Space>
          {methods.map(method => {
            const icons = {
              email: <MailOutlined />,
              sms: <MessageOutlined />,
              app: <SoundOutlined />
            }
            return (
              <Tag key={method} icon={icons[method as keyof typeof icons]}>
                {method === 'email' ? '邮件' : method === 'sms' ? '短信' : '应用'}
              </Tag>
            )
          })}
        </Space>
      ),
    },
    {
      title: '触发次数',
      dataIndex: 'triggered_count',
      key: 'triggered_count',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean, record: AlertRule) => (
        <Switch
          checked={isActive}
          onChange={() => handleToggleRule(record)}
          checkedChildren="启用"
          unCheckedChildren="停用"
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: AlertRule) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingRule(record)
              setModalVisible(true)
              form.setFieldsValue(record)
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteRule(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  const unreadCount = history.filter(h => !h.is_read).length

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <BellOutlined /> 预警系统
        </Title>
        <Paragraph type="secondary">
          智能监控股票价格、技术指标和AI信号，及时提醒投资机会和风险
        </Paragraph>
      </div>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃规则"
              value={rules.filter(r => r.is_active).length}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日触发"
              value={history.filter(h => h.triggered_at.includes('2025-07-27')).length}
              prefix={<WarningOutlined style={{ color: '#faad14' }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="未读预警"
              value={unreadCount}
              prefix={<BellOutlined style={{ color: '#ff4d4f' }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总规则数"
              value={rules.length}
              prefix={<CloseCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="rules">
        <TabPane tab="预警规则" key="rules">
          <Card
            title="我的预警规则"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingRule(null)
                  setModalVisible(true)
                  form.resetFields()
                }}
              >
                创建规则
              </Button>
            }
          >
            <Table
              dataSource={rules}
              columns={ruleColumns}
              loading={loading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <Badge count={unreadCount} size="small">
            预警历史
          </Badge>
        } key="history">
          <Card title="预警历史">
            <List
              dataSource={history}
              renderItem={(item) => (
                <List.Item
                  style={{
                    backgroundColor: item.is_read ? 'transparent' : '#f6ffed',
                    padding: '12px 16px',
                    borderRadius: 6,
                    marginBottom: 8
                  }}
                  actions={[
                    !item.is_read && (
                      <Button
                        type="link"
                        size="small"
                        onClick={() => handleMarkAsRead(item.id)}
                      >
                        标记已读
                      </Button>
                    )
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<WarningOutlined />}
                        style={{
                          backgroundColor: item.alert_type.includes('above') || item.alert_type.includes('overbought') ? '#ff4d4f' : '#52c41a'
                        }}
                      />
                    }
                    title={
                      <Space>
                        <Text strong>{item.stock_code} - {item.stock_name}</Text>
                        <Tag>{item.rule_name}</Tag>
                        {!item.is_read && <Badge status="processing" text="未读" />}
                      </Space>
                    }
                    description={
                      <div>
                        <div>{item.message}</div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.triggered_at}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 创建/编辑预警规则模态框 */}
      <Modal
        title={editingRule ? '编辑预警规则' : '创建预警规则'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateRule}
        >
          <Form.Item
            name="rule_name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" />
          </Form.Item>

          <Form.Item
            name="stock_code"
            label="股票代码"
            rules={[{ required: true, message: '请输入股票代码' }]}
          >
            <Input placeholder="如：000001" />
          </Form.Item>

          <Form.Item
            name="rule_type"
            label="预警类型"
            rules={[{ required: true, message: '请选择预警类型' }]}
          >
            <Select placeholder="选择预警类型">
              <Option value="price_alert">价格预警</Option>
              <Option value="technical_alert">技术指标预警</Option>
              <Option value="volume_alert">成交量预警</Option>
              <Option value="ai_alert">AI信号预警</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price_above"
                label="价格上限"
              >
                <InputNumber
                  placeholder="突破此价格时预警"
                  style={{ width: '100%' }}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="price_below"
                label="价格下限"
              >
                <InputNumber
                  placeholder="跌破此价格时预警"
                  style={{ width: '100%' }}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notification_methods"
            label="通知方式"
            rules={[{ required: true, message: '请选择通知方式' }]}
          >
            <Select mode="multiple" placeholder="选择通知方式">
              <Option value="app">应用通知</Option>
              <Option value="email">邮件通知</Option>
              <Option value="sms">短信通知</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingRule ? '更新规则' : '创建规则'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Alerts
