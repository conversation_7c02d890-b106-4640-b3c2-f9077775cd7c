import React from 'react'
import {
  Modal,
  Descriptions,
  Tag,
  Alert,
  Typography,
  Space,
  Progress,
  Divider,
  Row,
  Col,
  Card,
} from 'antd'
import {
  InfoCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  WarningOutlined,
} from '@ant-design/icons'
import { IndicatorInfo } from '@/utils/advancedIndicators'

const { Title, Text, Paragraph } = Typography

interface IndicatorDetailModalProps {
  visible: boolean
  onClose: () => void
  indicatorInfo: IndicatorInfo
  currentValue?: number
  signal?: {
    signal: 'buy' | 'sell' | 'neutral'
    strength: 'strong' | 'medium' | 'weak'
    description: string
  }
}

const IndicatorDetailModal: React.FC<IndicatorDetailModalProps> = ({
  visible,
  onClose,
  indicatorInfo,
  currentValue,
  signal
}) => {

  // 如果没有指标信息，不渲染模态框
  if (!indicatorInfo) {
    return null
  }
  
  const getSignalIcon = (signalType: string) => {
    switch (signalType) {
      case 'buy': return <ArrowUpOutlined style={{ color: '#52c41a' }} />
      case 'sell': return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />
      default: return <InfoCircleOutlined style={{ color: '#faad14' }} />
    }
  }

  const getSignalColor = (signalType: string) => {
    switch (signalType) {
      case 'buy': return 'success'
      case 'sell': return 'error'
      default: return 'warning'
    }
  }

  const getReliabilityColor = (reliability: string) => {
    switch (reliability) {
      case 'high': return '#52c41a'
      case 'medium': return '#faad14'
      case 'low': return '#ff4d4f'
      default: return '#d9d9d9'
    }
  }

  const getReliabilityPercent = (reliability: string) => {
    switch (reliability) {
      case 'high': return 85
      case 'medium': return 65
      case 'low': return 40
      default: return 0
    }
  }

  const calculateRangePosition = (value: number, min: number, max: number) => {
    return ((value - min) / (max - min)) * 100
  }

  return (
    <Modal
      title={
        <Space>
          <InfoCircleOutlined />
          {indicatorInfo.name} - 详细说明
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        
        {/* 基本信息 */}
        <Card title="指标概述" size="small">
          <Paragraph>{indicatorInfo.description}</Paragraph>
          
          <Row gutter={16}>
            <Col span={8}>
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="适用时间框架">
                  <Tag color="blue">{indicatorInfo.timeframe}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="可靠性">
                  <Progress
                    percent={getReliabilityPercent(indicatorInfo.reliability)}
                    strokeColor={getReliabilityColor(indicatorInfo.reliability)}
                    size="small"
                    format={() => indicatorInfo.reliability === 'high' ? '高' : indicatorInfo.reliability === 'medium' ? '中' : '低'}
                  />
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={16}>
              <Text strong>主要用途：</Text>
              <br />
              <Text>{indicatorInfo.usage}</Text>
            </Col>
          </Row>
        </Card>

        {/* 当前信号分析 */}
        {signal && currentValue !== undefined && (
          <Card title="当前信号分析" size="small">
            <Alert
              message={
                <Space>
                  {getSignalIcon(signal.signal)}
                  <Text strong>
                    {signal.signal === 'buy' ? '买入信号' : signal.signal === 'sell' ? '卖出信号' : '中性信号'}
                  </Text>
                  <Tag color={signal.strength === 'strong' ? 'red' : signal.strength === 'medium' ? 'orange' : 'blue'}>
                    {signal.strength === 'strong' ? '强' : signal.strength === 'medium' ? '中' : '弱'}
                  </Tag>
                </Space>
              }
              description={signal.description}
              type={getSignalColor(signal.signal)}
              showIcon
            />
            
            <div style={{ marginTop: 16 }}>
              <Text strong>当前数值：</Text>
              <Text style={{ fontSize: '16px', marginLeft: 8 }}>
                {typeof currentValue === 'number' ? currentValue.toFixed(3) : 'N/A'}
              </Text>
            </div>
          </Card>
        )}

        {/* 数值范围说明 */}
        <Card title="数值范围解读" size="small">
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <Text strong>安全范围</Text>
                <div style={{ 
                  background: '#f6ffed', 
                  border: '1px solid #b7eb8f', 
                  padding: '8px', 
                  borderRadius: '4px',
                  marginTop: 4
                }}>
                  <Text>{indicatorInfo.safeRange.min} ~ {indicatorInfo.safeRange.max}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    正常交易区间，风险相对较低
                  </Text>
                </div>
              </div>
              
              <div>
                <Text strong>极值范围</Text>
                <div style={{ 
                  background: '#fff2e8', 
                  border: '1px solid #ffbb96', 
                  padding: '8px', 
                  borderRadius: '4px',
                  marginTop: 4
                }}>
                  <Text>{indicatorInfo.extremeRange.min} ~ {indicatorInfo.extremeRange.max}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    极端情况区间，需要特别关注
                  </Text>
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <Text strong>中性区间</Text>
                <div style={{ 
                  background: '#fffbe6', 
                  border: '1px solid #ffe58f', 
                  padding: '8px', 
                  borderRadius: '4px',
                  marginTop: 4
                }}>
                  <Text>{indicatorInfo.neutralRange.min} ~ {indicatorInfo.neutralRange.max}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    观望区间，等待明确信号
                  </Text>
                </div>
              </div>

              {currentValue !== undefined && (
                <div>
                  <Text strong>当前位置</Text>
                  <div style={{ marginTop: 8 }}>
                    <Progress
                      percent={calculateRangePosition(
                        currentValue, 
                        indicatorInfo.extremeRange.min, 
                        indicatorInfo.extremeRange.max
                      )}
                      strokeColor={{
                        '0%': '#ff4d4f',
                        '25%': '#faad14',
                        '50%': '#52c41a',
                        '75%': '#faad14',
                        '100%': '#ff4d4f',
                      }}
                      format={() => ''}
                    />
                    <Text style={{ fontSize: '12px', color: '#666' }}>
                      在极值范围内的相对位置
                    </Text>
                  </div>
                </div>
              )}
            </Col>
          </Row>
        </Card>

        {/* 交易信号说明 */}
        <Card title="交易信号指南" size="small">
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 12 }}>
                <Space>
                  <ArrowUpOutlined style={{ color: '#52c41a' }} />
                  <Text strong style={{ color: '#52c41a' }}>买入信号</Text>
                </Space>
                <div style={{ marginTop: 4, paddingLeft: 20 }}>
                  <Text>{indicatorInfo.buySignal}</Text>
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div style={{ marginBottom: 12 }}>
                <Space>
                  <ArrowDownOutlined style={{ color: '#ff4d4f' }} />
                  <Text strong style={{ color: '#ff4d4f' }}>卖出信号</Text>
                </Space>
                <div style={{ marginTop: 4, paddingLeft: 20 }}>
                  <Text>{indicatorInfo.sellSignal}</Text>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* 使用建议 */}
        <Card title="使用建议" size="small">
          <Alert
            message="重要提醒"
            description="技术指标仅供参考，不应作为唯一的投资决策依据。建议结合多个指标、基本面分析和市场环境进行综合判断。"
            type="warning"
            showIcon
            icon={<WarningOutlined />}
          />
          
          <div style={{ marginTop: 16 }}>
            <Paragraph>
              <Text strong>解读要点：</Text>
              <br />
              {indicatorInfo.interpretation}
            </Paragraph>
            
            <Paragraph>
              <Text strong>最佳实践：</Text>
              <br />
              • 结合其他技术指标进行确认
              <br />
              • 关注指标的背离现象
              <br />
              • 考虑市场整体趋势和成交量
              <br />
              • 设置合理的止损位
            </Paragraph>
          </div>
        </Card>

      </Space>
    </Modal>
  )
}

export default IndicatorDetailModal
