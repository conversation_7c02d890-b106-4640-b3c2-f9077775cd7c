"""
数据管理服务
负责数据的获取、存储、更新和维护
"""

import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import List, Optional, Dict, Any
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.dialects.postgresql import insert

from app.core.database import AsyncSessionLocal
from app.models.stock import Stock, KlineData
from app.services.data_fetcher import DataFetcherManager
from app.core.config import settings

logger = logging.getLogger(__name__)

class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.data_fetcher = DataFetcherManager()
        
    async def initialize_stock_list(self, force_update: bool = False) -> int:
        """
        初始化股票列表
        
        Args:
            force_update: 是否强制更新
            
        Returns:
            添加的股票数量
        """
        async with AsyncSessionLocal() as db:
            try:
                # 检查是否已有股票数据
                if not force_update:
                    result = await db.execute(select(func.count(Stock.id)))
                    existing_count = result.scalar()
                    if existing_count > 0:
                        logger.info(f"股票列表已存在 {existing_count} 条记录，跳过初始化")
                        return 0
                
                logger.info("开始获取股票列表...")
                
                # 获取A股股票列表
                stock_list = await self.data_fetcher.get_stock_list()
                
                if not stock_list:
                    logger.warning("未获取到股票列表数据")
                    return 0
                
                added_count = 0
                batch_size = 100
                
                # 分批处理股票数据
                for i in range(0, len(stock_list), batch_size):
                    batch = stock_list[i:i + batch_size]
                    
                    for stock_info in batch:
                        try:
                            # 检查股票是否已存在
                            result = await db.execute(
                                select(Stock).filter(Stock.stock_code == stock_info['code'])
                            )
                            existing_stock = result.scalar_one_or_none()
                            
                            if existing_stock and not force_update:
                                continue
                            
                            # 创建或更新股票记录
                            stock_data = {
                                'stock_code': stock_info['code'],
                                'stock_name': stock_info['name'],
                                'industry': stock_info.get('industry', ''),
                                'market': stock_info.get('market', ''),
                                'list_date': stock_info.get('list_date'),
                                'is_active': True,
                                'updated_at': datetime.now()
                            }
                            
                            if existing_stock:
                                # 更新现有记录
                                for key, value in stock_data.items():
                                    setattr(existing_stock, key, value)
                            else:
                                # 创建新记录
                                stock = Stock(**stock_data)
                                db.add(stock)
                                added_count += 1
                            
                        except Exception as e:
                            logger.error(f"处理股票 {stock_info.get('code', 'Unknown')} 时出错: {e}")
                            continue
                    
                    # 提交批次
                    await db.commit()
                    logger.info(f"已处理 {min(i + batch_size, len(stock_list))} / {len(stock_list)} 只股票")
                
                logger.info(f"股票列表初始化完成，新增 {added_count} 只股票")
                return added_count
                
            except Exception as e:
                logger.error(f"初始化股票列表失败: {e}")
                await db.rollback()
                raise
    
    async def update_stock_data(self, 
                               stock_codes: Optional[List[str]] = None,
                               start_date: Optional[date] = None,
                               end_date: Optional[date] = None,
                               period: str = 'daily') -> Dict[str, int]:
        """
        更新股票K线数据
        
        Args:
            stock_codes: 股票代码列表，None表示更新所有股票
            start_date: 开始日期
            end_date: 结束日期
            period: 数据周期 (daily, weekly, monthly)
            
        Returns:
            更新统计信息
        """
        async with AsyncSessionLocal() as db:
            try:
                # 获取需要更新的股票列表
                if stock_codes:
                    result = await db.execute(
                        select(Stock).filter(Stock.stock_code.in_(stock_codes))
                    )
                else:
                    result = await db.execute(
                        select(Stock).filter(Stock.is_active == True)
                    )
                
                stocks = result.scalars().all()
                
                if not stocks:
                    logger.warning("没有找到需要更新的股票")
                    return {'updated': 0, 'added': 0, 'errors': 0}
                
                # 设置默认日期范围
                if not end_date:
                    end_date = datetime.now().date()
                if not start_date:
                    start_date = end_date - timedelta(days=365)  # 默认获取一年数据
                
                stats = {'updated': 0, 'added': 0, 'errors': 0}
                
                for stock in stocks:
                    try:
                        logger.info(f"更新股票 {stock.stock_code} - {stock.stock_name} 的数据")
                        
                        # 获取该股票的最新数据日期
                        result = await db.execute(
                            select(func.max(KlineData.trade_date))
                            .filter(and_(
                                KlineData.stock_code == stock.stock_code,
                                KlineData.period == period
                            ))
                        )
                        latest_date = result.scalar()
                        
                        # 确定数据获取的起始日期
                        fetch_start_date = start_date
                        if latest_date:
                            fetch_start_date = max(start_date, latest_date + timedelta(days=1))
                        
                        # 如果已经是最新数据，跳过
                        if fetch_start_date > end_date:
                            logger.info(f"股票 {stock.stock_code} 数据已是最新，跳过")
                            continue
                        
                        # 获取K线数据
                        kline_data = await self.data_fetcher.get_kline_data(
                            stock.stock_code,
                            period=period,
                            start_date=fetch_start_date,
                            end_date=end_date
                        )
                        
                        if not kline_data:
                            logger.warning(f"未获取到股票 {stock.stock_code} 的K线数据")
                            continue
                        
                        # 批量插入或更新数据
                        batch_stats = await self._batch_upsert_kline_data(
                            db, stock.stock_code, kline_data, period
                        )
                        
                        stats['added'] += batch_stats['added']
                        stats['updated'] += batch_stats['updated']
                        
                        # 每处理一只股票就提交一次，避免长时间锁定
                        await db.commit()
                        
                        # 添加延迟，避免请求过于频繁
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        logger.error(f"更新股票 {stock.stock_code} 数据失败: {e}")
                        stats['errors'] += 1
                        await db.rollback()
                        continue
                
                logger.info(f"数据更新完成: 新增 {stats['added']} 条，更新 {stats['updated']} 条，错误 {stats['errors']} 条")
                return stats
                
            except Exception as e:
                logger.error(f"更新股票数据失败: {e}")
                await db.rollback()
                raise
    
    async def _batch_upsert_kline_data(self, 
                                      db: AsyncSession, 
                                      stock_code: str, 
                                      kline_data: List[Dict], 
                                      period: str) -> Dict[str, int]:
        """
        批量插入或更新K线数据
        """
        stats = {'added': 0, 'updated': 0}
        
        for data in kline_data:
            try:
                # 检查数据是否已存在
                result = await db.execute(
                    select(KlineData).filter(and_(
                        KlineData.stock_code == stock_code,
                        KlineData.trade_date == data['date'],
                        KlineData.period == period
                    ))
                )
                existing_record = result.scalar_one_or_none()
                
                kline_record_data = {
                    'stock_code': stock_code,
                    'trade_date': data['date'],
                    'period': period,
                    'open_price': float(data['open']),
                    'high_price': float(data['high']),
                    'low_price': float(data['low']),
                    'close_price': float(data['close']),
                    'volume': int(data['volume']),
                    'turnover': float(data.get('amount', 0)),
                    'change': float(data.get('change', 0)),
                    'change_percent': float(data.get('pct_chg', 0)),
                    'updated_at': datetime.now()
                }
                
                if existing_record:
                    # 更新现有记录
                    for key, value in kline_record_data.items():
                        setattr(existing_record, key, value)
                    stats['updated'] += 1
                else:
                    # 创建新记录
                    kline_record = KlineData(**kline_record_data)
                    db.add(kline_record)
                    stats['added'] += 1
                    
            except Exception as e:
                logger.error(f"处理K线数据失败 {stock_code} {data.get('date')}: {e}")
                continue
        
        return stats
    
    async def get_data_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        async with AsyncSessionLocal() as db:
            try:
                # 股票统计
                result = await db.execute(select(func.count(Stock.id)))
                total_stocks = result.scalar()
                
                result = await db.execute(
                    select(func.count(Stock.id)).filter(Stock.is_active == True)
                )
                active_stocks = result.scalar()
                
                # K线数据统计
                result = await db.execute(select(func.count(KlineData.id)))
                total_klines = result.scalar()
                
                # 最新数据日期
                result = await db.execute(
                    select(func.max(KlineData.trade_date))
                )
                latest_date = result.scalar()
                
                # 按周期统计
                result = await db.execute(
                    select(KlineData.period, func.count(KlineData.id))
                    .group_by(KlineData.period)
                )
                period_stats = dict(result.all())
                
                # 数据覆盖率（有数据的股票数量）
                result = await db.execute(
                    select(func.count(func.distinct(KlineData.stock_code)))
                )
                stocks_with_data = result.scalar()
                
                return {
                    'stocks': {
                        'total': total_stocks,
                        'active': active_stocks,
                        'with_data': stocks_with_data,
                        'coverage_rate': round(stocks_with_data / max(active_stocks, 1) * 100, 2)
                    },
                    'klines': {
                        'total': total_klines,
                        'latest_date': latest_date.isoformat() if latest_date else None,
                        'by_period': period_stats
                    },
                    'updated_at': datetime.now().isoformat()
                }
                
            except Exception as e:
                logger.error(f"获取数据统计失败: {e}")
                raise
    
    async def cleanup_old_data(self, days_to_keep: int = 1095) -> int:
        """
        清理旧数据
        
        Args:
            days_to_keep: 保留的天数，默认3年
            
        Returns:
            删除的记录数
        """
        async with AsyncSessionLocal() as db:
            try:
                cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
                
                result = await db.execute(
                    select(func.count(KlineData.id))
                    .filter(KlineData.trade_date < cutoff_date)
                )
                count_to_delete = result.scalar()
                
                if count_to_delete == 0:
                    logger.info("没有需要清理的旧数据")
                    return 0
                
                # 删除旧数据
                await db.execute(
                    KlineData.__table__.delete()
                    .where(KlineData.trade_date < cutoff_date)
                )
                
                await db.commit()
                logger.info(f"已清理 {count_to_delete} 条旧数据（{cutoff_date} 之前）")
                return count_to_delete
                
            except Exception as e:
                logger.error(f"清理旧数据失败: {e}")
                await db.rollback()
                raise
