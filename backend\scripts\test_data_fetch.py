#!/usr/bin/env python3
"""
数据获取功能测试脚本
"""

import asyncio
import sys
import os
from datetime import date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.logging import setup_logging, get_logger
from app.services.data_fetcher import DataFetcherManager
from app.services.data_storage import DataStorageService
from app.services.data_sync import DataSyncService

logger = get_logger(__name__)


async def test_stock_list():
    """测试获取股票列表"""
    logger.info("=== 测试获取股票列表 ===")
    
    fetcher_manager = DataFetcherManager()
    stocks = await fetcher_manager.get_stock_list()
    
    if stocks:
        logger.info(f"成功获取 {len(stocks)} 只股票")
        # 显示前5只股票
        for i, stock in enumerate(stocks[:5]):
            logger.info(f"{i+1}. {stock['stock_code']} - {stock['stock_name']} ({stock['market']})")
        return True
    else:
        logger.error("获取股票列表失败")
        return False


async def test_kline_data():
    """测试获取K线数据"""
    logger.info("=== 测试获取K线数据 ===")
    
    # 测试股票代码
    test_stocks = ["000001", "600036", "000858"]
    
    fetcher_manager = DataFetcherManager()
    
    for stock_code in test_stocks:
        logger.info(f"获取股票 {stock_code} 的K线数据...")
        
        end_date = date.today()
        start_date = end_date - timedelta(days=7)  # 获取最近7天数据
        
        klines = await fetcher_manager.get_kline_data(
            stock_code, "daily", start_date, end_date
        )
        
        if klines:
            logger.info(f"成功获取 {len(klines)} 条K线数据")
            # 显示最新一条数据
            latest = klines[-1]
            logger.info(f"最新数据: {latest['trade_date']} 收盘价: {latest['close_price']}")
        else:
            logger.warning(f"股票 {stock_code} 未获取到K线数据")


async def test_realtime_data():
    """测试获取实时数据"""
    logger.info("=== 测试获取实时数据 ===")
    
    test_stocks = ["000001", "600036"]
    
    fetcher_manager = DataFetcherManager()
    realtime_data = await fetcher_manager.get_realtime_data(test_stocks)
    
    if realtime_data:
        logger.info(f"成功获取 {len(realtime_data)} 条实时数据")
        for data in realtime_data:
            logger.info(f"{data['stock_code']}: 当前价 {data['current_price']}, 涨跌幅 {data.get('change_percent', 0)}%")
    else:
        logger.warning("未获取到实时数据")


async def test_data_storage():
    """测试数据存储"""
    logger.info("=== 测试数据存储 ===")
    
    # 测试数据
    test_stock_data = [
        {
            "stock_code": "TEST001",
            "stock_name": "测试股票1",
            "market": "SZ",
            "industry": "测试行业"
        }
    ]
    
    async with DataStorageService() as storage:
        # 保存股票信息
        saved_count = await storage.save_stocks(test_stock_data)
        logger.info(f"保存股票信息: {saved_count} 条")
        
        # 查询股票列表
        stocks = await storage.get_stocks(limit=5)
        logger.info(f"查询到股票: {len(stocks)} 只")
        
        for stock in stocks:
            logger.info(f"- {stock.stock_code}: {stock.stock_name}")


async def test_data_sync():
    """测试数据同步"""
    logger.info("=== 测试数据同步 ===")
    
    sync_service = DataSyncService()
    
    # 测试同步股票列表
    logger.info("测试同步股票列表...")
    success = await sync_service.sync_stock_list()
    logger.info(f"股票列表同步结果: {'成功' if success else '失败'}")
    
    if success:
        # 测试同步少量K线数据
        logger.info("测试同步K线数据...")
        test_stocks = ["000001", "600036"]  # 只测试2只股票
        kline_success = await sync_service.sync_kline_data(test_stocks, "daily", 3)
        logger.info(f"K线数据同步结果: {'成功' if kline_success else '失败'}")


async def main():
    """主测试函数"""
    setup_logging()
    logger.info("🚀 开始数据获取功能测试")
    
    try:
        # 1. 测试获取股票列表
        await test_stock_list()
        
        # 2. 测试获取K线数据
        await test_kline_data()
        
        # 3. 测试获取实时数据
        await test_realtime_data()
        
        # 4. 测试数据存储
        await test_data_storage()
        
        # 5. 测试数据同步
        await test_data_sync()
        
        logger.info("✅ 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
