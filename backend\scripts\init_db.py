"""
数据库初始化脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import init_db, engine, health_check
from app.core.logging import setup_logging, get_logger
from app.core.config import settings

# 导入所有模型以确保它们被注册
from app.models import stock

logger = get_logger(__name__)


async def create_tables():
    """创建数据库表"""
    try:
        logger.info("开始创建数据库表...")
        await init_db()
        logger.info("数据库表创建成功!")
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise
    finally:
        await engine.dispose()


async def verify_database():
    """验证数据库连接"""
    try:
        logger.info("验证数据库连接...")

        # 健康检查
        health_status = await health_check()
        logger.info(f"数据库健康状态: {health_status}")

        if health_status['database'] != 'healthy':
            raise Exception("数据库连接不健康")

        logger.info("数据库验证完成")

    except Exception as e:
        logger.error(f"数据库验证失败: {e}")
        raise


async def main():
    """主函数"""
    setup_logging()

    try:
        logger.info(f"使用数据库类型: {settings.DATABASE_TYPE}")
        logger.info(f"数据库连接: {settings.SQLALCHEMY_DATABASE_URI}")

        # 确保数据目录存在
        import os
        os.makedirs("data", exist_ok=True)

        # 初始化数据库
        await create_tables()

        # 验证数据库
        await verify_database()

        logger.info("数据库初始化完成")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
