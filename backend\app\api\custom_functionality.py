"""
自定义功能管理API
提供自定义技术指标、股票形态识别、回测策略的管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text, and_, or_
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from loguru import logger
import json
import uuid
import asyncio

from app.api.professional_db_management import get_professional_db

router = APIRouter()

# 自定义功能类型
CUSTOM_FUNCTION_TYPES = {
    "technical_indicator": {
        "name": "技术指标",
        "description": "自定义技术分析指标",
        "parameters": ["period", "source_field", "calculation_method"],
        "output_type": "numeric"
    },
    "pattern_recognition": {
        "name": "形态识别",
        "description": "股票价格形态识别算法",
        "parameters": ["pattern_type", "time_window", "threshold"],
        "output_type": "boolean"
    },
    "backtesting_strategy": {
        "name": "回测策略",
        "description": "投资策略回测算法",
        "parameters": ["entry_conditions", "exit_conditions", "risk_management"],
        "output_type": "strategy_result"
    },
    "screening_filter": {
        "name": "选股过滤器",
        "description": "股票筛选条件",
        "parameters": ["filter_conditions", "ranking_method", "max_results"],
        "output_type": "stock_list"
    }
}

# 预定义的技术指标模板
INDICATOR_TEMPLATES = {
    "custom_ma": {
        "name": "自定义移动平均线",
        "description": "可配置周期的移动平均线",
        "code": """
def calculate(data, period=20, source='close'):
    import pandas as pd
    return data[source].rolling(window=period).mean()
        """,
        "parameters": {
            "period": {"type": "int", "default": 20, "min": 1, "max": 200},
            "source": {"type": "str", "default": "close", "options": ["open", "high", "low", "close", "volume"]}
        }
    },
    "custom_rsi": {
        "name": "自定义RSI",
        "description": "相对强弱指数",
        "code": """
def calculate(data, period=14, source='close'):
    import pandas as pd
    delta = data[source].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))
        """,
        "parameters": {
            "period": {"type": "int", "default": 14, "min": 2, "max": 100}
        }
    }
}

# 预定义的形态识别模板
PATTERN_TEMPLATES = {
    "double_top": {
        "name": "双顶形态",
        "description": "识别双顶反转形态",
        "code": """
def recognize(data, window=20, threshold=0.02):
    import pandas as pd
    import numpy as np
    
    highs = data['high'].rolling(window=window).max()
    peaks = []
    
    for i in range(window, len(data) - window):
        if data['high'].iloc[i] == highs.iloc[i]:
            peaks.append((i, data['high'].iloc[i]))
    
    # 识别双顶逻辑
    if len(peaks) >= 2:
        last_two_peaks = peaks[-2:]
        height_diff = abs(last_two_peaks[0][1] - last_two_peaks[1][1]) / last_two_peaks[0][1]
        return height_diff < threshold
    
    return False
        """,
        "parameters": {
            "window": {"type": "int", "default": 20, "min": 5, "max": 100},
            "threshold": {"type": "float", "default": 0.02, "min": 0.001, "max": 0.1}
        }
    }
}

class CustomFunction(BaseModel):
    """自定义功能模型"""
    function_id: Optional[str] = None
    function_name: str = Field(..., description="功能名称")
    function_type: str = Field(..., description="功能类型")
    description: Optional[str] = Field(None, description="功能描述")
    code: str = Field(..., description="功能代码")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="参数配置")
    is_active: bool = Field(True, description="是否激活")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    version: str = Field("1.0", description="版本号")
    tags: List[str] = Field(default_factory=list, description="标签")

class ExecutionResult(BaseModel):
    """执行结果模型"""
    execution_id: str
    function_id: str
    status: str  # success, error, running
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    started_at: datetime
    completed_at: Optional[datetime] = None

class BacktestResult(BaseModel):
    """回测结果模型"""
    strategy_name: str
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    profit_trades: int
    loss_trades: int
    start_date: str
    end_date: str
    benchmark_return: float

# 内存存储（生产环境应使用数据库）
custom_functions_store: Dict[str, CustomFunction] = {}
execution_results_store: Dict[str, ExecutionResult] = {}

@router.get("/functions", response_model=List[CustomFunction])
async def get_custom_functions(
    function_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    tags: Optional[str] = None
):
    """获取自定义功能列表"""
    try:
        functions = list(custom_functions_store.values())
        
        # 过滤条件
        if function_type:
            functions = [f for f in functions if f.function_type == function_type]
        
        if is_active is not None:
            functions = [f for f in functions if f.is_active == is_active]
        
        if tags:
            tag_list = tags.split(',')
            functions = [f for f in functions if any(tag in f.tags for tag in tag_list)]
        
        # 按创建时间倒序排列
        functions.sort(key=lambda x: x.created_at or datetime.min, reverse=True)
        
        return functions
        
    except Exception as e:
        logger.error(f"获取自定义功能失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取自定义功能失败: {str(e)}")

@router.post("/functions", response_model=CustomFunction)
async def create_custom_function(function: CustomFunction):
    """创建自定义功能"""
    try:
        # 生成功能ID
        function_id = str(uuid.uuid4())
        function.function_id = function_id
        function.created_at = datetime.now()
        function.updated_at = datetime.now()
        
        # 验证代码语法
        try:
            compile(function.code, '<string>', 'exec')
        except SyntaxError as e:
            raise HTTPException(status_code=400, detail=f"代码语法错误: {str(e)}")
        
        custom_functions_store[function_id] = function
        
        logger.info(f"创建自定义功能成功: {function_id}")
        return function
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建自定义功能失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建自定义功能失败: {str(e)}")

@router.put("/functions/{function_id}", response_model=CustomFunction)
async def update_custom_function(function_id: str, function: CustomFunction):
    """更新自定义功能"""
    try:
        if function_id not in custom_functions_store:
            raise HTTPException(status_code=404, detail="功能不存在")
        
        # 验证代码语法
        try:
            compile(function.code, '<string>', 'exec')
        except SyntaxError as e:
            raise HTTPException(status_code=400, detail=f"代码语法错误: {str(e)}")
        
        function.function_id = function_id
        function.updated_at = datetime.now()
        
        # 保留创建时间
        original_function = custom_functions_store[function_id]
        function.created_at = original_function.created_at
        
        custom_functions_store[function_id] = function
        
        logger.info(f"更新自定义功能成功: {function_id}")
        return function
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新自定义功能失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新自定义功能失败: {str(e)}")

@router.delete("/functions/{function_id}")
async def delete_custom_function(function_id: str):
    """删除自定义功能"""
    try:
        if function_id not in custom_functions_store:
            raise HTTPException(status_code=404, detail="功能不存在")
        
        del custom_functions_store[function_id]
        
        logger.info(f"删除自定义功能成功: {function_id}")
        return {"message": "功能删除成功", "function_id": function_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除自定义功能失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除自定义功能失败: {str(e)}")

@router.post("/functions/{function_id}/execute")
async def execute_custom_function(
    function_id: str,
    background_tasks: BackgroundTasks,
    parameters: Dict[str, Any] = {},
    stock_code: Optional[str] = None
):
    """执行自定义功能"""
    try:
        if function_id not in custom_functions_store:
            raise HTTPException(status_code=404, detail="功能不存在")
        
        function = custom_functions_store[function_id]
        
        if not function.is_active:
            raise HTTPException(status_code=400, detail="功能未激活")
        
        # 创建执行记录
        execution_id = str(uuid.uuid4())
        execution_result = ExecutionResult(
            execution_id=execution_id,
            function_id=function_id,
            status="running",
            started_at=datetime.now()
        )
        
        execution_results_store[execution_id] = execution_result
        
        # 在后台执行功能
        background_tasks.add_task(
            execute_function_task, 
            execution_id, 
            function, 
            parameters, 
            stock_code
        )
        
        return {
            "message": "功能执行已启动",
            "execution_id": execution_id,
            "function_id": function_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行自定义功能失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行自定义功能失败: {str(e)}")

@router.get("/functions/{function_id}/executions", response_model=List[ExecutionResult])
async def get_function_executions(function_id: str, limit: int = 20):
    """获取功能执行历史"""
    try:
        executions = [
            result for result in execution_results_store.values()
            if result.function_id == function_id
        ]
        
        # 按开始时间倒序排列
        executions.sort(key=lambda x: x.started_at, reverse=True)
        
        return executions[:limit]
        
    except Exception as e:
        logger.error(f"获取执行历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取执行历史失败: {str(e)}")

@router.get("/executions/{execution_id}", response_model=ExecutionResult)
async def get_execution_result(execution_id: str):
    """获取执行结果"""
    try:
        if execution_id not in execution_results_store:
            raise HTTPException(status_code=404, detail="执行记录不存在")
        
        return execution_results_store[execution_id]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取执行结果失败: {str(e)}")

@router.get("/templates")
async def get_function_templates():
    """获取功能模板"""
    return {
        "function_types": CUSTOM_FUNCTION_TYPES,
        "indicator_templates": INDICATOR_TEMPLATES,
        "pattern_templates": PATTERN_TEMPLATES
    }

@router.post("/functions/{function_id}/validate")
async def validate_function_code(function_id: str):
    """验证功能代码"""
    try:
        if function_id not in custom_functions_store:
            raise HTTPException(status_code=404, detail="功能不存在")
        
        function = custom_functions_store[function_id]
        
        # 语法检查
        try:
            compile(function.code, '<string>', 'exec')
        except SyntaxError as e:
            return {
                "is_valid": False,
                "error_type": "syntax_error",
                "error_message": str(e),
                "line_number": e.lineno
            }
        
        # 简单的运行时检查（使用模拟数据）
        try:
            import pandas as pd
            import numpy as np
            
            # 创建模拟数据
            mock_data = pd.DataFrame({
                'open': np.random.rand(100) * 100,
                'high': np.random.rand(100) * 100,
                'low': np.random.rand(100) * 100,
                'close': np.random.rand(100) * 100,
                'volume': np.random.rand(100) * 1000000
            })
            
            # 执行代码
            exec_globals = {'data': mock_data}
            exec(function.code, exec_globals)
            
            return {
                "is_valid": True,
                "message": "代码验证通过"
            }
            
        except Exception as e:
            return {
                "is_valid": False,
                "error_type": "runtime_error",
                "error_message": str(e)
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证功能代码失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证功能代码失败: {str(e)}")

@router.get("/statistics")
async def get_custom_function_statistics():
    """获取自定义功能统计信息"""
    try:
        total_functions = len(custom_functions_store)
        active_functions = len([f for f in custom_functions_store.values() if f.is_active])
        
        # 按类型统计
        type_stats = {}
        for function in custom_functions_store.values():
            type_stats[function.function_type] = type_stats.get(function.function_type, 0) + 1
        
        # 执行统计
        total_executions = len(execution_results_store)
        successful_executions = len([e for e in execution_results_store.values() if e.status == "success"])
        
        return {
            "total_functions": total_functions,
            "active_functions": active_functions,
            "function_types": type_stats,
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "success_rate": (successful_executions / max(total_executions, 1)) * 100
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

async def execute_function_task(
    execution_id: str,
    function: CustomFunction,
    parameters: Dict[str, Any],
    stock_code: Optional[str]
):
    """执行功能的后台任务"""
    try:
        execution_result = execution_results_store[execution_id]
        
        logger.info(f"开始执行自定义功能: {execution_id}")
        
        # 模拟执行过程
        await asyncio.sleep(2)  # 模拟计算时间
        
        # 模拟结果数据
        if function.function_type == "technical_indicator":
            result_data = {
                "indicator_values": [round(i * 0.1 + 50, 2) for i in range(100)],
                "signal": "buy" if parameters.get("period", 20) > 15 else "sell"
            }
        elif function.function_type == "pattern_recognition":
            result_data = {
                "pattern_detected": True,
                "confidence": 0.85,
                "pattern_type": "double_top"
            }
        elif function.function_type == "backtesting_strategy":
            result_data = {
                "total_return": 15.6,
                "annual_return": 12.3,
                "max_drawdown": -8.2,
                "sharpe_ratio": 1.45,
                "win_rate": 0.62
            }
        else:
            result_data = {"message": "功能执行完成"}
        
        execution_result.status = "success"
        execution_result.result_data = result_data
        execution_result.execution_time = 2.0
        execution_result.completed_at = datetime.now()
        
        logger.info(f"自定义功能执行完成: {execution_id}")
        
    except Exception as e:
        logger.error(f"执行自定义功能失败: {e}")
        execution_result.status = "error"
        execution_result.error_message = str(e)
        execution_result.completed_at = datetime.now()
