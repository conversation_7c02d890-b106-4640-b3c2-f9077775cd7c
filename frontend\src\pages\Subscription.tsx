import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Typography,
  Space,
  Tag,
  Progress,
  Table,
  Statistic,
  Modal,
  message,
  Descriptions,
  Alert,
} from 'antd'
import {
  CrownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ArrowUpOutlined,
  GiftOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography

interface SubscriptionPlan {
  id: string
  name: string
  price: number
  period: string
  features: string[]
  popular?: boolean
  current?: boolean
}

interface UsageRecord {
  id: string
  date: string
  service: string
  usage: number
  limit: number
  cost: number
}

const Subscription: React.FC = () => {
  const [upgradeModalVisible, setUpgradeModalVisible] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)

  const plans: SubscriptionPlan[] = [
    {
      id: 'free',
      name: '免费版',
      price: 0,
      period: '永久',
      features: [
        '基础股票查询',
        '简单技术指标',
        '每日10次API调用',
        '基础图表功能',
        '社区支持',
      ],
      current: true,
    },
    {
      id: 'pro',
      name: '专业版',
      price: 99,
      period: '月',
      features: [
        '所有免费版功能',
        '高级技术指标',
        '每日1000次API调用',
        '实时数据推送',
        '自定义预警',
        '投资组合分析',
        '邮件支持',
      ],
      popular: true,
    },
    {
      id: 'enterprise',
      name: '企业版',
      price: 999,
      period: '月',
      features: [
        '所有专业版功能',
        '无限API调用',
        '专属客户经理',
        '定制化功能',
        '数据导出',
        'API接口',
        '7x24小时支持',
      ],
    },
  ]

  const usageRecords: UsageRecord[] = [
    {
      id: '1',
      date: '2024-01-28',
      service: 'API调用',
      usage: 8,
      limit: 10,
      cost: 0,
    },
    {
      id: '2',
      date: '2024-01-27',
      service: 'API调用',
      usage: 10,
      limit: 10,
      cost: 0,
    },
    {
      id: '3',
      date: '2024-01-26',
      service: 'API调用',
      usage: 6,
      limit: 10,
      cost: 0,
    },
    {
      id: '4',
      date: '2024-01-25',
      service: 'API调用',
      usage: 9,
      limit: 10,
      cost: 0,
    },
  ]

  const handleUpgrade = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan)
    setUpgradeModalVisible(true)
  }

  const confirmUpgrade = async () => {
    try {
      // 模拟升级处理
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success(`成功升级到${selectedPlan?.name}！`)
      setUpgradeModalVisible(false)
    } catch (error) {
      message.error('升级失败，请重试')
    }
  }

  const usageColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '服务类型',
      dataIndex: 'service',
      key: 'service',
    },
    {
      title: '使用量',
      key: 'usage',
      render: (record: UsageRecord) => (
        <Space>
          <Text>{record.usage}</Text>
          <Text type="secondary">/ {record.limit}</Text>
          <Progress
            percent={(record.usage / record.limit) * 100}
            size="small"
            style={{ width: 60 }}
            showInfo={false}
          />
        </Space>
      ),
    },
    {
      title: '费用',
      dataIndex: 'cost',
      key: 'cost',
      render: (cost: number) => (
        <Text>{cost === 0 ? '免费' : `¥${cost.toFixed(2)}`}</Text>
      ),
    },
  ]

  const currentPlan = plans.find(p => p.current)
  const totalUsage = usageRecords.reduce((sum, record) => sum + record.usage, 0)
  const totalLimit = usageRecords.length > 0 ? usageRecords[0].limit * usageRecords.length : 0

  return (
    <div>
      <Title level={2}>订阅管理</Title>

      {/* 当前订阅状态 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} md={12}>
            <Space size="large">
              <div>
                <Title level={4} style={{ margin: 0 }}>
                  <CrownOutlined style={{ color: '#faad14' }} /> 当前订阅
                </Title>
                <Text type="secondary">您当前的订阅计划</Text>
              </div>
              <div>
                <Tag color="blue" style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {currentPlan?.name}
                </Tag>
                <div style={{ marginTop: 4 }}>
                  <Text strong>
                    ¥{currentPlan?.price}/{currentPlan?.period}
                  </Text>
                </div>
              </div>
            </Space>
          </Col>
          <Col xs={24} md={12}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="本月使用量"
                  value={totalUsage}
                  suffix={`/ ${totalLimit}`}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="使用率"
                  value={(totalUsage / totalLimit) * 100}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (totalUsage / totalLimit) > 0.8 ? '#cf1322' : '#3f8600' 
                  }}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 使用量警告 */}
      {(totalUsage / totalLimit) > 0.8 && (
        <Alert
          message="使用量警告"
          description="您本月的API调用量已超过80%，建议升级到更高级的套餐以获得更多配额。"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
          action={
            <Button size="small" type="primary" onClick={() => handleUpgrade(plans[1])}>
              立即升级
            </Button>
          }
        />
      )}

      {/* 订阅计划 */}
      <Card title="订阅计划" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {plans.map((plan) => (
            <Col xs={24} md={8} key={plan.id}>
              <Card
                style={{
                  position: 'relative',
                  border: plan.popular ? '2px solid #1890ff' : undefined,
                }}
                actions={[
                  plan.current ? (
                    <Button disabled icon={<CheckCircleOutlined />}>
                      当前计划
                    </Button>
                  ) : (
                    <Button
                      type={plan.popular ? 'primary' : 'default'}
                      icon={<ArrowUpOutlined />}
                      onClick={() => handleUpgrade(plan)}
                    >
                      {plan.price === 0 ? '降级' : '升级'}
                    </Button>
                  ),
                ]}
              >
                {plan.popular && (
                  <div
                    style={{
                      position: 'absolute',
                      top: -1,
                      right: 20,
                      background: '#1890ff',
                      color: 'white',
                      padding: '4px 12px',
                      borderRadius: '0 0 8px 8px',
                      fontSize: '12px',
                    }}
                  >
                    推荐
                  </div>
                )}
                
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                  <Title level={4}>{plan.name}</Title>
                  <div>
                    <Text style={{ fontSize: '32px', fontWeight: 'bold' }}>
                      ¥{plan.price}
                    </Text>
                    <Text type="secondary">/{plan.period}</Text>
                  </div>
                </div>

                <div>
                  {plan.features.map((feature, index) => (
                    <div key={index} style={{ marginBottom: 8 }}>
                      <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      <Text>{feature}</Text>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 使用记录 */}
      <Card title="使用记录">
        <Table
          dataSource={usageRecords}
          columns={usageColumns}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 升级确认模态框 */}
      <Modal
        title="确认升级"
        open={upgradeModalVisible}
        onOk={confirmUpgrade}
        onCancel={() => setUpgradeModalVisible(false)}
        okText="确认升级"
        cancelText="取消"
      >
        {selectedPlan && (
          <div>
            <Alert
              message="升级提醒"
              description="升级后将立即生效，费用将按比例计算。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Descriptions title="升级详情" bordered size="small">
              <Descriptions.Item label="目标计划">{selectedPlan.name}</Descriptions.Item>
              <Descriptions.Item label="价格">¥{selectedPlan.price}/{selectedPlan.period}</Descriptions.Item>
              <Descriptions.Item label="生效时间">立即生效</Descriptions.Item>
              <Descriptions.Item label="下次扣费">
                {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 16 }}>
              <Title level={5}>新增功能：</Title>
              <ul>
                {selectedPlan.features.slice(1).map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Subscription
