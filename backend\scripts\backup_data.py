#!/usr/bin/env python3
"""
数据备份脚本
支持数据库备份和数据导出
"""

import asyncio
import sys
import os
import subprocess
import json
import csv
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import AsyncSessionLocal
from app.models.stock import Stock, KlineData
from app.core.config import settings
from sqlalchemy import select

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def export_stocks_to_csv(output_dir: str):
    """导出股票列表到CSV"""
    logger.info("📊 导出股票列表到CSV")
    
    try:
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Stock))
            stocks = result.scalars().all()
            
            csv_file = os.path.join(output_dir, f"stocks_{datetime.now().strftime('%Y%m%d')}.csv")
            
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['股票代码', '股票名称', '市场', '行业', '上市日期', '是否活跃'])
                
                for stock in stocks:
                    writer.writerow([
                        stock.stock_code,
                        stock.stock_name,
                        stock.market,
                        stock.industry,
                        stock.list_date,
                        stock.is_active
                    ])
            
            logger.info(f"✅ 股票列表已导出: {csv_file} ({len(stocks)} 条记录)")
            return csv_file
            
    except Exception as e:
        logger.error(f"❌ 导出股票列表失败: {e}")
        raise

async def export_klines_to_csv(output_dir: str, stock_codes=None, days_back=30):
    """导出K线数据到CSV"""
    logger.info("📈 导出K线数据到CSV")
    
    try:
        async with AsyncSessionLocal() as db:
            # 构建查询
            query = select(KlineData)
            
            if stock_codes:
                query = query.filter(KlineData.stock_code.in_(stock_codes))
            
            if days_back:
                cutoff_date = datetime.now().date() - timedelta(days=days_back)
                query = query.filter(KlineData.trade_date >= cutoff_date)
            
            query = query.order_by(KlineData.stock_code, KlineData.trade_date)
            
            result = await db.execute(query)
            klines = result.scalars().all()
            
            csv_file = os.path.join(output_dir, f"klines_{datetime.now().strftime('%Y%m%d')}.csv")
            
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    '股票代码', '交易日期', '周期', '开盘价', '最高价', '最低价', 
                    '收盘价', '成交量', '成交额', '涨跌额', '涨跌幅'
                ])
                
                for kline in klines:
                    writer.writerow([
                        kline.stock_code,
                        kline.trade_date,
                        kline.period,
                        kline.open_price,
                        kline.high_price,
                        kline.low_price,
                        kline.close_price,
                        kline.volume,
                        kline.turnover,
                        kline.change,
                        kline.change_percent
                    ])
            
            logger.info(f"✅ K线数据已导出: {csv_file} ({len(klines)} 条记录)")
            return csv_file
            
    except Exception as e:
        logger.error(f"❌ 导出K线数据失败: {e}")
        raise

async def export_data_to_json(output_dir: str, stock_codes=None, days_back=30):
    """导出数据到JSON格式"""
    logger.info("📄 导出数据到JSON")
    
    try:
        async with AsyncSessionLocal() as db:
            # 导出股票信息
            result = await db.execute(select(Stock))
            stocks = result.scalars().all()
            
            stocks_data = []
            for stock in stocks:
                stocks_data.append({
                    'stock_code': stock.stock_code,
                    'stock_name': stock.stock_name,
                    'market': stock.market,
                    'industry': stock.industry,
                    'list_date': stock.list_date.isoformat() if stock.list_date else None,
                    'is_active': stock.is_active
                })
            
            # 导出K线数据
            query = select(KlineData)
            if stock_codes:
                query = query.filter(KlineData.stock_code.in_(stock_codes))
            if days_back:
                cutoff_date = datetime.now().date() - timedelta(days=days_back)
                query = query.filter(KlineData.trade_date >= cutoff_date)
            
            result = await db.execute(query)
            klines = result.scalars().all()
            
            klines_data = []
            for kline in klines:
                klines_data.append({
                    'stock_code': kline.stock_code,
                    'trade_date': kline.trade_date.isoformat(),
                    'period': kline.period,
                    'open_price': float(kline.open_price),
                    'high_price': float(kline.high_price),
                    'low_price': float(kline.low_price),
                    'close_price': float(kline.close_price),
                    'volume': kline.volume,
                    'turnover': float(kline.turnover),
                    'change': float(kline.change),
                    'change_percent': float(kline.change_percent)
                })
            
            # 组合数据
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'stock_codes': stock_codes,
                    'days_back': days_back,
                    'total_stocks': len(stocks_data),
                    'total_klines': len(klines_data)
                },
                'stocks': stocks_data,
                'klines': klines_data
            }
            
            json_file = os.path.join(output_dir, f"stock_data_{datetime.now().strftime('%Y%m%d')}.json")
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ JSON数据已导出: {json_file}")
            logger.info(f"   股票数量: {len(stocks_data)}")
            logger.info(f"   K线记录: {len(klines_data)}")
            
            return json_file
            
    except Exception as e:
        logger.error(f"❌ 导出JSON数据失败: {e}")
        raise

def backup_database(output_dir: str):
    """备份PostgreSQL数据库"""
    logger.info("💾 备份PostgreSQL数据库")
    
    try:
        # 从配置中获取数据库连接信息
        db_url = str(settings.SQLALCHEMY_DATABASE_URI)
        
        # 解析数据库URL (postgresql+asyncpg://user:pass@host:port/dbname)
        import re
        match = re.match(r'postgresql\+asyncpg://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        
        if not match:
            logger.error("无法解析数据库URL")
            return None
        
        user, password, host, port, dbname = match.groups()
        
        # 生成备份文件名
        backup_file = os.path.join(output_dir, f"stock_analysis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql")
        
        # 构建pg_dump命令
        cmd = [
            'pg_dump',
            '-h', host,
            '-p', port,
            '-U', user,
            '-d', dbname,
            '-f', backup_file,
            '--verbose',
            '--no-password'
        ]
        
        # 设置环境变量
        env = os.environ.copy()
        env['PGPASSWORD'] = password
        
        # 执行备份
        logger.info(f"执行数据库备份命令...")
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ 数据库备份成功: {backup_file}")
            
            # 检查文件大小
            file_size = os.path.getsize(backup_file)
            logger.info(f"   备份文件大小: {file_size / 1024 / 1024:.2f} MB")
            
            return backup_file
        else:
            logger.error(f"❌ 数据库备份失败:")
            logger.error(f"   错误输出: {result.stderr}")
            return None
            
    except Exception as e:
        logger.error(f"❌ 数据库备份异常: {e}")
        return None

async def create_backup_manifest(output_dir: str, files: list):
    """创建备份清单"""
    logger.info("📋 创建备份清单")
    
    try:
        manifest = {
            'backup_info': {
                'timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'description': '股票分析系统数据备份'
            },
            'files': []
        }
        
        for file_path in files:
            if file_path and os.path.exists(file_path):
                file_info = {
                    'filename': os.path.basename(file_path),
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'type': os.path.splitext(file_path)[1][1:],  # 文件扩展名
                    'created': datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()
                }
                manifest['files'].append(file_info)
        
        manifest_file = os.path.join(output_dir, f"backup_manifest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        with open(manifest_file, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 备份清单已创建: {manifest_file}")
        return manifest_file
        
    except Exception as e:
        logger.error(f"❌ 创建备份清单失败: {e}")
        raise

async def main():
    """主函数"""
    logger.info("🚀 开始数据备份任务")
    
    try:
        # 创建备份目录
        backup_dir = f"backups/backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        logger.info(f"📁 备份目录: {backup_dir}")
        
        backup_files = []
        
        # 1. 导出股票列表
        stocks_csv = await export_stocks_to_csv(backup_dir)
        backup_files.append(stocks_csv)
        
        # 2. 导出K线数据 (最近90天)
        klines_csv = await export_klines_to_csv(backup_dir, days_back=90)
        backup_files.append(klines_csv)
        
        # 3. 导出JSON格式数据
        json_file = await export_data_to_json(backup_dir, days_back=90)
        backup_files.append(json_file)
        
        # 4. 备份数据库
        db_backup = backup_database(backup_dir)
        if db_backup:
            backup_files.append(db_backup)
        
        # 5. 创建备份清单
        manifest_file = await create_backup_manifest(backup_dir, backup_files)
        backup_files.append(manifest_file)
        
        # 计算总大小
        total_size = sum(os.path.getsize(f) for f in backup_files if f and os.path.exists(f))
        
        logger.info(f"✅ 数据备份任务完成")
        logger.info(f"📁 备份目录: {backup_dir}")
        logger.info(f"📄 备份文件数: {len(backup_files)}")
        logger.info(f"💾 总大小: {total_size / 1024 / 1024:.2f} MB")
        
        return backup_dir
        
    except Exception as e:
        logger.error(f"❌ 数据备份任务失败: {e}")
        raise

if __name__ == "__main__":
    # 创建备份目录
    os.makedirs('backups', exist_ok=True)
    
    # 运行备份任务
    asyncio.run(main())
