"""
数据库连接配置
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData
import redis.asyncio as redis

from app.core.config import settings


# 数据库引擎
engine = create_async_engine(
    str(settings.SQLALCHEMY_DATABASE_URI),
    echo=settings.LOG_LEVEL == "DEBUG",
    pool_pre_ping=settings.DB_POOL_PRE_PING,
    pool_recycle=settings.DB_POOL_RECYCLE,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    # SQLite特殊配置
    connect_args={"check_same_thread": False} if settings.DATABASE_TYPE == "sqlite" else {}
)

# 会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

# Redis连接池
redis_pool = redis.ConnectionPool.from_url(
    settings.REDIS_URL,
    max_connections=20,
    retry_on_timeout=True
)

# Redis客户端
redis_client = redis.Redis(connection_pool=redis_pool)


class Base(DeclarativeBase):
    """数据库模型基类"""
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s"
        }
    )


async def get_db():
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_redis():
    """获取Redis客户端"""
    return redis_client


async def init_db():
    """初始化数据库"""
    # 导入所有模型以确保它们被注册到Base.metadata
    from app.models import stock  # noqa

    async with engine.begin() as conn:
        # 创建所有表
        await conn.run_sync(Base.metadata.create_all)


async def close_db():
    """关闭数据库连接"""
    await engine.dispose()
    await redis_client.close()


async def test_db_connection():
    """测试数据库连接"""
    try:
        from sqlalchemy import text
        async with AsyncSessionLocal() as session:
            # 执行简单查询测试连接
            result = await session.execute(text("SELECT 1"))
            return result.scalar() == 1
    except Exception as e:
        print(f"数据库连接测试失败: {e}")
        return False


async def test_redis_connection():
    """测试Redis连接"""
    try:
        await redis_client.ping()
        return True
    except Exception as e:
        print(f"Redis连接测试失败: {e}")
        return False


async def health_check():
    """数据库和Redis健康检查"""
    db_status = await test_db_connection()
    redis_status = await test_redis_connection()

    return {
        "database": "healthy" if db_status else "unhealthy",
        "redis": "healthy" if redis_status else "unhealthy",
        "overall": "healthy" if db_status and redis_status else "unhealthy"
    }
