import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tabs,
  Space,
  message,
  Statistic,
  Tag,
  Modal,
  Descriptions,
  Progress,
  Typography,
  Input,
  Select,
  DatePicker,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  DatabaseOutlined,
  LineChartOutlined,
  Pie<PERSON>hartOutlined,
  Bar<PERSON><PERSON>Outlined,
  InfoCircleOutlined,
  SearchOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface StockDetail {
  stockCode: string;
  stockName: string;
  fullName: string;
  market: string;
  exchange: string;
  listDate: string;
  totalShares: number;
  floatShares: number;
  totalMarketCap: number;
  floatMarketCap: number;
  industry: string;
  sector: string;
  concept: string;
  peRatio: number;
  pbRatio: number;
  roe: number;
  roa: number;
  legalRepresentative: string;
  generalManager: string;
  secretary: string;
  phone: string;
  email: string;
  website: string;
  mainBusiness: string;
  businessScope: string;
  companyProfile: string;
  employeeCount: number;
  registeredCapital: number;
  actualController: string;
  dataDate: string;
  updatedAt: string;
}

interface SectorData {
  sectorCode: string;
  sectorName: string;
  sectorType: string;
  stockCount: number;
  totalMarketCap: number;
  avgPeRatio: number;
  avgPbRatio: number;
  upCount: number;
  downCount: number;
  flatCount: number;
  totalVolume: number;
  totalTurnover: number;
  avgTurnoverRate: number;
  sectorChange: number;
  sectorChangeAmount: number;
  tradeDate: string;
}

interface SpotData {
  stockCode: string;
  stockName: string;
  currentPrice: number;
  openPrice: number;
  highPrice: number;
  lowPrice: number;
  preClose: number;
  changeAmount: number;
  changePercent: number;
  amplitude: number;
  volume: number;
  turnover: number;
  turnoverRate: number;
  volumeRatio: number;
  peRatio: number;
  pbRatio: number;
  totalMarketCap: number;
  floatMarketCap: number;
  speed: number;
  change5min: number;
  change60day: number;
  changeYtd: number;
  tradeDate: string;
  updateTime: string;
}

interface MarketSummary {
  exchange: string;
  marketType: string;
  listedCount: number;
  stockCount: number;
  totalShares: number;
  floatShares: number;
  totalMarketCap: number;
  floatMarketCap: number;
  avgPeRatio: number;
  turnoverAmount: number;
  turnoverVolume: number;
  reportDate: string;
}

const AKShareData: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stockDetails, setStockDetails] = useState<StockDetail[]>([]);
  const [sectorData, setSectorData] = useState<SectorData[]>([]);
  const [spotData, setSpotData] = useState<SpotData[]>([]);
  const [marketSummary, setMarketSummary] = useState<MarketSummary[]>([]);
  const [selectedDetail, setSelectedDetail] = useState<StockDetail | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [updateProgress, setUpdateProgress] = useState<{ [key: string]: number }>({});

  // 获取股票详细信息
  const fetchStockDetails = async (stockCode?: string) => {
    setLoading(true);
    try {
      const url = stockCode
        ? `/api/v1/akshare/stock-detail/${stockCode}`
        : '/api/v1/akshare/stock-detail';
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        if (stockCode) {
          setStockDetails([data]);
        } else {
          setStockDetails(data);
        }
      } else {
        message.error('获取股票详细信息失败');
      }
    } catch (error) {
      message.error('获取股票详细信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取板块数据
  const fetchSectorData = async (sectorType?: string, tradeDate?: string) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (sectorType) params.append('sector_type', sectorType);
      if (tradeDate) params.append('trade_date', tradeDate);
      
      const response = await fetch(`/api/v1/akshare/sectors?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setSectorData(data);
      } else {
        message.error('获取板块数据失败');
      }
    } catch (error) {
      message.error('获取板块数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取实时行情数据
  const fetchSpotData = async (stockCodes?: string, tradeDate?: string) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (stockCodes) params.append('stock_codes', stockCodes);
      if (tradeDate) params.append('trade_date', tradeDate);
      
      const response = await fetch(`/api/v1/akshare/spot-data?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setSpotData(data);
      } else {
        message.error('获取实时行情数据失败');
      }
    } catch (error) {
      message.error('获取实时行情数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取市场总貌数据
  const fetchMarketSummary = async (exchange?: string, reportDate?: string) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (exchange) params.append('exchange', exchange);
      if (reportDate) params.append('report_date', reportDate);
      
      const response = await fetch(`/api/v1/akshare/market-summary?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setMarketSummary(data);
      } else {
        message.error('获取市场总貌数据失败');
      }
    } catch (error) {
      message.error('获取市场总貌数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新股票详细信息
  const updateStockDetails = async (stockCodes?: string[]) => {
    try {
      const response = await fetch('/api/v1/data-management/akshare/update-stock-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ stock_codes: stockCodes }),
      });
      
      if (response.ok) {
        const result = await response.json();
        message.success(result.message);
        setUpdateProgress(prev => ({ ...prev, stockDetails: 0 }));
        
        // 模拟进度更新
        const interval = setInterval(() => {
          setUpdateProgress(prev => {
            const current = prev.stockDetails || 0;
            if (current >= 100) {
              clearInterval(interval);
              fetchStockDetails();
              return prev;
            }
            return { ...prev, stockDetails: current + 10 };
          });
        }, 1000);
      } else {
        message.error('启动更新任务失败');
      }
    } catch (error) {
      message.error('启动更新任务失败');
    }
  };

  // 更新实时行情数据
  const updateSpotData = async () => {
    try {
      const response = await fetch('/api/v1/data-management/akshare/update-spot-data', {
        method: 'POST',
      });
      
      if (response.ok) {
        const result = await response.json();
        message.success(result.message);
        setUpdateProgress(prev => ({ ...prev, spotData: 0 }));
        
        // 模拟进度更新
        const interval = setInterval(() => {
          setUpdateProgress(prev => {
            const current = prev.spotData || 0;
            if (current >= 100) {
              clearInterval(interval);
              fetchSpotData();
              return prev;
            }
            return { ...prev, spotData: current + 20 };
          });
        }, 500);
      } else {
        message.error('启动更新任务失败');
      }
    } catch (error) {
      message.error('启动更新任务失败');
    }
  };

  // 更新板块数据
  const updateSectorData = async () => {
    try {
      const response = await fetch('/api/v1/data-management/akshare/update-sector-data', {
        method: 'POST',
      });

      if (response.ok) {
        const result = await response.json();
        message.success(result.message);
        setUpdateProgress(prev => ({ ...prev, sectorData: 0 }));

        // 模拟进度更新
        const interval = setInterval(() => {
          setUpdateProgress(prev => {
            const current = prev.sectorData || 0;
            if (current >= 100) {
              clearInterval(interval);
              fetchSectorData();
              return prev;
            }
            return { ...prev, sectorData: current + 15 };
          });
        }, 800);
      } else {
        message.error('启动更新任务失败');
      }
    } catch (error) {
      message.error('启动更新任务失败');
    }
  };

  // 更新市场总貌数据
  const updateMarketSummary = async () => {
    try {
      const response = await fetch('/api/v1/data-management/akshare/update-market-summary', {
        method: 'POST',
      });

      if (response.ok) {
        const result = await response.json();
        message.success(result.message);
        setUpdateProgress(prev => ({ ...prev, marketSummary: 0 }));

        // 模拟进度更新
        const interval = setInterval(() => {
          setUpdateProgress(prev => {
            const current = prev.marketSummary || 0;
            if (current >= 100) {
              clearInterval(interval);
              fetchMarketSummary();
              return prev;
            }
            return { ...prev, marketSummary: current + 25 };
          });
        }, 400);
      } else {
        message.error('启动更新任务失败');
      }
    } catch (error) {
      message.error('启动更新任务失败');
    }
  };

  // 股票详细信息表格列定义
  const stockDetailColumns: ColumnsType<StockDetail> = [
    {
      title: '股票代码',
      dataIndex: 'stockCode',
      key: 'stockCode',
      width: 100,
      fixed: 'left',
    },
    {
      title: '股票名称',
      dataIndex: 'stockName',
      key: 'stockName',
      width: 120,
      fixed: 'left',
    },
    {
      title: '市场',
      dataIndex: 'market',
      key: 'market',
      width: 80,
    },
    {
      title: '交易所',
      dataIndex: 'exchange',
      key: 'exchange',
      width: 120,
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
      width: 100,
    },
    {
      title: '板块',
      dataIndex: 'sector',
      key: 'sector',
      width: 100,
    },
    {
      title: '总市值(亿)',
      dataIndex: 'totalMarketCap',
      key: 'totalMarketCap',
      width: 120,
      render: (value: number) => value ? (value / 100000000).toFixed(2) : '-',
    },
    {
      title: '流通市值(亿)',
      dataIndex: 'floatMarketCap',
      key: 'floatMarketCap',
      width: 120,
      render: (value: number) => value ? (value / 100000000).toFixed(2) : '-',
    },
    {
      title: '市盈率',
      dataIndex: 'peRatio',
      key: 'peRatio',
      width: 80,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '市净率',
      dataIndex: 'pbRatio',
      key: 'pbRatio',
      width: 80,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '上市日期',
      dataIndex: 'listDate',
      key: 'listDate',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Button
          type="link"
          icon={<InfoCircleOutlined />}
          onClick={() => {
            setSelectedDetail(record);
            setDetailModalVisible(true);
          }}
        >
          详情
        </Button>
      ),
    },
  ];

  // 板块数据表格列定义
  const sectorColumns: ColumnsType<SectorData> = [
    {
      title: '板块代码',
      dataIndex: 'sectorCode',
      key: 'sectorCode',
      width: 100,
    },
    {
      title: '板块名称',
      dataIndex: 'sectorName',
      key: 'sectorName',
      width: 150,
    },
    {
      title: '板块类型',
      dataIndex: 'sectorType',
      key: 'sectorType',
      width: 100,
      render: (value: string) => {
        const typeMap = {
          industry: '行业',
          concept: '概念',
          region: '地区'
        };
        return <Tag color="blue">{typeMap[value as keyof typeof typeMap] || value}</Tag>;
      },
    },
    {
      title: '股票数量',
      dataIndex: 'stockCount',
      key: 'stockCount',
      width: 100,
    },
    {
      title: '总市值(亿)',
      dataIndex: 'totalMarketCap',
      key: 'totalMarketCap',
      width: 120,
      render: (value: number) => value ? (value / 100000000).toFixed(2) : '-',
    },
    {
      title: '平均市盈率',
      dataIndex: 'avgPeRatio',
      key: 'avgPeRatio',
      width: 100,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '涨跌幅(%)',
      dataIndex: 'sectorChange',
      key: 'sectorChange',
      width: 100,
      render: (value: number) => {
        if (!value) return '-';
        const color = value > 0 ? 'red' : value < 0 ? 'green' : 'gray';
        return <Text style={{ color }}>{value > 0 ? '+' : ''}{value.toFixed(2)}%</Text>;
      },
    },
    {
      title: '上涨家数',
      dataIndex: 'upCount',
      key: 'upCount',
      width: 80,
      render: (value: number) => <Text style={{ color: 'red' }}>{value}</Text>,
    },
    {
      title: '下跌家数',
      dataIndex: 'downCount',
      key: 'downCount',
      width: 80,
      render: (value: number) => <Text style={{ color: 'green' }}>{value}</Text>,
    },
    {
      title: '平盘家数',
      dataIndex: 'flatCount',
      key: 'flatCount',
      width: 80,
    },
    {
      title: '交易日期',
      dataIndex: 'tradeDate',
      key: 'tradeDate',
      width: 100,
    },
  ];

  // 实时行情表格列定义
  const spotColumns: ColumnsType<SpotData> = [
    {
      title: '股票代码',
      dataIndex: 'stockCode',
      key: 'stockCode',
      width: 100,
      fixed: 'left',
    },
    {
      title: '股票名称',
      dataIndex: 'stockName',
      key: 'stockName',
      width: 120,
      fixed: 'left',
    },
    {
      title: '最新价',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      width: 80,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '涨跌额',
      dataIndex: 'changeAmount',
      key: 'changeAmount',
      width: 80,
      render: (value: number) => {
        if (!value) return '-';
        const color = value > 0 ? 'red' : value < 0 ? 'green' : 'gray';
        return <Text style={{ color }}>{value > 0 ? '+' : ''}{value.toFixed(2)}</Text>;
      },
    },
    {
      title: '涨跌幅(%)',
      dataIndex: 'changePercent',
      key: 'changePercent',
      width: 100,
      render: (value: number) => {
        if (!value) return '-';
        const color = value > 0 ? 'red' : value < 0 ? 'green' : 'gray';
        return <Text style={{ color }}>{value > 0 ? '+' : ''}{value.toFixed(2)}%</Text>;
      },
    },
    {
      title: '开盘价',
      dataIndex: 'openPrice',
      key: 'openPrice',
      width: 80,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '最高价',
      dataIndex: 'highPrice',
      key: 'highPrice',
      width: 80,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '最低价',
      dataIndex: 'lowPrice',
      key: 'lowPrice',
      width: 80,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      width: 100,
      render: (value: number) => value ? (value / 10000).toFixed(2) + '万' : '-',
    },
    {
      title: '成交额',
      dataIndex: 'turnover',
      key: 'turnover',
      width: 100,
      render: (value: number) => value ? (value / 100000000).toFixed(2) + '亿' : '-',
    },
    {
      title: '换手率(%)',
      dataIndex: 'turnoverRate',
      key: 'turnoverRate',
      width: 100,
      render: (value: number) => value ? value.toFixed(2) + '%' : '-',
    },
    {
      title: '市盈率',
      dataIndex: 'peRatio',
      key: 'peRatio',
      width: 80,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  // 市场总貌表格列定义
  const marketSummaryColumns: ColumnsType<MarketSummary> = [
    {
      title: '交易所',
      dataIndex: 'exchange',
      key: 'exchange',
      width: 100,
      render: (value: string) => {
        const exchangeMap = {
          SSE: '上交所',
          SZSE: '深交所'
        };
        return exchangeMap[value as keyof typeof exchangeMap] || value;
      },
    },
    {
      title: '市场类型',
      dataIndex: 'marketType',
      key: 'marketType',
      width: 100,
    },
    {
      title: '上市公司数',
      dataIndex: 'listedCount',
      key: 'listedCount',
      width: 100,
    },
    {
      title: '上市股票数',
      dataIndex: 'stockCount',
      key: 'stockCount',
      width: 100,
    },
    {
      title: '总股本(亿股)',
      dataIndex: 'totalShares',
      key: 'totalShares',
      width: 120,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '流通股本(亿股)',
      dataIndex: 'floatShares',
      key: 'floatShares',
      width: 120,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '总市值(亿元)',
      dataIndex: 'totalMarketCap',
      key: 'totalMarketCap',
      width: 120,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '流通市值(亿元)',
      dataIndex: 'floatMarketCap',
      key: 'floatMarketCap',
      width: 120,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '平均市盈率',
      dataIndex: 'avgPeRatio',
      key: 'avgPeRatio',
      width: 100,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '成交金额(亿元)',
      dataIndex: 'turnoverAmount',
      key: 'turnoverAmount',
      width: 120,
      render: (value: number) => value ? value.toFixed(2) : '-',
    },
    {
      title: '报告日期',
      dataIndex: 'reportDate',
      key: 'reportDate',
      width: 100,
    },
  ];

  useEffect(() => {
    // 初始化加载数据
    fetchMarketSummary();
    fetchSectorData();
    fetchSpotData();
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DatabaseOutlined /> AKShare数据管理
      </Title>

      <Tabs defaultActiveKey="market-summary">
        <TabPane tab={<span><BarChartOutlined />市场总貌</span>} key="market-summary">
          <Card>
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Select
                  placeholder="选择交易所"
                  style={{ width: '100%' }}
                  allowClear
                  onChange={(value) => fetchMarketSummary(value)}
                >
                  <Option key="SSE" value="SSE">上交所</Option>
                  <Option key="SZSE" value="SZSE">深交所</Option>
                </Select>
              </Col>
              <Col span={6}>
                <DatePicker
                  placeholder="选择报告日期"
                  style={{ width: '100%' }}
                  onChange={(date) => fetchMarketSummary(undefined, date?.format('YYYY-MM-DD'))}
                />
              </Col>
              <Col span={12}>
                <Space>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={() => fetchMarketSummary()}
                    loading={loading}
                  >
                    刷新数据
                  </Button>
                  <Button
                    icon={<DatabaseOutlined />}
                    onClick={updateMarketSummary}
                    loading={updateProgress.marketSummary !== undefined && updateProgress.marketSummary < 100}
                  >
                    更新数据
                  </Button>
                </Space>
              </Col>
            </Row>

            {updateProgress.marketSummary !== undefined && updateProgress.marketSummary < 100 && (
              <Progress
                percent={updateProgress.marketSummary}
                status="active"
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={marketSummaryColumns}
              dataSource={marketSummary}
              rowKey={(record, index) => record.exchange || `market-${index}`}
              loading={loading}
              scroll={{ x: 1200 }}
              pagination={false}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><PieChartOutlined />板块数据</span>} key="sector-data">
          <Card>
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Select
                  placeholder="选择板块类型"
                  style={{ width: '100%' }}
                  allowClear
                  onChange={(value) => fetchSectorData(value)}
                >
                  <Option key="industry" value="industry">行业板块</Option>
                  <Option key="concept" value="concept">概念板块</Option>
                  <Option key="region" value="region">地区板块</Option>
                </Select>
              </Col>
              <Col span={6}>
                <DatePicker
                  placeholder="选择交易日期"
                  style={{ width: '100%' }}
                  onChange={(date) => fetchSectorData(undefined, date?.format('YYYY-MM-DD'))}
                />
              </Col>
              <Col span={12}>
                <Space>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={() => fetchSectorData()}
                    loading={loading}
                  >
                    刷新数据
                  </Button>
                  <Button
                    icon={<DatabaseOutlined />}
                    onClick={updateSectorData}
                    loading={updateProgress.sectorData !== undefined && updateProgress.sectorData < 100}
                  >
                    更新数据
                  </Button>
                </Space>
              </Col>
            </Row>

            {updateProgress.sectorData !== undefined && updateProgress.sectorData < 100 && (
              <Progress
                percent={updateProgress.sectorData}
                status="active"
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={sectorColumns}
              dataSource={sectorData}
              rowKey={(record, index) => record.sectorCode || `sector-${index}`}
              loading={loading}
              scroll={{ x: 1400 }}
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><LineChartOutlined />实时行情</span>} key="spot-data">
          <Card>
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Input
                  placeholder="输入股票代码，多个用逗号分隔"
                  prefix={<SearchOutlined />}
                  onPressEnter={(e) => {
                    const value = (e.target as HTMLInputElement).value;
                    fetchSpotData(value);
                  }}
                />
              </Col>
              <Col span={6}>
                <DatePicker
                  placeholder="选择交易日期"
                  style={{ width: '100%' }}
                  onChange={(date) => fetchSpotData(undefined, date?.format('YYYY-MM-DD'))}
                />
              </Col>
              <Col span={12}>
                <Space>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={() => fetchSpotData()}
                    loading={loading}
                  >
                    刷新数据
                  </Button>
                  <Button
                    icon={<DatabaseOutlined />}
                    onClick={updateSpotData}
                    loading={updateProgress.spotData !== undefined && updateProgress.spotData < 100}
                  >
                    更新数据
                  </Button>
                </Space>
              </Col>
            </Row>

            {updateProgress.spotData !== undefined && updateProgress.spotData < 100 && (
              <Progress
                percent={updateProgress.spotData}
                status="active"
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={spotColumns}
              dataSource={spotData}
              rowKey={(record, index) => record.stockCode || `spot-${index}`}
              loading={loading}
              scroll={{ x: 1600 }}
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><InfoCircleOutlined />股票详情</span>} key="stock-detail">
          <Card>
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Input
                  placeholder="输入股票代码"
                  prefix={<SearchOutlined />}
                  onPressEnter={(e) => {
                    const value = (e.target as HTMLInputElement).value;
                    if (value) {
                      fetchStockDetails(value);
                    }
                  }}
                />
              </Col>
              <Col span={18}>
                <Space>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={() => fetchStockDetails()}
                    loading={loading}
                  >
                    刷新数据
                  </Button>
                  <Button
                    icon={<DatabaseOutlined />}
                    onClick={() => updateStockDetails()}
                    loading={updateProgress.stockDetails !== undefined && updateProgress.stockDetails < 100}
                  >
                    更新全部
                  </Button>
                </Space>
              </Col>
            </Row>

            {updateProgress.stockDetails !== undefined && updateProgress.stockDetails < 100 && (
              <Progress
                percent={updateProgress.stockDetails}
                status="active"
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={stockDetailColumns}
              dataSource={stockDetails}
              rowKey={(record, index) => record.stockCode || `detail-${index}`}
              loading={loading}
              scroll={{ x: 1800 }}
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 股票详情Modal */}
      <Modal
        title={`股票详情 - ${selectedDetail?.stockName} (${selectedDetail?.stockCode})`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedDetail && (
          <Descriptions bordered column={2} size="small">
            <Descriptions.Item label="股票代码">{selectedDetail.stockCode}</Descriptions.Item>
            <Descriptions.Item label="股票名称">{selectedDetail.stockName}</Descriptions.Item>
            <Descriptions.Item label="公司全称" span={2}>{selectedDetail.fullName}</Descriptions.Item>
            <Descriptions.Item label="市场">{selectedDetail.market}</Descriptions.Item>
            <Descriptions.Item label="交易所">{selectedDetail.exchange}</Descriptions.Item>
            <Descriptions.Item label="上市日期">{selectedDetail.listDate}</Descriptions.Item>
            <Descriptions.Item label="行业">{selectedDetail.industry}</Descriptions.Item>
            <Descriptions.Item label="板块">{selectedDetail.sector}</Descriptions.Item>
            <Descriptions.Item label="概念">{selectedDetail.concept}</Descriptions.Item>
            <Descriptions.Item label="总股本(股)">
              {selectedDetail.totalShares ? selectedDetail.totalShares.toLocaleString() : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="流通股本(股)">
              {selectedDetail.floatShares ? selectedDetail.floatShares.toLocaleString() : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="总市值(元)">
              {selectedDetail.totalMarketCap ? selectedDetail.totalMarketCap.toLocaleString() : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="流通市值(元)">
              {selectedDetail.floatMarketCap ? selectedDetail.floatMarketCap.toLocaleString() : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="市盈率">{selectedDetail.peRatio || '-'}</Descriptions.Item>
            <Descriptions.Item label="市净率">{selectedDetail.pbRatio || '-'}</Descriptions.Item>
            <Descriptions.Item label="ROE">{selectedDetail.roe || '-'}</Descriptions.Item>
            <Descriptions.Item label="ROA">{selectedDetail.roa || '-'}</Descriptions.Item>
            <Descriptions.Item label="法定代表人">{selectedDetail.legalRepresentative || '-'}</Descriptions.Item>
            <Descriptions.Item label="总经理">{selectedDetail.generalManager || '-'}</Descriptions.Item>
            <Descriptions.Item label="董秘">{selectedDetail.secretary || '-'}</Descriptions.Item>
            <Descriptions.Item label="联系电话">{selectedDetail.phone || '-'}</Descriptions.Item>
            <Descriptions.Item label="邮箱">{selectedDetail.email || '-'}</Descriptions.Item>
            <Descriptions.Item label="网站">{selectedDetail.website || '-'}</Descriptions.Item>
            <Descriptions.Item label="员工人数">{selectedDetail.employeeCount || '-'}</Descriptions.Item>
            <Descriptions.Item label="注册资本(元)">
              {selectedDetail.registeredCapital ? selectedDetail.registeredCapital.toLocaleString() : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="实际控制人" span={2}>{selectedDetail.actualController || '-'}</Descriptions.Item>
            <Descriptions.Item label="主营业务" span={2}>{selectedDetail.mainBusiness || '-'}</Descriptions.Item>
            <Descriptions.Item label="经营范围" span={2}>{selectedDetail.businessScope || '-'}</Descriptions.Item>
            <Descriptions.Item label="公司简介" span={2}>{selectedDetail.companyProfile || '-'}</Descriptions.Item>
            <Descriptions.Item label="数据日期">{selectedDetail.dataDate}</Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {selectedDetail.updatedAt ? dayjs(selectedDetail.updatedAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default AKShareData;
