"""
Celery应用配置
"""

from celery import Celery
from app.core.config import settings

# 创建Celery应用实例
celery_app = Celery(
    "stock_analyzer",
    broker=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}",
    backend=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}",
    include=[
        "app.tasks.data_tasks",
        "app.tasks.analysis_tasks",
        "app.tasks.notification_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    
    # 时区设置
    timezone="Asia/Shanghai",
    enable_utc=True,
    
    # 任务路由
    task_routes={
        "app.tasks.data_tasks.*": {"queue": "data"},
        "app.tasks.analysis_tasks.*": {"queue": "analysis"},
        "app.tasks.notification_tasks.*": {"queue": "notifications"},
    },
    
    # 任务结果过期时间
    result_expires=3600,
    
    # 任务重试配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # 定时任务配置
    beat_schedule={
        # 每5分钟同步实时数据
        "sync-realtime-data": {
            "task": "app.tasks.data_tasks.sync_realtime_data_task",
            "schedule": 300.0,  # 5分钟
        },
        
        # 每小时同步K线数据
        "sync-kline-data": {
            "task": "app.tasks.data_tasks.sync_kline_data_task",
            "schedule": 3600.0,  # 1小时
        },
        
        # 每天凌晨2点同步股票列表
        "sync-stock-list": {
            "task": "app.tasks.data_tasks.sync_stock_list_task",
            "schedule": {
                "hour": 2,
                "minute": 0,
            },
        },
        
        # 每天凌晨3点进行数据质量检查
        "data-quality-check": {
            "task": "app.tasks.data_tasks.data_quality_check_task",
            "schedule": {
                "hour": 3,
                "minute": 0,
            },
        },
        
        # 每30分钟清理过期数据
        "cleanup-expired-data": {
            "task": "app.tasks.data_tasks.cleanup_expired_data_task",
            "schedule": 1800.0,  # 30分钟
        },
    },
)

# 任务发现
celery_app.autodiscover_tasks()
