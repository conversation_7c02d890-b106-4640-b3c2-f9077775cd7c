.PHONY: help setup dev clean test lint format install-deps

# 默认目标
help:
	@echo "A股智能分析系统 - 开发命令"
	@echo "=========================="
	@echo "setup          - 初始化项目环境"
	@echo "dev            - 启动开发环境"
	@echo "install-deps   - 安装所有依赖"
	@echo "test           - 运行测试"
	@echo "lint           - 代码检查"
	@echo "format         - 代码格式化"
	@echo "clean          - 清理临时文件"
	@echo "db-init        - 初始化数据库"
	@echo "db-migrate     - 运行数据库迁移"

# 项目初始化
setup:
	@echo "🚀 初始化项目..."
	python scripts/setup.py

# 启动开发环境
dev:
	@echo "🚀 启动开发环境..."
	python scripts/dev.py

# 安装依赖
install-deps:
	@echo "📦 安装后端依赖..."
	cd backend && pip install -r requirements.txt
	@echo "📦 安装前端依赖..."
	cd frontend && npm install

# 运行测试
test:
	@echo "🧪 运行后端测试..."
	cd backend && python -m pytest tests/ -v
	@echo "🧪 运行前端测试..."
	cd frontend && npm test

# 代码检查
lint:
	@echo "🔍 后端代码检查..."
	cd backend && flake8 app/
	cd backend && mypy app/
	@echo "🔍 前端代码检查..."
	cd frontend && npm run lint

# 代码格式化
format:
	@echo "✨ 格式化后端代码..."
	cd backend && black app/
	cd backend && isort app/
	@echo "✨ 格式化前端代码..."
	cd frontend && npm run lint:fix

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name ".pytest_cache" -delete
	find . -type d -name "*.egg-info" -delete
	rm -rf backend/dist/
	rm -rf frontend/dist/
	rm -rf frontend/node_modules/.cache/

# 数据库操作
db-init:
	@echo "🗄️ 初始化数据库..."
	cd backend && python scripts/init_db.py

db-migrate:
	@echo "🗄️ 运行数据库迁移..."
	cd backend && alembic upgrade head

# 生产环境构建
build:
	@echo "🏗️ 构建生产版本..."
	cd frontend && npm run build
	@echo "✅ 构建完成"

# Docker相关命令
docker-dev:
	@echo "🐳 启动Docker开发环境..."
	cd docker && docker-compose -f docker-compose.dev.yml up -d

docker-stop:
	@echo "🐳 停止Docker服务..."
	cd docker && docker-compose -f docker-compose.dev.yml down

# 安装开发工具
install-dev-tools:
	@echo "🛠️ 安装开发工具..."
	cd backend && pip install black isort flake8 mypy pytest pytest-asyncio
	cd frontend && npm install --save-dev prettier eslint
