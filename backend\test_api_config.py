"""
测试API配置功能
"""

import requests
import json

def test_api_config_endpoints():
    base_url = "http://localhost:8001"
    
    print("🚀 开始测试API配置功能")
    
    # 1. 测试获取API配置列表
    print("\n1. 测试获取API配置列表")
    try:
        response = requests.get(f"{base_url}/api/v1/data-management/api-configs")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            configs = response.json()
            print(f"配置数量: {len(configs)}")
            for config in configs:
                print(f"  - {config['name']} ({config['type']}): {'启用' if config['enabled'] else '禁用'}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 2. 测试更新API配置
    print("\n2. 测试更新API配置")
    try:
        test_config = {
            "id": "tushare",
            "name": "Tushare Pro",
            "type": "tushare",
            "enabled": True,
            "api_key": "test_token_123456",
            "endpoint": "http://api.tushare.pro",
            "rate_limit": 200,
            "timeout": 30000,
            "priority": 2
        }
        
        response = requests.put(
            f"{base_url}/api/v1/data-management/api-configs/tushare",
            json=test_config
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"更新结果: {result['message']}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 3. 测试API连接测试
    print("\n3. 测试API连接测试")
    apis_to_test = ['akshare', 'tushare', 'sina']
    
    for api_id in apis_to_test:
        try:
            response = requests.post(f"{base_url}/api/v1/data-management/api-configs/{api_id}/test")
            print(f"{api_id} 连接测试 - 状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                status = "✅ 成功" if result.get('success') else "❌ 失败"
                print(f"  结果: {status} - {result.get('message', '')}")
            else:
                print(f"  错误响应: {response.text}")
        except Exception as e:
            print(f"  错误: {e}")
    
    # 4. 验证配置更新是否生效
    print("\n4. 验证配置更新")
    try:
        response = requests.get(f"{base_url}/api/v1/data-management/api-configs")
        if response.status_code == 200:
            configs = response.json()
            tushare_config = next((c for c in configs if c['id'] == 'tushare'), None)
            if tushare_config:
                print(f"Tushare配置验证:")
                print(f"  名称: {tushare_config['name']}")
                print(f"  API Key: {tushare_config.get('api_key', 'None')}")
                print(f"  端点: {tushare_config.get('endpoint', 'None')}")
                print(f"  启用状态: {'是' if tushare_config['enabled'] else '否'}")
            else:
                print("未找到Tushare配置")
    except Exception as e:
        print(f"错误: {e}")
    
    print("\n✅ API配置功能测试完成！")

if __name__ == "__main__":
    test_api_config_endpoints()
