"""
AI预测相关API端点
"""

from typing import Optional
from datetime import date, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging import get_logger
from app.services.ai_service import AIAnalysisService, DeepSeekAIService
from app.services.ai_storage import AIPredictionStorageService
from app.tasks.analysis_tasks import ai_prediction_task

router = APIRouter()
logger = get_logger(__name__)


@router.post("/{stock_code}/predict")
async def predict_stock(
    stock_code: str,
    background_tasks: BackgroundTasks,
    days: int = Query(5, ge=1, le=30, description="预测天数")
):
    """AI股票预测"""
    try:
        # 启动后台任务
        task = ai_prediction_task.delay(stock_code, days)

        # 同时进行同步预测（用于立即返回结果）
        ai_service = DeepSeekAIService()
        prediction_result = await ai_service.predict_price(stock_code, days)

        if "error" not in prediction_result:
            # 保存预测结果
            async with AIPredictionStorageService() as storage:
                await storage.save_prediction(prediction_result)

        return {
            "code": 200,
            "message": "AI预测完成",
            "data": {
                "task_id": task.id,
                "prediction_result": prediction_result,
                "predicted_at": prediction_result.get("predicted_at")
            }
        }
    except Exception as e:
        logger.error(f"AI预测失败: {e}")
        raise HTTPException(status_code=500, detail="AI预测失败")


@router.get("/{stock_code}/trend")
async def analyze_trend(stock_code: str):
    """AI趋势分析"""
    try:
        ai_service = DeepSeekAIService()
        trend_result = await ai_service.analyze_trend(stock_code)

        return {
            "code": 200,
            "message": "success",
            "data": trend_result
        }
    except Exception as e:
        logger.error(f"趋势分析失败: {e}")
        raise HTTPException(status_code=500, detail="趋势分析失败")


@router.get("/{stock_code}/pattern")
async def detect_pattern(stock_code: str):
    """AI形态识别"""
    try:
        ai_service = DeepSeekAIService()
        pattern_result = await ai_service.recognize_patterns(stock_code)

        if "error" not in pattern_result:
            # 保存形态识别结果
            async with AIPredictionStorageService() as storage:
                await storage.save_pattern_recognition(pattern_result)

        return {
            "code": 200,
            "message": "success",
            "data": pattern_result
        }
    except Exception as e:
        logger.error(f"形态识别失败: {e}")
        raise HTTPException(status_code=500, detail="形态识别失败")


@router.get("/{stock_code}/comprehensive")
async def comprehensive_analysis(stock_code: str):
    """AI综合分析"""
    try:
        ai_service = AIAnalysisService()
        analysis_result = await ai_service.comprehensive_analysis(stock_code)

        # 保存分析结果
        if "error" not in analysis_result:
            async with AIPredictionStorageService() as storage:
                # 保存价格预测
                if "price_prediction" in analysis_result and "error" not in analysis_result["price_prediction"]:
                    await storage.save_prediction(analysis_result["price_prediction"])

                # 保存形态识别
                if "pattern_recognition" in analysis_result and "error" not in analysis_result["pattern_recognition"]:
                    await storage.save_pattern_recognition(analysis_result["pattern_recognition"])

        return {
            "code": 200,
            "message": "success",
            "data": analysis_result
        }
    except Exception as e:
        logger.error(f"综合分析失败: {e}")
        raise HTTPException(status_code=500, detail="综合分析失败")


@router.get("/predictions")
async def get_predictions(
    stock_code: Optional[str] = Query(None, description="股票代码"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制")
):
    """获取AI预测历史"""
    try:
        async with AIPredictionStorageService() as storage:
            predictions = await storage.get_predictions(stock_code, start_date, end_date, limit)

        predictions_data = []
        for prediction in predictions:
            predictions_data.append({
                "id": prediction.id,
                "stock_code": prediction.stock_code,
                "prediction_date": prediction.prediction_date.isoformat(),
                "target_date": prediction.target_date.isoformat(),
                "predicted_price": float(prediction.predicted_price) if prediction.predicted_price else None,
                "confidence_score": float(prediction.confidence_score) if prediction.confidence_score else None,
                "trend_direction": prediction.trend_direction,
                "prediction_basis": prediction.prediction_basis,
                "model_version": prediction.model_version
            })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "predictions": predictions_data,
                "total": len(predictions_data),
                "filters": {
                    "stock_code": stock_code,
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None
                }
            }
        }
    except Exception as e:
        logger.error(f"获取AI预测历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取AI预测历史失败")


@router.get("/patterns")
async def get_patterns(
    stock_code: Optional[str] = Query(None, description="股票代码"),
    pattern_type: Optional[str] = Query(None, description="形态类型"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制")
):
    """获取形态识别历史"""
    try:
        async with AIPredictionStorageService() as storage:
            patterns = await storage.get_pattern_recognitions(stock_code, pattern_type, limit=limit)

        patterns_data = []
        for pattern in patterns:
            patterns_data.append({
                "id": pattern.id,
                "stock_code": pattern.stock_code,
                "recognition_date": pattern.recognition_date.isoformat(),
                "pattern_type": pattern.pattern_type,
                "pattern_name": pattern.pattern_name,
                "confidence": float(pattern.confidence) if pattern.confidence else None,
                "completion_rate": float(pattern.completion_rate) if pattern.completion_rate else None,
                "support_level": float(pattern.support_level) if pattern.support_level else None,
                "resistance_level": float(pattern.resistance_level) if pattern.resistance_level else None,
                "trading_suggestion": pattern.trading_suggestion,
                "description": pattern.description
            })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "patterns": patterns_data,
                "total": len(patterns_data),
                "filters": {
                    "stock_code": stock_code,
                    "pattern_type": pattern_type
                }
            }
        }
    except Exception as e:
        logger.error(f"获取形态识别历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取形态识别历史失败")


@router.get("/statistics")
async def get_ai_statistics():
    """获取AI分析统计信息"""
    try:
        async with AIPredictionStorageService() as storage:
            stats = await storage.get_prediction_statistics()
            accuracy = await storage.get_prediction_accuracy(30)

        return {
            "code": 200,
            "message": "success",
            "data": {
                "prediction_statistics": stats,
                "accuracy_metrics": accuracy,
                "generated_at": stats.get("generated_at")
            }
        }
    except Exception as e:
        logger.error(f"获取AI统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取AI统计信息失败")
