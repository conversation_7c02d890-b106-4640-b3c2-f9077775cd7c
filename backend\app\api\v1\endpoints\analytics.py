"""
统计分析相关API端点
"""

from typing import List, Optional
from datetime import date, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel
from decimal import Decimal

from app.core.logging import get_logger
from app.services.analytics_service import PortfolioAnalyticsService
from app.services.backtest_service import BacktestEngine
from app.api.v1.endpoints.auth import get_current_user
from app.models.user import User

router = APIRouter()
logger = get_logger(__name__)


class PortfolioCreate(BaseModel):
    """创建投资组合请求模型"""
    name: str
    description: Optional[str] = None
    portfolio_type: str = "virtual"  # real/virtual/backtest
    initial_capital: float


class TransactionAdd(BaseModel):
    """添加交易记录请求模型"""
    stock_code: str
    transaction_type: str  # buy/sell
    shares: float
    price: float
    amount: float
    commission: Optional[float] = 0
    tax: Optional[float] = 0
    total_cost: float
    reason: Optional[str] = None
    strategy: Optional[str] = None
    transaction_date: Optional[str] = None


class BacktestConfig(BaseModel):
    """回测配置请求模型"""
    strategy_name: str
    start_date: str  # YYYY-MM-DD
    end_date: str    # YYYY-MM-DD
    initial_capital: float
    stock_pool: Optional[List[str]] = None
    # 策略参数
    short_ma: Optional[int] = 5
    long_ma: Optional[int] = 20
    oversold: Optional[int] = 30
    overbought: Optional[int] = 70
    confidence_threshold: Optional[float] = 0.7
    max_pe: Optional[float] = 15
    max_pb: Optional[float] = 2


@router.post("/portfolios")
async def create_portfolio(
    portfolio_data: PortfolioCreate,
    current_user: User = Depends(get_current_user)
):
    """创建投资组合"""
    try:
        async with PortfolioAnalyticsService() as analytics_service:
            portfolio_id = await analytics_service.create_portfolio(
                current_user.id, portfolio_data.dict()
            )

        if portfolio_id > 0:
            return {
                "code": 200,
                "message": "投资组合创建成功",
                "data": {
                    "portfolio_id": portfolio_id
                }
            }
        else:
            raise HTTPException(status_code=400, detail="创建投资组合失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建投资组合失败: {e}")
        raise HTTPException(status_code=500, detail="创建投资组合失败")


@router.post("/portfolios/{portfolio_id}/transactions")
async def add_transaction(
    portfolio_id: int,
    transaction_data: TransactionAdd,
    current_user: User = Depends(get_current_user)
):
    """添加交易记录"""
    try:
        async with PortfolioAnalyticsService() as analytics_service:
            success = await analytics_service.add_transaction(
                portfolio_id, transaction_data.dict()
            )

        if success:
            return {
                "code": 200,
                "message": "交易记录添加成功",
                "data": {
                    "portfolio_id": portfolio_id,
                    "transaction": transaction_data.dict()
                }
            }
        else:
            raise HTTPException(status_code=400, detail="添加交易记录失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加交易记录失败: {e}")
        raise HTTPException(status_code=500, detail="添加交易记录失败")


@router.get("/portfolios/{portfolio_id}/performance")
async def get_portfolio_performance(
    portfolio_id: int,
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    current_user: User = Depends(get_current_user)
):
    """获取投资组合表现分析"""
    try:
        # 解析日期参数
        start_date_obj = None
        end_date_obj = None

        if start_date:
            start_date_obj = date.fromisoformat(start_date)
        if end_date:
            end_date_obj = date.fromisoformat(end_date)

        async with PortfolioAnalyticsService() as analytics_service:
            performance = await analytics_service.calculate_portfolio_performance(
                portfolio_id, start_date_obj, end_date_obj
            )

        if "error" in performance:
            raise HTTPException(status_code=400, detail=performance["error"])

        return {
            "code": 200,
            "message": "success",
            "data": performance
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取投资组合表现失败: {e}")
        raise HTTPException(status_code=500, detail="获取投资组合表现失败")


@router.get("/portfolios/{portfolio_id}/risk-metrics")
async def get_risk_metrics(
    portfolio_id: int,
    period_days: int = Query(252, description="计算周期天数"),
    current_user: User = Depends(get_current_user)
):
    """获取风险指标分析"""
    try:
        async with PortfolioAnalyticsService() as analytics_service:
            risk_metrics = await analytics_service.calculate_risk_metrics(
                portfolio_id, period_days
            )

        if "error" in risk_metrics:
            raise HTTPException(status_code=400, detail=risk_metrics["error"])

        return {
            "code": 200,
            "message": "success",
            "data": risk_metrics
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取风险指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取风险指标失败")


@router.post("/backtest")
async def run_backtest(
    backtest_config: BacktestConfig,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """运行策略回测"""
    try:
        # 验证日期格式
        try:
            start_date = date.fromisoformat(backtest_config.start_date)
            end_date = date.fromisoformat(backtest_config.end_date)
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD 格式")

        # 验证日期范围
        if start_date >= end_date:
            raise HTTPException(status_code=400, detail="开始日期必须早于结束日期")

        if end_date > date.today():
            raise HTTPException(status_code=400, detail="结束日期不能超过今天")

        # 设置默认股票池
        if not backtest_config.stock_pool:
            backtest_config.stock_pool = ["000001", "000002", "600000", "600036", "000858"]

        # 启动后台回测任务
        async def run_backtest_task():
            async with BacktestEngine() as backtest_engine:
                backtest_id = await backtest_engine.run_backtest(
                    current_user.id, backtest_config.dict()
                )
                return backtest_id

        # 同步执行回测（小规模回测）
        async with BacktestEngine() as backtest_engine:
            backtest_id = await backtest_engine.run_backtest(
                current_user.id, backtest_config.dict()
            )

        if backtest_id > 0:
            return {
                "code": 200,
                "message": "回测完成",
                "data": {
                    "backtest_id": backtest_id,
                    "strategy_name": backtest_config.strategy_name,
                    "period": {
                        "start_date": backtest_config.start_date,
                        "end_date": backtest_config.end_date
                    }
                }
            }
        else:
            raise HTTPException(status_code=400, detail="回测执行失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"运行回测失败: {e}")
        raise HTTPException(status_code=500, detail="运行回测失败")


@router.get("/backtest/{backtest_id}")
async def get_backtest_result(
    backtest_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取回测结果"""
    try:
        from sqlalchemy import select
        from app.models.analytics import BacktestResult
        from app.core.database import AsyncSessionLocal

        async with AsyncSessionLocal() as session:
            stmt = select(BacktestResult).where(
                BacktestResult.id == backtest_id,
                BacktestResult.user_id == current_user.id
            )
            result = await session.execute(stmt)
            backtest = result.scalar_one_or_none()

            if not backtest:
                raise HTTPException(status_code=404, detail="回测结果不存在")

            return {
                "code": 200,
                "message": "success",
                "data": {
                    "backtest_id": backtest.id,
                    "strategy_name": backtest.strategy_name,
                    "strategy_config": backtest.strategy_config,
                    "period": {
                        "start_date": backtest.start_date.isoformat(),
                        "end_date": backtest.end_date.isoformat(),
                        "trading_days": (backtest.end_date - backtest.start_date).days
                    },
                    "capital": {
                        "initial_capital": float(backtest.initial_capital),
                        "final_value": float(backtest.final_value),
                        "total_return": float(backtest.total_return),
                        "total_return_pct": float(backtest.total_return_pct),
                        "annualized_return": float(backtest.annualized_return)
                    },
                    "risk_metrics": {
                        "volatility": float(backtest.volatility),
                        "sharpe_ratio": float(backtest.sharpe_ratio),
                        "max_drawdown": float(backtest.max_drawdown),
                        "calmar_ratio": float(backtest.calmar_ratio) if backtest.calmar_ratio else None
                    },
                    "trading_stats": {
                        "total_trades": backtest.total_trades,
                        "winning_trades": backtest.winning_trades,
                        "losing_trades": backtest.losing_trades,
                        "win_rate": float(backtest.win_rate)
                    },
                    "benchmark_comparison": {
                        "benchmark_return": float(backtest.benchmark_return) if backtest.benchmark_return else None,
                        "alpha": float(backtest.alpha) if backtest.alpha else None,
                        "beta": float(backtest.beta) if backtest.beta else None
                    },
                    "performance_chart": backtest.performance_data[-100:] if backtest.performance_data else [],  # 最近100个数据点
                    "recent_trades": backtest.trade_data[-20:] if backtest.trade_data else [],  # 最近20笔交易
                    "status": backtest.status,
                    "created_at": backtest.created_at.isoformat()
                }
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取回测结果失败: {e}")
        raise HTTPException(status_code=500, detail="获取回测结果失败")


@router.get("/backtest")
async def get_user_backtests(
    strategy_name: Optional[str] = Query(None, description="策略名称"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    current_user: User = Depends(get_current_user)
):
    """获取用户的回测历史"""
    try:
        from sqlalchemy import select, desc
        from app.models.analytics import BacktestResult
        from app.core.database import AsyncSessionLocal

        async with AsyncSessionLocal() as session:
            stmt = select(BacktestResult).where(BacktestResult.user_id == current_user.id)

            if strategy_name:
                stmt = stmt.where(BacktestResult.strategy_name == strategy_name)

            stmt = stmt.order_by(desc(BacktestResult.created_at)).limit(limit)

            result = await session.execute(stmt)
            backtests = result.scalars().all()

            backtests_data = []
            for backtest in backtests:
                backtests_data.append({
                    "backtest_id": backtest.id,
                    "strategy_name": backtest.strategy_name,
                    "period": {
                        "start_date": backtest.start_date.isoformat(),
                        "end_date": backtest.end_date.isoformat()
                    },
                    "performance": {
                        "total_return_pct": float(backtest.total_return_pct),
                        "annualized_return": float(backtest.annualized_return),
                        "sharpe_ratio": float(backtest.sharpe_ratio),
                        "max_drawdown": float(backtest.max_drawdown)
                    },
                    "trading_stats": {
                        "total_trades": backtest.total_trades,
                        "win_rate": float(backtest.win_rate)
                    },
                    "status": backtest.status,
                    "created_at": backtest.created_at.isoformat()
                })

            return {
                "code": 200,
                "message": "success",
                "data": {
                    "backtests": backtests_data,
                    "total": len(backtests_data),
                    "filters": {
                        "strategy_name": strategy_name,
                        "limit": limit
                    }
                }
            }

    except Exception as e:
        logger.error(f"获取回测历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取回测历史失败")


@router.get("/strategies")
async def get_available_strategies():
    """获取可用的回测策略"""
    try:
        strategies = [
            {
                "name": "ma_crossover",
                "display_name": "移动平均线交叉策略",
                "description": "基于短期和长期移动平均线交叉的趋势跟踪策略",
                "parameters": {
                    "short_ma": {"type": "int", "default": 5, "description": "短期均线周期"},
                    "long_ma": {"type": "int", "default": 20, "description": "长期均线周期"}
                },
                "risk_level": "中等",
                "suitable_market": "趋势市场"
            },
            {
                "name": "rsi_reversal",
                "display_name": "RSI反转策略",
                "description": "基于RSI指标的超买超卖反转策略",
                "parameters": {
                    "oversold": {"type": "int", "default": 30, "description": "超卖阈值"},
                    "overbought": {"type": "int", "default": 70, "description": "超买阈值"}
                },
                "risk_level": "中等",
                "suitable_market": "震荡市场"
            },
            {
                "name": "ai_momentum",
                "display_name": "AI动量策略",
                "description": "基于AI预测信号的动量策略",
                "parameters": {
                    "confidence_threshold": {"type": "float", "default": 0.7, "description": "置信度阈值"}
                },
                "risk_level": "高",
                "suitable_market": "全市场"
            },
            {
                "name": "value_investing",
                "display_name": "价值投资策略",
                "description": "基于基本面指标的价值投资策略",
                "parameters": {
                    "max_pe": {"type": "float", "default": 15, "description": "最大市盈率"},
                    "max_pb": {"type": "float", "default": 2, "description": "最大市净率"}
                },
                "risk_level": "低",
                "suitable_market": "熊市/震荡市"
            }
        ]

        return {
            "code": 200,
            "message": "success",
            "data": {
                "strategies": strategies,
                "total": len(strategies)
            }
        }

    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略列表失败")
