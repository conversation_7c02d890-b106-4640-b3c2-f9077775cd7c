import React, { useState, useEffect } from 'react'
import {
  Layout, Menu, Card, Table, Button, Space, Input, Select, Modal, Form,
  Statistic, Row, Col, Tag, Tooltip, Drawer, Tabs, Alert, Progress,
  Popconfirm, message, Upload, Badge, Typography, Divider
} from 'antd'
import {
  DatabaseOutlined, TableOutlined, SettingOutlined, UploadOutlined,
  DownloadOutlined, EditOutlined, DeleteOutlined, PlusOutlined,
  SearchOutlined, FilterOutlined, ReloadOutlined, InfoCircleOutlined,
  BarChartOutlined, FileTextOutlined, ToolOutlined, MonitorOutlined,
  FolderOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { professionalDbService } from '../services/professionalDbService'
import DataClassificationManager from '../components/DataClassificationManager'
import DataUpdateStrategyManager from '../components/DataUpdateStrategyManager'
import CustomFunctionalityManager from '../components/CustomFunctionalityManager'

const { Sider, Content, Header } = Layout
const { Option } = Select
const { Search } = Input
const { Title, Text } = Typography
const { TabPane } = Tabs

interface DatabaseOverview {
  database_info: {
    total_tables: number
    total_records: number
    active_stocks: number
    last_update: string | null
  }
  tables_info: Array<{
    table_name: string
    display_name: string
    category: string
    record_count: number
    latest_update: string | null
  }>
  category_stats: Record<string, { tables: number; records: number }>
  recent_updates: Array<{
    id: number
    data_type: string
    stock_code: string | null
    status: string
    records_count: number
    update_time: string
    duration: number
  }>
}

interface TableStructure {
  table_name: string
  model_name: string
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    primary_key: boolean
    foreign_key: boolean
    unique: boolean
    comment: string | null
  }>
  indexes: Array<{
    name: string
    columns: string[]
    unique: boolean
  }>
}

const ProfessionalDatabaseManager: React.FC = () => {
  const [selectedTable, setSelectedTable] = useState<string>('')
  const [overview, setOverview] = useState<DatabaseOverview | null>(null)
  const [tableStructure, setTableStructure] = useState<TableStructure | null>(null)
  const [tableData, setTableData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0
  })
  const [searchText, setSearchText] = useState('')
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<any>(null)
  const [structureDrawerVisible, setStructureDrawerVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  // 菜单项配置
  const menuItems = [
    {
      key: 'overview',
      icon: <MonitorOutlined />,
      label: '数据库概览',
    },
    {
      key: 'classification',
      icon: <FolderOutlined />,
      label: '数据分类管理',
    },
    {
      key: 'update-strategy',
      icon: <ClockCircleOutlined />,
      label: '更新策略管理',
    },
    {
      key: 'custom-functionality',
      icon: <SettingOutlined />,
      label: '自定义功能管理',
    },
    {
      key: 'tables',
      icon: <DatabaseOutlined />,
      label: '数据表管理',
      children: [
        { key: 'basic', label: '基础数据', icon: <TableOutlined /> },
        { key: 'trading', label: '交易数据', icon: <BarChartOutlined /> },
        { key: 'sentiment', label: '情绪数据', icon: <InfoCircleOutlined /> },
        { key: 'sector', label: '板块数据', icon: <FileTextOutlined /> },
        { key: 'fundamental', label: '基本面数据', icon: <FileTextOutlined /> },
        { key: 'custom', label: '自定义功能', icon: <ToolOutlined /> },
        { key: 'system', label: '系统数据', icon: <SettingOutlined /> },
      ]
    },
    {
      key: 'tools',
      icon: <ToolOutlined />,
      label: '数据工具',
      children: [
        { key: 'import', label: '数据导入', icon: <UploadOutlined /> },
        { key: 'export', label: '数据导出', icon: <DownloadOutlined /> },
        { key: 'quality', label: '数据质量', icon: <MonitorOutlined /> },
        { key: 'backup', label: '备份管理', icon: <DatabaseOutlined /> },
      ]
    }
  ]

  // 表分类映射
  const tableCategories = {
    basic: ['stock_basic_info'],
    trading: ['stock_kline_data', 'stock_realtime_data'],
    sentiment: ['stock_money_flow', 'northbound_capital'],
    sector: ['sector_info', 'stock_sector_mapping'],
    fundamental: ['stock_financial_data', 'company_info'],
    custom: ['custom_indicators', 'stock_patterns', 'backtest_strategies'],
    system: ['data_update_logs']
  }

  useEffect(() => {
    loadOverview()
  }, [])

  useEffect(() => {
    if (selectedTable) {
      loadTableStructure(selectedTable)
      loadTableData(selectedTable)
    }
  }, [selectedTable, pagination.current, pagination.pageSize, searchText, filters])

  const loadOverview = async () => {
    try {
      setLoading(true)
      const data = await professionalDbService.getDatabaseOverview()
      setOverview(data)
    } catch (error) {
      message.error('加载数据库概览失败')
    } finally {
      setLoading(false)
    }
  }

  const loadTableStructure = async (tableName: string) => {
    try {
      const structure = await professionalDbService.getTableStructure(tableName)
      setTableStructure(structure)
    } catch (error) {
      message.error('加载表结构失败')
    }
  }

  const loadTableData = async (tableName: string) => {
    try {
      setLoading(true)
      const response = await professionalDbService.getTableData(tableName, {
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText,
        filters: Object.keys(filters).length > 0 ? JSON.stringify(filters) : undefined
      })
      
      setTableData(response.data)
      setPagination(prev => ({
        ...prev,
        total: response.pagination.total_records
      }))
    } catch (error) {
      message.error('加载表数据失败')
    } finally {
      setLoading(false)
    }
  }

  const handleTableSelect = (tableName: string) => {
    setSelectedTable(tableName)
    setSelectedRowKeys([])
    setPagination(prev => ({ ...prev, current: 1 }))
    setSearchText('')
    setFilters({})
  }

  const handleSearch = (value: string) => {
    setSearchText(value)
    setPagination(prev => ({ ...prev, current: 1 }))
  }

  const handleTableChange = (paginationInfo: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }))
  }

  const handleEdit = (record: any) => {
    setEditingRecord(record)
    setEditModalVisible(true)
  }

  const handleDelete = async (recordId: number) => {
    try {
      await professionalDbService.deleteRecord(selectedTable, recordId)
      message.success('删除成功')
      loadTableData(selectedTable)
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录')
      return
    }

    try {
      await professionalDbService.batchDeleteRecords(selectedTable, selectedRowKeys as number[])
      message.success(`成功删除 ${selectedRowKeys.length} 条记录`)
      setSelectedRowKeys([])
      loadTableData(selectedTable)
    } catch (error) {
      message.error('批量删除失败')
    }
  }

  const renderOverview = () => {
    if (!overview) return null

    return (
      <div style={{ padding: '24px' }}>
        <Title level={2}>数据库概览</Title>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="数据表总数"
                value={overview.database_info.total_tables}
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="记录总数"
                value={overview.database_info.total_records}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃股票"
                value={overview.database_info.active_stocks}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="最后更新"
                value={overview.database_info.last_update ?
                  new Date(overview.database_info.last_update).toLocaleString() : '无'
                }
                prefix={<ReloadOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 分类统计 */}
        <Card title="数据分类统计" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            {Object.entries(overview.category_stats).map(([category, stats]) => (
              <Col span={8} key={category}>
                <Card size="small">
                  <Statistic
                    title={category}
                    value={stats.records}
                    suffix={`/ ${stats.tables} 表`}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </Card>

        {/* 表信息列表 */}
        <Card title="数据表详情">
          <Table
            dataSource={overview.tables_info}
            rowKey="table_name"
            columns={[
              {
                title: '表名',
                dataIndex: 'table_name',
                key: 'table_name',
                render: (text) => <Text code>{text}</Text>
              },
              {
                title: '显示名称',
                dataIndex: 'display_name',
                key: 'display_name'
              },
              {
                title: '分类',
                dataIndex: 'category',
                key: 'category',
                render: (category) => <Tag color="blue">{category}</Tag>
              },
              {
                title: '记录数',
                dataIndex: 'record_count',
                key: 'record_count',
                render: (count) => count.toLocaleString()
              },
              {
                title: '最后更新',
                dataIndex: 'latest_update',
                key: 'latest_update',
                render: (time) => time ? new Date(time).toLocaleString() : '无'
              },
              {
                title: '操作',
                key: 'actions',
                render: (_, record) => (
                  <Space>
                    <Button
                      size="small"
                      onClick={() => handleTableSelect(record.table_name)}
                    >
                      查看数据
                    </Button>
                    <Button
                      size="small"
                      icon={<InfoCircleOutlined />}
                      onClick={() => {
                        setSelectedTable(record.table_name)
                        setStructureDrawerVisible(true)
                      }}
                    >
                      表结构
                    </Button>
                  </Space>
                )
              }
            ]}
            pagination={false}
          />
        </Card>

        {/* 最近更新日志 */}
        <Card title="最近更新日志" style={{ marginTop: '24px' }}>
          <Table
            dataSource={overview.recent_updates}
            rowKey="id"
            size="small"
            columns={[
              {
                title: '数据类型',
                dataIndex: 'data_type',
                key: 'data_type'
              },
              {
                title: '股票代码',
                dataIndex: 'stock_code',
                key: 'stock_code',
                render: (code) => code || '-'
              },
              {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                render: (status) => (
                  <Tag color={status === 'success' ? 'green' : 'red'}>
                    {status}
                  </Tag>
                )
              },
              {
                title: '记录数',
                dataIndex: 'records_count',
                key: 'records_count'
              },
              {
                title: '更新时间',
                dataIndex: 'update_time',
                key: 'update_time',
                render: (time) => new Date(time).toLocaleString()
              },
              {
                title: '耗时(秒)',
                dataIndex: 'duration',
                key: 'duration',
                render: (duration) => duration?.toFixed(2) || '0.00'
              }
            ]}
            pagination={{ pageSize: 10 }}
          />
        </Card>
      </div>
    )
  }

  const renderTableManagement = () => {
    if (!selectedTable || !tableStructure) {
      return (
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Alert
            message="请选择要管理的数据表"
            description="从左侧菜单选择一个数据表开始管理"
            type="info"
            showIcon
          />
        </div>
      )
    }

    // 动态生成表格列
    const columns: ColumnsType<any> = tableStructure.columns
      .filter(col => !col.name.includes('password')) // 过滤敏感字段
      .map(col => ({
        title: col.comment || col.name,
        dataIndex: col.name,
        key: col.name,
        width: col.type.includes('TEXT') ? 200 : 120,
        ellipsis: true,
        render: (value: any) => {
          if (value === null || value === undefined) return '-'
          if (typeof value === 'string' && value.length > 50) {
            return (
              <Tooltip title={value}>
                <Text ellipsis>{value}</Text>
              </Tooltip>
            )
          }
          return value
        }
      }))

    // 添加操作列
    columns.push({
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    })

    return (
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3}>
            {tableStructure.model_name} 数据管理
            <Tag color="blue" style={{ marginLeft: '8px' }}>
              {tableData.length} / {pagination.total} 条记录
            </Tag>
          </Title>

          <Space>
            <Button
              icon={<InfoCircleOutlined />}
              onClick={() => setStructureDrawerVisible(true)}
            >
              表结构
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => message.info('数据导入功能开发中')}
            >
              导入数据
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => message.info('数据导出功能开发中')}
            >
              导出数据
            </Button>
          </Space>
        </div>

        {/* 搜索和过滤工具栏 */}
        <Card size="small" style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 16]} align="middle">
            <Col span={8}>
              <Search
                placeholder="搜索数据..."
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <Button
                icon={<FilterOutlined />}
                onClick={() => message.info('高级过滤功能开发中')}
              >
                高级过滤
              </Button>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <Space>
                {selectedRowKeys.length > 0 && (
                  <Popconfirm
                    title={`确定删除选中的 ${selectedRowKeys.length} 条记录吗？`}
                    onConfirm={handleBatchDelete}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button danger icon={<DeleteOutlined />}>
                      批量删除 ({selectedRowKeys.length})
                    </Button>
                  </Popconfirm>
                )}
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => loadTableData(selectedTable)}
                >
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 数据表格 */}
        <Card>
          <Table
            dataSource={tableData}
            columns={columns}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
            }}
            onChange={handleTableChange}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              type: 'checkbox'
            }}
            scroll={{ x: 'max-content', y: 600 }}
            size="small"
          />
        </Card>
      </div>
    )
  }

  const renderStructureDrawer = () => {
    if (!tableStructure) return null

    return (
      <Drawer
        title={`${tableStructure.model_name} 表结构`}
        placement="right"
        width={800}
        open={structureDrawerVisible}
        onClose={() => setStructureDrawerVisible(false)}
      >
        <Tabs defaultActiveKey="columns">
          <TabPane tab="字段信息" key="columns">
            <Table
              dataSource={tableStructure.columns}
              rowKey="name"
              pagination={false}
              size="small"
              columns={[
                {
                  title: '字段名',
                  dataIndex: 'name',
                  key: 'name',
                  render: (text) => <Text code>{text}</Text>
                },
                {
                  title: '类型',
                  dataIndex: 'type',
                  key: 'type',
                  render: (text) => <Tag color="blue">{text}</Tag>
                },
                {
                  title: '属性',
                  key: 'properties',
                  render: (_, record) => (
                    <Space>
                      {record.primary_key && <Tag color="red">主键</Tag>}
                      {record.foreign_key && <Tag color="orange">外键</Tag>}
                      {record.unique && <Tag color="purple">唯一</Tag>}
                      {!record.nullable && <Tag color="green">非空</Tag>}
                    </Space>
                  )
                },
                {
                  title: '说明',
                  dataIndex: 'comment',
                  key: 'comment',
                  render: (text) => text || '-'
                }
              ]}
            />
          </TabPane>

          <TabPane tab="索引信息" key="indexes">
            <Table
              dataSource={tableStructure.indexes}
              rowKey="name"
              pagination={false}
              size="small"
              columns={[
                {
                  title: '索引名',
                  dataIndex: 'name',
                  key: 'name',
                  render: (text) => <Text code>{text}</Text>
                },
                {
                  title: '字段',
                  dataIndex: 'columns',
                  key: 'columns',
                  render: (columns) => columns.join(', ')
                },
                {
                  title: '类型',
                  dataIndex: 'unique',
                  key: 'unique',
                  render: (unique) => (
                    <Tag color={unique ? 'orange' : 'blue'}>
                      {unique ? '唯一索引' : '普通索引'}
                    </Tag>
                  )
                }
              ]}
            />
          </TabPane>
        </Tabs>
      </Drawer>
    )
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          <DatabaseOutlined style={{ fontSize: '24px', marginRight: '12px', color: '#1890ff' }} />
          <Title level={3} style={{ margin: 0 }}>
            专业级股票数据库管理系统
          </Title>
          <div style={{ marginLeft: 'auto' }}>
            <Space>
              <Badge count={overview?.recent_updates.filter(u => u.status === 'failed').length || 0}>
                <Button icon={<MonitorOutlined />}>
                  系统状态
                </Button>
              </Badge>
              <Button icon={<SettingOutlined />}>
                系统设置
              </Button>
            </Space>
          </div>
        </div>
      </Header>

      <Layout>
        <Sider width={280} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
          <Menu
            mode="inline"
            selectedKeys={[activeTab]}
            openKeys={['tables']}
            style={{ height: '100%', borderRight: 0 }}
            items={menuItems}
            onClick={({ key }) => {
              if (key === 'overview') {
                setActiveTab('overview')
                setSelectedTable('')
              } else if (key === 'classification') {
                setActiveTab('classification')
                setSelectedTable('')
              } else if (key === 'update-strategy') {
                setActiveTab('update-strategy')
                setSelectedTable('')
              } else if (key === 'custom-functionality') {
                setActiveTab('custom-functionality')
                setSelectedTable('')
              } else if (Object.keys(tableCategories).includes(key)) {
                setActiveTab(key)
                // 自动选择该分类下的第一个表
                const tables = tableCategories[key as keyof typeof tableCategories]
                if (tables.length > 0) {
                  handleTableSelect(tables[0])
                }
              } else {
                setActiveTab(key)
              }
            }}
          />

          {/* 表列表 */}
          {activeTab !== 'overview' && Object.keys(tableCategories).includes(activeTab) && (
            <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>
              <Title level={5}>数据表</Title>
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                {tableCategories[activeTab as keyof typeof tableCategories].map(tableName => (
                  <div
                    key={tableName}
                    style={{
                      padding: '8px 12px',
                      cursor: 'pointer',
                      borderRadius: '4px',
                      marginBottom: '4px',
                      backgroundColor: selectedTable === tableName ? '#e6f7ff' : 'transparent',
                      border: selectedTable === tableName ? '1px solid #91d5ff' : '1px solid transparent'
                    }}
                    onClick={() => handleTableSelect(tableName)}
                  >
                    <Text code style={{ fontSize: '12px' }}>
                      {tableName}
                    </Text>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Sider>

        <Content style={{ background: '#f5f5f5' }}>
          {activeTab === 'overview' ? renderOverview() :
           activeTab === 'classification' ? <DataClassificationManager /> :
           activeTab === 'update-strategy' ? <DataUpdateStrategyManager /> :
           activeTab === 'custom-functionality' ? <CustomFunctionalityManager /> :
           renderTableManagement()}
        </Content>
      </Layout>

      {/* 表结构抽屉 */}
      {renderStructureDrawer()}

      {/* 编辑记录模态框 */}
      <Modal
        title="编辑记录"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <Alert
          message="记录编辑功能"
          description="详细的记录编辑功能正在开发中，将支持所有字段的可视化编辑。"
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        {editingRecord && (
          <div>
            <Title level={5}>当前记录数据：</Title>
            <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
              {JSON.stringify(editingRecord, null, 2)}
            </pre>
          </div>
        )}
      </Modal>
    </Layout>
  )
}

export default ProfessionalDatabaseManager
