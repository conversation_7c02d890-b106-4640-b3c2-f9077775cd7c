/**
 * 量价分析系统
 * 实现专业的成交量和资金流向分析
 */

import { PriceData } from './advancedIndicators'

export interface MoneyFlowResult {
  value: number
  timestamp: string
}

export interface LargeOrder {
  timestamp: string
  price: number
  volume: number
  direction: 'buy' | 'sell'
  amount: number
  type: 'large' | 'super' | 'institutional'
}

export interface BlockTrade {
  timestamp: string
  price: number
  volume: number
  amount: number
  impact: number
}

export interface DarkPoolData {
  timestamp: string
  estimatedVolume: number
  priceImpact: number
  institutionalFlow: number
}

export interface VolumeAnalysisResult {
  onBalanceVolume: MoneyFlowResult[]
  chaikinMoneyFlow: MoneyFlowResult[]
  volumeWeightedAveragePrice: MoneyFlowResult[]
  accumulationDistribution: MoneyFlowResult[]
  moneyFlowIndex: MoneyFlowResult[]
}

/**
 * 量价分析计算器
 */
export class VolumeAnalysis {

  /**
   * 计算平衡成交量 (On Balance Volume)
   */
  static onBalanceVolume(data: PriceData[]): MoneyFlowResult[] {
    if (data.length < 2) return []
    
    const results: MoneyFlowResult[] = []
    let obv = 0
    
    for (let i = 1; i < data.length; i++) {
      const currentClose = data[i].close
      const prevClose = data[i - 1].close
      const volume = data[i].volume
      
      if (currentClose > prevClose) {
        obv += volume
      } else if (currentClose < prevClose) {
        obv -= volume
      }
      // 如果价格相等，OBV保持不变
      
      results.push({
        value: obv,
        timestamp: data[i].timestamp
      })
    }
    
    return results
  }

  /**
   * 计算Chaikin资金流量 (Chaikin Money Flow)
   */
  static chaikinMoneyFlow(data: PriceData[], period: number = 20): MoneyFlowResult[] {
    if (data.length < period) return []
    
    const results: MoneyFlowResult[] = []
    
    for (let i = period - 1; i < data.length; i++) {
      let sumMoneyFlowVolume = 0
      let sumVolume = 0
      
      for (let j = i - period + 1; j <= i; j++) {
        const candle = data[j]
        const high = candle.high
        const low = candle.low
        const close = candle.close
        const volume = candle.volume
        
        // 计算资金流量乘数
        const moneyFlowMultiplier = high === low ? 0 : ((close - low) - (high - close)) / (high - low)
        const moneyFlowVolume = moneyFlowMultiplier * volume
        
        sumMoneyFlowVolume += moneyFlowVolume
        sumVolume += volume
      }
      
      const cmf = sumVolume === 0 ? 0 : sumMoneyFlowVolume / sumVolume
      
      results.push({
        value: cmf,
        timestamp: data[i].timestamp
      })
    }
    
    return results
  }

  /**
   * 计算成交量加权平均价格 (VWAP)
   */
  static volumeWeightedAveragePrice(data: PriceData[], period?: number): MoneyFlowResult[] {
    if (data.length === 0) return []
    
    const results: MoneyFlowResult[] = []
    const useRollingVWAP = period !== undefined
    
    if (useRollingVWAP && period) {
      // 滚动VWAP
      for (let i = period - 1; i < data.length; i++) {
        let sumPriceVolume = 0
        let sumVolume = 0
        
        for (let j = i - period + 1; j <= i; j++) {
          const candle = data[j]
          const typicalPrice = (candle.high + candle.low + candle.close) / 3
          sumPriceVolume += typicalPrice * candle.volume
          sumVolume += candle.volume
        }
        
        const vwap = sumVolume === 0 ? 0 : sumPriceVolume / sumVolume
        
        results.push({
          value: vwap,
          timestamp: data[i].timestamp
        })
      }
    } else {
      // 累积VWAP (从第一个数据点开始)
      let cumulativePriceVolume = 0
      let cumulativeVolume = 0
      
      for (let i = 0; i < data.length; i++) {
        const candle = data[i]
        const typicalPrice = (candle.high + candle.low + candle.close) / 3
        
        cumulativePriceVolume += typicalPrice * candle.volume
        cumulativeVolume += candle.volume
        
        const vwap = cumulativeVolume === 0 ? 0 : cumulativePriceVolume / cumulativeVolume
        
        results.push({
          value: vwap,
          timestamp: candle.timestamp
        })
      }
    }
    
    return results
  }

  /**
   * 计算累积/派发线 (Accumulation/Distribution Line)
   */
  static accumulationDistribution(data: PriceData[]): MoneyFlowResult[] {
    if (data.length === 0) return []
    
    const results: MoneyFlowResult[] = []
    let adLine = 0
    
    for (let i = 0; i < data.length; i++) {
      const candle = data[i]
      const high = candle.high
      const low = candle.low
      const close = candle.close
      const volume = candle.volume
      
      // 计算资金流量乘数
      const moneyFlowMultiplier = high === low ? 0 : ((close - low) - (high - close)) / (high - low)
      const moneyFlowVolume = moneyFlowMultiplier * volume
      
      adLine += moneyFlowVolume
      
      results.push({
        value: adLine,
        timestamp: candle.timestamp
      })
    }
    
    return results
  }

  /**
   * 计算资金流量指数 (Money Flow Index)
   */
  static moneyFlowIndex(data: PriceData[], period: number = 14): MoneyFlowResult[] {
    if (data.length < period + 1) return []
    
    const results: MoneyFlowResult[] = []
    
    for (let i = period; i < data.length; i++) {
      let positiveMoneyFlow = 0
      let negativeMoneyFlow = 0
      
      for (let j = i - period + 1; j <= i; j++) {
        const currentCandle = data[j]
        const prevCandle = data[j - 1]
        
        const currentTypicalPrice = (currentCandle.high + currentCandle.low + currentCandle.close) / 3
        const prevTypicalPrice = (prevCandle.high + prevCandle.low + prevCandle.close) / 3
        
        const rawMoneyFlow = currentTypicalPrice * currentCandle.volume
        
        if (currentTypicalPrice > prevTypicalPrice) {
          positiveMoneyFlow += rawMoneyFlow
        } else if (currentTypicalPrice < prevTypicalPrice) {
          negativeMoneyFlow += rawMoneyFlow
        }
      }
      
      const moneyFlowRatio = negativeMoneyFlow === 0 ? 100 : positiveMoneyFlow / negativeMoneyFlow
      const mfi = 100 - (100 / (1 + moneyFlowRatio))
      
      results.push({
        value: mfi,
        timestamp: data[i].timestamp
      })
    }
    
    return results
  }

  /**
   * 检测大单交易
   */
  static detectLargeOrders(data: PriceData[], volumeThreshold: number = 1000000): LargeOrder[] {
    const results: LargeOrder[] = []
    
    // 计算平均成交量
    const avgVolume = data.reduce((sum, candle) => sum + candle.volume, 0) / data.length
    
    for (let i = 1; i < data.length; i++) {
      const candle = data[i]
      const prevCandle = data[i - 1]
      
      // 判断是否为大单
      if (candle.volume > volumeThreshold || candle.volume > avgVolume * 3) {
        const priceChange = candle.close - prevCandle.close
        const direction: 'buy' | 'sell' = priceChange >= 0 ? 'buy' : 'sell'
        
        let type: 'large' | 'super' | 'institutional' = 'large'
        if (candle.volume > avgVolume * 10) {
          type = 'institutional'
        } else if (candle.volume > avgVolume * 5) {
          type = 'super'
        }
        
        results.push({
          timestamp: candle.timestamp,
          price: candle.close,
          volume: candle.volume,
          direction,
          amount: candle.close * candle.volume,
          type
        })
      }
    }
    
    return results
  }

  /**
   * 分析大宗交易
   */
  static analyzeBlockTrades(data: PriceData[], minBlockSize: number = 10000): BlockTrade[] {
    const results: BlockTrade[] = []
    
    for (let i = 1; i < data.length; i++) {
      const candle = data[i]
      const prevCandle = data[i - 1]
      
      // 检测异常成交量
      if (candle.volume >= minBlockSize) {
        const priceImpact = Math.abs(candle.close - candle.open) / candle.open
        const volumeImpact = candle.volume / (prevCandle.volume || 1)
        
        results.push({
          timestamp: candle.timestamp,
          price: (candle.high + candle.low) / 2,
          volume: candle.volume,
          amount: candle.close * candle.volume,
          impact: priceImpact * volumeImpact
        })
      }
    }
    
    return results.sort((a, b) => b.impact - a.impact)
  }

  /**
   * 模拟暗池活动分析
   */
  static analyzeDarkPoolActivity(data: PriceData[]): DarkPoolData[] {
    const results: DarkPoolData[] = []
    
    for (let i = 5; i < data.length; i++) {
      const recentData = data.slice(i - 5, i + 1)
      
      // 计算价格波动与成交量的关系
      const priceVolatility = this.calculateVolatility(recentData)
      const volumeVariation = this.calculateVolumeVariation(recentData)
      
      // 如果价格波动小但成交量大，可能存在暗池交易
      const darkPoolIndicator = volumeVariation / (priceVolatility + 0.001)
      
      if (darkPoolIndicator > 2) {
        const estimatedDarkVolume = data[i].volume * 0.3 // 估算30%为暗池交易
        
        results.push({
          timestamp: data[i].timestamp,
          estimatedVolume: estimatedDarkVolume,
          priceImpact: priceVolatility,
          institutionalFlow: darkPoolIndicator
        })
      }
    }
    
    return results
  }

  /**
   * 计算价格波动率
   */
  private static calculateVolatility(data: PriceData[]): number {
    if (data.length < 2) return 0
    
    const returns = []
    for (let i = 1; i < data.length; i++) {
      const returnRate = (data[i].close - data[i - 1].close) / data[i - 1].close
      returns.push(returnRate)
    }
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
    
    return Math.sqrt(variance)
  }

  /**
   * 计算成交量变异系数
   */
  private static calculateVolumeVariation(data: PriceData[]): number {
    if (data.length === 0) return 0
    
    const volumes = data.map(d => d.volume)
    const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length
    const variance = volumes.reduce((sum, v) => sum + Math.pow(v - avgVolume, 2), 0) / volumes.length
    
    return avgVolume === 0 ? 0 : Math.sqrt(variance) / avgVolume
  }

  /**
   * 综合量价分析
   */
  static comprehensiveVolumeAnalysis(data: PriceData[], period: number = 20): VolumeAnalysisResult {
    return {
      onBalanceVolume: this.onBalanceVolume(data),
      chaikinMoneyFlow: this.chaikinMoneyFlow(data, period),
      volumeWeightedAveragePrice: this.volumeWeightedAveragePrice(data, period),
      accumulationDistribution: this.accumulationDistribution(data),
      moneyFlowIndex: this.moneyFlowIndex(data, period)
    }
  }
}

export default VolumeAnalysis
