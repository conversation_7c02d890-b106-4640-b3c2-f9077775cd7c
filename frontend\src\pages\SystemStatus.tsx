import React, { useState } from 'react'
import { Typography, Row, Col, Card, Tabs, Space, Tag, Button, Alert } from 'antd'
import {
  DashboardOutlined,
  BugOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons'
import PerformanceMonitor from '@/components/PerformanceMonitor'
import FunctionalityTester from '@/components/FunctionalityTester'
import { useRealtimeData } from '@/hooks/useRealtimeData'
import { useStockData } from '@/hooks/useStockData'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs

const SystemStatus: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')

  // 获取系统状态数据
  const { isConnected, stats: realtimeStats } = useRealtimeData()
  const { stats: stockStats } = useStockData()

  // 系统健康状态
  const systemHealth = {
    frontend: true,
    backend: true,
    database: true,
    realtime: isConnected,
    cache: true,
  }

  const healthyServices = Object.values(systemHealth).filter(Boolean).length
  const totalServices = Object.keys(systemHealth).length
  const overallHealth = healthyServices === totalServices

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <Space>
            <DashboardOutlined />
            系统状态监控
            <Tag color={overallHealth ? 'green' : 'orange'}>
              {overallHealth ? '系统正常' : '部分异常'}
            </Tag>
          </Space>
        </Title>
        <Paragraph type="secondary">
          实时监控系统性能、功能完整性和服务状态
        </Paragraph>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 系统概览 */}
        <TabPane
          tab={
            <span>
              <InfoCircleOutlined />
              系统概览
            </span>
          }
          key="overview"
        >
          <Row gutter={[24, 24]}>
            {/* 服务状态 */}
            <Col span={12}>
              <Card title="服务状态" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text>前端服务</Text>
                    <Tag color="green" icon={<CheckCircleOutlined />}>运行中</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text>后端API</Text>
                    <Tag color="green" icon={<CheckCircleOutlined />}>运行中</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text>数据库</Text>
                    <Tag color="green" icon={<CheckCircleOutlined />}>连接正常</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text>实时数据</Text>
                    <Tag color={isConnected ? 'green' : 'orange'} 
                         icon={isConnected ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}>
                      {isConnected ? '连接正常' : '模拟模式'}
                    </Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text>缓存系统</Text>
                    <Tag color="green" icon={<CheckCircleOutlined />}>运行中</Tag>
                  </div>
                </Space>
              </Card>
            </Col>

            {/* 数据统计 */}
            <Col span={12}>
              <Card title="数据统计" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>股票数据:</Text>
                    <Text strong>{stockStats.totalStocks} 只股票</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>自选股:</Text>
                    <Text strong>{stockStats.watchedStocks} 只</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>上涨股票:</Text>
                    <Text strong style={{ color: '#52c41a' }}>{stockStats.gainers} 只</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>下跌股票:</Text>
                    <Text strong style={{ color: '#ff4d4f' }}>{stockStats.losers} 只</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>缓存大小:</Text>
                    <Text strong>{stockStats.cacheSize} 项</Text>
                  </div>
                </Space>
              </Card>
            </Col>

            {/* 系统信息 */}
            <Col span={24}>
              <Card title="系统信息" size="small">
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                          v1.0.0
                        </div>
                        <div>系统版本</div>
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a' }}>
                          React 18
                        </div>
                        <div>前端框架</div>
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#722ed1' }}>
                          FastAPI
                        </div>
                        <div>后端框架</div>
                      </div>
                    </Card>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>

          {/* 系统健康提示 */}
          <div style={{ marginTop: 24 }}>
            {overallHealth ? (
              <Alert
                message="系统运行正常"
                description="所有服务运行正常，系统性能良好"
                type="success"
                showIcon
              />
            ) : (
              <Alert
                message="系统部分异常"
                description="部分服务可能存在问题，建议检查系统状态"
                type="warning"
                showIcon
              />
            )}
          </div>
        </TabPane>

        {/* 性能监控 */}
        <TabPane
          tab={
            <span>
              <DashboardOutlined />
              性能监控
            </span>
          }
          key="performance"
        >
          <PerformanceMonitor autoRefresh={true} refreshInterval={5000} />
        </TabPane>

        {/* 功能测试 */}
        <TabPane
          tab={
            <span>
              <BugOutlined />
              功能测试
            </span>
          }
          key="testing"
        >
          <FunctionalityTester />
        </TabPane>

        {/* 系统配置 */}
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              系统配置
            </span>
          }
          key="config"
        >
          <Row gutter={[24, 24]}>
            <Col span={12}>
              <Card title="前端配置" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>开发模式:</Text>
                    <Tag color="blue">启用</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>热重载:</Text>
                    <Tag color="green">启用</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>缓存策略:</Text>
                    <Tag color="orange">5分钟</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>自动刷新:</Text>
                    <Tag color="green">30秒</Tag>
                  </div>
                </Space>
              </Card>
            </Col>

            <Col span={12}>
              <Card title="后端配置" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>API端口:</Text>
                    <Tag>8000</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>数据库:</Text>
                    <Tag color="blue">SQLite</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>日志级别:</Text>
                    <Tag color="orange">INFO</Tag>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>CORS:</Text>
                    <Tag color="green">启用</Tag>
                  </div>
                </Space>
              </Card>
            </Col>

            <Col span={24}>
              <Card title="环境变量" size="small">
                <Row gutter={[16, 8]}>
                  <Col span={8}>
                    <Text code>NODE_ENV=development</Text>
                  </Col>
                  <Col span={8}>
                    <Text code>REACT_APP_API_URL=http://localhost:8000</Text>
                  </Col>
                  <Col span={8}>
                    <Text code>REACT_APP_VERSION=1.0.0</Text>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  )
}

export default SystemStatus
