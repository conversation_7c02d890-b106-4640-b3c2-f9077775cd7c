#!/usr/bin/env python3
"""
预警系统功能测试脚本
"""

import asyncio
import sys
import os
from datetime import date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.logging import setup_logging, get_logger
from app.services.alert_service import AlertRuleEngine, AlertNotificationService, AlertManagementService

logger = get_logger(__name__)


async def test_alert_management():
    """测试预警管理功能"""
    logger.info("=== 测试预警管理功能 ===")
    
    try:
        async with AlertManagementService() as alert_service:
            # 创建默认模板
            logger.info("创建默认预警模板...")
            await alert_service.create_default_templates()
            logger.info("✅ 默认模板创建完成")
            
            # 创建测试预警规则
            logger.info("创建测试预警规则...")
            
            # 价格预警规则
            price_rule_data = {
                "stock_code": "000001",
                "rule_name": "价格突破预警",
                "rule_type": "price",
                "alert_level": "high",
                "condition_type": ">",
                "target_value": 12.0,
                "trigger_frequency": "once",
                "notification_methods": {
                    "email": {"enabled": True},
                    "push": {"enabled": True}
                }
            }
            
            price_rule_id = await alert_service.create_alert_rule(price_rule_data)
            logger.info(f"✅ 价格预警规则创建成功，ID: {price_rule_id}")
            
            # 技术指标预警规则
            indicator_rule_data = {
                "stock_code": "000001",
                "rule_name": "RSI超买预警",
                "rule_type": "indicator",
                "alert_level": "medium",
                "condition_type": ">",
                "target_value": 80.0,
                "indicator_name": "rsi",
                "trigger_frequency": "daily",
                "notification_methods": {
                    "push": {"enabled": True}
                }
            }
            
            indicator_rule_id = await alert_service.create_alert_rule(indicator_rule_data)
            logger.info(f"✅ 技术指标预警规则创建成功，ID: {indicator_rule_id}")
            
            # AI信号预警规则
            ai_rule_data = {
                "stock_code": "000001",
                "rule_name": "AI买入信号",
                "rule_type": "ai",
                "alert_level": "high",
                "condition_type": ">",
                "target_value": 0.8,
                "rule_config": {
                    "check_predictions": True,
                    "direction": "up"
                },
                "trigger_frequency": "once",
                "notification_methods": {
                    "email": {"enabled": True},
                    "sms": {"enabled": True}
                }
            }
            
            ai_rule_id = await alert_service.create_alert_rule(ai_rule_data)
            logger.info(f"✅ AI信号预警规则创建成功，ID: {ai_rule_id}")
            
            # 查询创建的规则
            rules = await alert_service.get_alert_rules(stock_code="000001")
            logger.info(f"✅ 查询到 {len(rules)} 条预警规则")
            
            for rule in rules:
                logger.info(f"  - {rule.rule_name} ({rule.rule_type}) - {rule.alert_level}")
            
    except Exception as e:
        logger.error(f"预警管理测试失败: {e}")


async def test_alert_rule_engine():
    """测试预警规则引擎"""
    logger.info("=== 测试预警规则引擎 ===")
    
    test_stock = "000001"
    
    try:
        async with AlertRuleEngine() as rule_engine:
            # 测试价格预警评估
            logger.info("测试价格预警评估...")
            price_events = await rule_engine.evaluate_price_alerts(test_stock)
            logger.info(f"✅ 价格预警评估完成，触发 {len(price_events)} 个事件")
            
            for event in price_events:
                logger.info(f"  - {event['event_title']}: {event['event_message']}")
            
            # 测试技术指标预警评估
            logger.info("测试技术指标预警评估...")
            indicator_events = await rule_engine.evaluate_indicator_alerts(test_stock)
            logger.info(f"✅ 技术指标预警评估完成，触发 {len(indicator_events)} 个事件")
            
            for event in indicator_events:
                logger.info(f"  - {event['event_title']}: {event['event_message']}")
            
            # 测试AI信号预警评估
            logger.info("测试AI信号预警评估...")
            ai_events = await rule_engine.evaluate_ai_alerts(test_stock)
            logger.info(f"✅ AI信号预警评估完成，触发 {len(ai_events)} 个事件")
            
            for event in ai_events:
                logger.info(f"  - {event['event_title']}: {event['event_message']}")
            
            # 测试成交量预警评估
            logger.info("测试成交量预警评估...")
            volume_events = await rule_engine.evaluate_volume_alerts(test_stock)
            logger.info(f"✅ 成交量预警评估完成，触发 {len(volume_events)} 个事件")
            
            for event in volume_events:
                logger.info(f"  - {event['event_title']}: {event['event_message']}")
            
            # 统计总事件数
            total_events = len(price_events) + len(indicator_events) + len(ai_events) + len(volume_events)
            logger.info(f"总计触发 {total_events} 个预警事件")
            
    except Exception as e:
        logger.error(f"预警规则引擎测试失败: {e}")


async def test_alert_notifications():
    """测试预警通知功能"""
    logger.info("=== 测试预警通知功能 ===")
    
    try:
        # 创建模拟预警事件
        mock_event = {
            "rule_id": 1,
            "stock_code": "000001",
            "event_type": "price",
            "alert_level": "high",
            "event_title": "【价格预警】000001 价格突破",
            "event_message": "股票 000001 价格 12.50，突破上方 12.00。当前时间：2025-07-27",
            "trigger_value": 12.50,
            "trigger_data": {
                "current_price": 12.50,
                "target_value": 12.00,
                "condition_type": ">"
            },
            "market_data": {
                "realtime": None,
                "latest_kline": None
            }
        }
        
        async with AlertNotificationService() as notification_service:
            # 测试发送预警通知
            logger.info("测试发送预警通知...")
            success = await notification_service.send_alert_notification(mock_event)
            
            if success:
                logger.info("✅ 预警通知发送成功")
            else:
                logger.warning("⚠️ 预警通知发送失败")
            
    except Exception as e:
        logger.error(f"预警通知测试失败: {e}")


async def test_alert_queries():
    """测试预警查询功能"""
    logger.info("=== 测试预警查询功能 ===")
    
    try:
        async with AlertManagementService() as alert_service:
            # 查询所有预警规则
            logger.info("查询所有预警规则...")
            all_rules = await alert_service.get_alert_rules()
            logger.info(f"✅ 查询到 {len(all_rules)} 条预警规则")
            
            # 按类型查询
            price_rules = await alert_service.get_alert_rules(rule_type="price")
            logger.info(f"✅ 价格预警规则: {len(price_rules)} 条")
            
            indicator_rules = await alert_service.get_alert_rules(rule_type="indicator")
            logger.info(f"✅ 技术指标预警规则: {len(indicator_rules)} 条")
            
            ai_rules = await alert_service.get_alert_rules(rule_type="ai")
            logger.info(f"✅ AI信号预警规则: {len(ai_rules)} 条")
            
            # 查询预警事件
            logger.info("查询预警事件...")
            events = await alert_service.get_alert_events(limit=10)
            logger.info(f"✅ 查询到 {len(events)} 条预警事件")
            
            for event in events[:3]:  # 显示前3条
                logger.info(f"  - {event.event_title} ({event.alert_level}) - {event.triggered_at}")
            
    except Exception as e:
        logger.error(f"预警查询测试失败: {e}")


async def test_data_requirements():
    """测试数据需求"""
    logger.info("=== 测试数据需求 ===")
    
    try:
        # 检查K线数据
        from app.services.data_storage import DataStorageService
        async with DataStorageService() as data_storage:
            klines = await data_storage.get_kline_data("000001", "daily", limit=5)
            realtime = await data_storage.get_realtime_data("000001")
        
        if klines:
            logger.info(f"✅ 找到 {len(klines)} 条K线数据")
            latest_kline = klines[-1]
            logger.info(f"最新K线: {latest_kline.trade_date} 收盘价: {latest_kline.close_price}")
        else:
            logger.warning("❌ 没有找到K线数据，价格预警功能受限")
        
        if realtime:
            logger.info(f"✅ 找到实时数据: 当前价格 {realtime.current_price}")
        else:
            logger.warning("❌ 没有找到实时数据，将使用K线数据")
        
        # 检查技术指标数据
        from app.services.indicator_storage import IndicatorStorageService
        async with IndicatorStorageService() as indicator_storage:
            indicators = await indicator_storage.get_indicators("000001", "daily", limit=5)
        
        if indicators:
            logger.info(f"✅ 找到 {len(indicators)} 条技术指标数据")
            latest_indicator = indicators[-1]
            logger.info(f"最新指标: RSI={latest_indicator.rsi}, MACD={latest_indicator.macd}")
        else:
            logger.warning("❌ 没有找到技术指标数据，技术指标预警功能受限")
        
        # 检查AI预测数据
        from app.services.ai_storage import AIPredictionStorageService
        async with AIPredictionStorageService() as ai_storage:
            predictions = await ai_storage.get_predictions(stock_code="000001", limit=3)
            patterns = await ai_storage.get_pattern_recognitions(stock_code="000001", limit=3)
        
        if predictions:
            logger.info(f"✅ 找到 {len(predictions)} 条AI预测数据")
        else:
            logger.warning("❌ 没有找到AI预测数据，AI预警功能受限")
        
        if patterns:
            logger.info(f"✅ 找到 {len(patterns)} 条形态识别数据")
        else:
            logger.warning("❌ 没有找到形态识别数据，形态预警功能受限")
            
    except Exception as e:
        logger.error(f"数据需求测试失败: {e}")


async def test_comprehensive_monitoring():
    """测试综合监控功能"""
    logger.info("=== 测试综合监控功能 ===")
    
    test_stock = "000001"
    
    try:
        # 先创建一些测试规则
        async with AlertManagementService() as alert_service:
            await alert_service.create_default_templates()
        
        # 执行综合监控
        async with AlertRuleEngine() as rule_engine:
            all_events = []
            
            # 评估所有类型的预警
            price_events = await rule_engine.evaluate_price_alerts(test_stock)
            indicator_events = await rule_engine.evaluate_indicator_alerts(test_stock)
            ai_events = await rule_engine.evaluate_ai_alerts(test_stock)
            volume_events = await rule_engine.evaluate_volume_alerts(test_stock)
            
            all_events.extend(price_events)
            all_events.extend(indicator_events)
            all_events.extend(ai_events)
            all_events.extend(volume_events)
            
            logger.info(f"✅ 综合监控完成，共触发 {len(all_events)} 个预警事件")
            
            # 按级别统计
            level_stats = {}
            for event in all_events:
                level = event["alert_level"]
                level_stats[level] = level_stats.get(level, 0) + 1
            
            logger.info(f"预警级别分布: {level_stats}")
            
            # 发送通知
            notification_count = 0
            async with AlertNotificationService() as notification_service:
                for event in all_events:
                    try:
                        if await notification_service.send_alert_notification(event):
                            notification_count += 1
                    except Exception as e:
                        logger.error(f"发送通知失败: {e}")
            
            logger.info(f"✅ 成功发送 {notification_count} 条预警通知")
            
    except Exception as e:
        logger.error(f"综合监控测试失败: {e}")


async def main():
    """主测试函数"""
    setup_logging()
    logger.info("🚨 开始预警系统功能测试")
    
    try:
        # 1. 测试数据需求
        await test_data_requirements()
        
        # 2. 测试预警管理功能
        await test_alert_management()
        
        # 3. 测试预警规则引擎
        await test_alert_rule_engine()
        
        # 4. 测试预警通知功能
        await test_alert_notifications()
        
        # 5. 测试预警查询功能
        await test_alert_queries()
        
        # 6. 测试综合监控功能
        await test_comprehensive_monitoring()
        
        logger.info("✅ 所有预警系统测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
