#!/usr/bin/env python3
"""
真实A股数据初始化脚本
使用akshare获取真实的A股数据
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.data_manager import DataManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """主函数"""
    logger.info("🚀 开始真实A股数据初始化")
    
    try:
        data_manager = DataManager()
        
        # 步骤1: 初始化股票列表
        logger.info("📋 步骤1: 初始化股票列表")
        stock_count = await data_manager.initialize_stock_list(force_update=True)
        logger.info(f"✅ 股票列表初始化完成，处理了 {stock_count} 只股票")
        
        # 步骤2: 获取数据统计
        logger.info("📊 步骤2: 获取当前数据统计")
        stats = await data_manager.get_data_statistics()
        logger.info(f"📈 当前数据统计:")
        logger.info(f"   总股票数: {stats['stocks']['total']}")
        logger.info(f"   活跃股票数: {stats['stocks']['active']}")
        logger.info(f"   有数据股票数: {stats['stocks']['with_data']}")
        logger.info(f"   K线记录数: {stats['klines']['total']}")
        
        # 步骤3: 更新热门股票的历史数据
        logger.info("📈 步骤3: 更新热门股票历史数据")
        
        # 选择一些热门股票进行数据更新
        popular_stocks = [
            "000001",  # 平安银行
            "000002",  # 万科A
            "600000",  # 浦发银行
            "600036",  # 招商银行
            "600519",  # 贵州茅台
            "000858",  # 五粮液
            "300015",  # 爱尔眼科
            "002415",  # 海康威视
            "000725",  # 京东方A
            "002594",  # 比亚迪
            "600276",  # 恒瑞医药
            "000063",  # 中兴通讯
            "002230",  # 科大讯飞
            "300059",  # 东方财富
            "600887",  # 伊利股份
        ]
        
        # 获取最近3个月的数据
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=90)
        
        logger.info(f"📅 数据日期范围: {start_date} 到 {end_date}")
        logger.info(f"🎯 目标股票: {len(popular_stocks)} 只")
        
        result = await data_manager.update_stock_data(
            stock_codes=popular_stocks,
            start_date=start_date,
            end_date=end_date,
            period='daily'
        )
        
        logger.info(f"✅ 热门股票数据更新完成:")
        logger.info(f"   新增记录: {result['added']}")
        logger.info(f"   更新记录: {result['updated']}")
        logger.info(f"   错误数量: {result['errors']}")
        
        # 步骤4: 获取更新后的统计信息
        logger.info("📊 步骤4: 获取更新后的数据统计")
        final_stats = await data_manager.get_data_statistics()
        logger.info(f"📈 最终数据统计:")
        logger.info(f"   总股票数: {final_stats['stocks']['total']}")
        logger.info(f"   活跃股票数: {final_stats['stocks']['active']}")
        logger.info(f"   有数据股票数: {final_stats['stocks']['with_data']}")
        logger.info(f"   K线记录数: {final_stats['klines']['total']}")
        logger.info(f"   数据覆盖率: {final_stats['stocks']['coverage_rate']}%")
        logger.info(f"   最新数据日期: {final_stats['klines']['latest_date']}")
        
        # 步骤5: 数据质量检查
        logger.info("🔍 步骤5: 数据质量检查")
        
        if final_stats['klines']['total'] > 0:
            avg_records = final_stats['klines']['total'] / max(final_stats['stocks']['with_data'], 1)
            logger.info(f"📊 平均每只股票记录数: {avg_records:.1f}")
            
            if avg_records >= 60:  # 大约3个月的交易日
                logger.info("✅ 数据质量良好")
            elif avg_records >= 20:
                logger.info("⚠️ 数据质量一般，建议获取更多历史数据")
            else:
                logger.info("❌ 数据质量较差，需要获取更多数据")
        
        logger.info("🎉 真实A股数据初始化完成！")
        
        # 提供后续建议
        logger.info("\n📝 后续建议:")
        logger.info("1. 可以通过API /api/v1/data/stats 查看数据统计")
        logger.info("2. 可以通过API /api/v1/data/klines/update 更新更多股票数据")
        logger.info("3. 建议设置定时任务每日更新数据")
        logger.info("4. 前端现在可以连接到真实的后端API获取数据")
        
    except Exception as e:
        logger.error(f"❌ 真实数据初始化失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
