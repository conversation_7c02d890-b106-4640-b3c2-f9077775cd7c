#!/usr/bin/env python3
"""
智能股票分析平台演示脚本
演示系统的主要功能和API调用
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import time

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

class StockAnalyzerDemo:
    def __init__(self):
        self.session = None
        self.token = None
        self.user_id = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def print_section(self, title):
        """打印章节标题"""
        print(f"\n{'='*60}")
        print(f"🎯 {title}")
        print(f"{'='*60}")
    
    def print_step(self, step):
        """打印步骤"""
        print(f"\n📌 {step}")
        print("-" * 40)
    
    async def register_and_login(self):
        """用户注册和登录演示"""
        self.print_section("用户认证系统演示")
        
        # 注册用户
        self.print_step("1. 用户注册")
        register_data = {
            "email": "<EMAIL>",
            "password": "DemoPassword123!",
            "username": "demo_user",
            "full_name": "演示用户"
        }
        
        async with self.session.post(f"{BASE_URL}/auth/register", json=register_data) as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"✅ 注册成功: {result['message']}")
                self.user_id = result['data']['user_id']
            else:
                print(f"⚠️ 注册失败或用户已存在")
        
        # 用户登录
        self.print_step("2. 用户登录")
        login_data = {
            "username": "demo_user",
            "password": "DemoPassword123!"
        }
        
        async with self.session.post(f"{BASE_URL}/auth/login", json=login_data) as resp:
            if resp.status == 200:
                result = await resp.json()
                self.token = result['data']['access_token']
                print(f"✅ 登录成功")
                print(f"📊 用户信息: {result['data']['user']['username']}")
            else:
                print(f"❌ 登录失败")
                return False
        
        return True
    
    async def demo_stock_data(self):
        """股票数据功能演示"""
        self.print_section("股票数据系统演示")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # 获取股票列表
        self.print_step("1. 获取股票列表")
        async with self.session.get(f"{BASE_URL}/stocks/list", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                stocks = result['data']['stocks'][:5]  # 只显示前5个
                print(f"✅ 获取到 {len(result['data']['stocks'])} 只股票")
                for stock in stocks:
                    print(f"   📈 {stock['code']} - {stock['name']}")
            else:
                print("❌ 获取股票列表失败")
                return
        
        # 获取股票实时价格
        self.print_step("2. 获取实时股票价格")
        stock_code = "000001"  # 平安银行
        async with self.session.get(f"{BASE_URL}/stocks/{stock_code}/price", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                price_data = result['data']
                print(f"✅ {stock_code} 实时价格信息:")
                print(f"   💰 当前价格: {price_data['current_price']}")
                print(f"   📊 涨跌幅: {price_data['change_percent']:.2f}%")
                print(f"   📈 今日最高: {price_data['high']}")
                print(f"   📉 今日最低: {price_data['low']}")
            else:
                print(f"❌ 获取 {stock_code} 价格失败")
    
    async def demo_technical_indicators(self):
        """技术指标演示"""
        self.print_section("技术指标计算演示")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        stock_code = "000001"
        
        self.print_step("1. 计算技术指标")
        async with self.session.get(f"{BASE_URL}/indicators/{stock_code}/calculate", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                indicators = result['data']
                print(f"✅ {stock_code} 技术指标:")
                
                # 显示主要指标
                if 'ma' in indicators:
                    ma_data = indicators['ma']
                    print(f"   📊 移动平均线:")
                    print(f"      MA5: {ma_data.get('ma5', 'N/A')}")
                    print(f"      MA20: {ma_data.get('ma20', 'N/A')}")
                
                if 'rsi' in indicators:
                    rsi_data = indicators['rsi']
                    print(f"   📈 RSI指标: {rsi_data.get('rsi14', 'N/A')}")
                
                if 'macd' in indicators:
                    macd_data = indicators['macd']
                    print(f"   📊 MACD指标:")
                    print(f"      MACD: {macd_data.get('macd', 'N/A')}")
                    print(f"      信号线: {macd_data.get('signal', 'N/A')}")
            else:
                print(f"❌ 计算技术指标失败")
    
    async def demo_ai_prediction(self):
        """AI预测演示"""
        self.print_section("AI智能预测演示")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        stock_code = "000001"
        
        self.print_step("1. 获取AI价格预测")
        async with self.session.post(f"{BASE_URL}/ai/{stock_code}/predict", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                prediction = result['data']
                print(f"✅ {stock_code} AI预测结果:")
                print(f"   🎯 预测价格: {prediction['predicted_price']}")
                print(f"   📊 置信度: {prediction['confidence']:.2%}")
                print(f"   📈 趋势: {prediction['trend']}")
                print(f"   ⏰ 预测时间: {prediction['prediction_time']}")
            else:
                print(f"❌ AI预测失败")
        
        self.print_step("2. 获取交易信号")
        async with self.session.get(f"{BASE_URL}/ai/{stock_code}/signals", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                signals = result['data']['signals'][:3]  # 显示最近3个信号
                print(f"✅ 最近交易信号:")
                for signal in signals:
                    print(f"   🚦 {signal['signal_type']} - 强度: {signal['strength']:.2f} - {signal['timestamp']}")
            else:
                print(f"❌ 获取交易信号失败")
    
    async def demo_stock_screening(self):
        """智能选股演示"""
        self.print_section("智能选股系统演示")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        self.print_step("1. 获取选股策略")
        async with self.session.get(f"{BASE_URL}/selection/strategies", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                strategies = result['data']['strategies']
                print(f"✅ 可用选股策略 ({len(strategies)} 个):")
                for strategy in strategies:
                    print(f"   🎯 {strategy['display_name']} - {strategy['description']}")
            else:
                print(f"❌ 获取选股策略失败")
        
        self.print_step("2. 执行选股筛选")
        screen_params = {
            "strategy": "ma_crossover",
            "parameters": {
                "short_ma": 5,
                "long_ma": 20
            },
            "limit": 10
        }
        
        async with self.session.post(f"{BASE_URL}/selection/screen", json=screen_params, headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                stocks = result['data']['stocks'][:5]  # 显示前5个结果
                print(f"✅ 选股结果 (显示前5个):")
                for stock in stocks:
                    print(f"   📈 {stock['code']} - {stock['name']} (评分: {stock['score']:.2f})")
            else:
                print(f"❌ 选股筛选失败")
    
    async def demo_alerts(self):
        """预警系统演示"""
        self.print_section("智能预警系统演示")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        self.print_step("1. 创建价格预警")
        alert_data = {
            "stock_code": "000001",
            "alert_type": "price",
            "condition": "greater_than",
            "threshold": 15.0,
            "message": "平安银行价格突破15元"
        }
        
        async with self.session.post(f"{BASE_URL}/alerts/rules", json=alert_data, headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"✅ 预警创建成功: {result['data']['message']}")
            else:
                print(f"❌ 创建预警失败")
        
        self.print_step("2. 获取预警规则")
        async with self.session.get(f"{BASE_URL}/alerts/rules", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                rules = result['data']['rules'][:3]  # 显示前3个
                print(f"✅ 当前预警规则:")
                for rule in rules:
                    print(f"   ⚠️ {rule['stock_code']} - {rule['message']}")
            else:
                print(f"❌ 获取预警规则失败")
    
    async def demo_analytics(self):
        """统计分析演示"""
        self.print_section("统计分析功能演示")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        self.print_step("1. 获取分析策略")
        async with self.session.get(f"{BASE_URL}/analytics/strategies", headers=headers) as resp:
            if resp.status == 200:
                result = await resp.json()
                strategies = result['data']['strategies']
                print(f"✅ 分析策略 ({len(strategies)} 个):")
                for strategy in strategies:
                    print(f"   📊 {strategy['name']} - {strategy['description']}")
            else:
                print(f"❌ 获取分析策略失败")
    
    async def run_demo(self):
        """运行完整演示"""
        print("🚀 智能股票分析平台功能演示")
        print("=" * 60)
        print("本演示将展示系统的主要功能模块")
        print("请确保后端服务正在运行 (http://localhost:8000)")
        print("=" * 60)
        
        # 检查服务状态
        try:
            async with self.session.get(f"{BASE_URL}/health") as resp:
                if resp.status == 200:
                    print("✅ 后端服务连接正常")
                else:
                    print("❌ 后端服务连接失败")
                    return
        except Exception as e:
            print(f"❌ 无法连接到后端服务: {e}")
            return
        
        # 执行演示步骤
        if await self.register_and_login():
            await self.demo_stock_data()
            await self.demo_technical_indicators()
            await self.demo_ai_prediction()
            await self.demo_stock_screening()
            await self.demo_alerts()
            await self.demo_analytics()
            
            print("\n" + "="*60)
            print("🎉 演示完成！")
            print("✅ 所有主要功能模块演示成功")
            print("🌐 前端应用: http://localhost:3000")
            print("📚 API文档: http://localhost:8000/docs")
            print("="*60)
        else:
            print("❌ 用户认证失败，演示终止")

async def main():
    """主函数"""
    async with StockAnalyzerDemo() as demo:
        await demo.run_demo()

if __name__ == "__main__":
    asyncio.run(main())
