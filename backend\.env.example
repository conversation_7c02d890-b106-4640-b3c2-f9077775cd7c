# 数据库配置
DATABASE_TYPE=sqlite
DATABASE_URL=sqlite+aiosqlite:///data/stock_analyzer.db
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=stock_analyzer
POSTGRES_PORT=5432
SQLITE_DB_PATH=data/stock_analyzer.db

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# DeepSeek AI配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat

# 数据源配置
TUSHARE_TOKEN=your_tushare_token_here
AKSHARE_ENABLED=true
DATA_UPDATE_INTERVAL=3600

# 系统配置
SECRET_KEY=your_secret_key_here
DEBUG=true
LOG_LEVEL=INFO
API_V1_STR=/api/v1

# 缓存配置
CACHE_TTL=300
STOCK_DATA_CACHE_TTL=1800
INDICATOR_CACHE_TTL=600

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://localhost:3001"]
ALLOWED_HOSTS=["localhost","127.0.0.1"]

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 安全配置
ACCESS_TOKEN_EXPIRE_MINUTES=11520
REFRESH_TOKEN_EXPIRE_MINUTES=43200

# 数据更新配置
DATA_UPDATE_INTERVAL=300
MARKET_HOURS_START=09:30
MARKET_HOURS_END=15:00
