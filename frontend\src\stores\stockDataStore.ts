/**
 * 股票数据状态管理
 * 使用Zustand进行全局状态管理
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { stockDataService, StockInfo } from '@/services/stockDataService'
import { PriceData } from '@/utils/advancedIndicators'

export interface StockDataState {
  // 当前选中的股票
  selectedStock: StockInfo | null
  
  // 价格数据
  priceData: PriceData[]
  
  // 自选股列表
  watchlist: string[]
  
  // 搜索历史
  searchHistory: string[]
  
  // 加载状态
  loading: boolean
  
  // 错误信息
  error: string | null
  
  // 最后更新时间
  lastUpdated: number | null
}

export interface StockDataActions {
  // 搜索并设置股票
  searchStock: (symbol: string) => Promise<void>
  
  // 添加到自选股
  addToWatchlist: (symbol: string) => void
  
  // 从自选股移除
  removeFromWatchlist: (symbol: string) => void
  
  // 清除错误
  clearError: () => void
  
  // 刷新数据
  refreshData: () => Promise<void>
  
  // 清除所有数据
  clearData: () => void
  
  // 设置加载状态
  setLoading: (loading: boolean) => void
}

type StockDataStore = StockDataState & StockDataActions

export const useStockDataStore = create<StockDataStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      selectedStock: null,
      priceData: [],
      watchlist: ['000001', '000002', '600000', '600036'],
      searchHistory: [],
      loading: false,
      error: null,
      lastUpdated: null,

      // Actions
      searchStock: async (symbol: string) => {
        const trimmedSymbol = symbol.trim().toUpperCase()
        if (!trimmedSymbol) return

        set({ loading: true, error: null })

        try {
          const result = await stockDataService.getStockData(trimmedSymbol)
          
          if (result.success) {
            set({
              selectedStock: result.stock,
              priceData: result.priceData,
              loading: false,
              lastUpdated: Date.now()
            })

            // 添加到搜索历史
            const { searchHistory } = get()
            const newHistory = [trimmedSymbol, ...searchHistory.filter(s => s !== trimmedSymbol)].slice(0, 10)
            set({ searchHistory: newHistory })

          } else {
            set({
              loading: false,
              error: result.message || '获取股票数据失败'
            })
          }
        } catch (error) {
          console.error('搜索股票失败:', error)
          set({
            loading: false,
            error: '网络错误，请检查连接后重试'
          })
        }
      },

      addToWatchlist: (symbol: string) => {
        const { watchlist } = get()
        const upperSymbol = symbol.toUpperCase()
        
        if (!watchlist.includes(upperSymbol)) {
          set({ watchlist: [...watchlist, upperSymbol] })
        }
      },

      removeFromWatchlist: (symbol: string) => {
        const { watchlist } = get()
        const upperSymbol = symbol.toUpperCase()
        
        set({ watchlist: watchlist.filter(s => s !== upperSymbol) })
      },

      clearError: () => {
        set({ error: null })
      },

      refreshData: async () => {
        const { selectedStock } = get()
        if (selectedStock) {
          // 清除缓存并重新获取数据
          stockDataService.clearCache()
          await get().searchStock(selectedStock.code)
        }
      },

      clearData: () => {
        set({
          selectedStock: null,
          priceData: [],
          error: null,
          lastUpdated: null
        })
      },

      setLoading: (loading: boolean) => {
        set({ loading })
      }
    }),
    {
      name: 'stock-data-store', // 本地存储的key
      storage: createJSONStorage(() => localStorage),
      // 只持久化部分状态
      partialize: (state) => ({
        watchlist: state.watchlist,
        searchHistory: state.searchHistory,
        selectedStock: state.selectedStock,
        lastUpdated: state.lastUpdated
      }),
    }
  )
)

// 导出选择器hooks，用于性能优化
export const useSelectedStock = () => useStockDataStore(state => state.selectedStock)
export const usePriceData = () => useStockDataStore(state => state.priceData)
export const useWatchlist = () => useStockDataStore(state => state.watchlist)
export const useSearchHistory = () => useStockDataStore(state => state.searchHistory)
export const useLoading = () => useStockDataStore(state => state.loading)
export const useError = () => useStockDataStore(state => state.error)

// 导出actions
export const useStockActions = () => useStockDataStore(state => ({
  searchStock: state.searchStock,
  addToWatchlist: state.addToWatchlist,
  removeFromWatchlist: state.removeFromWatchlist,
  clearError: state.clearError,
  refreshData: state.refreshData,
  clearData: state.clearData,
  setLoading: state.setLoading
}))

// 数据验证和清理工具
export const stockDataUtils = {
  // 检查数据是否过期（超过5分钟）
  isDataStale: (lastUpdated: number | null): boolean => {
    if (!lastUpdated) return true
    return Date.now() - lastUpdated > 5 * 60 * 1000
  },

  // 格式化股票代码
  formatSymbol: (symbol: string): string => {
    return symbol.trim().toUpperCase()
  },

  // 验证股票代码格式
  isValidSymbol: (symbol: string): boolean => {
    const formatted = symbol.trim()
    // 支持中国A股代码（6位数字）和美股代码（1-5个字母）
    return /^[0-9]{6}$/.test(formatted) || /^[A-Z]{1,5}$/.test(formatted.toUpperCase())
  },

  // 获取数据新鲜度描述
  getDataFreshness: (lastUpdated: number | null): string => {
    if (!lastUpdated) return '无数据'
    
    const minutes = Math.floor((Date.now() - lastUpdated) / (1000 * 60))
    
    if (minutes < 1) return '刚刚更新'
    if (minutes < 5) return `${minutes}分钟前`
    if (minutes < 60) return `${minutes}分钟前（建议刷新）`
    
    const hours = Math.floor(minutes / 60)
    return `${hours}小时前（数据可能过期）`
  }
}

export default useStockDataStore
