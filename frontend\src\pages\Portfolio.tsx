import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  DatePicker,
  message,
  Tabs,
  Progress,
  Divider,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TrophyOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface Portfolio {
  id: number
  name: string
  description: string
  initial_capital: number
  current_value: number
  total_return: number
  total_return_pct: number
  created_at: string
}

interface Holding {
  id: number
  stock_code: string
  stock_name: string
  shares: number
  avg_cost: number
  current_price: number
  market_value: number
  unrealized_pnl: number
  unrealized_pnl_pct: number
}

interface Transaction {
  id: number
  stock_code: string
  stock_name: string
  transaction_type: 'buy' | 'sell'
  shares: number
  price: number
  amount: number
  transaction_date: string
}

const Portfolio: React.FC = () => {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([])
  const [selectedPortfolio, setSelectedPortfolio] = useState<Portfolio | null>(null)
  const [holdings, setHoldings] = useState<Holding[]>([])
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [transactionModalVisible, setTransactionModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [transactionForm] = Form.useForm()

  useEffect(() => {
    loadPortfolios()
  }, [])

  const loadPortfolios = async () => {
    setLoading(true)
    try {
      // 模拟数据
      const mockPortfolios: Portfolio[] = [
        {
          id: 1,
          name: '稳健投资组合',
          description: '以大盘蓝筹股为主的稳健投资组合',
          initial_capital: 100000,
          current_value: 115000,
          total_return: 15000,
          total_return_pct: 15.0,
          created_at: '2024-01-01',
        },
        {
          id: 2,
          name: '成长投资组合',
          description: '专注于成长股的投资组合',
          initial_capital: 50000,
          current_value: 58000,
          total_return: 8000,
          total_return_pct: 16.0,
          created_at: '2024-02-01',
        },
      ]
      setPortfolios(mockPortfolios)
      if (mockPortfolios.length > 0) {
        setSelectedPortfolio(mockPortfolios[0])
        loadPortfolioDetails(mockPortfolios[0].id)
      }
    } catch (error) {
      message.error('加载投资组合失败')
    } finally {
      setLoading(false)
    }
  }

  const loadPortfolioDetails = async (portfolioId: number) => {
    try {
      // 模拟持仓数据
      const mockHoldings: Holding[] = [
        {
          id: 1,
          stock_code: '000001',
          stock_name: '平安银行',
          shares: 1000,
          avg_cost: 12.00,
          current_price: 12.45,
          market_value: 12450,
          unrealized_pnl: 450,
          unrealized_pnl_pct: 3.75,
        },
        {
          id: 2,
          stock_code: '600036',
          stock_name: '招商银行',
          shares: 500,
          avg_cost: 44.00,
          current_price: 45.67,
          market_value: 22835,
          unrealized_pnl: 835,
          unrealized_pnl_pct: 3.80,
        },
      ]

      // 模拟交易记录
      const mockTransactions: Transaction[] = [
        {
          id: 1,
          stock_code: '000001',
          stock_name: '平安银行',
          transaction_type: 'buy',
          shares: 1000,
          price: 12.00,
          amount: 12000,
          transaction_date: '2024-01-15',
        },
        {
          id: 2,
          stock_code: '600036',
          stock_name: '招商银行',
          transaction_type: 'buy',
          shares: 500,
          price: 44.00,
          amount: 22000,
          transaction_date: '2024-01-20',
        },
      ]

      setHoldings(mockHoldings)
      setTransactions(mockTransactions)
    } catch (error) {
      message.error('加载投资组合详情失败')
    }
  }

  const handleCreatePortfolio = async (values: any) => {
    try {
      // 这里应该调用API创建投资组合
      message.success('投资组合创建成功')
      setCreateModalVisible(false)
      form.resetFields()
      loadPortfolios()
    } catch (error) {
      message.error('创建投资组合失败')
    }
  }

  const handleAddTransaction = async (values: any) => {
    try {
      // 这里应该调用API添加交易记录
      message.success('交易记录添加成功')
      setTransactionModalVisible(false)
      transactionForm.resetFields()
      if (selectedPortfolio) {
        loadPortfolioDetails(selectedPortfolio.id)
      }
    } catch (error) {
      message.error('添加交易记录失败')
    }
  }

  const portfolioColumns: ColumnsType<Portfolio> = [
    {
      title: '组合名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setSelectedPortfolio(record)
            loadPortfolioDetails(record.id)
          }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '初始资金',
      dataIndex: 'initial_capital',
      key: 'initial_capital',
      render: (value) => `¥${value.toLocaleString()}`,
    },
    {
      title: '当前价值',
      dataIndex: 'current_value',
      key: 'current_value',
      render: (value) => `¥${value.toLocaleString()}`,
    },
    {
      title: '总收益',
      dataIndex: 'total_return',
      key: 'total_return',
      render: (value, record) => (
        <Space>
          <Text type={value >= 0 ? 'success' : 'danger'}>
            ¥{value.toLocaleString()}
          </Text>
          <Text type={record.total_return_pct >= 0 ? 'success' : 'danger'}>
            ({record.total_return_pct >= 0 ? '+' : ''}{record.total_return_pct.toFixed(2)}%)
          </Text>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button icon={<EditOutlined />} size="small">
            编辑
          </Button>
          <Button icon={<DeleteOutlined />} size="small" danger>
            删除
          </Button>
        </Space>
      ),
    },
  ]

  const holdingColumns: ColumnsType<Holding> = [
    {
      title: '股票',
      key: 'stock',
      render: (_, record) => (
        <Space>
          <Text strong>{record.stock_code}</Text>
          <Text>{record.stock_name}</Text>
        </Space>
      ),
    },
    {
      title: '持股数量',
      dataIndex: 'shares',
      key: 'shares',
      render: (value) => value.toLocaleString(),
    },
    {
      title: '平均成本',
      dataIndex: 'avg_cost',
      key: 'avg_cost',
      render: (value) => `¥${value.toFixed(2)}`,
    },
    {
      title: '当前价格',
      dataIndex: 'current_price',
      key: 'current_price',
      render: (value) => `¥${value.toFixed(2)}`,
    },
    {
      title: '市值',
      dataIndex: 'market_value',
      key: 'market_value',
      render: (value) => `¥${value.toLocaleString()}`,
    },
    {
      title: '浮动盈亏',
      key: 'unrealized_pnl',
      render: (_, record) => (
        <Space>
          <Text type={record.unrealized_pnl >= 0 ? 'success' : 'danger'}>
            ¥{record.unrealized_pnl.toLocaleString()}
          </Text>
          <Text type={record.unrealized_pnl_pct >= 0 ? 'success' : 'danger'}>
            ({record.unrealized_pnl_pct >= 0 ? '+' : ''}{record.unrealized_pnl_pct.toFixed(2)}%)
          </Text>
        </Space>
      ),
    },
  ]

  const transactionColumns: ColumnsType<Transaction> = [
    {
      title: '股票',
      key: 'stock',
      render: (_, record) => (
        <Space>
          <Text strong>{record.stock_code}</Text>
          <Text>{record.stock_name}</Text>
        </Space>
      ),
    },
    {
      title: '操作',
      dataIndex: 'transaction_type',
      key: 'transaction_type',
      render: (value) => (
        <Tag color={value === 'buy' ? 'green' : 'red'}>
          {value === 'buy' ? '买入' : '卖出'}
        </Tag>
      ),
    },
    {
      title: '数量',
      dataIndex: 'shares',
      key: 'shares',
      render: (value) => value.toLocaleString(),
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (value) => `¥${value.toFixed(2)}`,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (value) => `¥${value.toLocaleString()}`,
    },
    {
      title: '交易时间',
      dataIndex: 'transaction_date',
      key: 'transaction_date',
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>投资组合管理</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建投资组合
        </Button>
      </div>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="我的投资组合">
            <Table
              dataSource={portfolios}
              columns={portfolioColumns}
              loading={loading}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </Col>

        {selectedPortfolio && (
          <>
            <Col span={24}>
              <Card title={`${selectedPortfolio.name} - 概览`}>
                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic
                      title="初始资金"
                      value={selectedPortfolio.initial_capital}
                      prefix={<DollarOutlined />}
                      suffix="¥"
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="当前价值"
                      value={selectedPortfolio.current_value}
                      prefix={<DollarOutlined />}
                      suffix="¥"
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="总收益"
                      value={selectedPortfolio.total_return}
                      prefix={selectedPortfolio.total_return >= 0 ? <RiseOutlined /> : <FallOutlined />}
                      suffix="¥"
                      valueStyle={{ color: selectedPortfolio.total_return >= 0 ? '#3f8600' : '#cf1322' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="收益率"
                      value={selectedPortfolio.total_return_pct}
                      prefix={<TrophyOutlined />}
                      suffix="%"
                      precision={2}
                      valueStyle={{ color: selectedPortfolio.total_return_pct >= 0 ? '#3f8600' : '#cf1322' }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>

            <Col span={24}>
              <Card
                title="投资组合详情"
                extra={
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setTransactionModalVisible(true)}
                  >
                    添加交易
                  </Button>
                }
              >
                <Tabs defaultActiveKey="holdings">
                  <TabPane tab="持仓明细" key="holdings">
                    <Table
                      dataSource={holdings}
                      columns={holdingColumns}
                      rowKey="id"
                      pagination={false}
                    />
                  </TabPane>
                  <TabPane tab="交易记录" key="transactions">
                    <Table
                      dataSource={transactions}
                      columns={transactionColumns}
                      rowKey="id"
                      pagination={{ pageSize: 10 }}
                    />
                  </TabPane>
                </Tabs>
              </Card>
            </Col>
          </>
        )}
      </Row>

      {/* 创建投资组合模态框 */}
      <Modal
        title="创建投资组合"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreatePortfolio}
        >
          <Form.Item
            name="name"
            label="组合名称"
            rules={[{ required: true, message: '请输入组合名称' }]}
          >
            <Input placeholder="请输入组合名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="组合描述"
          >
            <Input.TextArea placeholder="请输入组合描述" rows={3} />
          </Form.Item>
          <Form.Item
            name="initial_capital"
            label="初始资金"
            rules={[{ required: true, message: '请输入初始资金' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入初始资金"
              min={1000}
              formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
            />
          </Form.Item>
          <Form.Item
            name="portfolio_type"
            label="组合类型"
            initialValue="virtual"
          >
            <Select>
              <Option value="virtual">虚拟组合</Option>
              <Option value="real">实盘组合</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加交易记录模态框 */}
      <Modal
        title="添加交易记录"
        open={transactionModalVisible}
        onCancel={() => setTransactionModalVisible(false)}
        footer={null}
      >
        <Form
          form={transactionForm}
          layout="vertical"
          onFinish={handleAddTransaction}
        >
          <Form.Item
            name="stock_code"
            label="股票代码"
            rules={[{ required: true, message: '请输入股票代码' }]}
          >
            <Input placeholder="请输入股票代码，如：000001" />
          </Form.Item>
          <Form.Item
            name="transaction_type"
            label="交易类型"
            rules={[{ required: true, message: '请选择交易类型' }]}
          >
            <Select placeholder="请选择交易类型">
              <Option value="buy">买入</Option>
              <Option value="sell">卖出</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="shares"
            label="交易数量"
            rules={[{ required: true, message: '请输入交易数量' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入交易数量"
              min={100}
              step={100}
            />
          </Form.Item>
          <Form.Item
            name="price"
            label="交易价格"
            rules={[{ required: true, message: '请输入交易价格' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入交易价格"
              min={0.01}
              step={0.01}
              precision={2}
            />
          </Form.Item>
          <Form.Item
            name="transaction_date"
            label="交易日期"
            rules={[{ required: true, message: '请选择交易日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                添加
              </Button>
              <Button onClick={() => setTransactionModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Portfolio
