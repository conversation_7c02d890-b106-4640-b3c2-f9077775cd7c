"""
自选股服务模块
"""

import asyncio
import secrets
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func, or_, update, delete
from sqlalchemy.dialects.sqlite import insert

from app.core.logging import get_logger
from app.core.database import AsyncSessionLocal
from app.models.watchlist import (
    Watchlist, WatchlistStock, WatchlistGroup, WatchlistShare, 
    WatchlistTemplate, UserStockNote, StockTag, UserStockTag
)
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


class WatchlistService:
    """自选股服务"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def create_watchlist(self, user_id: int, watchlist_data: Dict[str, Any]) -> int:
        """创建自选股列表"""
        try:
            logger.info(f"用户 {user_id} 创建自选股列表: {watchlist_data.get('name')}")
            
            # 检查是否是第一个列表（设为默认）
            existing_count = await self._get_user_watchlist_count(user_id)
            is_default = existing_count == 0 or watchlist_data.get("is_default", False)
            
            # 如果设为默认，取消其他默认列表
            if is_default:
                await self._clear_default_watchlists(user_id)
            
            watchlist = Watchlist(
                user_id=user_id,
                name=watchlist_data["name"],
                description=watchlist_data.get("description"),
                color=watchlist_data.get("color", "#1890ff"),
                icon=watchlist_data.get("icon", "star"),
                is_default=is_default,
                is_public=watchlist_data.get("is_public", False),
                sort_order=watchlist_data.get("sort_order", existing_count)
            )
            
            self.session.add(watchlist)
            await self.session.commit()
            await self.session.refresh(watchlist)
            
            logger.info(f"自选股列表创建成功，ID: {watchlist.id}")
            return watchlist.id
            
        except Exception as e:
            logger.error(f"创建自选股列表失败: {e}")
            await self.session.rollback()
            return 0
    
    async def get_user_watchlists(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的自选股列表"""
        try:
            stmt = select(Watchlist).where(
                Watchlist.user_id == user_id
            ).order_by(Watchlist.sort_order, Watchlist.created_at)
            
            result = await self.session.execute(stmt)
            watchlists = result.scalars().all()
            
            watchlist_data = []
            for watchlist in watchlists:
                # 获取列表中的股票数量
                stock_count = await self._get_watchlist_stock_count(watchlist.id)
                
                watchlist_data.append({
                    "id": watchlist.id,
                    "name": watchlist.name,
                    "description": watchlist.description,
                    "color": watchlist.color,
                    "icon": watchlist.icon,
                    "is_default": watchlist.is_default,
                    "is_public": watchlist.is_public,
                    "sort_order": watchlist.sort_order,
                    "stock_count": stock_count,
                    "total_value": float(watchlist.total_value) if watchlist.total_value else 0,
                    "total_change": float(watchlist.total_change) if watchlist.total_change else 0,
                    "total_change_pct": float(watchlist.total_change_pct) if watchlist.total_change_pct else 0,
                    "created_at": watchlist.created_at.isoformat(),
                    "updated_at": watchlist.updated_at.isoformat()
                })
            
            return watchlist_data
            
        except Exception as e:
            logger.error(f"获取用户自选股列表失败: {e}")
            return []
    
    async def add_stock_to_watchlist(self, user_id: int, watchlist_id: int, stock_data: Dict[str, Any]) -> bool:
        """添加股票到自选股列表"""
        try:
            stock_code = stock_data["stock_code"]
            logger.info(f"用户 {user_id} 添加股票 {stock_code} 到列表 {watchlist_id}")
            
            # 检查股票是否已存在
            existing_stock = await self._get_watchlist_stock(watchlist_id, stock_code)
            if existing_stock:
                logger.warning(f"股票 {stock_code} 已在列表中")
                return False
            
            # 获取当前价格
            current_price = await self._get_current_stock_price(stock_code)
            
            # 获取列表中股票数量用于排序
            stock_count = await self._get_watchlist_stock_count(watchlist_id)
            
            watchlist_stock = WatchlistStock(
                watchlist_id=watchlist_id,
                user_id=user_id,
                stock_code=stock_code,
                added_price=current_price,
                added_reason=stock_data.get("added_reason"),
                tags=stock_data.get("tags", {}),
                alert_enabled=stock_data.get("alert_enabled", True),
                notes=stock_data.get("notes"),
                target_price=Decimal(str(stock_data["target_price"])) if stock_data.get("target_price") else None,
                stop_loss_price=Decimal(str(stock_data["stop_loss_price"])) if stock_data.get("stop_loss_price") else None,
                sort_order=stock_count
            )
            
            self.session.add(watchlist_stock)
            
            # 更新列表统计
            await self._update_watchlist_stats(watchlist_id)
            
            await self.session.commit()
            
            logger.info(f"股票 {stock_code} 添加成功")
            return True
            
        except Exception as e:
            logger.error(f"添加股票到自选股失败: {e}")
            await self.session.rollback()
            return False
    
    async def get_watchlist_stocks(self, user_id: int, watchlist_id: int, 
                                 include_market_data: bool = True) -> List[Dict[str, Any]]:
        """获取自选股列表中的股票"""
        try:
            stmt = select(WatchlistStock).where(
                and_(
                    WatchlistStock.watchlist_id == watchlist_id,
                    WatchlistStock.user_id == user_id
                )
            ).order_by(WatchlistStock.sort_order, WatchlistStock.created_at)
            
            result = await self.session.execute(stmt)
            stocks = result.scalars().all()
            
            stock_data = []
            for stock in stocks:
                stock_info = {
                    "id": stock.id,
                    "stock_code": stock.stock_code,
                    "added_price": float(stock.added_price) if stock.added_price else None,
                    "added_reason": stock.added_reason,
                    "tags": stock.tags or {},
                    "alert_enabled": stock.alert_enabled,
                    "notes": stock.notes,
                    "target_price": float(stock.target_price) if stock.target_price else None,
                    "stop_loss_price": float(stock.stop_loss_price) if stock.stop_loss_price else None,
                    "sort_order": stock.sort_order,
                    "view_count": stock.view_count,
                    "last_viewed_at": stock.last_viewed_at.isoformat() if stock.last_viewed_at else None,
                    "created_at": stock.created_at.isoformat()
                }
                
                # 获取市场数据
                if include_market_data:
                    market_data = await self._get_stock_market_data(stock.stock_code)
                    stock_info.update(market_data)
                
                stock_data.append(stock_info)
            
            return stock_data
            
        except Exception as e:
            logger.error(f"获取自选股股票失败: {e}")
            return []
    
    async def remove_stock_from_watchlist(self, user_id: int, watchlist_id: int, stock_code: str) -> bool:
        """从自选股列表移除股票"""
        try:
            logger.info(f"用户 {user_id} 从列表 {watchlist_id} 移除股票 {stock_code}")
            
            stmt = delete(WatchlistStock).where(
                and_(
                    WatchlistStock.watchlist_id == watchlist_id,
                    WatchlistStock.user_id == user_id,
                    WatchlistStock.stock_code == stock_code
                )
            )
            
            result = await self.session.execute(stmt)
            
            if result.rowcount > 0:
                # 更新列表统计
                await self._update_watchlist_stats(watchlist_id)
                await self.session.commit()
                
                logger.info(f"股票 {stock_code} 移除成功")
                return True
            else:
                logger.warning(f"股票 {stock_code} 不在列表中")
                return False
                
        except Exception as e:
            logger.error(f"移除股票失败: {e}")
            await self.session.rollback()
            return False
    
    async def update_stock_settings(self, user_id: int, stock_id: int, settings: Dict[str, Any]) -> bool:
        """更新股票设置"""
        try:
            logger.info(f"用户 {user_id} 更新股票设置，股票ID: {stock_id}")
            
            update_data = {}
            
            if "target_price" in settings:
                update_data["target_price"] = Decimal(str(settings["target_price"])) if settings["target_price"] else None
            
            if "stop_loss_price" in settings:
                update_data["stop_loss_price"] = Decimal(str(settings["stop_loss_price"])) if settings["stop_loss_price"] else None
            
            if "alert_enabled" in settings:
                update_data["alert_enabled"] = settings["alert_enabled"]
            
            if "notes" in settings:
                update_data["notes"] = settings["notes"]
            
            if "tags" in settings:
                update_data["tags"] = settings["tags"]
            
            if update_data:
                update_data["updated_at"] = datetime.utcnow()
                
                stmt = update(WatchlistStock).where(
                    and_(
                        WatchlistStock.id == stock_id,
                        WatchlistStock.user_id == user_id
                    )
                ).values(**update_data)
                
                result = await self.session.execute(stmt)
                await self.session.commit()
                
                return result.rowcount > 0
            
            return True
            
        except Exception as e:
            logger.error(f"更新股票设置失败: {e}")
            await self.session.rollback()
            return False
    
    async def create_stock_note(self, user_id: int, note_data: Dict[str, Any]) -> int:
        """创建股票笔记"""
        try:
            logger.info(f"用户 {user_id} 创建股票笔记: {note_data.get('title')}")
            
            note = UserStockNote(
                user_id=user_id,
                stock_code=note_data["stock_code"],
                title=note_data["title"],
                content=note_data["content"],
                note_type=note_data.get("note_type", "idea"),
                related_price=Decimal(str(note_data["related_price"])) if note_data.get("related_price") else None,
                related_date=note_data.get("related_date"),
                tags=note_data.get("tags", {}),
                is_private=note_data.get("is_private", True),
                is_important=note_data.get("is_important", False),
                reminder_at=note_data.get("reminder_at")
            )
            
            self.session.add(note)
            await self.session.commit()
            await self.session.refresh(note)
            
            logger.info(f"股票笔记创建成功，ID: {note.id}")
            return note.id
            
        except Exception as e:
            logger.error(f"创建股票笔记失败: {e}")
            await self.session.rollback()
            return 0
    
    async def get_stock_notes(self, user_id: int, stock_code: Optional[str] = None, 
                            note_type: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取股票笔记"""
        try:
            stmt = select(UserStockNote).where(UserStockNote.user_id == user_id)
            
            if stock_code:
                stmt = stmt.where(UserStockNote.stock_code == stock_code)
            
            if note_type:
                stmt = stmt.where(UserStockNote.note_type == note_type)
            
            stmt = stmt.order_by(desc(UserStockNote.created_at)).limit(limit)
            
            result = await self.session.execute(stmt)
            notes = result.scalars().all()
            
            notes_data = []
            for note in notes:
                notes_data.append({
                    "id": note.id,
                    "stock_code": note.stock_code,
                    "title": note.title,
                    "content": note.content,
                    "note_type": note.note_type,
                    "related_price": float(note.related_price) if note.related_price else None,
                    "related_date": note.related_date.isoformat() if note.related_date else None,
                    "tags": note.tags or {},
                    "is_private": note.is_private,
                    "is_important": note.is_important,
                    "reminder_at": note.reminder_at.isoformat() if note.reminder_at else None,
                    "is_reminded": note.is_reminded,
                    "created_at": note.created_at.isoformat(),
                    "updated_at": note.updated_at.isoformat()
                })
            
            return notes_data
            
        except Exception as e:
            logger.error(f"获取股票笔记失败: {e}")
            return []
    
    async def _get_user_watchlist_count(self, user_id: int) -> int:
        """获取用户自选股列表数量"""
        try:
            stmt = select(func.count(Watchlist.id)).where(Watchlist.user_id == user_id)
            result = await self.session.execute(stmt)
            return result.scalar() or 0
        except Exception as e:
            logger.error(f"获取用户自选股列表数量失败: {e}")
            return 0
    
    async def _clear_default_watchlists(self, user_id: int):
        """清除用户的默认自选股列表"""
        try:
            stmt = update(Watchlist).where(
                and_(Watchlist.user_id == user_id, Watchlist.is_default == True)
            ).values(is_default=False)
            
            await self.session.execute(stmt)
        except Exception as e:
            logger.error(f"清除默认自选股列表失败: {e}")
    
    async def _get_watchlist_stock_count(self, watchlist_id: int) -> int:
        """获取自选股列表中的股票数量"""
        try:
            stmt = select(func.count(WatchlistStock.id)).where(WatchlistStock.watchlist_id == watchlist_id)
            result = await self.session.execute(stmt)
            return result.scalar() or 0
        except Exception as e:
            logger.error(f"获取自选股股票数量失败: {e}")
            return 0
    
    async def _get_watchlist_stock(self, watchlist_id: int, stock_code: str) -> Optional[WatchlistStock]:
        """获取自选股列表中的特定股票"""
        try:
            stmt = select(WatchlistStock).where(
                and_(
                    WatchlistStock.watchlist_id == watchlist_id,
                    WatchlistStock.stock_code == stock_code
                )
            )
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取自选股股票失败: {e}")
            return None
    
    async def _get_current_stock_price(self, stock_code: str) -> Optional[Decimal]:
        """获取股票当前价格"""
        try:
            async with DataStorageService() as data_storage:
                # 尝试获取实时数据
                realtime = await data_storage.get_realtime_data(stock_code)
                if realtime and realtime.current_price:
                    return Decimal(str(realtime.current_price))
                
                # 获取最新K线数据
                klines = await data_storage.get_kline_data(stock_code, "daily", limit=1)
                if klines:
                    return Decimal(str(klines[-1].close_price))
                
            return None
        except Exception as e:
            logger.error(f"获取股票价格失败: {e}")
            return None
    
    async def _get_stock_market_data(self, stock_code: str) -> Dict[str, Any]:
        """获取股票市场数据"""
        try:
            async with DataStorageService() as data_storage:
                # 获取实时数据
                realtime = await data_storage.get_realtime_data(stock_code)
                if realtime:
                    return {
                        "current_price": float(realtime.current_price) if realtime.current_price else None,
                        "change_amount": float(realtime.change_amount) if realtime.change_amount else None,
                        "change_percent": float(realtime.change_percent) if realtime.change_percent else None,
                        "volume": realtime.volume,
                        "turnover": float(realtime.turnover) if realtime.turnover else None,
                        "high": float(realtime.high) if realtime.high else None,
                        "low": float(realtime.low) if realtime.low else None,
                        "open": float(realtime.open) if realtime.open else None,
                        "prev_close": float(realtime.prev_close) if realtime.prev_close else None,
                        "market_cap": float(realtime.market_cap) if realtime.market_cap else None,
                        "pe_ratio": float(realtime.pe_ratio) if realtime.pe_ratio else None,
                        "update_time": realtime.update_time.isoformat() if realtime.update_time else None
                    }
                
                # 获取最新K线数据作为备选
                klines = await data_storage.get_kline_data(stock_code, "daily", limit=1)
                if klines:
                    latest = klines[-1]
                    return {
                        "current_price": float(latest.close_price),
                        "change_amount": float(latest.close_price - latest.open_price),
                        "change_percent": float((latest.close_price - latest.open_price) / latest.open_price * 100),
                        "volume": latest.volume,
                        "high": float(latest.high_price),
                        "low": float(latest.low_price),
                        "open": float(latest.open_price),
                        "prev_close": float(latest.pre_close),
                        "update_time": latest.trade_date.isoformat()
                    }
            
            return {}
        except Exception as e:
            logger.error(f"获取股票市场数据失败: {e}")
            return {}
    
    async def _update_watchlist_stats(self, watchlist_id: int):
        """更新自选股列表统计信息"""
        try:
            # 获取股票数量
            stock_count = await self._get_watchlist_stock_count(watchlist_id)
            
            # 更新统计信息
            stmt = update(Watchlist).where(Watchlist.id == watchlist_id).values(
                stock_count=stock_count,
                updated_at=datetime.utcnow()
            )
            
            await self.session.execute(stmt)
            
        except Exception as e:
            logger.error(f"更新自选股列表统计失败: {e}")
