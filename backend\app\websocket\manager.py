"""
WebSocket连接管理器
"""

import json
import asyncio
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import uuid

from app.core.logging import get_logger

logger = get_logger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储所有活跃连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储用户订阅的股票代码
        self.user_subscriptions: Dict[str, Set[str]] = {}
        # 存储股票代码对应的订阅用户
        self.stock_subscribers: Dict[str, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str = None) -> str:
        """接受WebSocket连接"""
        await websocket.accept()
        
        if not client_id:
            client_id = str(uuid.uuid4())
        
        self.active_connections[client_id] = websocket
        self.user_subscriptions[client_id] = set()
        
        logger.info(f"WebSocket客户端 {client_id} 已连接")
        
        # 发送连接成功消息
        await self.send_personal_message({
            "type": "connection",
            "status": "connected",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }, client_id)
        
        return client_id
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            # 清理订阅关系
            if client_id in self.user_subscriptions:
                for stock_code in self.user_subscriptions[client_id]:
                    if stock_code in self.stock_subscribers:
                        self.stock_subscribers[stock_code].discard(client_id)
                        if not self.stock_subscribers[stock_code]:
                            del self.stock_subscribers[stock_code]
                del self.user_subscriptions[client_id]
            
            # 移除连接
            del self.active_connections[client_id]
            logger.info(f"WebSocket客户端 {client_id} 已断开")
    
    async def send_personal_message(self, message: dict, client_id: str):
        """发送个人消息"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"发送消息给客户端 {client_id} 失败: {e}")
                self.disconnect(client_id)
    
    async def broadcast(self, message: dict):
        """广播消息给所有连接"""
        if not self.active_connections:
            return
        
        message_text = json.dumps(message)
        disconnected_clients = []
        
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(message_text)
            except Exception as e:
                logger.error(f"广播消息给客户端 {client_id} 失败: {e}")
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def broadcast_to_stock_subscribers(self, stock_code: str, message: dict):
        """向订阅特定股票的用户广播消息"""
        if stock_code not in self.stock_subscribers:
            return
        
        message_text = json.dumps(message)
        disconnected_clients = []
        
        for client_id in self.stock_subscribers[stock_code].copy():
            if client_id in self.active_connections:
                try:
                    await self.active_connections[client_id].send_text(message_text)
                except Exception as e:
                    logger.error(f"发送股票消息给客户端 {client_id} 失败: {e}")
                    disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    def subscribe_stock(self, client_id: str, stock_code: str):
        """订阅股票"""
        if client_id not in self.user_subscriptions:
            self.user_subscriptions[client_id] = set()
        
        self.user_subscriptions[client_id].add(stock_code)
        
        if stock_code not in self.stock_subscribers:
            self.stock_subscribers[stock_code] = set()
        
        self.stock_subscribers[stock_code].add(client_id)
        logger.info(f"客户端 {client_id} 订阅股票 {stock_code}")
    
    def unsubscribe_stock(self, client_id: str, stock_code: str):
        """取消订阅股票"""
        if client_id in self.user_subscriptions:
            self.user_subscriptions[client_id].discard(stock_code)
        
        if stock_code in self.stock_subscribers:
            self.stock_subscribers[stock_code].discard(client_id)
            if not self.stock_subscribers[stock_code]:
                del self.stock_subscribers[stock_code]
        
        logger.info(f"客户端 {client_id} 取消订阅股票 {stock_code}")
    
    def get_connection_stats(self) -> dict:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.active_connections),
            "total_subscriptions": sum(len(subs) for subs in self.user_subscriptions.values()),
            "subscribed_stocks": len(self.stock_subscribers),
            "active_clients": list(self.active_connections.keys())
        }


# 全局连接管理器实例
manager = ConnectionManager()


class RealtimeDataBroadcaster:
    """实时数据广播器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.manager = connection_manager
        self.is_running = False
        
    async def start_broadcasting(self):
        """开始广播实时数据"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("开始实时数据广播")
        
        # 启动各种数据广播任务
        await asyncio.gather(
            self.broadcast_stock_prices(),
            self.broadcast_ai_signals(),
            self.broadcast_alerts(),
            return_exceptions=True
        )
    
    async def stop_broadcasting(self):
        """停止广播"""
        self.is_running = False
        logger.info("停止实时数据广播")
    
    async def broadcast_stock_prices(self):
        """广播股票价格更新"""
        while self.is_running:
            try:
                # 模拟股票价格更新
                import random
                stock_codes = ["000001", "000002", "600000", "600036", "000858"]
                
                for stock_code in stock_codes:
                    if stock_code in self.manager.stock_subscribers:
                        price_change = random.uniform(-0.5, 0.5)
                        change_percent = random.uniform(-5.0, 5.0)
                        
                        message = {
                            "type": "stock_price_update",
                            "data": {
                                "stock_code": stock_code,
                                "price_change": round(price_change, 2),
                                "change_percent": round(change_percent, 2),
                                "timestamp": datetime.now().isoformat(),
                                "volume": random.randint(100000, 1000000)
                            }
                        }
                        
                        await self.manager.broadcast_to_stock_subscribers(stock_code, message)
                
                await asyncio.sleep(2)  # 每2秒更新一次
                
            except Exception as e:
                logger.error(f"广播股票价格失败: {e}")
                await asyncio.sleep(5)
    
    async def broadcast_ai_signals(self):
        """广播AI信号"""
        while self.is_running:
            try:
                await asyncio.sleep(10)  # 每10秒发送一次AI信号
                
                import random
                stock_codes = ["000001", "000002", "600000"]
                stock_code = random.choice(stock_codes)
                
                if stock_code in self.manager.stock_subscribers:
                    signal_types = ["买入", "卖出", "持有"]
                    signal_type = random.choice(signal_types)
                    
                    message = {
                        "type": "ai_signal",
                        "data": {
                            "stock_code": stock_code,
                            "signal": signal_type,
                            "confidence": round(random.uniform(0.6, 0.95), 2),
                            "reasoning": f"基于技术指标分析，建议{signal_type}",
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    
                    await self.manager.broadcast_to_stock_subscribers(stock_code, message)
                
            except Exception as e:
                logger.error(f"广播AI信号失败: {e}")
                await asyncio.sleep(10)
    
    async def broadcast_alerts(self):
        """广播预警信息"""
        while self.is_running:
            try:
                await asyncio.sleep(15)  # 每15秒发送一次预警
                
                import random
                stock_codes = ["000001", "000002", "600000"]
                stock_code = random.choice(stock_codes)
                
                alert_types = ["价格突破", "形态确认", "指标信号"]
                alert_type = random.choice(alert_types)
                levels = ["info", "warning", "error"]
                level = random.choice(levels)
                
                message = {
                    "type": "alert",
                    "data": {
                        "stock_code": stock_code,
                        "alert_type": alert_type,
                        "message": f"{stock_code} 触发{alert_type}预警",
                        "level": level,
                        "timestamp": datetime.now().isoformat()
                    }
                }
                
                # 广播给所有连接的客户端
                await self.manager.broadcast(message)
                
            except Exception as e:
                logger.error(f"广播预警信息失败: {e}")
                await asyncio.sleep(15)


# 全局广播器实例
broadcaster = RealtimeDataBroadcaster(manager)
