import React, { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'
import {
  Card,
  Row,
  Col,
  Typography,
  Space,
  Button,
  Switch,
  Segmented,
  Spin,
  Tag,
  Statistic,
  Progress,
  Divider,
  theme
} from 'antd'
import {
  ReloadOutlined,
  StarOutlined,
  StarFilled,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons'
import { useStockDataSimple } from '../hooks/useStockDataSimple'
import { stockService } from '../services/apiService'

import { STOCK_COLORS } from '../config/colors'
import AdvancedCandlestickChart from '../components/charts/AdvancedCandlestickChart'
import KeltnerChannel<PERSON>hart from '../components/charts/KeltnerChannel<PERSON>hart'
import RSIChart from '../components/charts/RSIChart'
import MACDChart from '../components/charts/MACDChart'
import KDJ<PERSON>hart from '../components/charts/KDJChart'
import Bollinger<PERSON>hart from '../components/charts/BollingerChart'

const { Text, Title } = Typography
const { useToken } = theme

interface PriceData {
  timestamp: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}



const IntegratedStockAnalysis: React.FC = () => {
  const [searchParams] = useSearchParams()
  const {
    selectedStock,
    setSelectedStock,
    stockList,
    fetchStockList
  } = useStockDataSimple()

  // 状态管理
  const [timeFrame, setTimeFrame] = useState('daily')
  const [showVolume, setShowVolume] = useState(true)

  // 简单的加载状态
  const [loading, setLoading] = useState(false)

  // 数据状态
  const [priceData, setPriceData] = useState<PriceData[]>([])
  const [fundamentalsData, setFundamentalsData] = useState<any>(null)
  const [realtimeData, setRealtimeData] = useState<any>(null)
  const [spotData, setSpotData] = useState<any>(null) // 完整的实时行情数据

  // 时间周期选项
  const timeFrameOptions = [
    { label: '分时', value: 'tick' },
    { label: '5分钟', value: '5m' },
    { label: '15分钟', value: '15m' },
    { label: '30分钟', value: '30m' },
    { label: '1小时', value: '1h' },
    { label: '日线', value: 'daily' },
    { label: '周线', value: 'weekly' },
    { label: '月线', value: 'monthly' }
  ]



  // 初始化
  useEffect(() => {
    fetchStockList()
  }, [fetchStockList])

  useEffect(() => {
    const stockCode = searchParams.get('code')
    if (stockCode && stockList.length > 0) {
      const stock = stockList.find(s => s.code === stockCode)
      if (stock) {
        setSelectedStock(stock)
      }
    } else if (!selectedStock && stockList.length > 0) {
      // 默认选择招商银行
      const defaultStock = stockList.find(s => s.code === '600036') || stockList[0]
      setSelectedStock(defaultStock)
    }
  }, [searchParams, stockList, selectedStock, setSelectedStock])

  // 获取实时数据
  const fetchRealtimeData = async (stockCode: string) => {
    try {
      const response = await stockService.getRealtimeData(stockCode)
      if (response.code === 200 && response.data) {
        console.log('实时数据获取成功:', response.data)
        setRealtimeData(response.data)
      }
    } catch (error) {
      console.error('获取实时数据失败:', error)
      setRealtimeData(null)
    }
  }

  // 检测是否为模拟数据
  const isMockData = (data: any) => {
    // 模拟数据的特征：价格都是固定值（如10.5, 10.2等）
    return data && (
      data.current_price === 10.5 ||
      (data.current_price === 10.5 && data.open_price === 10.2 && data.high_price === 10.8)
    )
  }

  // 获取完整的股票实时行情数据
  const fetchSpotData = async (stockCode: string) => {
    try {
      const response = await stockService.getStockSpotData(stockCode)
      if (response.code === 200 && response.data && response.data.length > 0) {
        const data = response.data[0]
        console.log('完整实时行情数据获取成功:', data)

        // 如果是模拟数据，设置为null以显示"--"
        if (isMockData(data)) {
          console.warn('检测到模拟数据，将显示"--"')
          setSpotData(null)
        } else {
          setSpotData(data)
        }
      }
    } catch (error) {
      console.error('获取完整实时行情数据失败:', error)
      setSpotData(null)
    }
  }

  // 获取真实K线数据
  const fetchKlineData = async (stockCode: string, period: string = 'daily'): Promise<PriceData[]> => {
    try {
      const response = await stockService.getKlineData(stockCode, period, undefined, undefined, 60)
      if (response.code === 200 && response.data?.klines) {
        return response.data.klines.map((item: any) => ({
          timestamp: item.trade_date,
          open: item.open_price,
          high: item.high_price,
          low: item.low_price,
          close: item.close_price,
          volume: item.volume
        }))
      }
      return []
    } catch (error) {
      console.error('获取K线数据失败:', error)
      return []
    }
  }

  // 获取真实基本面数据
  const fetchFundamentalsData = async (stockCode: string) => {
    try {
      const response = await stockService.getStockFundamentals(stockCode)
      if (response.code === 200 && response.data) {
        setFundamentalsData(response.data)
      } else {
        console.warn('基本面数据获取失败，API返回错误')
        setFundamentalsData(null)
      }
    } catch (error) {
      console.error('获取基本面数据失败:', error)
      setFundamentalsData(null) // 设置为null，不抛出错误
    }
  }



  // 加载所有数据
  const loadAllData = useCallback(async () => {
    if (!selectedStock) return

    console.log('开始加载数据...')
    setLoading(true)

    try {
      // 获取K线数据
      console.log('获取K线数据，周期:', timeFrame)
      const priceData = await fetchKlineData(selectedStock.code, timeFrame)
      setPriceData(priceData)
      console.log('K线数据获取完成，数据量:', priceData.length)

      // 获取实时行情
      console.log('获取实时行情')
      await fetchRealtimeData(selectedStock.code)
      console.log('实时行情获取完成')

      // 获取完整实时行情数据
      console.log('获取完整实时行情数据')
      await fetchSpotData(selectedStock.code)
      console.log('完整实时行情数据获取完成')

      // 获取基本面数据
      console.log('获取基本面数据')
      await fetchFundamentalsData(selectedStock.code)
      console.log('基本面数据获取完成')

    } catch (error) {
      console.error('数据加载失败:', error)
    } finally {
      console.log('完成加载')
      setLoading(false)
    }
  }, [selectedStock, timeFrame])

  // 加载数据
  useEffect(() => {
    if (selectedStock) {
      loadAllData()
    }
  }, [selectedStock, loadAllData])

  // 刷新数据
  const refreshData = () => {
    loadAllData()
  }

  if (!selectedStock) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text>加载股票数据中...</Text>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '16px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {loading && (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '10px' }}>加载中...</div>
        </div>
      )}
      {/* 第一行 - 头部信息区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        {/* 左侧一半 - 股票基本信息 + 价格信息 + 基础指标 */}
        <Col span={12}>
          <Card size="small" styles={{ body: { padding: '16px' } }} style={{ height: '200px' }}>
            {/* 股票基本信息 */}
            <div style={{ marginBottom: '12px' }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Space size="large" align="center">
                    <div>
                      <Title level={4} style={{ margin: 0, marginBottom: '4px' }}>
                        {selectedStock.name} ({selectedStock.code})
                      </Title>
                      <Text type="secondary" style={{ fontSize: '12px' }}>金融业</Text>
                    </div>

                    {/* 现价和涨跌幅 */}
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ marginBottom: '4px' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>现价</Text>
                      </div>
                      <Space align="center">
                        {(spotData?.change_amount || 0) >= 0 ? (
                          <RiseOutlined style={{ color: 'rgb(214, 10, 34)' }} />
                        ) : (
                          <FallOutlined style={{ color: 'rgb(3, 123, 102)' }} />
                        )}
                        <Text
                          style={{
                            fontSize: '20px',
                            fontWeight: 'bold',
                            color: (spotData?.change_amount || 0) >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                          }}
                        >
                          ¥{spotData?.current_price?.toFixed(2) || (priceData.length > 0 ? priceData[priceData.length - 1].close.toFixed(2) : '--')}
                        </Text>
                      </Space>
                      <div style={{ marginTop: '4px' }}>
                        <Text
                          style={{
                            fontSize: '14px',
                            fontWeight: 'bold',
                            color: (spotData?.change_amount || 0) >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                          }}
                        >
                          {spotData?.change_amount ? (
                            <>
                              {spotData.change_amount >= 0 ? '+' : ''}{spotData.change_amount.toFixed(2)}
                              ({spotData.change_amount >= 0 ? '+' : ''}{(spotData.change_percent * 100).toFixed(2)}%)
                            </>
                          ) : '--'}
                        </Text>
                      </div>
                    </div>
                  </Space>
                </Col>


              </Row>
            </div>

            <Divider style={{ margin: '8px 0' }} />

            {/* 价格信息 + 基础指标融合 */}
            <Row gutter={[16, 8]}>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>今开</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.open_price?.toFixed(2) || '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>最高</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold', color: 'rgb(214, 10, 34)' }}>
                    {spotData?.high_price?.toFixed(2) || '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>最低</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold', color: 'rgb(3, 123, 102)' }}>
                    {spotData?.low_price?.toFixed(2) || '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>昨收</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.pre_close?.toFixed(2) || '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>成交量</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.volume ? `${(spotData.volume / 10000).toFixed(1)}万手` : '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>成交额</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.turnover ? `${(spotData.turnover / 100000000).toFixed(2)}亿` : '--'}
                  </div>
                </div>
              </Col>
            </Row>

            <Row gutter={[16, 8]} style={{ marginTop: '8px' }}>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>换手率</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.turnover_rate ? `${(spotData.turnover_rate * 100).toFixed(2)}%` : '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>市盈率(TTM)</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.pe_ratio?.toFixed(2) || '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>市净率</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.pb_ratio?.toFixed(2) || '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>总市值</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.total_market_cap ? `${(spotData.total_market_cap / 100000000).toFixed(0)}亿` : '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>流通市值</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.float_market_cap ? `${(spotData.float_market_cap / 100000000).toFixed(0)}亿` : '--'}
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>振幅</Text>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {spotData?.amplitude ? `${(spotData.amplitude * 100).toFixed(2)}%` : '--'}
                  </div>
                </div>
              </Col>

              {/* 基础指标 */}
              {fundamentalsData && (
                <>
                  <Col span={6}>
                    <div style={{ textAlign: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>市盈率</Text>
                      <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                        {fundamentalsData.pe_ratio?.toFixed(1) || '25.6'}
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div style={{ textAlign: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>市净率</Text>
                      <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                        {fundamentalsData.pb_ratio?.toFixed(1) || '3.2'}
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div style={{ textAlign: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>ROE</Text>
                      <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                        {(fundamentalsData.roe * 100)?.toFixed(1) || '12.5'}%
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div style={{ textAlign: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>市值</Text>
                      <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                        {(fundamentalsData.market_cap / 100000000)?.toFixed(0) || '1250'}亿
                      </div>
                    </div>
                  </Col>
                </>
              )}
            </Row>
          </Card>
        </Col>

        {/* 右侧一半的一半 - 个股人气排名 */}
        <Col span={6}>
          <Card
            title="个股人气排名"
            size="small"
            styles={{ body: { padding: '12px' } }}
            style={{ height: '200px' }}
          >
            <div style={{ textAlign: 'center', color: '#999' }}>
              <Text>人气数据获取失败</Text>
              <div style={{ fontSize: '12px', marginTop: '8px' }}>
                API接口暂未实现
              </div>
            </div>
          </Card>
        </Col>

        {/* 最右侧的一半 - 热词 */}
        <Col span={6}>
          <Card
            title="热词"
            size="small"
            styles={{ body: { padding: '12px' } }}
            style={{ height: '200px' }}
          >
            <div style={{ textAlign: 'center', color: '#999' }}>
              <Text>热词数据获取失败</Text>
              <div style={{ fontSize: '12px', marginTop: '8px' }}>
                API接口暂未实现
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 控制栏 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col span={24}>
          <Card size="small" styles={{ body: { padding: '8px 16px' } }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Space>
                  <Text strong style={{ fontSize: '14px' }}>时间周期：</Text>
                  <Segmented
                    options={timeFrameOptions}
                    value={timeFrame}
                    onChange={setTimeFrame}
                    size="small"
                  />

                  <Button
                    icon={<ReloadOutlined />}
                    onClick={refreshData}
                    loading={loading}
                    size="small"
                  >
                    刷新数据
                  </Button>
                  <Button
                    type="primary"
                    onClick={loadAllData}
                    loading={loading}
                    size="small"
                  >
                    重新加载
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      {loading ? (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text>数据加载中...</Text>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* 第二行 - K线图区域 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
            {/* 左侧 - K线图 */}
            <Col span={12}>
              <Card
                title="K线图"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '500px' }}
                extra={
                  <Space size="small">
                    <Switch
                      checked={showVolume}
                      onChange={setShowVolume}
                      size="small"
                    />
                    <Text style={{ fontSize: '12px' }}>成交量</Text>
                  </Space>
                }
              >
                {priceData.length > 0 ? (
                  <AdvancedCandlestickChart
                    title=""
                    data={priceData.map(item => ({
                      timestamp: item.timestamp,
                      open: item.open,
                      high: item.high,
                      low: item.low,
                      close: item.close,
                      volume: item.volume
                    }))}
                    height={440}
                    showVolume={showVolume}
                    maLines={[5, 10, 20, 30]}
                  />
                ) : (
                  <div style={{
                    height: '440px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Spin />
                  </div>
                )}
              </Card>
            </Col>

            {/* 右侧 - 肯特纳通道图 */}
            <Col span={12}>
              <Card
                title="肯特纳通道"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '500px' }}
              >
                {priceData.length > 0 && (
                  <KeltnerChannelChart
                    data={priceData}
                    height={440}
                  />
                )}
              </Card>
            </Col>
          </Row>

          {/* 第三行 - 高级指标图（不带K线） */}
          <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
            {/* 左侧 - RSI指标 */}
            <Col span={12}>
              <Card
                title="RSI相对强弱指标"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '300px' }}
              >
                {priceData.length > 0 && (
                  <RSIChart
                    data={priceData}
                    height={250}
                  />
                )}
              </Card>
            </Col>

            {/* 右侧 - MACD指标 */}
            <Col span={12}>
              <Card
                title="MACD指标"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '300px' }}
              >
                {priceData.length > 0 && (
                  <MACDChart
                    data={priceData}
                    height={250}
                  />
                )}
              </Card>
            </Col>
          </Row>

          {/* 第四行 - 高级指标图（不带K线） */}
          <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
            {/* 左侧 - KDJ指标 */}
            <Col span={12}>
              <Card
                title="KDJ随机指标"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '300px' }}
              >
                {priceData.length > 0 && (
                  <KDJChart
                    data={priceData}
                    height={250}
                  />
                )}
              </Card>
            </Col>

            {/* 右侧 - 布林带指标 */}
            <Col span={12}>
              <Card
                title="布林带指标"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '300px' }}
              >
                {priceData.length > 0 && (
                  <BollingerChart
                    data={priceData}
                    height={250}
                  />
                )}
              </Card>
            </Col>
          </Row>

          {/* 第五行 - 其他内容 */}
          <Row gutter={[16, 16]}>
            {/* AI预测分析 */}
            <Col span={12}>
              <Card
                title="AI预测分析"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '300px' }}
              >
                <div style={{ textAlign: 'center', color: '#999' }}>
                  <Text>AI分析获取失败</Text>
                  <div style={{ fontSize: '12px', marginTop: '8px' }}>
                    API接口暂未实现
                  </div>
                </div>
              </Card>
            </Col>

            {/* 综合评分 */}
            <Col span={12}>
              <Card
                title="综合评分"
                size="small"
                styles={{ body: { padding: '12px' } }}
                style={{ height: '300px' }}
              >
                {fundamentalsData ? (
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <div style={{ textAlign: 'center' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>技术面评分</Text>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                          {(75 + (fundamentalsData.pe_ratio % 20)).toFixed(0)}
                        </div>
                        <Progress
                          percent={75 + (fundamentalsData.pe_ratio % 20)}
                          size="small"
                          showInfo={false}
                          strokeColor={{
                            '0%': '#ff4d4f',
                            '60%': '#faad14',
                            '80%': '#52c41a',
                          }}
                        />
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{ textAlign: 'center' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>基本面评分</Text>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                          {(80 + (fundamentalsData.pb_ratio % 15)).toFixed(0)}
                        </div>
                        <Progress
                          percent={80 + (fundamentalsData.pb_ratio % 15)}
                          size="small"
                          showInfo={false}
                          strokeColor={{
                            '0%': '#ff4d4f',
                            '60%': '#faad14',
                            '80%': '#52c41a',
                          }}
                        />
                      </div>
                    </Col>
                    <Col span={24}>
                      <div style={{ textAlign: 'center', marginTop: '16px' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>综合推荐度</Text>
                        <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#722ed1' }}>
                          {((75 + (fundamentalsData.pe_ratio % 20) + 80 + (fundamentalsData.pb_ratio % 15)) / 2).toFixed(0)}
                        </div>
                        <div style={{ fontSize: '14px', color: '#666' }}>
                          {((75 + (fundamentalsData.pe_ratio % 20) + 80 + (fundamentalsData.pb_ratio % 15)) / 2) > 85 ? '强烈推荐' :
                           ((75 + (fundamentalsData.pe_ratio % 20) + 80 + (fundamentalsData.pb_ratio % 15)) / 2) > 70 ? '推荐' : '谨慎'}
                        </div>
                      </div>
                    </Col>
                  </Row>
                ) : (
                  <div style={{ textAlign: 'center', color: '#999' }}>
                    <Text>综合评分获取失败</Text>
                    <div style={{ fontSize: '12px', marginTop: '8px' }}>
                      基本面数据获取失败
                    </div>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </>
      )}
    </div>
  )
}

export default IntegratedStockAnalysis
