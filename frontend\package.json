{"name": "stock-analyzer-frontend", "version": "1.0.0", "description": "A股智能分析系统前端", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@ant-design/icons": "^5.2.0", "@ant-design/plots": "^2.6.2", "antd": "^5.12.0", "axios": "^1.6.0", "classnames": "^2.3.0", "dayjs": "^1.11.0", "echarts": "^5.4.0", "echarts-for-react": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "socket.io-client": "^4.7.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "typescript": "^5.0.0", "vite": "^5.0.0"}, "keywords": ["react", "typescript", "stock", "analyzer", "ai"], "author": "Stock Analyzer Team", "license": "MIT"}