import React, { useMemo } from 'react'
import { Card, Typography, Row, Col, Statistic } from 'antd'
import { TrendingUpOutlined, TrendingDownOutlined } from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { CHART_CONFIG } from '@/config/dataConfig'

const { Title, Text } = Typography

interface ChartData {
  name: string
  value: number
  change: number
  changePercent: number
}

interface SimpleChartProps {
  title: string
  data: ChartData[]
  type?: 'line' | 'bar' | 'pie'
}

const SimpleChart: React.FC<SimpleChartProps> = ({ title, data, type = 'line' }) => {
  // 准备ECharts配置
  const echartsOption = useMemo(() => {
    if (type === 'line') {
      return {
        animation: true,
        tooltip: {
          trigger: 'axis',
          formatter: function (params: any) {
            const data = params[0]
            if (data) {
              return `
                <div>
                  <div>${data.name}</div>
                  <div>数值: ${data.value.toFixed(2)}</div>
                </div>
              `
            }
            return ''
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.name),
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [
          {
            name: title,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            data: data.map(item => item.value),
            lineStyle: {
              color: '#1890ff',
              width: 2
            },
            itemStyle: {
              color: '#1890ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#1890ff40'
                  },
                  {
                    offset: 1,
                    color: '#1890ff10'
                  }
                ]
              }
            }
          }
        ]
      }
    }

    if (type === 'bar') {
      return {
        animation: true,
        tooltip: {
          trigger: 'axis',
          formatter: function (params: any) {
            const data = params[0]
            if (data) {
              return `
                <div>
                  <div>${data.name}</div>
                  <div>数值: ${data.value.toFixed(2)}</div>
                </div>
              `
            }
            return ''
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.name),
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [
          {
            name: title,
            type: 'bar',
            data: data.map(item => ({
              value: item.value,
              itemStyle: {
                color: item.change >= 0 ? CHART_CONFIG.CANDLESTICK.UP_COLOR : CHART_CONFIG.CANDLESTICK.DOWN_COLOR
              }
            })),
            barWidth: '60%'
          }
        ]
      }
    }

    // 饼图
    return {
      animation: true,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: title,
          type: 'pie',
          radius: '50%',
          data: data.map(item => ({
            value: item.value,
            name: item.name
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  }, [data, type, title])

  return (
    <Card title={title}>
      <ReactECharts
        option={echartsOption}
        style={{ height: 200 }}
        opts={{ renderer: 'canvas' }}
      />

      <div style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          {data.map((item, index) => (
            <Col span={24 / Math.min(data.length, 4)} key={index}>
              <Statistic
                title={item.name}
                value={item.value}
                precision={2}
                valueStyle={{
                  color: item.change >= 0 ? CHART_CONFIG.CANDLESTICK.UP_COLOR : CHART_CONFIG.CANDLESTICK.DOWN_COLOR,
                  fontSize: 16
                }}
                prefix={item.change >= 0 ? <TrendingUpOutlined /> : <TrendingDownOutlined />}
                suffix={
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    ({item.change >= 0 ? '+' : ''}{item.changePercent.toFixed(2)}%)
                  </Text>
                }
              />
            </Col>
          ))}
        </Row>
      </div>
    </Card>
  )
}

export default SimpleChart
