"""
自选股相关数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, DateTime, Boolean, Integer, Text, Numeric, JSON, Index, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class Watchlist(Base):
    """自选股列表表"""
    __tablename__ = "watchlists"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    
    # 列表信息
    name: Mapped[str] = mapped_column(String(100), comment="列表名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="列表描述")
    color: Mapped[Optional[str]] = mapped_column(String(20), comment="列表颜色")
    icon: Mapped[Optional[str]] = mapped_column(String(50), comment="列表图标")
    
    # 设置
    is_default: Mapped[bool] = mapped_column(<PERSON>olean, default=False, comment="是否默认列表")
    is_public: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否公开")
    sort_order: Mapped[int] = mapped_column(Integer, default=0, comment="排序顺序")
    
    # 统计信息
    stock_count: Mapped[int] = mapped_column(Integer, default=0, comment="股票数量")
    total_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), comment="总市值")
    total_change: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="总涨跌额")
    total_change_pct: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4), comment="总涨跌幅")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    __table_args__ = (
        Index('ix_watchlist_user_default', 'user_id', 'is_default'),
        Index('ix_watchlist_user_order', 'user_id', 'sort_order'),
    )


class WatchlistStock(Base):
    """自选股股票表"""
    __tablename__ = "watchlist_stocks"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    watchlist_id: Mapped[int] = mapped_column(Integer, index=True, comment="自选股列表ID")
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    stock_code: Mapped[str] = mapped_column(String(20), index=True, comment="股票代码")
    
    # 添加信息
    added_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="添加时价格")
    added_reason: Mapped[Optional[str]] = mapped_column(Text, comment="添加原因")
    tags: Mapped[Optional[dict]] = mapped_column(JSON, comment="标签")
    
    # 用户设置
    alert_enabled: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用预警")
    notes: Mapped[Optional[str]] = mapped_column(Text, comment="备注")
    sort_order: Mapped[int] = mapped_column(Integer, default=0, comment="排序顺序")
    
    # 目标价格
    target_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="目标价格")
    stop_loss_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="止损价格")
    
    # 统计信息
    view_count: Mapped[int] = mapped_column(Integer, default=0, comment="查看次数")
    last_viewed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后查看时间")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    __table_args__ = (
        Index('ix_watchlist_stock_user_code', 'user_id', 'stock_code'),
        Index('ix_watchlist_stock_list_order', 'watchlist_id', 'sort_order'),
    )


class WatchlistGroup(Base):
    """自选股分组表"""
    __tablename__ = "watchlist_groups"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    
    # 分组信息
    name: Mapped[str] = mapped_column(String(100), comment="分组名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="分组描述")
    color: Mapped[Optional[str]] = mapped_column(String(20), comment="分组颜色")
    
    # 设置
    sort_order: Mapped[int] = mapped_column(Integer, default=0, comment="排序顺序")
    is_expanded: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否展开")
    
    # 统计
    watchlist_count: Mapped[int] = mapped_column(Integer, default=0, comment="列表数量")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class WatchlistShare(Base):
    """自选股分享表"""
    __tablename__ = "watchlist_shares"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    watchlist_id: Mapped[int] = mapped_column(Integer, index=True, comment="自选股列表ID")
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="分享用户ID")
    
    # 分享信息
    share_code: Mapped[str] = mapped_column(String(32), unique=True, index=True, comment="分享码")
    title: Mapped[str] = mapped_column(String(200), comment="分享标题")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="分享描述")
    
    # 权限设置
    is_public: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否公开")
    allow_copy: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否允许复制")
    password: Mapped[Optional[str]] = mapped_column(String(100), comment="访问密码")
    
    # 统计信息
    view_count: Mapped[int] = mapped_column(Integer, default=0, comment="查看次数")
    copy_count: Mapped[int] = mapped_column(Integer, default=0, comment="复制次数")
    like_count: Mapped[int] = mapped_column(Integer, default=0, comment="点赞次数")
    
    # 时间设置
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="过期时间")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_share_user_public', 'user_id', 'is_public'),
        Index('ix_share_expires', 'expires_at'),
    )


class WatchlistTemplate(Base):
    """自选股模板表"""
    __tablename__ = "watchlist_templates"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, index=True, comment="创建用户ID")
    
    # 模板信息
    name: Mapped[str] = mapped_column(String(100), comment="模板名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="模板描述")
    category: Mapped[str] = mapped_column(String(50), comment="模板分类")
    tags: Mapped[Optional[dict]] = mapped_column(JSON, comment="标签")
    
    # 模板内容
    stock_codes: Mapped[dict] = mapped_column(JSON, comment="股票代码列表")
    selection_criteria: Mapped[Optional[dict]] = mapped_column(JSON, comment="选股标准")
    
    # 设置
    is_system: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否系统模板")
    is_public: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否公开")
    
    # 统计
    use_count: Mapped[int] = mapped_column(Integer, default=0, comment="使用次数")
    rating: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2), comment="评分")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_template_category_public', 'category', 'is_public'),
        Index('ix_template_system', 'is_system'),
    )


class UserStockNote(Base):
    """用户股票笔记表"""
    __tablename__ = "user_stock_notes"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    stock_code: Mapped[str] = mapped_column(String(20), index=True, comment="股票代码")
    
    # 笔记内容
    title: Mapped[str] = mapped_column(String(200), comment="笔记标题")
    content: Mapped[str] = mapped_column(Text, comment="笔记内容")
    note_type: Mapped[str] = mapped_column(String(20), comment="笔记类型: analysis/news/idea/trade")
    
    # 关联信息
    related_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4), comment="关联价格")
    related_date: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="关联日期")
    tags: Mapped[Optional[dict]] = mapped_column(JSON, comment="标签")
    
    # 设置
    is_private: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否私有")
    is_important: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否重要")
    
    # 提醒
    reminder_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="提醒时间")
    is_reminded: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否已提醒")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_note_user_stock', 'user_id', 'stock_code'),
        Index('ix_note_type_important', 'note_type', 'is_important'),
        Index('ix_note_reminder', 'reminder_at', 'is_reminded'),
    )


class StockTag(Base):
    """股票标签表"""
    __tablename__ = "stock_tags"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    
    # 标签信息
    name: Mapped[str] = mapped_column(String(50), comment="标签名称")
    color: Mapped[str] = mapped_column(String(20), comment="标签颜色")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="标签描述")
    
    # 统计
    use_count: Mapped[int] = mapped_column(Integer, default=0, comment="使用次数")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_tag_user_name', 'user_id', 'name'),
    )


class UserStockTag(Base):
    """用户股票标签关联表"""
    __tablename__ = "user_stock_tags"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="用户ID")
    stock_code: Mapped[str] = mapped_column(String(20), index=True, comment="股票代码")
    tag_id: Mapped[int] = mapped_column(Integer, index=True, comment="标签ID")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_stock_tag_user_stock', 'user_id', 'stock_code'),
        Index('ix_stock_tag_tag', 'tag_id'),
    )
