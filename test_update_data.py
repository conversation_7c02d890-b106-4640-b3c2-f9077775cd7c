#!/usr/bin/env python3
"""
测试数据更新API的脚本
"""

import requests
import json
import time

def test_force_update_all():
    """测试强制更新所有数据API"""
    url = "http://localhost:8000/api/v1/data-management/akshare/force-update-all"
    
    try:
        print("🚀 开始调用强制更新所有数据API...")
        response = requests.post(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"📝 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 等待一段时间让后台任务执行
            print("\n⏳ 等待后台任务执行（60秒）...")
            time.sleep(60)
            
            # 检查更新状态
            check_update_status()
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📝 错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

def check_update_status():
    """检查数据更新状态"""
    url = "http://localhost:8000/api/v1/akshare/spot-data"
    
    try:
        print("\n🔍 检查实时行情数据...")
        response = requests.get(f"{url}?limit=5", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("data"):
                print("✅ 实时行情数据获取成功!")
                print(f"📊 数据条数: {len(result['data'])}")
                
                # 检查是否是真实数据（非模拟数据）
                first_stock = result["data"][0]
                if first_stock.get("current_price") != 10.5:  # 模拟数据的固定价格
                    print("🎉 数据库已更新为真实数据!")
                    print(f"📈 示例股票: {first_stock.get('stock_name')} ({first_stock.get('stock_code')})")
                    print(f"💰 当前价格: {first_stock.get('current_price')}")
                    print(f"📊 涨跌幅: {first_stock.get('change_percent')}%")
                else:
                    print("⚠️  数据库仍然是模拟数据")
            else:
                print("❌ 没有获取到实时行情数据")
        else:
            print(f"❌ 获取实时行情数据失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查数据状态失败: {e}")

def test_spot_data_update():
    """测试实时行情数据更新API"""
    url = "http://localhost:8000/api/v1/data-management/akshare/update-spot-data"
    
    try:
        print("\n🚀 开始调用实时行情数据更新API...")
        response = requests.post(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 实时行情数据更新API调用成功!")
            print(f"📝 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 实时行情数据更新API调用失败: {response.status_code}")
            print(f"📝 错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 调用实时行情数据更新API失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🔄 AKShare数据更新测试")
    print("=" * 60)
    
    # 首先测试实时行情数据更新
    test_spot_data_update()
    
    # 等待一段时间
    print("\n⏳ 等待30秒后进行强制更新...")
    time.sleep(30)
    
    # 然后测试强制更新所有数据
    test_force_update_all()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成!")
    print("=" * 60)
