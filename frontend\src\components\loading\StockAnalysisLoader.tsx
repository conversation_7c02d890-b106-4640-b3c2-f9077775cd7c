/**
 * 股票分析页面加载进度组件
 * 显示详细的数据获取和计算状态
 */

import React from 'react'
import { Card, Progress, Steps, Spin, Alert, Typography, Row, Col, Space, Button } from 'antd'
import {
  LoadingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  Bar<PERSON>hartOutlined,
  Line<PERSON>hartOutlined,
  DashboardOutlined,
  CloseOutlined,
  DatabaseOutlined,
  <PERSON>boltOutlined,
  RobotOutlined,
  RiseOutlined,
  AreaChartOutlined
} from '@ant-design/icons'
import PerformanceAnalyzer from '../performance/PerformanceAnalyzer'

const { Text, Title } = Typography
const { Step } = Steps

export interface LoadingStep {
  key: string
  title: string
  description: string
  status: 'waiting' | 'process' | 'finish' | 'error'
  error?: string
  duration?: number
  icon?: React.ReactNode
}

export interface StockAnalysisLoaderProps {
  visible: boolean
  steps: LoadingStep[]
  currentStep: number
  overallProgress: number
  stockCode?: string
  stockName?: string
  onCancel?: () => void
}

const StockAnalysisLoader: React.FC<StockAnalysisLoaderProps> = ({
  visible,
  steps,
  currentStep,
  overallProgress,
  stockCode,
  stockName,
  onCancel
}) => {
  if (!visible) return null

  const getStepIcon = (step: LoadingStep, index: number) => {
    // 根据步骤key获取对应图标
    const getDefaultIcon = (key: string) => {
      switch (key) {
        case 'kline': return <LineChartOutlined />
        case 'realtime': return <ThunderboltOutlined />
        case 'fundamentals': return <DatabaseOutlined />
        case 'rsi': return <RiseOutlined />
        case 'macd': return <BarChartOutlined />
        case 'kdj': return <DashboardOutlined />
        case 'bollinger': return <AreaChartOutlined />
        case 'keltner': return <AreaChartOutlined />
        case 'ai': return <RobotOutlined />
        default: return <LoadingOutlined />
      }
    }

    switch (step.status) {
      case 'finish':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      case 'process':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />
      default:
        return step.icon || getDefaultIcon(step.key)
    }
  }

  const getProgressStatus = () => {
    const hasError = steps.some(step => step.status === 'error')
    if (hasError) return 'exception'
    if (overallProgress === 100) return 'success'
    return 'active'
  }

  const completedSteps = steps.filter(step => step.status === 'finish').length
  const errorSteps = steps.filter(step => step.status === 'error').length
  const totalSteps = steps.length
  const isCompleted = overallProgress === 100

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: '800px',
          maxHeight: '90vh',
          overflow: 'auto'
        }}
        bodyStyle={{ padding: '32px' }}
      >
        {/* 头部信息 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={3} style={{ marginBottom: '8px' }}>
            <BarChartOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            正在加载股票分析数据
          </Title>
          {stockCode && stockName && (
            <Text type="secondary" style={{ fontSize: '16px' }}>
              {stockName} ({stockCode})
            </Text>
          )}
        </div>

        {/* 整体进度 */}
        <div style={{ marginBottom: '32px' }}>
          <Row justify="space-between" align="middle" style={{ marginBottom: '8px' }}>
            <Col>
              <Text strong>整体进度</Text>
            </Col>
            <Col>
              <Text strong style={{ color: '#1890ff' }}>
                {completedSteps}/{totalSteps} 步骤完成
              </Text>
            </Col>
          </Row>
          <Progress
            percent={overallProgress}
            status={getProgressStatus()}
            strokeWidth={8}
            format={(percent) => `${percent}%`}
          />
          {errorSteps > 0 && (
            <Text type="danger" style={{ fontSize: '12px', marginTop: '4px' }}>
              {errorSteps} 个步骤失败
            </Text>
          )}
        </div>

        {/* 详细步骤 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={4} style={{ marginBottom: '16px' }}>
            <DashboardOutlined style={{ marginRight: '8px' }} />
            加载详情
          </Title>
          
          <Steps
            direction="vertical"
            current={currentStep}
            size="small"
          >
            {steps.map((step, index) => (
              <Step
                key={step.key}
                title={
                  <Space>
                    {getStepIcon(step, index)}
                    <Text strong={step.status === 'process'}>
                      {step.title}
                    </Text>
                    {step.duration && step.status === 'finish' && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        ({step.duration}ms)
                      </Text>
                    )}
                  </Space>
                }
                description={
                  <div>
                    <Text type="secondary">{step.description}</Text>
                    {step.error && (
                      <Alert
                        message={step.error}
                        type="error"
                        size="small"
                        style={{ marginTop: '8px' }}
                        showIcon
                      />
                    )}
                  </div>
                }
                status={
                  step.status === 'error' ? 'error' :
                  step.status === 'finish' ? 'finish' :
                  step.status === 'process' ? 'process' : 'wait'
                }
              />
            ))}
          </Steps>
        </div>

        {/* 性能统计 */}
        {completedSteps > 0 && (
          <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
            <Title level={5} style={{ marginBottom: '12px' }}>
              <LineChartOutlined style={{ marginRight: '8px' }} />
              性能统计
            </Title>
            <Row gutter={[16, 8]}>
              <Col span={8}>
                <Text type="secondary">已完成步骤：</Text>
                <Text strong style={{ color: '#52c41a' }}>{completedSteps}</Text>
              </Col>
              <Col span={8}>
                <Text type="secondary">失败步骤：</Text>
                <Text strong style={{ color: '#ff4d4f' }}>{errorSteps}</Text>
              </Col>
              <Col span={8}>
                <Text type="secondary">总耗时：</Text>
                <Text strong>
                  {steps
                    .filter(step => step.duration)
                    .reduce((total, step) => total + (step.duration || 0), 0)
                  }ms
                </Text>
              </Col>
            </Row>
          </div>
        )}

        {/* 性能分析 */}
        <PerformanceAnalyzer
          steps={steps}
          visible={isCompleted}
        />

        {/* 底部操作 */}
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          {isCompleted ? (
            <Button
              type="primary"
              icon={<CloseOutlined />}
              onClick={onCancel}
            >
              关闭
            </Button>
          ) : (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              正在从多个数据源获取最新的股票数据，请稍候...
            </Text>
          )}
        </div>
      </Card>
    </div>
  )
}

export default StockAnalysisLoader
