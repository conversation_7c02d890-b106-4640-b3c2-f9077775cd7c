<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #1890ff 0%, #69c0ff 100%);
            color: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #52c41a;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #52c41a;
            margin: 8px 0;
        }
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 16px;
        }
        .content-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .test-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Dashboard页面重新设计完成！</h1>
            <p>新版Dashboard包含了现代化的设计、实时数据展示、快速操作按钮和美观的统计卡片。</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>投资组合总值</h3>
                <div class="stat-value">¥125,678.90</div>
                <small>较昨日 +2.34%</small>
            </div>
            <div class="stat-card">
                <h3>今日收益</h3>
                <div class="stat-value success">¥1,234.56</div>
                <small>盈利 0.98%</small>
            </div>
            <div class="stat-card">
                <h3>总收益率</h3>
                <div class="stat-value warning">15.67%</div>
                <small>年化收益率 18.9%</small>
            </div>
            <div class="stat-card">
                <h3>活跃预警</h3>
                <div class="stat-value error">4</div>
                <small>1 个高风险预警</small>
            </div>
        </div>

        <div class="content-grid">
            <div class="content-card">
                <h3>📊 市场概览</h3>
                <div class="test-item">
                    <strong>上证指数</strong>
                    <span style="float: right;">
                        <span>3,245.67</span>
                        <span class="success">+0.47%</span>
                    </span>
                </div>
                <div class="test-item">
                    <strong>深证成指</strong>
                    <span style="float: right;">
                        <span>12,456.78</span>
                        <span class="error">-0.19%</span>
                    </span>
                </div>
                <div class="test-item">
                    <strong>创业板指</strong>
                    <span style="float: right;">
                        <span>2,567.89</span>
                        <span class="success">+0.35%</span>
                    </span>
                </div>
                <div class="test-item">
                    <strong>科创50</strong>
                    <span style="float: right;">
                        <span>1,234.56</span>
                        <span class="error">-0.46%</span>
                    </span>
                </div>
            </div>

            <div class="content-card">
                <h3>⭐ 热门股票</h3>
                <div class="test-item">
                    <strong>000001 平安银行</strong>
                    <span style="float: right;">
                        <span>¥12.45</span>
                        <span class="success">+1.88%</span>
                    </span>
                </div>
                <div class="test-item">
                    <strong>000002 万科A</strong>
                    <span style="float: right;">
                        <span>¥18.67</span>
                        <span class="error">-2.35%</span>
                    </span>
                </div>
                <div class="test-item">
                    <strong>600000 浦发银行</strong>
                    <span style="float: right;">
                        <span>¥9.87</span>
                        <span class="success">+1.23%</span>
                    </span>
                </div>
                <div class="test-item">
                    <strong>600036 招商银行</strong>
                    <span style="float: right;">
                        <span>¥45.67</span>
                        <span class="success">+2.77%</span>
                    </span>
                </div>
            </div>

            <div class="content-card">
                <h3>🔔 最新预警</h3>
                <div class="test-item">
                    <strong>价格预警</strong> <small class="success">000001</small>
                    <br><small>平安银行突破阻力位 12.50</small>
                    <span style="float: right; font-size: 12px;">10:30</span>
                </div>
                <div class="test-item">
                    <strong>AI信号</strong> <small class="warning">000002</small>
                    <br><small>万科A出现强烈买入信号</small>
                    <span style="float: right; font-size: 12px;">09:45</span>
                </div>
                <div class="test-item">
                    <strong>技术指标</strong> <small class="error">600036</small>
                    <br><small>招商银行RSI达到超买区域</small>
                    <span style="float: right; font-size: 12px;">09:15</span>
                </div>
                <div class="test-item">
                    <strong>成交量</strong> <small class="warning">000858</small>
                    <br><small>五粮液成交量异常放大</small>
                    <span style="float: right; font-size: 12px;">08:50</span>
                </div>
            </div>

            <div class="content-card">
                <h3>📈 投资组合表现</h3>
                <div style="margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span><strong>股票配置</strong></span>
                        <span>75%</span>
                    </div>
                    <div style="background: #f0f0f0; height: 8px; border-radius: 4px;">
                        <div style="background: #52c41a; height: 8px; width: 75%; border-radius: 4px;"></div>
                    </div>
                    <small>¥94,259</small>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span><strong>基金配置</strong></span>
                        <span>20%</span>
                    </div>
                    <div style="background: #f0f0f0; height: 8px; border-radius: 4px;">
                        <div style="background: #1890ff; height: 8px; width: 20%; border-radius: 4px;"></div>
                    </div>
                    <small>¥25,136</small>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span><strong>现金配置</strong></span>
                        <span>5%</span>
                    </div>
                    <div style="background: #f0f0f0; height: 8px; border-radius: 4px;">
                        <div style="background: #faad14; height: 8px; width: 5%; border-radius: 4px;"></div>
                    </div>
                    <small>¥6,284</small>
                </div>

                <hr style="margin: 16px 0;">
                
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; text-align: center;">
                    <div>
                        <div style="font-size: 18px; font-weight: bold;">12</div>
                        <small>持仓股票</small>
                    </div>
                    <div>
                        <div style="font-size: 18px; font-weight: bold; color: #52c41a;">8</div>
                        <small>盈利股票</small>
                    </div>
                    <div>
                        <div style="font-size: 18px; font-weight: bold; color: #ff4d4f;">4</div>
                        <small>亏损股票</small>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 24px; padding: 16px; background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 8px;">
            <h4 style="color: #52c41a; margin: 0 0 8px 0;">✅ Dashboard重新设计完成</h4>
            <p style="margin: 0; color: #389e0d;">
                新版Dashboard采用了现代化的设计语言，包含实时数据展示、快速操作入口、美观的统计卡片和完整的功能模块。
                用户体验得到了显著提升！
            </p>
        </div>

        <div style="margin-top: 16px; text-align: center;">
            <a href="http://localhost:3000" style="
                display: inline-block;
                padding: 12px 24px;
                background: #1890ff;
                color: white;
                text-decoration: none;
                border-radius: 6px;
                font-weight: bold;
            ">🚀 查看实际Dashboard</a>
        </div>
    </div>
</body>
</html>
