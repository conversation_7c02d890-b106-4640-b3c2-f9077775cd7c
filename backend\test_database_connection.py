"""
测试数据库连接和基础功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import health_check, AsyncSessionLocal
from app.core.logging import setup_logging, get_logger
from app.models.stock import Stock, KlineData
from app.services.data_storage import DataStorageService
from sqlalchemy import select, func
from datetime import datetime, date
from decimal import Decimal

logger = get_logger(__name__)


async def test_database_health():
    """测试数据库健康状态"""
    logger.info("测试数据库健康状态...")
    
    health_status = await health_check()
    logger.info(f"健康状态: {health_status}")
    
    return health_status['overall'] == 'healthy'


async def test_basic_operations():
    """测试基础数据库操作"""
    logger.info("测试基础数据库操作...")
    
    try:
        async with DataStorageService() as storage:
            # 测试插入股票数据
            test_stock_data = [{
                'stock_code': 'TEST001',
                'stock_name': '测试股票',
                'market': 'A股',
                'industry': '测试行业'
            }]
            
            saved_count = await storage.save_stocks(test_stock_data)
            logger.info(f"保存股票数据: {saved_count} 条")
            
            # 测试查询股票数据
            stocks = await storage.get_stock_list(limit=5)
            logger.info(f"查询到股票数量: {len(stocks)}")
            
            if stocks:
                for stock in stocks:
                    logger.info(f"股票: {stock.stock_code} - {stock.stock_name}")
            
            # 测试插入K线数据
            if stocks:
                test_kline_data = [{
                    'stock_code': stocks[0].stock_code,
                    'trade_date': date.today(),
                    'period': 'daily',
                    'open_price': 10.0,
                    'high_price': 11.0,
                    'low_price': 9.5,
                    'close_price': 10.5,
                    'volume': 1000000,
                    'turnover': 10500000.0,
                    'change': 0.5,
                    'change_percent': 5.0
                }]

                kline_saved = await storage.save_kline_data(test_kline_data)
                logger.info(f"保存K线数据: {kline_saved} 条")
                
                # 查询K线数据
                klines = await storage.get_kline_data(stocks[0].stock_code, limit=5)
                logger.info(f"查询到K线数据: {len(klines)} 条")
        
        return True
        
    except Exception as e:
        logger.error(f"基础操作测试失败: {e}")
        return False


async def test_data_statistics():
    """测试数据统计"""
    logger.info("测试数据统计...")
    
    try:
        async with AsyncSessionLocal() as session:
            # 统计股票数量
            result = await session.execute(select(func.count(Stock.id)))
            stock_count = result.scalar()
            logger.info(f"股票总数: {stock_count}")
            
            # 统计K线数据数量
            result = await session.execute(select(func.count(KlineData.id)))
            kline_count = result.scalar()
            logger.info(f"K线数据总数: {kline_count}")
            
            # 查询最新的几条股票记录
            result = await session.execute(
                select(Stock).order_by(Stock.created_at.desc()).limit(3)
            )
            recent_stocks = result.scalars().all()
            
            logger.info("最新股票记录:")
            for stock in recent_stocks:
                logger.info(f"  {stock.stock_code} - {stock.stock_name} ({stock.created_at})")
        
        return True
        
    except Exception as e:
        logger.error(f"数据统计测试失败: {e}")
        return False


async def main():
    """主函数"""
    setup_logging()
    
    logger.info("🚀 开始数据库连接和功能测试")
    
    # 测试1: 数据库健康检查
    logger.info("=" * 50)
    logger.info("测试1: 数据库健康检查")
    health_ok = await test_database_health()
    
    if not health_ok:
        logger.error("数据库健康检查失败，停止测试")
        return
    
    # 测试2: 基础操作
    logger.info("=" * 50)
    logger.info("测试2: 基础数据库操作")
    basic_ok = await test_basic_operations()
    
    # 测试3: 数据统计
    logger.info("=" * 50)
    logger.info("测试3: 数据统计")
    stats_ok = await test_data_statistics()
    
    # 总结
    logger.info("=" * 50)
    logger.info("测试总结:")
    logger.info(f"  数据库健康: {'✅' if health_ok else '❌'}")
    logger.info(f"  基础操作: {'✅' if basic_ok else '❌'}")
    logger.info(f"  数据统计: {'✅' if stats_ok else '❌'}")
    
    if all([health_ok, basic_ok, stats_ok]):
        logger.info("🎉 所有测试通过！数据库连接和基础功能正常")
    else:
        logger.error("❌ 部分测试失败，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())
