"""
数据同步服务模块
"""

import asyncio
from typing import List, Optional
from datetime import datetime, date, timedelta
from app.core.logging import get_logger
from app.services.data_fetcher import DataFetcherManager
from app.services.data_storage import DataStorageService

logger = get_logger(__name__)


class DataSyncService:
    """数据同步服务"""
    
    def __init__(self):
        self.fetcher_manager = DataFetcherManager()
        
    async def sync_stock_list(self) -> bool:
        """同步股票列表"""
        try:
            logger.info("开始同步股票列表...")

            # 获取清洗后的股票列表
            stocks_data = await self.fetcher_manager.get_cleaned_stock_list()

            if not stocks_data:
                logger.warning("未获取到股票数据")
                return False

            # 保存到数据库
            async with DataStorageService() as storage:
                saved_count = await storage.save_stocks(stocks_data)

            logger.info(f"股票列表同步完成，保存 {saved_count} 条记录")
            return saved_count > 0

        except Exception as e:
            logger.error(f"同步股票列表失败: {e}")
            return False
    
    async def sync_kline_data(
        self, 
        stock_codes: List[str],
        period: str = "daily",
        days: int = 30
    ) -> bool:
        """同步K线数据"""
        try:
            logger.info(f"开始同步 {len(stock_codes)} 只股票的K线数据...")
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            total_saved = 0
            
            for stock_code in stock_codes:
                try:
                    # 获取清洗后的K线数据
                    kline_data = await self.fetcher_manager.get_cleaned_kline_data(
                        stock_code, period, start_date, end_date
                    )

                    if kline_data:
                        # 保存到数据库
                        async with DataStorageService() as storage:
                            saved_count = await storage.save_kline_data(kline_data)
                            total_saved += saved_count

                    # 避免请求过于频繁
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.error(f"同步股票 {stock_code} K线数据失败: {e}")
                    continue
            
            logger.info(f"K线数据同步完成，总共保存 {total_saved} 条记录")
            return total_saved > 0
            
        except Exception as e:
            logger.error(f"同步K线数据失败: {e}")
            return False
    
    async def sync_realtime_data(self, stock_codes: List[str]) -> bool:
        """同步实时数据"""
        try:
            logger.info(f"开始同步 {len(stock_codes)} 只股票的实时数据...")
            
            # 分批获取实时数据（避免单次请求过多）
            batch_size = 50
            total_saved = 0
            
            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]
                
                try:
                    # 获取清洗后的实时数据
                    realtime_data = await self.fetcher_manager.get_cleaned_realtime_data(batch_codes)

                    if realtime_data:
                        # 保存到数据库
                        async with DataStorageService() as storage:
                            saved_count = await storage.save_realtime_data(realtime_data)
                            total_saved += saved_count

                    # 批次间隔
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"同步批次 {i//batch_size + 1} 实时数据失败: {e}")
                    continue
            
            logger.info(f"实时数据同步完成，总共保存 {total_saved} 条记录")
            return total_saved > 0
            
        except Exception as e:
            logger.error(f"同步实时数据失败: {e}")
            return False
    
    async def sync_popular_stocks_data(self, days: int = 7) -> bool:
        """同步热门股票数据"""
        try:
            logger.info("开始同步热门股票数据...")
            
            # 获取热门股票代码（这里可以根据实际需求调整）
            popular_stocks = [
                "000001",  # 平安银行
                "000002",  # 万科A
                "000858",  # 五粮液
                "002415",  # 海康威视
                "600036",  # 招商银行
                "600519",  # 贵州茅台
                "600887",  # 伊利股份
                "000858",  # 五粮液
            ]
            
            # 同步K线数据
            success = await self.sync_kline_data(popular_stocks, "daily", days)
            
            if success:
                logger.info("热门股票数据同步完成")
            else:
                logger.warning("热门股票数据同步失败")
                
            return success
            
        except Exception as e:
            logger.error(f"同步热门股票数据失败: {e}")
            return False
    
    async def full_sync(self) -> bool:
        """完整数据同步"""
        try:
            logger.info("开始完整数据同步...")
            
            # 1. 同步股票列表
            stock_list_success = await self.sync_stock_list()
            
            if not stock_list_success:
                logger.error("股票列表同步失败，终止完整同步")
                return False
            
            # 2. 获取前100只股票进行数据同步
            async with DataStorageService() as storage:
                stocks = await storage.get_stocks(limit=100)
                stock_codes = [stock.stock_code for stock in stocks]
            
            if not stock_codes:
                logger.warning("未找到股票代码，跳过K线数据同步")
                return stock_list_success
            
            # 3. 同步K线数据（最近30天）
            kline_success = await self.sync_kline_data(stock_codes[:20], "daily", 30)  # 限制前20只
            
            # 4. 同步实时数据
            realtime_success = await self.sync_realtime_data(stock_codes[:10])  # 限制前10只
            
            overall_success = stock_list_success and kline_success and realtime_success
            
            if overall_success:
                logger.info("完整数据同步成功")
            else:
                logger.warning("完整数据同步部分失败")
                
            return overall_success
            
        except Exception as e:
            logger.error(f"完整数据同步失败: {e}")
            return False
    
    async def incremental_sync(self) -> bool:
        """增量数据同步（仅同步最新数据）"""
        try:
            logger.info("开始增量数据同步...")
            
            # 获取活跃股票列表
            async with DataStorageService() as storage:
                stocks = await storage.get_stocks(limit=50)  # 限制数量
                stock_codes = [stock.stock_code for stock in stocks]
            
            if not stock_codes:
                logger.warning("未找到股票代码，跳过增量同步")
                return False
            
            # 同步最近3天的K线数据
            kline_success = await self.sync_kline_data(stock_codes, "daily", 3)
            
            # 同步实时数据
            realtime_success = await self.sync_realtime_data(stock_codes)
            
            success = kline_success and realtime_success
            
            if success:
                logger.info("增量数据同步成功")
            else:
                logger.warning("增量数据同步失败")
                
            return success
            
        except Exception as e:
            logger.error(f"增量数据同步失败: {e}")
            return False
