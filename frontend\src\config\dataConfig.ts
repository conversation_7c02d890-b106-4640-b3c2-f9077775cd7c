/**
 * 数据配置文件
 * 管理数据源、API端点和缓存设置
 */

// 环境配置
export const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
} as const

// 当前环境
export const CURRENT_ENV = import.meta.env.MODE || ENV.DEVELOPMENT

// API配置
export const API_CONFIG = {
  // 基础URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  
  // 超时设置
  TIMEOUT: 30000,
  
  // 重试次数
  RETRY_COUNT: 3,
  
  // API端点
  ENDPOINTS: {
    STOCKS: '/stocks',
    KLINE: '/stocks/{symbol}/kline',
    INDICATORS: '/stocks/{symbol}/indicators',
    REALTIME: '/stocks/{symbol}/realtime',
    SEARCH: '/stocks/search',
    WATCHLIST: '/user/watchlist'
  }
}

// 数据源配置
export const DATA_SOURCE_CONFIG = {
  // 是否使用真实API（false=使用模拟数据）
  USE_REAL_API: import.meta.env.VITE_USE_REAL_API === 'true' || true, // 默认使用真实API
  
  // 模拟数据配置
  MOCK_CONFIG: {
    // 模拟网络延迟（毫秒）
    NETWORK_DELAY: CURRENT_ENV === ENV.DEVELOPMENT ? 500 : 100,
    
    // 是否模拟网络错误
    SIMULATE_ERRORS: CURRENT_ENV === ENV.DEVELOPMENT,
    
    // 错误概率（0-1）
    ERROR_PROBABILITY: 0.1,
    
    // 默认股票列表
    DEFAULT_STOCKS: ['000001', '000002', '600000', '600036', '000858', '002415', '600519', '000002']
  }
}

// 缓存配置
export const CACHE_CONFIG = {
  // 股票数据缓存时间（毫秒）
  STOCK_DATA_TTL: 5 * 60 * 1000, // 5分钟
  
  // 价格数据缓存时间
  PRICE_DATA_TTL: 1 * 60 * 1000, // 1分钟
  
  // 技术指标缓存时间
  INDICATORS_TTL: 10 * 60 * 1000, // 10分钟
  
  // 最大缓存条目数
  MAX_CACHE_SIZE: 100,
  
  // 本地存储键名
  STORAGE_KEYS: {
    STOCK_DATA: 'stock-data-store',
    USER_PREFERENCES: 'user-preferences',
    SEARCH_HISTORY: 'search-history'
  }
}

// 实时数据配置
export const REALTIME_CONFIG = {
  // WebSocket URL
  WS_URL: import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws',
  
  // 重连配置
  RECONNECT: {
    MAX_ATTEMPTS: 5,
    DELAY: 1000,
    BACKOFF_FACTOR: 1.5
  },
  
  // 心跳配置
  HEARTBEAT: {
    INTERVAL: 30000,
    TIMEOUT: 5000
  }
}

import { CANDLESTICK_COLORS, INDICATOR_COLORS } from './colors'

// 图表配置
export const CHART_CONFIG = {
  // 默认显示的数据点数量
  DEFAULT_DATA_POINTS: 100,

  // K线图配置
  CANDLESTICK: CANDLESTICK_COLORS,

  // 技术指标颜色
  INDICATOR_COLORS: INDICATOR_COLORS
}

// 性能配置
export const PERFORMANCE_CONFIG = {
  // 虚拟滚动阈值
  VIRTUAL_SCROLL_THRESHOLD: 1000,
  
  // 防抖延迟（毫秒）
  DEBOUNCE_DELAY: 300,
  
  // 节流延迟（毫秒）
  THROTTLE_DELAY: 100,
  
  // 懒加载配置
  LAZY_LOAD: {
    ROOT_MARGIN: '50px',
    THRESHOLD: 0.1
  }
}

// 用户体验配置
export const UX_CONFIG = {
  // 消息显示时间（毫秒）
  MESSAGE_DURATION: 3000,
  
  // 加载动画最小显示时间
  MIN_LOADING_TIME: 500,
  
  // 自动刷新间隔（毫秒）
  AUTO_REFRESH_INTERVAL: 60000, // 1分钟
  
  // 搜索历史最大条目数
  MAX_SEARCH_HISTORY: 10
}

// 开发工具配置
export const DEV_CONFIG = {
  // 是否启用调试日志
  ENABLE_DEBUG_LOGS: CURRENT_ENV === ENV.DEVELOPMENT,
  
  // 是否显示性能监控
  ENABLE_PERFORMANCE_MONITOR: CURRENT_ENV === ENV.DEVELOPMENT,
  
  // 是否启用Redux DevTools
  ENABLE_REDUX_DEVTOOLS: CURRENT_ENV === ENV.DEVELOPMENT
}

// 导出统一配置对象
export const CONFIG = {
  ENV: CURRENT_ENV,
  API: API_CONFIG,
  DATA_SOURCE: DATA_SOURCE_CONFIG,
  CACHE: CACHE_CONFIG,
  REALTIME: REALTIME_CONFIG,
  CHART: CHART_CONFIG,
  PERFORMANCE: PERFORMANCE_CONFIG,
  UX: UX_CONFIG,
  DEV: DEV_CONFIG
} as const

// 配置验证函数
export const validateConfig = (): boolean => {
  const errors: string[] = []
  
  // 验证API配置
  if (!API_CONFIG.BASE_URL) {
    errors.push('API_CONFIG.BASE_URL is required')
  }
  
  // 验证WebSocket配置
  if (DATA_SOURCE_CONFIG.USE_REAL_API && !REALTIME_CONFIG.WS_URL) {
    errors.push('REALTIME_CONFIG.WS_URL is required when using real API')
  }
  
  // 验证缓存配置
  if (CACHE_CONFIG.STOCK_DATA_TTL <= 0) {
    errors.push('CACHE_CONFIG.STOCK_DATA_TTL must be positive')
  }
  
  if (errors.length > 0) {
    console.error('Configuration validation failed:', errors)
    return false
  }
  
  return true
}

// 获取配置信息（用于调试）
export const getConfigInfo = () => {
  return {
    environment: CURRENT_ENV,
    dataSource: DATA_SOURCE_CONFIG.USE_REAL_API ? 'Real API' : 'Mock Data',
    apiUrl: API_CONFIG.BASE_URL,
    wsUrl: REALTIME_CONFIG.WS_URL,
    cacheEnabled: true,
    debugMode: DEV_CONFIG.ENABLE_DEBUG_LOGS
  }
}

// 在开发环境下打印配置信息
if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
  console.log('📊 Stock Analysis App Configuration:', getConfigInfo())
  
  if (!validateConfig()) {
    console.warn('⚠️ Configuration validation failed!')
  }
}

export default CONFIG
