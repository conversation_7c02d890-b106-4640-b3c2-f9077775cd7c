import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Button,
  Table,
  Tabs,
  Typography,
  Space,
  Tag,
  Statistic,
  Progress,
  Alert,
  Spin,
  Tooltip,
  Descriptions,
} from 'antd'
import {
  DollarOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons'
import { Column, Pie, Gauge } from '@ant-design/plots'
import FundamentalAnalysis, { 
  FinancialData, 
  FinancialQuality, 
  ValuationMetrics, 
  DCFValuation,
  ComparableAnalysis 
} from '@/utils/fundamentalAnalysis'

const { Title, Text } = Typography
const { Option } = Select

interface FundamentalAnalysisData {
  financialData: FinancialData
  prevFinancialData: FinancialData
  financialQuality: FinancialQuality
  valuationMetrics: ValuationMetrics
  dcfValuation: DCFValuation
  comparableAnalysis: ComparableAnalysis
}

const FundamentalAnalysisPage: React.FC = () => {
  const [selectedStock, setSelectedStock] = useState('000001')
  const [loading, setLoading] = useState(false)
  const [analysisData, setAnalysisData] = useState<FundamentalAnalysisData | null>(null)
  const [activeTab, setActiveTab] = useState('quality')

  useEffect(() => {
    loadFundamentalAnalysis()
  }, [selectedStock])

  const loadFundamentalAnalysis = async () => {
    setLoading(true)
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 模拟财务数据
      const mockFinancialData: FinancialData = {
        totalAssets: **********,
        totalLiabilities: **********,
        shareholdersEquity: *********,
        currentAssets: ********0,
        currentLiabilities: *********,
        cash: *********,
        inventory: *********,
        accountsReceivable: *********,
        longTermDebt: ********0,
        revenue: 1*********,
        grossProfit: *********,
        operatingIncome: *********,
        netIncome: *********,
        ebitda: *********,
        interestExpense: ********,
        operatingCashFlow: *********,
        investingCashFlow: -********,
        financingCashFlow: -********,
        freeCashFlow: *********,
        marketCap: **********,
        sharesOutstanding: *********,
        stockPrice: 18.0,
        period: '2024Q3',
        year: 2024,
        quarter: 3
      }

      const mockPrevFinancialData: FinancialData = {
        ...mockFinancialData,
        revenue: 1*********,
        netIncome: 1********,
        operatingCashFlow: *********,
        period: '2023Q3',
        year: 2023,
        quarter: 3
      }

      // 计算分析指标
      const financialQuality = FundamentalAnalysis.assessFinancialQuality(mockFinancialData, mockPrevFinancialData)
      const valuationMetrics = FundamentalAnalysis.calculateValuationMetrics(mockFinancialData)
      const dcfValuation = FundamentalAnalysis.calculateDCFValuation(
        mockFinancialData,
        [0.15, 0.12, 0.10, 0.08, 0.06], // 5年增长率
        0.03, // 永续增长率
        0.10  // WACC
      )

      // 模拟行业对比数据
      const industryData: FinancialData[] = [
        mockFinancialData,
        { ...mockFinancialData, stockPrice: 20.0, netIncome: ********* },
        { ...mockFinancialData, stockPrice: 15.0, netIncome: ********* },
      ]
      const comparableAnalysis = FundamentalAnalysis.performComparableAnalysis(mockFinancialData, industryData)

      setAnalysisData({
        financialData: mockFinancialData,
        prevFinancialData: mockPrevFinancialData,
        financialQuality,
        valuationMetrics,
        dcfValuation,
        comparableAnalysis
      })
    } catch (error) {
      console.error('Failed to load fundamental analysis:', error)
    } finally {
      setLoading(false)
    }
  }

  const renderFinancialQuality = () => {
    if (!analysisData) return null

    const { financialQuality } = analysisData

    const getScoreColor = (score: number, max: number) => {
      const ratio = score / max
      if (ratio >= 0.8) return '#52c41a'
      if (ratio >= 0.6) return '#faad14'
      return '#ff4d4f'
    }

    const getZScoreStatus = (score: number) => {
      if (score > 2.99) return { status: 'success', text: '安全区域', color: '#52c41a' }
      if (score > 1.81) return { status: 'warning', text: '灰色区域', color: '#faad14' }
      return { status: 'error', text: '危险区域', color: '#ff4d4f' }
    }

    const getMScoreStatus = (score: number) => {
      if (score < -2.22) return { status: 'success', text: '低风险', color: '#52c41a' }
      return { status: 'error', text: '高风险', color: '#ff4d4f' }
    }

    const zScoreStatus = getZScoreStatus(financialQuality.altmanZScore)
    const mScoreStatus = getMScoreStatus(financialQuality.benishMScore)

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="财务健康评分" size="small">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="Piotroski F-Score"
                  value={financialQuality.pitroskiScore}
                  suffix="/ 9"
                  valueStyle={{ color: getScoreColor(financialQuality.pitroskiScore, 9) }}
                />
                <Progress
                  percent={(financialQuality.pitroskiScore / 9) * 100}
                  strokeColor={getScoreColor(financialQuality.pitroskiScore, 9)}
                  size="small"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="盈利质量"
                  value={financialQuality.earningsQuality}
                  precision={2}
                  valueStyle={{ color: financialQuality.earningsQuality > 1 ? '#52c41a' : '#ff4d4f' }}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  经营现金流/净利润
                </Text>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="风险评估" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Altman Z-Score: </Text>
                <Tag color={zScoreStatus.color}>{financialQuality.altmanZScore.toFixed(2)}</Tag>
                <Tag color={zScoreStatus.status}>{zScoreStatus.text}</Tag>
              </div>
              <div>
                <Text strong>Beneish M-Score: </Text>
                <Tag color={mScoreStatus.color}>{financialQuality.benishMScore.toFixed(2)}</Tag>
                <Tag color={mScoreStatus.status}>{mScoreStatus.text}</Tag>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="财务比率分析" size="small">
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="流动比率"
                  value={financialQuality.currentRatio}
                  precision={2}
                  valueStyle={{ color: financialQuality.currentRatio > 1.5 ? '#52c41a' : '#faad14' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="速动比率"
                  value={financialQuality.quickRatio}
                  precision={2}
                  valueStyle={{ color: financialQuality.quickRatio > 1 ? '#52c41a' : '#faad14' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="资产负债率"
                  value={financialQuality.debtToEquity}
                  precision={2}
                  valueStyle={{ color: financialQuality.debtToEquity < 1 ? '#52c41a' : '#ff4d4f' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="现金转换周期"
                  value={financialQuality.cashConversionCycle}
                  precision={0}
                  suffix="天"
                  valueStyle={{ color: financialQuality.cashConversionCycle < 60 ? '#52c41a' : '#faad14' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    )
  }

  const renderValuation = () => {
    if (!analysisData) return null

    const { valuationMetrics, dcfValuation, comparableAnalysis } = analysisData

    const valuationData = [
      { metric: 'P/E', value: valuationMetrics.pe, industry: comparableAnalysis.industryAvgPE },
      { metric: 'P/B', value: valuationMetrics.pb, industry: comparableAnalysis.industryAvgPB },
      { metric: 'P/S', value: valuationMetrics.ps, industry: 2.5 },
      { metric: 'EV/EBITDA', value: valuationMetrics.evEbitda, industry: 12.0 },
    ]

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="估值指标对比" size="small">
            <Column
              data={valuationData.flatMap(item => [
                { metric: item.metric, type: '当前', value: item.value },
                { metric: item.metric, type: '行业均值', value: item.industry }
              ])}
              xField="metric"
              yField="value"
              seriesField="type"
              isGroup={true}
              height={250}
              color={['#1890ff', '#52c41a']}
            />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="DCF估值模型" size="small">
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="内在价值">¥{dcfValuation.intrinsicValue.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="当前价格">¥{dcfValuation.currentPrice.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="上涨空间">
                <Tag color={dcfValuation.upside > 0 ? 'green' : 'red'}>
                  {(dcfValuation.upside * 100).toFixed(1)}%
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="WACC">{(dcfValuation.wacc * 100).toFixed(1)}%</Descriptions.Item>
              <Descriptions.Item label="永续增长率">{(dcfValuation.terminalGrowthRate * 100).toFixed(1)}%</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="相对估值分析" size="small">
            <Alert
              message="估值结论"
              description={`基于行业对比分析，该股票目前${
                comparableAnalysis.relativeValuation === 'undervalued' ? '被低估' :
                comparableAnalysis.relativeValuation === 'overvalued' ? '被高估' : '估值合理'
              }。在同行业中排名第${comparableAnalysis.percentileRanking.toFixed(0)}百分位。`}
              type={
                comparableAnalysis.relativeValuation === 'undervalued' ? 'success' :
                comparableAnalysis.relativeValuation === 'overvalued' ? 'warning' : 'info'
              }
              showIcon
            />
          </Card>
        </Col>
      </Row>
    )
  }

  const renderFinancialData = () => {
    if (!analysisData) return null

    const { financialData } = analysisData

    const balanceSheetData = [
      { item: '总资产', value: financialData.totalAssets / 1000000, unit: '百万' },
      { item: '总负债', value: financialData.totalLiabilities / 1000000, unit: '百万' },
      { item: '股东权益', value: financialData.shareholdersEquity / 1000000, unit: '百万' },
      { item: '现金', value: financialData.cash / 1000000, unit: '百万' },
    ]

    const incomeStatementData = [
      { item: '营业收入', value: financialData.revenue / 1000000, unit: '百万' },
      { item: '毛利润', value: financialData.grossProfit / 1000000, unit: '百万' },
      { item: '营业利润', value: financialData.operatingIncome / 1000000, unit: '百万' },
      { item: '净利润', value: financialData.netIncome / 1000000, unit: '百万' },
    ]

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="资产负债表" size="small">
            <Table
              dataSource={balanceSheetData}
              size="small"
              pagination={false}
              columns={[
                { title: '项目', dataIndex: 'item', key: 'item' },
                { 
                  title: '金额(百万)', 
                  dataIndex: 'value', 
                  key: 'value',
                  render: (value: number) => value.toFixed(0)
                },
              ]}
            />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="利润表" size="small">
            <Table
              dataSource={incomeStatementData}
              size="small"
              pagination={false}
              columns={[
                { title: '项目', dataIndex: 'item', key: 'item' },
                { 
                  title: '金额(百万)', 
                  dataIndex: 'value', 
                  key: 'value',
                  render: (value: number) => value.toFixed(0)
                },
              ]}
            />
          </Card>
        </Col>
      </Row>
    )
  }

  const tabItems = [
    {
      key: 'quality',
      label: (
        <span>
          <CheckCircleOutlined />
          财务质量
        </span>
      ),
      children: renderFinancialQuality(),
    },
    {
      key: 'valuation',
      label: (
        <span>
          <DollarOutlined />
          估值分析
        </span>
      ),
      children: renderValuation(),
    },
    {
      key: 'financial',
      label: (
        <span>
          <InfoCircleOutlined />
          财务数据
        </span>
      ),
      children: renderFinancialData(),
    },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>基本面分析</Title>
        <Space>
          <Select
            value={selectedStock}
            onChange={setSelectedStock}
            style={{ width: 120 }}
          >
            <Option value="000001">平安银行</Option>
            <Option value="600519">贵州茅台</Option>
            <Option value="300750">宁德时代</Option>
            <Option value="002594">比亚迪</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadFundamentalAnalysis}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在分析财务数据...</Text>
          </div>
        </div>
      ) : (
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      )}
    </div>
  )
}

export default FundamentalAnalysisPage
