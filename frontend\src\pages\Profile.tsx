import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  Avatar,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  Select,
  DatePicker,
  message,
  Tabs,
  Table,
  Tag,
} from 'antd'
import {
  UserOutlined,
  UploadOutlined,
  EditOutlined,
  SaveOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select

interface UserProfile {
  username: string
  email: string
  phone: string
  realName: string
  gender: string
  birthday: string
  location: string
  bio: string
  avatar: string
  riskLevel: string
  investmentExperience: string
}

interface LoginRecord {
  id: string
  loginTime: string
  ip: string
  location: string
  device: string
  status: string
}

const Profile: React.FC = () => {
  const [form] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [editing, setEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [profile, setProfile] = useState<UserProfile>({
    username: 'admin',
    email: '<EMAIL>',
    phone: '138****8888',
    realName: '张三',
    gender: 'male',
    birthday: '1990-01-01',
    location: '北京市',
    bio: '专业投资者，专注于价值投资和长期持有策略。',
    avatar: '',
    riskLevel: 'moderate',
    investmentExperience: '5-10年',
  })

  const [loginRecords] = useState<LoginRecord[]>([
    {
      id: '1',
      loginTime: '2024-01-28 19:30:15',
      ip: '*************',
      location: '北京市',
      device: 'Chrome 120.0 / Windows 10',
      status: 'success',
    },
    {
      id: '2',
      loginTime: '2024-01-28 09:15:32',
      ip: '*************',
      location: '北京市',
      device: 'Chrome 120.0 / Windows 10',
      status: 'success',
    },
    {
      id: '3',
      loginTime: '2024-01-27 18:45:21',
      ip: '*************',
      location: '北京市',
      device: 'Chrome 120.0 / Windows 10',
      status: 'success',
    },
    {
      id: '4',
      loginTime: '2024-01-27 08:30:45',
      ip: '*********',
      location: '上海市',
      device: 'Safari 17.0 / macOS',
      status: 'failed',
    },
  ])

  const handleSave = async (values: any) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setProfile({ ...profile, ...values })
      setEditing(false)
      message.success('个人信息更新成功！')
    } catch (error) {
      message.error('更新失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordChange = async (values: any) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('密码修改成功！')
      passwordForm.resetFields()
    } catch (error) {
      message.error('密码修改失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功！')
      // 这里应该更新头像URL
    } else if (info.file.status === 'error') {
      message.error('头像上传失败！')
    }
  }

  const loginColumns = [
    {
      title: '登录时间',
      dataIndex: 'loginTime',
      key: 'loginTime',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '登录地点',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '设备信息',
      dataIndex: 'device',
      key: 'device',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'success' ? 'green' : 'red'}>
          {status === 'success' ? '成功' : '失败'}
        </Tag>
      ),
    },
  ]

  const tabItems = [
    {
      key: 'profile',
      label: '基本信息',
      children: (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card>
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    size={120}
                    icon={<UserOutlined />}
                    src={profile.avatar}
                    style={{ marginBottom: 16 }}
                  />
                  <div>
                    <Upload
                      showUploadList={false}
                      onChange={handleAvatarChange}
                      beforeUpload={() => false}
                    >
                      <Button icon={<UploadOutlined />}>
                        更换头像
                      </Button>
                    </Upload>
                  </div>
                  <Divider />
                  <Space direction="vertical" size={8}>
                    <Title level={4}>{profile.realName}</Title>
                    <Text type="secondary">@{profile.username}</Text>
                    <Text>{profile.bio}</Text>
                  </Space>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={16}>
              <Card
                title="个人信息"
                extra={
                  <Button
                    type={editing ? 'default' : 'primary'}
                    icon={editing ? <SaveOutlined /> : <EditOutlined />}
                    onClick={() => {
                      if (editing) {
                        form.submit()
                      } else {
                        setEditing(true)
                      }
                    }}
                    loading={loading}
                  >
                    {editing ? '保存' : '编辑'}
                  </Button>
                }
              >
                <Form
                  form={form}
                  layout="vertical"
                  initialValues={{
                    ...profile,
                    birthday: profile.birthday ? dayjs(profile.birthday) : null,
                  }}
                  onFinish={handleSave}
                >
                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item label="用户名" name="username">
                        <Input disabled />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="真实姓名"
                        name="realName"
                        rules={[{ required: true, message: '请输入真实姓名' }]}
                      >
                        <Input disabled={!editing} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="邮箱"
                        name="email"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' },
                        ]}
                      >
                        <Input disabled={!editing} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="手机号"
                        name="phone"
                        rules={[{ required: true, message: '请输入手机号' }]}
                      >
                        <Input disabled={!editing} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item label="性别" name="gender">
                        <Select disabled={!editing}>
                          <Option value="male">男</Option>
                          <Option value="female">女</Option>
                          <Option value="other">其他</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item label="生日" name="birthday">
                        <DatePicker style={{ width: '100%' }} disabled={!editing} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item label="所在地" name="location">
                        <Input disabled={!editing} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item label="风险偏好" name="riskLevel">
                        <Select disabled={!editing}>
                          <Option value="conservative">保守型</Option>
                          <Option value="moderate">稳健型</Option>
                          <Option value="aggressive">积极型</Option>
                          <Option value="speculative">投机型</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item label="投资经验" name="investmentExperience">
                        <Select disabled={!editing}>
                          <Option value="0-1年">0-1年</Option>
                          <Option value="1-3年">1-3年</Option>
                          <Option value="3-5年">3-5年</Option>
                          <Option value="5-10年">5-10年</Option>
                          <Option value="10年以上">10年以上</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24}>
                      <Form.Item label="个人简介" name="bio">
                        <Input.TextArea rows={4} disabled={!editing} />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </Card>
            </Col>
          </Row>
        )
    },
    {
      key: 'security',
      label: '安全设置',
      children: (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card title="修改密码">
                <Form
                  form={passwordForm}
                  layout="vertical"
                  onFinish={handlePasswordChange}
                >
                  <Form.Item
                    label="当前密码"
                    name="currentPassword"
                    rules={[{ required: true, message: '请输入当前密码' }]}
                  >
                    <Input.Password
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>
                  <Form.Item
                    label="新密码"
                    name="newPassword"
                    rules={[
                      { required: true, message: '请输入新密码' },
                      { min: 6, message: '密码至少6位' },
                    ]}
                  >
                    <Input.Password
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>
                  <Form.Item
                    label="确认新密码"
                    name="confirmPassword"
                    dependencies={['newPassword']}
                    rules={[
                      { required: true, message: '请确认新密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('newPassword') === value) {
                            return Promise.resolve()
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'))
                        },
                      }),
                    ]}
                  >
                    <Input.Password
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      修改密码
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="安全提示">
                <Space direction="vertical" size={16}>
                  <div>
                    <Text strong>密码安全建议：</Text>
                    <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                      <li>使用至少8位字符</li>
                      <li>包含大小写字母、数字和特殊字符</li>
                      <li>不要使用常见密码</li>
                      <li>定期更换密码</li>
                    </ul>
                  </div>
                  <div>
                    <Text strong>账户安全：</Text>
                    <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                      <li>不要在公共设备上登录</li>
                      <li>及时退出登录</li>
                      <li>定期检查登录记录</li>
                      <li>发现异常及时联系客服</li>
                    </ul>
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>
        )
    },
    {
      key: 'loginHistory',
      label: '登录记录',
      children: (
          <Card title="最近登录记录">
            <Table
              dataSource={loginRecords}
              columns={loginColumns}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        )
    }
  ]

  return (
    <div>
      <Title level={2}>个人资料</Title>

      <Tabs defaultActiveKey="profile" items={tabItems} />
    </div>
  )
}

export default Profile
