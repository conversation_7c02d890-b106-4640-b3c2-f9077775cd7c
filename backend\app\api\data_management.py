"""
数据管理API - 处理多接口数据获取、本地存储和管理
数据流程: 多接口获取数据 → 数据处理 → 本地存储 → 前后端调用
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta, date
import asyncio
import logging
import os
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text
from app.core.database import AsyncSessionLocal
from app.models.stock import Stock, KlineData, FinancialNews, TechnicalIndicator, DataUpdateLog, APIConfiguration, SystemConfiguration
from app.services.data_sources import AKShareService, TushareService, SinaService
from app.services.data_processor import DataProcessor
from app.services.akshare_service import AKShareService as NewAKShareService
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.2f} {size_names[i]}"


async def calculate_database_size(db: AsyncSession) -> Dict[str, str]:
    """计算数据库表大小"""
    try:
        table_sizes = {}

        if settings.DATABASE_TYPE == "sqlite":
            # SQLite数据库大小计算
            db_path = settings.SQLITE_DB_PATH
            if os.path.exists(db_path):
                total_size = os.path.getsize(db_path)

                # 估算各表大小（基于记录数比例）
                tables_info = [
                    ("stocks", Stock, "股票基础信息"),
                    ("kline_data", KlineData, "K线数据"),
                    ("financial_news", FinancialNews, "财经新闻"),
                    ("technical_indicators", TechnicalIndicator, "技术指标")
                ]

                total_records = 0
                table_records = {}

                for table_name, model, display_name in tables_info:
                    result = await db.execute(select(func.count(model.id)))
                    count = result.scalar()
                    table_records[display_name] = count
                    total_records += count

                # 按记录数比例分配大小
                for display_name, count in table_records.items():
                    if total_records > 0:
                        estimated_size = int(total_size * count / total_records)
                        table_sizes[display_name] = format_size(estimated_size)
                    else:
                        table_sizes[display_name] = "0 B"

                table_sizes["总大小"] = format_size(total_size)
            else:
                table_sizes = {"总大小": "0 B"}

        elif settings.DATABASE_TYPE == "postgresql":
            # PostgreSQL数据库大小计算
            try:
                # 查询表大小
                size_query = text("""
                    SELECT
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                    FROM pg_tables
                    WHERE schemaname = 'public'
                    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                """)

                result = await db.execute(size_query)
                rows = result.fetchall()

                table_mapping = {
                    "stocks": "股票基础信息",
                    "kline_data": "K线数据",
                    "financial_news": "财经新闻",
                    "technical_indicators": "技术指标"
                }

                total_size_bytes = 0
                for row in rows:
                    table_name = row[1]
                    size_pretty = row[2]
                    size_bytes = row[3]

                    display_name = table_mapping.get(table_name, table_name)
                    table_sizes[display_name] = size_pretty
                    total_size_bytes += size_bytes

                table_sizes["总大小"] = format_size(total_size_bytes)

            except Exception as e:
                logger.warning(f"PostgreSQL大小查询失败: {e}")
                table_sizes = {"总大小": "计算失败"}
        else:
            table_sizes = {"总大小": "不支持的数据库类型"}

        return table_sizes

    except Exception as e:
        logger.error(f"计算数据库大小失败: {e}")
        return {"总大小": "计算失败"}

# 数据模型
class APIConfig(BaseModel):
    id: str
    name: str
    type: str
    enabled: bool
    api_key: Optional[str] = None
    endpoint: Optional[str] = None
    rate_limit: int
    timeout: int
    priority: int

class DatabaseStats(BaseModel):
    total_stocks: int
    today_updates: int
    last_update_time: str
    data_size: str
    news_count: int
    news_updated_today: int
    table_stats: List[Dict[str, Any]]

class UpdateTask(BaseModel):
    id: str
    name: str
    type: str
    status: str
    progress: int
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    records_processed: Optional[int] = None
    total_records: Optional[int] = None
    error_message: Optional[str] = None

class AutoUpdateSettings(BaseModel):
    enabled: bool
    stock_data_interval: int
    news_interval: int
    weekend_update: bool
    update_time: str
    retry_count: int
    enable_notification: bool

# 数据完整性相关模型
class StockDataIntegrity(BaseModel):
    stock_code: str
    stock_name: str
    market: str
    list_date: Optional[str] = None

    # 日K数据状态
    daily_data_start: Optional[str] = None
    daily_data_end: Optional[str] = None
    daily_data_count: int = 0
    daily_missing_days: int = 0
    daily_completeness: float = 0.0

    # 周K数据状态
    weekly_data_start: Optional[str] = None
    weekly_data_end: Optional[str] = None
    weekly_data_count: int = 0
    weekly_missing_weeks: int = 0
    weekly_completeness: float = 0.0

    # 月K数据状态
    monthly_data_start: Optional[str] = None
    monthly_data_end: Optional[str] = None
    monthly_data_count: int = 0
    monthly_missing_months: int = 0
    monthly_completeness: float = 0.0

    # 整体状态
    overall_status: str  # complete, partial, missing
    last_update: Optional[str] = None

class DataIntegrityStats(BaseModel):
    total_stocks: int
    complete_stocks: int
    partial_stocks: int
    missing_stocks: int

    # 数据覆盖统计
    daily_coverage: float
    weekly_coverage: float
    monthly_coverage: float

    # 时间范围
    earliest_date: Optional[str] = None
    latest_date: Optional[str] = None

    # 更新统计
    last_check_time: str

class DataUpdateRequest(BaseModel):
    update_type: str  # fill_missing, specific_stocks, date_range, full_update
    stock_codes: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    periods: Optional[List[str]] = None  # daily, weekly, monthly
    force_update: bool = False

# 全局任务状态管理
running_tasks: Dict[str, UpdateTask] = {}

# 任务执行日志
task_logs: Dict[str, List[Dict[str, Any]]] = {}

# 数据源服务实例
data_sources = {
    'akshare': AKShareService(),
    'tushare': TushareService(),
    'sina': SinaService()
}

@router.get("/api-configs", response_model=List[APIConfig])
async def get_api_configs():
    """获取API配置列表"""
    try:
        async with AsyncSessionLocal() as db:
            # 从数据库获取API配置
            result = await db.execute(select(APIConfiguration))
            api_configs = result.scalars().all()

            # 如果数据库中没有配置，创建默认配置
            if not api_configs:
                default_configs = [
                    APIConfiguration(
                        api_id="akshare",
                        name="AKShare",
                        api_type="akshare",
                        enabled=True,
                        rate_limit=100,
                        timeout=30000,
                        priority=1
                    ),
                    APIConfiguration(
                        api_id="tushare",
                        name="Tushare",
                        api_type="tushare",
                        enabled=True,
                        api_key=getattr(settings, 'TUSHARE_API_KEY', ''),
                        endpoint="http://api.tushare.pro",
                        rate_limit=200,
                        timeout=30000,
                        priority=2
                    ),
                    APIConfiguration(
                        api_id="sina",
                        name="新浪财经",
                        api_type="sina",
                        enabled=True,
                        endpoint="http://hq.sinajs.cn",
                        rate_limit=500,
                        timeout=15000,
                        priority=3
                    )
                ]

                for config in default_configs:
                    db.add(config)
                await db.commit()
                api_configs = default_configs

        # 转换为响应格式
        configs = []
        for config in api_configs:
            configs.append({
                "id": config.api_type,
                "name": config.name,
                "type": config.api_type,
                "enabled": config.enabled,
                "api_key": config.api_key,
                "endpoint": config.endpoint,
                "rate_limit": config.rate_limit,
                "timeout": config.timeout,
                "priority": config.priority
            })

        return configs
    except Exception as e:
        logger.error(f"获取API配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取API配置失败")

@router.put("/api-configs/{config_id}")
async def update_api_config(config_id: str, config: APIConfig):
    """更新API配置"""
    try:
        async with AsyncSessionLocal() as db:
            # 查找现有配置
            result = await db.execute(
                select(APIConfiguration).where(APIConfiguration.api_type == config_id)
            )
            api_config = result.scalar_one_or_none()

            if not api_config:
                # 创建新配置
                api_config = APIConfiguration(
                    name=config.name,
                    api_type=config.type,
                    enabled=config.enabled,
                    api_key=config.api_key,
                    endpoint=config.endpoint,
                    rate_limit=config.rate_limit,
                    timeout=config.timeout,
                    priority=config.priority
                )
                db.add(api_config)
            else:
                # 更新现有配置
                api_config.name = config.name
                api_config.enabled = config.enabled
                api_config.api_key = config.api_key
                api_config.endpoint = config.endpoint
                api_config.rate_limit = config.rate_limit
                api_config.timeout = config.timeout
                api_config.priority = config.priority
                api_config.updated_at = datetime.now()

            await db.commit()
            logger.info(f"更新API配置: {config_id}")
            return {"message": "API配置已更新", "config": config}
    except Exception as e:
        logger.error(f"更新API配置失败: {e}")
        raise HTTPException(status_code=500, detail="更新配置失败")

@router.post("/api-configs/{api_id}/test")
async def test_api_connection(api_id: str):
    """测试API连接"""
    try:
        if api_id not in data_sources:
            raise HTTPException(status_code=404, detail="API不存在")
        
        service = data_sources[api_id]
        result = await service.test_connection()
        
        if result:
            return {"success": True, "message": "连接测试成功"}
        else:
            return {"success": False, "message": "连接测试失败"}
    except Exception as e:
        logger.error(f"API连接测试失败: {e}")
        raise HTTPException(status_code=500, detail="连接测试失败")

@router.get("/database/stats", response_model=DatabaseStats)
async def get_database_stats():
    """获取数据库状态统计"""
    try:
        async with AsyncSessionLocal() as db:
            # 查询真实数据库统计信息
            result = await db.execute(select(func.count(Stock.id)))
            total_stocks = result.scalar()

            today = datetime.now().date()
            result = await db.execute(
                select(func.count(Stock.id)).where(Stock.updated_at >= today)
            )
            today_updates = result.scalar()

            # 获取最后更新时间
            result = await db.execute(
                select(Stock).order_by(Stock.updated_at.desc()).limit(1)
            )
            last_stock = result.scalar_one_or_none()
            last_update_time = last_stock.updated_at.strftime("%Y-%m-%d %H:%M:%S") if last_stock and last_stock.updated_at else "未知"

            # 新闻统计
            result = await db.execute(select(func.count(FinancialNews.id)))
            news_count = result.scalar()

            result = await db.execute(
                select(func.count(FinancialNews.id)).where(FinancialNews.created_at >= today)
            )
            news_updated_today = result.scalar()

            # K线数据统计
            result = await db.execute(
                select(func.count(KlineData.id)).where(KlineData.period == 'daily')
            )
            daily_kline_count = result.scalar()

            result = await db.execute(
                select(func.count(KlineData.id)).where(KlineData.period == 'minute')
            )
            minute_kline_count = result.scalar()

            # 技术指标统计
            result = await db.execute(select(func.count(TechnicalIndicator.id)))
            indicator_count = result.scalar()

            # 计算真实的数据库大小
            table_sizes = await calculate_database_size(db)

            # 表统计信息
            table_stats = [
                {"name": "股票基础信息", "count": total_stocks, "size": table_sizes.get("股票基础信息", "0 B")},
                {"name": "日线数据", "count": daily_kline_count, "size": table_sizes.get("K线数据", "0 B")},
                {"name": "分钟数据", "count": minute_kline_count, "size": table_sizes.get("K线数据", "0 B")},
                {"name": "财经新闻", "count": news_count, "size": table_sizes.get("财经新闻", "0 B")},
                {"name": "技术指标", "count": indicator_count, "size": table_sizes.get("技术指标", "0 B")}
            ]

            return DatabaseStats(
                total_stocks=total_stocks,
                today_updates=today_updates,
                last_update_time=last_update_time,
                data_size=table_sizes.get("总大小", "0 B"),
                news_count=news_count,
                news_updated_today=news_updated_today,
                table_stats=table_stats
            )
    except Exception as e:
        logger.error(f"获取数据库统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据库统计失败")

@router.get("/update-tasks", response_model=List[UpdateTask])
async def get_update_tasks():
    """获取更新任务列表"""
    try:
        # 返回当前运行的任务和最近的任务历史
        tasks = list(running_tasks.values())
        return tasks
    except Exception as e:
        logger.error(f"获取更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="获取更新任务失败")

@router.get("/update-tasks/{task_id}/details")
async def get_task_details(task_id: str):
    """获取任务详细信息"""
    try:
        if task_id not in running_tasks:
            raise HTTPException(status_code=404, detail="任务不存在")

        task = running_tasks[task_id]

        # 获取任务执行日志
        logs = task_logs.get(task_id, [])

        # 获取数据更新详情
        update_details = await get_task_update_details(task_id, task.type)

        return {
            "task": task.dict(),
            "logs": logs,
            "update_details": update_details,
            "execution_info": {
                "created_at": task.start_time,
                "updated_at": task.end_time or task.start_time,
                "duration": calculate_task_duration(task),
                "data_source": get_data_source_info(task.type)
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务详情失败")

@router.get("/auto-update/settings", response_model=AutoUpdateSettings)
async def get_auto_update_settings():
    """获取自动更新设置"""
    try:
        async with AsyncSessionLocal() as db:
            # 从数据库获取自动更新设置
            result = await db.execute(
                select(SystemConfiguration).where(
                    SystemConfiguration.config_key == "auto_update_settings"
                )
            )
            config = result.scalar_one_or_none()

            if config:
                import json
                settings_data = json.loads(config.config_value)
                return AutoUpdateSettings(**settings_data)
            else:
                # 返回默认设置并保存到数据库
                default_settings = AutoUpdateSettings(
                    enabled=True,
                    stock_data_interval=30,  # 30分钟
                    news_interval=60,        # 60分钟
                    weekend_update=False,
                    update_time="09:00",
                    retry_count=3,
                    enable_notification=True
                )

                # 保存默认设置到数据库
                import json
                config = SystemConfiguration(
                    config_key="auto_update_settings",
                    config_value=json.dumps(default_settings.dict()),
                    config_type="auto_update",
                    description="自动更新设置配置"
                )
                db.add(config)
                await db.commit()

                return default_settings
    except Exception as e:
        logger.error(f"获取自动更新设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取自动更新设置失败")

@router.put("/auto-update/settings")
async def update_auto_update_settings(settings: AutoUpdateSettings):
    """更新自动更新设置"""
    try:
        async with AsyncSessionLocal() as db:
            # 查找现有配置
            result = await db.execute(
                select(SystemConfiguration).where(
                    SystemConfiguration.config_key == "auto_update_settings"
                )
            )
            config = result.scalar_one_or_none()

            import json
            settings_json = json.dumps(settings.dict())

            if config:
                # 更新现有配置
                config.config_value = settings_json
                config.updated_at = datetime.now()
            else:
                # 创建新配置
                config = SystemConfiguration(
                    config_key="auto_update_settings",
                    config_value=settings_json,
                    config_type="auto_update",
                    description="自动更新设置配置"
                )
                db.add(config)

            await db.commit()
            return {"message": "自动更新设置已保存", "settings": settings}
    except Exception as e:
        logger.error(f"更新自动更新设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新自动更新设置失败")

@router.post("/update-tasks/start")
async def start_update_task(
    task_request: Dict[str, str],
    background_tasks: BackgroundTasks
):
    """启动更新任务"""
    try:
        task_type = task_request.get("type")
        task_id = f"{task_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建任务
        task = UpdateTask(
            id=task_id,
            name=get_task_name(task_type),
            type=task_type,
            status="running",
            progress=0,
            start_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        running_tasks[task_id] = task
        
        # 在后台执行任务
        background_tasks.add_task(execute_update_task, task_id, task_type)
        
        return {"success": True, "task_id": task_id, "message": "任务已启动"}
    except Exception as e:
        logger.error(f"启动更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")

async def execute_update_task(task_id: str, task_type: str):
    """执行更新任务"""
    try:
        add_task_log(task_id, "INFO", f"开始执行{get_task_name(task_type)}任务")

        async with AsyncSessionLocal() as db:
            task = running_tasks[task_id]
            processor = DataProcessor(db)

        if task_type == "stock_basic":
            add_task_log(task_id, "INFO", "开始更新股票基础信息")
            await processor.update_stock_basic_info(task)
            add_task_log(task_id, "INFO", "股票基础信息更新完成")
        elif task_type == "stock_price":
            add_task_log(task_id, "INFO", "开始更新股票价格数据")
            await processor.update_stock_prices(task)
            add_task_log(task_id, "INFO", "股票价格数据更新完成")
        elif task_type == "financial_news":
            add_task_log(task_id, "INFO", "开始更新财经新闻")
            await processor.update_financial_news(task)
            add_task_log(task_id, "INFO", "财经新闻更新完成")
        elif task_type == "technical_indicators":
            add_task_log(task_id, "INFO", "开始计算技术指标")
            await processor.calculate_technical_indicators(task)
            add_task_log(task_id, "INFO", "技术指标计算完成")

        # 任务完成
        task.status = "completed"
        task.progress = 100
        task.end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        add_task_log(task_id, "INFO", f"任务执行完成，耗时: {calculate_task_duration(task)}")
        logger.info(f"任务 {task_id} 执行完成")
    except Exception as e:
        logger.error(f"执行任务失败: {e}")
        task = running_tasks.get(task_id)
        if task:
            task.status = "failed"
            task.error_message = str(e)
            task.end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        add_task_log(task_id, "ERROR", f"任务执行失败: {str(e)}")



@router.post("/database/clean-expired")
async def clean_expired_data():
    """清理过期数据"""
    try:
        async with AsyncSessionLocal() as db:
            # 删除30天前的分钟级数据
            cutoff_date = datetime.now() - timedelta(days=30)
            result = await db.execute(
                select(KlineData).where(
                    KlineData.period == 'minute',
                    KlineData.trade_date < cutoff_date
                )
            )
            records_to_delete = result.scalars().all()

            for record in records_to_delete:
                await db.delete(record)

            await db.commit()
            deleted_count = len(records_to_delete)
            logger.info(f"清理过期数据完成，删除 {deleted_count} 条记录")
            return {"success": True, "deleted_count": deleted_count}
    except Exception as e:
        logger.error(f"清理过期数据失败: {e}")
        raise HTTPException(status_code=500, detail="清理数据失败")

@router.post("/database/optimize")
async def optimize_database():
    """优化数据库"""
    try:
        async with AsyncSessionLocal() as db:
            # 执行数据库优化操作
            await db.execute("VACUUM;")
            await db.execute("REINDEX;")
            await db.commit()

            logger.info("数据库优化完成")
            return {"success": True, "message": "数据库优化完成"}
    except Exception as e:
        logger.error(f"数据库优化失败: {e}")
        raise HTTPException(status_code=500, detail="数据库优化失败")

# 辅助函数
def get_task_name(task_type: str) -> str:
    """根据任务类型获取任务名称"""
    task_names = {
        "stock_basic": "股票基础信息更新",
        "stock_price": "实时价格数据更新",
        "financial_news": "财经新闻更新",
        "technical_indicators": "技术指标计算"
    }
    return task_names.get(task_type, f"未知任务类型: {task_type}")

def add_task_log(task_id: str, level: str, message: str, data: Dict[str, Any] = None):
    """添加任务执行日志"""
    if task_id not in task_logs:
        task_logs[task_id] = []

    log_entry = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "level": level,  # INFO, WARNING, ERROR
        "message": message,
        "data": data or {}
    }
    task_logs[task_id].append(log_entry)

def calculate_task_duration(task: UpdateTask) -> str:
    """计算任务执行时长"""
    if not task.start_time:
        return "未开始"

    start = datetime.strptime(task.start_time, "%Y-%m-%d %H:%M:%S")

    if task.end_time:
        end = datetime.strptime(task.end_time, "%Y-%m-%d %H:%M:%S")
        duration = end - start
    else:
        duration = datetime.now() - start

    total_seconds = int(duration.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60

    if hours > 0:
        return f"{hours}小时{minutes}分钟{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分钟{seconds}秒"
    else:
        return f"{seconds}秒"

def get_data_source_info(task_type: str) -> Dict[str, Any]:
    """获取数据源信息"""
    data_source_info = {
        "stock_basic": {
            "source": "AKShare",
            "api": "stock_info_a_code_name",
            "description": "获取A股股票基础信息，包括股票代码、名称、行业、概念等",
            "update_frequency": "每日更新",
            "data_fields": ["股票代码", "股票名称", "所属行业", "概念板块", "上市日期", "市场类型"]
        },
        "stock_price": {
            "source": "AKShare + Tushare",
            "api": "stock_zh_a_hist + daily",
            "description": "获取股票历史价格数据和实时价格信息",
            "update_frequency": "实时更新",
            "data_fields": ["开盘价", "最高价", "最低价", "收盘价", "成交量", "成交额", "涨跌幅"]
        },
        "financial_news": {
            "source": "新浪财经",
            "api": "news_sina",
            "description": "获取最新财经新闻和市场资讯",
            "update_frequency": "每30分钟更新",
            "data_fields": ["新闻标题", "发布时间", "新闻内容", "来源", "相关股票"]
        },
        "technical_indicators": {
            "source": "本地计算",
            "api": "talib + pandas",
            "description": "基于价格数据计算技术指标",
            "update_frequency": "每日收盘后更新",
            "data_fields": ["MA5", "MA10", "MA20", "MACD", "RSI", "KDJ", "BOLL"]
        },
        "specific_stocks": {
            "source": "AKShare",
            "api": "stock_zh_a_hist",
            "description": "更新指定股票的K线数据，包括日K、周K、月K等周期数据",
            "update_frequency": "手动触发",
            "data_fields": ["开盘价", "最高价", "最低价", "收盘价", "成交量", "成交额", "涨跌幅", "涨跌额"]
        },
        "fill_missing": {
            "source": "AKShare",
            "api": "stock_zh_a_hist",
            "description": "补充缺失的K线数据，自动检测数据缺口并填补",
            "update_frequency": "手动触发",
            "data_fields": ["开盘价", "最高价", "最低价", "收盘价", "成交量", "成交额", "涨跌幅", "涨跌额"]
        },
        "date_range": {
            "source": "AKShare",
            "api": "stock_zh_a_hist",
            "description": "更新指定时间范围内的K线数据",
            "update_frequency": "手动触发",
            "data_fields": ["开盘价", "最高价", "最低价", "收盘价", "成交量", "成交额", "涨跌幅", "涨跌额"]
        },
        "full_update": {
            "source": "AKShare",
            "api": "stock_zh_a_hist",
            "description": "全量更新所有股票的K线数据，适用于初始化或大规模数据更新",
            "update_frequency": "手动触发",
            "data_fields": ["开盘价", "最高价", "最低价", "收盘价", "成交量", "成交额", "涨跌幅", "涨跌额"]
        }
    }
    return data_source_info.get(task_type, {})

async def get_task_update_details(task_id: str, task_type: str) -> Dict[str, Any]:
    """获取任务数据更新详情"""
    try:
        async with AsyncSessionLocal() as db:
            if task_type == "stock_basic":
                # 获取股票基础信息更新详情
                try:
                    result = await db.execute(
                        text("SELECT COUNT(*) as total_stocks FROM stocks WHERE is_active = true")
                    )
                    total_stocks = result.scalar() or 0
                except:
                    # 如果查询失败，使用备用查询
                    result = await db.execute(text("SELECT COUNT(*) FROM stocks"))
                    total_stocks = result.scalar() or 0

                try:
                    result = await db.execute(
                        text("SELECT COUNT(*) as updated_today FROM stocks WHERE DATE(updated_at) = CURRENT_DATE")
                    )
                    updated_today = result.scalar() or 0
                except:
                    # 如果查询失败，设为0
                    updated_today = 0

                return {
                    "total_stocks": total_stocks,
                    "updated_today": updated_today,
                    "update_type": "股票基础信息",
                    "data_summary": f"共{total_stocks}只股票，今日更新{updated_today}只"
                }

            elif task_type == "stock_price":
                # 获取价格数据更新详情
                try:
                    result = await db.execute(
                        text("SELECT COUNT(*) as total_records FROM kline_data WHERE DATE(trade_date) = CURRENT_DATE")
                    )
                    today_records = result.scalar() or 0
                except:
                    today_records = 0

                try:
                    result = await db.execute(
                        text("SELECT COUNT(DISTINCT stock_code) as stocks_count FROM kline_data WHERE DATE(trade_date) = CURRENT_DATE")
                    )
                    stocks_count = result.scalar() or 0
                except:
                    stocks_count = 0

                return {
                    "today_records": today_records,
                    "stocks_count": stocks_count,
                    "update_type": "股票价格数据",
                    "data_summary": f"今日更新{stocks_count}只股票的{today_records}条价格记录"
                }

            elif task_type == "financial_news":
                # 获取新闻更新详情
                try:
                    result = await db.execute(
                        text("SELECT COUNT(*) as total_news FROM financial_news WHERE DATE(created_at) = CURRENT_DATE")
                    )
                    today_news = result.scalar() or 0
                except:
                    today_news = 0

                return {
                    "today_news": today_news,
                    "update_type": "财经新闻",
                    "data_summary": f"今日更新{today_news}条财经新闻"
                }

            elif task_type == "technical_indicators":
                # 获取技术指标更新详情
                try:
                    result = await db.execute(
                        text("SELECT COUNT(*) as total_indicators FROM technical_indicators WHERE DATE(trade_date) = CURRENT_DATE")
                    )
                    today_indicators = result.scalar() or 0
                except:
                    today_indicators = 0

                try:
                    result = await db.execute(
                        text("SELECT COUNT(DISTINCT stock_code) as stocks_count FROM technical_indicators WHERE DATE(trade_date) = CURRENT_DATE")
                    )
                    stocks_count = result.scalar() or 0
                except:
                    stocks_count = 0

                return {
                    "today_indicators": today_indicators,
                    "stocks_count": stocks_count,
                    "update_type": "技术指标",
                    "data_summary": f"今日计算{stocks_count}只股票的{today_indicators}条技术指标"
                }

    except Exception as e:
        logger.error(f"获取任务更新详情失败: {e}")
        return {
            "error": str(e),
            "update_type": task_type,
            "data_summary": "获取详情失败"
        }


# ==================== 数据完整性管理API ====================

@router.get("/data-integrity/stats", response_model=DataIntegrityStats)
async def get_data_integrity_stats():
    """获取数据完整性统计信息"""
    try:
        async with AsyncSessionLocal() as db:
            # 获取股票总数
            result = await db.execute(select(func.count(Stock.id)))
            total_stocks = result.scalar() or 0

            # 获取有数据的股票数量
            result = await db.execute(
                select(func.count(func.distinct(KlineData.stock_code)))
            )
            stocks_with_data = result.scalar() or 0

            # 计算完整性统计
            complete_stocks = 0
            partial_stocks = stocks_with_data
            missing_stocks = total_stocks - stocks_with_data

            # 获取数据时间范围
            result = await db.execute(
                select(func.min(KlineData.trade_date), func.max(KlineData.trade_date))
            )
            date_range = result.first()
            earliest_date = date_range[0].strftime('%Y-%m-%d') if date_range[0] else None
            latest_date = date_range[1].strftime('%Y-%m-%d') if date_range[1] else None

            # 计算各周期数据覆盖率
            daily_coverage = 0.0
            weekly_coverage = 0.0
            monthly_coverage = 0.0

            if total_stocks > 0:
                # 日K覆盖率
                result = await db.execute(
                    select(func.count(func.distinct(KlineData.stock_code)))
                    .where(KlineData.period == 'daily')
                )
                daily_stocks = result.scalar() or 0
                daily_coverage = (daily_stocks / total_stocks) * 100

                # 周K覆盖率
                result = await db.execute(
                    select(func.count(func.distinct(KlineData.stock_code)))
                    .where(KlineData.period == 'weekly')
                )
                weekly_stocks = result.scalar() or 0
                weekly_coverage = (weekly_stocks / total_stocks) * 100

                # 月K覆盖率
                result = await db.execute(
                    select(func.count(func.distinct(KlineData.stock_code)))
                    .where(KlineData.period == 'monthly')
                )
                monthly_stocks = result.scalar() or 0
                monthly_coverage = (monthly_stocks / total_stocks) * 100

            return DataIntegrityStats(
                total_stocks=total_stocks,
                complete_stocks=complete_stocks,
                partial_stocks=partial_stocks,
                missing_stocks=missing_stocks,
                daily_coverage=daily_coverage,
                weekly_coverage=weekly_coverage,
                monthly_coverage=monthly_coverage,
                earliest_date=earliest_date,
                latest_date=latest_date,
                last_check_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )

    except Exception as e:
        logger.error(f"获取数据完整性统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据完整性统计失败: {str(e)}")


@router.get("/data-integrity/stocks", response_model=List[StockDataIntegrity])
async def get_stocks_data_integrity(
    page: int = 1,
    page_size: int = 50,
    stock_code: Optional[str] = None,
    status_filter: Optional[str] = None  # complete, partial, missing
):
    """获取股票数据完整性详情"""
    try:
        async with AsyncSessionLocal() as db:
            # 构建基础查询
            query = select(Stock)

            # 添加股票代码过滤
            if stock_code:
                query = query.where(Stock.stock_code.like(f"%{stock_code}%"))

            # 分页
            offset = (page - 1) * page_size
            query = query.offset(offset).limit(page_size)

            result = await db.execute(query)
            stocks = result.scalars().all()

            stock_integrity_list = []

            for stock in stocks:
                # 获取该股票的K线数据统计
                integrity = await get_stock_data_integrity(db, stock)

                # 应用状态过滤
                if status_filter and integrity.overall_status != status_filter:
                    continue

                stock_integrity_list.append(integrity)

            return stock_integrity_list

    except Exception as e:
        logger.error(f"获取股票数据完整性失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票数据完整性失败: {str(e)}")


async def get_stock_data_integrity(db: AsyncSession, stock: Stock) -> StockDataIntegrity:
    """获取单个股票的数据完整性信息"""
    try:
        # 日K数据统计
        daily_result = await db.execute(
            select(
                func.min(KlineData.trade_date),
                func.max(KlineData.trade_date),
                func.count(KlineData.id)
            ).where(
                KlineData.stock_code == stock.stock_code,
                KlineData.period == 'daily'
            )
        )
        daily_data = daily_result.first()
        daily_start = daily_data[0].strftime('%Y-%m-%d') if daily_data[0] else None
        daily_end = daily_data[1].strftime('%Y-%m-%d') if daily_data[1] else None
        daily_count = daily_data[2] or 0

        # 计算日K数据完整性
        daily_missing_days = 0
        daily_completeness = 0.0
        if daily_start and daily_end:
            # 简化计算：假设工作日数据
            from datetime import datetime
            start_date = datetime.strptime(daily_start, '%Y-%m-%d').date()
            end_date = datetime.strptime(daily_end, '%Y-%m-%d').date()
            total_days = (end_date - start_date).days + 1
            expected_trading_days = int(total_days * 0.7)  # 假设70%是交易日
            daily_missing_days = max(0, expected_trading_days - daily_count)
            daily_completeness = (daily_count / expected_trading_days * 100) if expected_trading_days > 0 else 0

        # 周K数据统计
        weekly_result = await db.execute(
            select(
                func.min(KlineData.trade_date),
                func.max(KlineData.trade_date),
                func.count(KlineData.id)
            ).where(
                KlineData.stock_code == stock.stock_code,
                KlineData.period == 'weekly'
            )
        )
        weekly_data = weekly_result.first()
        weekly_start = weekly_data[0].strftime('%Y-%m-%d') if weekly_data[0] else None
        weekly_end = weekly_data[1].strftime('%Y-%m-%d') if weekly_data[1] else None
        weekly_count = weekly_data[2] or 0

        # 计算周K数据完整性
        weekly_missing_weeks = 0
        weekly_completeness = 0.0
        if weekly_start and weekly_end:
            start_date = datetime.strptime(weekly_start, '%Y-%m-%d').date()
            end_date = datetime.strptime(weekly_end, '%Y-%m-%d').date()
            total_weeks = (end_date - start_date).days // 7 + 1
            weekly_missing_weeks = max(0, total_weeks - weekly_count)
            weekly_completeness = (weekly_count / total_weeks * 100) if total_weeks > 0 else 0

        # 月K数据统计
        monthly_result = await db.execute(
            select(
                func.min(KlineData.trade_date),
                func.max(KlineData.trade_date),
                func.count(KlineData.id)
            ).where(
                KlineData.stock_code == stock.stock_code,
                KlineData.period == 'monthly'
            )
        )
        monthly_data = monthly_result.first()
        monthly_start = monthly_data[0].strftime('%Y-%m-%d') if monthly_data[0] else None
        monthly_end = monthly_data[1].strftime('%Y-%m-%d') if monthly_data[1] else None
        monthly_count = monthly_data[2] or 0

        # 计算月K数据完整性
        monthly_missing_months = 0
        monthly_completeness = 0.0
        if monthly_start and monthly_end:
            start_date = datetime.strptime(monthly_start, '%Y-%m-%d').date()
            end_date = datetime.strptime(monthly_end, '%Y-%m-%d').date()
            total_months = (end_date.year - start_date.year) * 12 + (end_date.month - start_date.month) + 1
            monthly_missing_months = max(0, total_months - monthly_count)
            monthly_completeness = (monthly_count / total_months * 100) if total_months > 0 else 0

        # 确定整体状态
        overall_status = "missing"
        if daily_count > 0 or weekly_count > 0 or monthly_count > 0:
            if daily_completeness >= 90 and weekly_completeness >= 90 and monthly_completeness >= 90:
                overall_status = "complete"
            else:
                overall_status = "partial"

        # 获取最后更新时间
        last_update_result = await db.execute(
            select(func.max(KlineData.created_at))
            .where(KlineData.stock_code == stock.stock_code)
        )
        last_update = last_update_result.scalar()
        last_update_str = last_update.strftime('%Y-%m-%d %H:%M:%S') if last_update else None

        return StockDataIntegrity(
            stock_code=stock.stock_code,
            stock_name=stock.stock_name,
            market=stock.market,
            list_date=stock.list_date.strftime('%Y-%m-%d') if stock.list_date else None,

            daily_data_start=daily_start,
            daily_data_end=daily_end,
            daily_data_count=daily_count,
            daily_missing_days=daily_missing_days,
            daily_completeness=round(daily_completeness, 2),

            weekly_data_start=weekly_start,
            weekly_data_end=weekly_end,
            weekly_data_count=weekly_count,
            weekly_missing_weeks=weekly_missing_weeks,
            weekly_completeness=round(weekly_completeness, 2),

            monthly_data_start=monthly_start,
            monthly_data_end=monthly_end,
            monthly_data_count=monthly_count,
            monthly_missing_months=monthly_missing_months,
            monthly_completeness=round(monthly_completeness, 2),

            overall_status=overall_status,
            last_update=last_update_str
        )

    except Exception as e:
        logger.error(f"获取股票 {stock.stock_code} 数据完整性失败: {e}")
        # 返回默认值
        return StockDataIntegrity(
            stock_code=stock.stock_code,
            stock_name=stock.stock_name,
            market=stock.market,
            list_date=stock.list_date.strftime('%Y-%m-%d') if stock.list_date else None,
            overall_status="missing"
        )


# ==================== 数据更新功能API ====================

@router.post("/data-integrity/update")
async def update_stock_data(
    request: DataUpdateRequest,
    background_tasks: BackgroundTasks
):
    """执行数据更新操作"""
    try:
        task_id = f"{request.update_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 创建任务
        task = UpdateTask(
            id=task_id,
            name=get_update_task_name(request.update_type),
            type=request.update_type,
            status="running",
            progress=0,
            start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

        running_tasks[task_id] = task
        task_logs[task_id] = []

        # 启动后台任务
        background_tasks.add_task(execute_data_update, task_id, request)

        return {
            "message": "数据更新任务已启动",
            "task_id": task_id,
            "update_type": request.update_type
        }

    except Exception as e:
        logger.error(f"启动数据更新任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动数据更新任务失败: {str(e)}")


def get_update_task_name(update_type: str) -> str:
    """获取更新任务名称"""
    task_names = {
        "fill_missing": "补缺数据更新",
        "specific_stocks": "指定股票更新",
        "date_range": "指定时间范围更新",
        "full_update": "全量数据更新"
    }
    return task_names.get(update_type, "数据更新")


async def execute_data_update(task_id: str, request: DataUpdateRequest):
    """执行数据更新任务"""
    try:
        task = running_tasks[task_id]
        add_task_log(task_id, "INFO", f"开始执行{task.name}任务")

        async with AsyncSessionLocal() as db:
            if request.update_type == "fill_missing":
                await fill_missing_data(db, task_id, request)
            elif request.update_type == "specific_stocks":
                await update_specific_stocks(db, task_id, request)
            elif request.update_type == "date_range":
                await update_date_range(db, task_id, request)
            elif request.update_type == "full_update":
                await full_data_update(db, task_id, request)
            else:
                raise ValueError(f"不支持的更新类型: {request.update_type}")

        # 任务完成
        task.status = "completed"
        task.progress = 100
        task.end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        add_task_log(task_id, "INFO", f"{task.name}任务执行完成")

    except Exception as e:
        logger.error(f"执行数据更新任务失败: {e}")
        task = running_tasks.get(task_id)
        if task:
            task.status = "failed"
            task.error_message = str(e)
            add_task_log(task_id, "ERROR", f"任务执行失败: {str(e)}")


async def fill_missing_data(db: AsyncSession, task_id: str, request: DataUpdateRequest):
    """补缺数据更新"""
    add_task_log(task_id, "INFO", "开始分析数据缺失情况")

    # 获取所有股票
    result = await db.execute(select(Stock).where(Stock.is_active == True))
    stocks = result.scalars().all()

    total_stocks = len(stocks)
    processed = 0

    for stock in stocks:
        try:
            # 检查该股票的数据缺失情况
            missing_periods = await check_missing_data(db, stock.stock_code, request.periods)

            if missing_periods:
                add_task_log(task_id, "INFO", f"为股票 {stock.stock_code} 补充缺失数据: {missing_periods}")

                # 获取缺失数据
                for period in missing_periods:
                    await fetch_and_save_kline_data(db, stock.stock_code, period, request.start_date, request.end_date)

            processed += 1
            progress = int((processed / total_stocks) * 100)
            running_tasks[task_id].progress = progress

        except Exception as e:
            add_task_log(task_id, "WARNING", f"处理股票 {stock.stock_code} 时出错: {str(e)}")
            continue

    add_task_log(task_id, "INFO", f"补缺数据更新完成，处理了 {processed}/{total_stocks} 只股票")


async def update_specific_stocks(db: AsyncSession, task_id: str, request: DataUpdateRequest):
    """指定股票更新"""
    if not request.stock_codes:
        raise ValueError("指定股票更新需要提供股票代码列表")

    add_task_log(task_id, "INFO", f"开始更新指定股票: {request.stock_codes}")

    total_stocks = len(request.stock_codes)
    processed = 0

    periods = request.periods or ['daily', 'weekly', 'monthly']

    for stock_code in request.stock_codes:
        try:
            # 验证股票是否存在
            result = await db.execute(select(Stock).where(Stock.stock_code == stock_code))
            stock = result.scalar_one_or_none()

            if not stock:
                add_task_log(task_id, "WARNING", f"股票 {stock_code} 不存在，跳过")
                continue

            add_task_log(task_id, "INFO", f"更新股票 {stock_code} 的数据")

            # 更新各周期数据
            for period in periods:
                await fetch_and_save_kline_data(db, stock_code, period, request.start_date, request.end_date, request.force_update)

            processed += 1
            progress = int((processed / total_stocks) * 100)
            running_tasks[task_id].progress = progress

        except Exception as e:
            add_task_log(task_id, "WARNING", f"更新股票 {stock_code} 时出错: {str(e)}")
            continue

    add_task_log(task_id, "INFO", f"指定股票更新完成，处理了 {processed}/{total_stocks} 只股票")


async def update_date_range(db: AsyncSession, task_id: str, request: DataUpdateRequest):
    """指定时间范围更新"""
    if not request.start_date or not request.end_date:
        raise ValueError("指定时间范围更新需要提供开始和结束日期")

    add_task_log(task_id, "INFO", f"开始更新时间范围 {request.start_date} 到 {request.end_date} 的数据")

    # 获取股票列表
    stock_codes = request.stock_codes
    if not stock_codes:
        result = await db.execute(select(Stock.stock_code).where(Stock.is_active == True))
        stock_codes = [row[0] for row in result.all()]

    total_stocks = len(stock_codes)
    processed = 0

    periods = request.periods or ['daily']

    for stock_code in stock_codes:
        try:
            add_task_log(task_id, "INFO", f"更新股票 {stock_code} 在 {request.start_date} 到 {request.end_date} 的数据")

            for period in periods:
                await fetch_and_save_kline_data(db, stock_code, period, request.start_date, request.end_date, request.force_update)

            processed += 1
            progress = int((processed / total_stocks) * 100)
            running_tasks[task_id].progress = progress

        except Exception as e:
            add_task_log(task_id, "WARNING", f"更新股票 {stock_code} 时出错: {str(e)}")
            continue

    add_task_log(task_id, "INFO", f"时间范围更新完成，处理了 {processed}/{total_stocks} 只股票")


async def full_data_update(db: AsyncSession, task_id: str, request: DataUpdateRequest):
    """全量数据更新"""
    add_task_log(task_id, "INFO", "开始全量数据更新")

    # 获取所有活跃股票
    result = await db.execute(select(Stock).where(Stock.is_active == True))
    stocks = result.scalars().all()

    total_stocks = len(stocks)
    processed = 0

    periods = request.periods or ['daily', 'weekly', 'monthly']

    for stock in stocks:
        try:
            add_task_log(task_id, "INFO", f"全量更新股票 {stock.stock_code} 的数据")

            # 确定更新的时间范围
            start_date = request.start_date or stock.list_date.strftime('%Y-%m-%d') if stock.list_date else '2020-01-01'
            end_date = request.end_date or datetime.now().strftime('%Y-%m-%d')

            for period in periods:
                await fetch_and_save_kline_data(db, stock.stock_code, period, start_date, end_date, True)

            processed += 1
            progress = int((processed / total_stocks) * 100)
            running_tasks[task_id].progress = progress

        except Exception as e:
            add_task_log(task_id, "WARNING", f"全量更新股票 {stock.stock_code} 时出错: {str(e)}")
            continue

    add_task_log(task_id, "INFO", f"全量数据更新完成，处理了 {processed}/{total_stocks} 只股票")


async def check_missing_data(db: AsyncSession, stock_code: str, periods: Optional[List[str]] = None) -> List[str]:
    """检查股票的缺失数据周期"""
    missing_periods = []
    periods = periods or ['daily', 'weekly', 'monthly']

    for period in periods:
        result = await db.execute(
            select(func.count(KlineData.id))
            .where(KlineData.stock_code == stock_code, KlineData.period == period)
        )
        count = result.scalar() or 0

        if count == 0:
            missing_periods.append(period)

    return missing_periods


async def fetch_and_save_kline_data(
    db: AsyncSession,
    stock_code: str,
    period: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    force_update: bool = False
):
    """获取并保存K线数据"""
    try:
        from app.services.data_fetcher import DataFetcherManager
        from app.services.data_storage import DataStorageService
        from datetime import datetime, date, timedelta

        logger.info(f"开始获取股票 {stock_code} 的 {period} 数据")

        # 设置日期范围
        if not end_date:
            end_date = date.today()
        else:
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date() if isinstance(end_date, str) else end_date

        if not start_date:
            start_date = end_date - timedelta(days=365)  # 默认获取一年数据
        elif isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()

        if not force_update:
            # 检查最新数据日期，只获取缺失的数据
            result = await db.execute(
                select(func.max(KlineData.trade_date))
                .where(
                    KlineData.stock_code == stock_code,
                    KlineData.period == period
                )
            )
            latest_date = result.scalar()
            if latest_date:
                # 如果有数据，只更新最新日期之后的数据
                incremental_start = latest_date + timedelta(days=1)
                logger.info(f"股票 {stock_code} 的 {period} 数据最新日期为 {latest_date}，从 {incremental_start} 开始增量更新")
                if incremental_start > end_date:
                    logger.info(f"股票 {stock_code} 的 {period} 数据已是最新，无需更新")
                    return
                start_date = incremental_start

        # 使用真实的AKShare API获取数据
        fetcher_manager = DataFetcherManager()
        kline_data = await fetcher_manager.get_cleaned_kline_data(
            stock_code=stock_code,
            period=period,
            start_date=start_date,
            end_date=end_date
        )

        if kline_data:
            # 保存到数据库
            async with DataStorageService() as storage:
                saved_count = await storage.save_kline_data(kline_data)
                logger.info(f"成功保存股票 {stock_code} 的 {period} 数据 {saved_count} 条")
        else:
            logger.warning(f"未获取到股票 {stock_code} 的 {period} 数据")

    except Exception as e:
        logger.error(f"获取股票 {stock_code} 的 {period} 数据失败: {e}")
        raise

# AKShare数据更新接口
@router.post("/akshare/update-stock-details")
async def update_akshare_stock_details(
    background_tasks: BackgroundTasks,
    stock_codes: Optional[List[str]] = None
):
    """更新AKShare股票详细信息"""
    try:
        async def update_task():
            async with AsyncSessionLocal() as db:
                akshare_service = NewAKShareService(db)

                # 如果没有指定股票代码，获取所有股票
                if not stock_codes:
                    result = await db.execute(select(Stock.stock_code))
                    codes = [row[0] for row in result.fetchall()]
                else:
                    codes = stock_codes

                # 执行更新
                results = await akshare_service.update_all_stock_details(codes)

                # 记录更新日志
                log_entry = DataUpdateLog(
                    task_type="akshare_stock_details",
                    status="completed" if results["failed"] == 0 else "partial_success",
                    total_records=results["total"],
                    success_records=results["success"],
                    failed_records=results["failed"],
                    error_message="; ".join(results["errors"]) if results["errors"] else None,
                    start_time=datetime.utcnow(),
                    end_time=datetime.utcnow()
                )
                db.add(log_entry)
                await db.commit()

                logger.info(f"AKShare股票详细信息更新完成: {results}")

        background_tasks.add_task(update_task)

        return {
            "message": "AKShare股票详细信息更新任务已启动",
            "task_type": "akshare_stock_details",
            "stock_count": len(stock_codes) if stock_codes else "all"
        }

    except Exception as e:
        logger.error(f"启动AKShare股票详细信息更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动更新任务失败")

@router.post("/akshare/update-spot-data")
async def update_akshare_spot_data(background_tasks: BackgroundTasks):
    """更新AKShare实时行情数据"""
    try:
        async def update_task():
            async with AsyncSessionLocal() as db:
                akshare_service = NewAKShareService(db)

                # 执行更新
                results = await akshare_service.update_all_spot_data()

                # 记录更新日志
                log_entry = DataUpdateLog(
                    task_type="akshare_spot_data",
                    status="completed" if results["failed"] == 0 else "partial_success",
                    total_records=results["total"],
                    success_records=results["success"],
                    failed_records=results["failed"],
                    error_message="; ".join(results["errors"]) if results["errors"] else None,
                    start_time=datetime.utcnow(),
                    end_time=datetime.utcnow()
                )
                db.add(log_entry)
                await db.commit()

                logger.info(f"AKShare实时行情数据更新完成: {results}")

        background_tasks.add_task(update_task)

        return {
            "message": "AKShare实时行情数据更新任务已启动",
            "task_type": "akshare_spot_data"
        }

    except Exception as e:
        logger.error(f"启动AKShare实时行情数据更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动更新任务失败")

@router.post("/akshare/force-update-all")
async def force_update_all_akshare_data(background_tasks: BackgroundTasks):
    """强制更新所有AKShare数据（实时行情+主要股票K线数据）"""
    try:
        async def force_update_task():
            async with AsyncSessionLocal() as db:
                akshare_service = NewAKShareService(db)

                logger.info("开始强制更新所有AKShare数据")

                # 1. 更新实时行情数据
                logger.info("步骤1: 强制更新实时行情数据")
                spot_results = await akshare_service.update_all_spot_data()
                logger.info(f"实时行情数据更新结果: {spot_results}")

                # 2. 更新主要股票的K线数据
                logger.info("步骤2: 更新主要股票K线数据")
                main_stocks = ["000001", "000002", "600000", "600036", "000858", "002415", "300059"]

                from app.services.data_fetcher import DataFetcherManager
                data_fetcher = DataFetcherManager()

                kline_results = {"total": 0, "success": 0, "failed": 0, "errors": []}

                for stock_code in main_stocks:
                    try:
                        logger.info(f"更新股票 {stock_code} 的K线数据")

                        # 更新日线、周线、月线数据
                        for period in ["daily", "weekly", "monthly"]:
                            try:
                                kline_data = await data_fetcher.get_kline_data(stock_code, period)
                                if kline_data:
                                    kline_results["total"] += len(kline_data)
                                    kline_results["success"] += len(kline_data)
                                    logger.info(f"股票 {stock_code} {period} 获取到 {len(kline_data)} 条数据")
                                else:
                                    kline_results["failed"] += 1
                                    kline_results["errors"].append(f"{stock_code}-{period}: 无数据")
                            except Exception as e:
                                kline_results["failed"] += 1
                                kline_results["errors"].append(f"{stock_code}-{period}: {str(e)}")
                                logger.error(f"更新股票 {stock_code} {period} 数据失败: {e}")

                        # 添加延迟避免频率限制
                        await asyncio.sleep(3)

                    except Exception as e:
                        kline_results["failed"] += 1
                        kline_results["errors"].append(f"{stock_code}: {str(e)}")
                        logger.error(f"更新股票 {stock_code} 失败: {e}")

                # 记录更新日志
                log_entry = DataUpdateLog(
                    task_type="force_update_all",
                    status="completed" if (spot_results["failed"] + kline_results["failed"]) == 0 else "partial_success",
                    total_records=spot_results["total"] + kline_results["total"],
                    success_records=spot_results["success"] + kline_results["success"],
                    failed_records=spot_results["failed"] + kline_results["failed"],
                    error_message="; ".join(spot_results["errors"] + kline_results["errors"]) if (spot_results["errors"] + kline_results["errors"]) else None,
                    start_time=datetime.utcnow(),
                    end_time=datetime.utcnow()
                )
                db.add(log_entry)
                await db.commit()

                logger.info(f"强制更新所有数据完成 - 实时行情: {spot_results}, K线数据: {kline_results}")

        background_tasks.add_task(force_update_task)

        return {
            "message": "强制更新所有AKShare数据任务已启动",
            "task_type": "force_update_all",
            "description": "将更新实时行情数据和主要股票的K线数据（日线、周线、月线）"
        }

    except Exception as e:
        logger.error(f"启动强制更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动强制更新任务失败")

@router.post("/akshare/update-sector-data")
async def update_akshare_sector_data(background_tasks: BackgroundTasks):
    """更新AKShare板块数据"""
    try:
        async def update_task():
            async with AsyncSessionLocal() as db:
                akshare_service = NewAKShareService(db)

                # 执行更新
                results = await akshare_service.update_all_sector_data()

                # 记录更新日志
                log_entry = DataUpdateLog(
                    task_type="akshare_sector_data",
                    status="completed" if results["failed"] == 0 else "partial_success",
                    total_records=results["total"],
                    success_records=results["success"],
                    failed_records=results["failed"],
                    error_message="; ".join(results["errors"]) if results["errors"] else None,
                    start_time=datetime.utcnow(),
                    end_time=datetime.utcnow()
                )
                db.add(log_entry)
                await db.commit()

                logger.info(f"AKShare板块数据更新完成: {results}")

        background_tasks.add_task(update_task)

        return {
            "message": "AKShare板块数据更新任务已启动",
            "task_type": "akshare_sector_data"
        }

    except Exception as e:
        logger.error(f"启动AKShare板块数据更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动更新任务失败")

@router.post("/akshare/update-market-summary")
async def update_akshare_market_summary(background_tasks: BackgroundTasks):
    """更新AKShare市场总貌数据"""
    try:
        async def update_task():
            async with AsyncSessionLocal() as db:
                akshare_service = NewAKShareService(db)

                # 更新沪深两市数据
                exchanges = ["SSE", "SZSE"]
                total_success = 0
                total_failed = 0
                errors = []

                for exchange in exchanges:
                    try:
                        summary_data = await akshare_service.fetch_market_summary(exchange)
                        if summary_data:
                            success = await akshare_service.save_market_summary(summary_data)
                            if success:
                                total_success += 1
                            else:
                                total_failed += 1
                                errors.append(f"保存 {exchange} 市场总貌数据失败")
                        else:
                            total_failed += 1
                            errors.append(f"获取 {exchange} 市场总貌数据失败")
                    except Exception as e:
                        total_failed += 1
                        errors.append(f"处理 {exchange} 市场总貌数据时出错: {str(e)}")

                # 记录更新日志
                log_entry = DataUpdateLog(
                    task_type="akshare_market_summary",
                    status="completed" if total_failed == 0 else "partial_success",
                    total_records=len(exchanges),
                    success_records=total_success,
                    failed_records=total_failed,
                    error_message="; ".join(errors) if errors else None,
                    start_time=datetime.utcnow(),
                    end_time=datetime.utcnow()
                )
                db.add(log_entry)
                await db.commit()

                logger.info(f"AKShare市场总貌数据更新完成: 成功{total_success}, 失败{total_failed}")

        background_tasks.add_task(update_task)

        return {
            "message": "AKShare市场总貌数据更新任务已启动",
            "task_type": "akshare_market_summary"
        }

    except Exception as e:
        logger.error(f"启动AKShare市场总貌数据更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动更新任务失败")
