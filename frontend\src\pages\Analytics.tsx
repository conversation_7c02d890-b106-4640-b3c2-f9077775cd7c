import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Table,
  Space,
  Typography,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Tag,
  message,
  Modal,
  Tabs,
  Statistic,
  Progress,
  Divider,
  List,
  Avatar,
} from 'antd'
import {
  <PERSON><PERSON><PERSON>Outlined,
  PlusOutlined,
  PlayCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
} from '@ant-design/icons'
import { analyticsService } from '@/services/apiService'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { RangePicker } = DatePicker
const { TabPane } = Tabs

interface BacktestResult {
  id: number
  strategy_name: string
  start_date: string
  end_date: string
  initial_capital: number
  final_value: number
  total_return: number
  total_return_pct: number
  annualized_return: number
  volatility: number
  sharpe_ratio: number
  max_drawdown: number
  total_trades: number
  win_rate: number
  status: string
  created_at: string
}

interface Strategy {
  name: string
  display_name: string
  description: string
  risk_level: string
  suitable_market: string
  parameters: any
}

const Analytics: React.FC = () => {
  const [form] = Form.useForm()
  const [backtests, setBacktests] = useState<BacktestResult[]>([])
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [loading, setLoading] = useState(false)
  const [runningBacktest, setRunningBacktest] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [selectedBacktest, setSelectedBacktest] = useState<BacktestResult | null>(null)

  useEffect(() => {
    loadStrategies()
    loadBacktests()
  }, [])

  const loadStrategies = async () => {
    try {
      const response = await analyticsService.getStrategies()
      if (response.code === 200) {
        setStrategies(response.data.strategies)
      }
    } catch (error) {
      // 使用模拟数据
      const mockStrategies: Strategy[] = [
        {
          name: 'ma_crossover',
          display_name: '移动平均线交叉策略',
          description: '基于短期和长期移动平均线交叉的趋势跟踪策略',
          risk_level: '中等',
          suitable_market: '趋势市场',
          parameters: {
            short_ma: { type: 'int', default: 5, description: '短期均线周期' },
            long_ma: { type: 'int', default: 20, description: '长期均线周期' }
          }
        },
        {
          name: 'rsi_reversal',
          display_name: 'RSI反转策略',
          description: '基于RSI指标的超买超卖反转策略',
          risk_level: '中等',
          suitable_market: '震荡市场',
          parameters: {
            oversold: { type: 'int', default: 30, description: '超卖阈值' },
            overbought: { type: 'int', default: 70, description: '超买阈值' }
          }
        },
        {
          name: 'ai_momentum',
          display_name: 'AI动量策略',
          description: '基于AI预测信号的动量策略',
          risk_level: '高',
          suitable_market: '全市场',
          parameters: {
            confidence_threshold: { type: 'float', default: 0.7, description: '置信度阈值' }
          }
        },
        {
          name: 'value_investing',
          display_name: '价值投资策略',
          description: '基于基本面指标的价值投资策略',
          risk_level: '低',
          suitable_market: '熊市/震荡市',
          parameters: {
            max_pe: { type: 'float', default: 15, description: '最大市盈率' },
            max_pb: { type: 'float', default: 2, description: '最大市净率' }
          }
        }
      ]
      setStrategies(mockStrategies)
    }
  }

  const loadBacktests = async () => {
    setLoading(true)
    try {
      // 模拟回测历史数据
      const mockBacktests: BacktestResult[] = [
        {
          id: 1,
          strategy_name: 'ma_crossover',
          start_date: '2024-01-01',
          end_date: '2024-12-31',
          initial_capital: 100000,
          final_value: 115000,
          total_return: 15000,
          total_return_pct: 15.0,
          annualized_return: 15.0,
          volatility: 18.5,
          sharpe_ratio: 0.85,
          max_drawdown: -8.2,
          total_trades: 24,
          win_rate: 62.5,
          status: 'completed',
          created_at: '2025-07-27 10:30:00'
        },
        {
          id: 2,
          strategy_name: 'rsi_reversal',
          start_date: '2024-01-01',
          end_date: '2024-12-31',
          initial_capital: 100000,
          final_value: 108000,
          total_return: 8000,
          total_return_pct: 8.0,
          annualized_return: 8.0,
          volatility: 15.2,
          sharpe_ratio: 0.52,
          max_drawdown: -12.5,
          total_trades: 36,
          win_rate: 58.3,
          status: 'completed',
          created_at: '2025-07-27 09:15:00'
        },
        {
          id: 3,
          strategy_name: 'ai_momentum',
          start_date: '2024-01-01',
          end_date: '2024-12-31',
          initial_capital: 100000,
          final_value: 125000,
          total_return: 25000,
          total_return_pct: 25.0,
          annualized_return: 25.0,
          volatility: 22.8,
          sharpe_ratio: 1.12,
          max_drawdown: -15.3,
          total_trades: 18,
          win_rate: 72.2,
          status: 'completed',
          created_at: '2025-07-26 16:45:00'
        }
      ]
      setBacktests(mockBacktests)
    } catch (error) {
      message.error('加载回测历史失败')
    } finally {
      setLoading(false)
    }
  }

  const handleRunBacktest = async (values: any) => {
    setRunningBacktest(true)
    try {
      // 模拟运行回测
      await new Promise(resolve => setTimeout(resolve, 3000))
      message.success('回测完成')
      loadBacktests()
      setModalVisible(false)
    } catch (error) {
      message.error('回测失败')
    } finally {
      setRunningBacktest(false)
    }
  }

  const backtestColumns = [
    {
      title: '策略名称',
      dataIndex: 'strategy_name',
      key: 'strategy_name',
      render: (value: string) => {
        const strategy = strategies.find(s => s.name === value)
        return strategy ? strategy.display_name : value
      },
    },
    {
      title: '回测期间',
      key: 'period',
      render: (record: BacktestResult) => (
        <Space direction="vertical" size="small">
          <Text style={{ fontSize: 12 }}>{record.start_date}</Text>
          <Text style={{ fontSize: 12 }}>{record.end_date}</Text>
        </Space>
      ),
    },
    {
      title: '总收益',
      key: 'return',
      render: (record: BacktestResult) => (
        <Space direction="vertical" size="small">
          <Text strong type={record.total_return_pct >= 0 ? 'success' : 'danger'}>
            {record.total_return_pct >= 0 ? '+' : ''}{record.total_return_pct.toFixed(2)}%
          </Text>
          <Text style={{ fontSize: 12 }}>
            ¥{record.total_return.toLocaleString()}
          </Text>
        </Space>
      ),
    },
    {
      title: '年化收益',
      dataIndex: 'annualized_return',
      key: 'annualized_return',
      render: (value: number) => (
        <Text type={value >= 0 ? 'success' : 'danger'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Text>
      ),
    },
    {
      title: '夏普比率',
      dataIndex: 'sharpe_ratio',
      key: 'sharpe_ratio',
      render: (value: number) => (
        <Text type={value >= 1 ? 'success' : value >= 0.5 ? 'warning' : 'danger'}>
          {value.toFixed(2)}
        </Text>
      ),
    },
    {
      title: '最大回撤',
      dataIndex: 'max_drawdown',
      key: 'max_drawdown',
      render: (value: number) => (
        <Text type="danger">
          {value.toFixed(2)}%
        </Text>
      ),
    },
    {
      title: '胜率',
      dataIndex: 'win_rate',
      key: 'win_rate',
      render: (value: number) => (
        <Progress
          percent={value}
          size="small"
          strokeColor={value >= 60 ? '#52c41a' : value >= 50 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '交易次数',
      dataIndex: 'total_trades',
      key: 'total_trades',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: BacktestResult) => (
        <Button
          type="link"
          onClick={() => setSelectedBacktest(record)}
        >
          查看详情
        </Button>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <BarChartOutlined /> 统计分析
        </Title>
        <Paragraph type="secondary">
          策略回测、表现分析和风险评估工具
        </Paragraph>
      </div>

      <Tabs defaultActiveKey="backtest">
        <TabPane tab="策略回测" key="backtest">
          <Card
            title="回测历史"
            extra={
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={() => setModalVisible(true)}
              >
                新建回测
              </Button>
            }
          >
            <Table
              dataSource={backtests}
              columns={backtestColumns}
              loading={loading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="策略库" key="strategies">
          <Row gutter={[16, 16]}>
            {strategies.map(strategy => (
              <Col span={12} key={strategy.name}>
                <Card
                  title={strategy.display_name}
                  extra={
                    <Tag color={
                      strategy.risk_level === '低' ? 'green' :
                      strategy.risk_level === '中等' ? 'orange' : 'red'
                    }>
                      {strategy.risk_level}风险
                    </Tag>
                  }
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Paragraph style={{ marginBottom: 8 }}>
                      {strategy.description}
                    </Paragraph>
                    <div>
                      <Text type="secondary">适用市场: </Text>
                      <Text>{strategy.suitable_market}</Text>
                    </div>
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => {
                        setModalVisible(true)
                        form.setFieldsValue({ strategy: strategy.name })
                      }}
                    >
                      运行回测
                    </Button>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>
      </Tabs>

      {/* 新建回测模态框 */}
      <Modal
        title="新建策略回测"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleRunBacktest}
        >
          <Form.Item
            name="strategy"
            label="选择策略"
            rules={[{ required: true, message: '请选择策略' }]}
          >
            <Select placeholder="选择回测策略">
              {strategies.map(strategy => (
                <Option key={strategy.name} value={strategy.name}>
                  {strategy.display_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="date_range"
            label="回测期间"
            rules={[{ required: true, message: '请选择回测期间' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="initial_capital"
            label="初始资金"
            rules={[{ required: true, message: '请输入初始资金' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="100000"
              formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value!.replace(/¥\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={runningBacktest}
                icon={<PlayCircleOutlined />}
              >
                开始回测
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 回测详情 */}
      {selectedBacktest && (
        <Modal
          title={`回测详情 - ${strategies.find(s => s.name === selectedBacktest.strategy_name)?.display_name}`}
          open={!!selectedBacktest}
          onCancel={() => setSelectedBacktest(null)}
          footer={null}
          width={800}
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card size="small" title="收益指标">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Statistic
                    title="总收益率"
                    value={selectedBacktest.total_return_pct}
                    precision={2}
                    suffix="%"
                    valueStyle={{
                      color: selectedBacktest.total_return_pct >= 0 ? '#3f8600' : '#cf1322'
                    }}
                  />
                  <Statistic
                    title="年化收益率"
                    value={selectedBacktest.annualized_return}
                    precision={2}
                    suffix="%"
                    valueStyle={{
                      color: selectedBacktest.annualized_return >= 0 ? '#3f8600' : '#cf1322'
                    }}
                  />
                  <Statistic
                    title="夏普比率"
                    value={selectedBacktest.sharpe_ratio}
                    precision={2}
                  />
                </Space>
              </Card>
            </Col>

            <Col span={12}>
              <Card size="small" title="风险指标">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Statistic
                    title="最大回撤"
                    value={Math.abs(selectedBacktest.max_drawdown)}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: '#cf1322' }}
                  />
                  <Statistic
                    title="波动率"
                    value={selectedBacktest.volatility}
                    precision={2}
                    suffix="%"
                  />
                  <Statistic
                    title="胜率"
                    value={selectedBacktest.win_rate}
                    precision={1}
                    suffix="%"
                  />
                </Space>
              </Card>
            </Col>
          </Row>
        </Modal>
      )}
    </div>
  )
}

export default Analytics
