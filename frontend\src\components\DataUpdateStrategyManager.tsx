/**
 * 数据更新策略管理组件
 * 提供定时批量更新、按需更新、增量更新机制管理
 */

import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Space, Tag, Modal, Form, Input, Select, 
  Switch, InputNumber, TimePicker, Checkbox, message, Progress,
  Statistic, Row, Col, Alert, Descriptions, Badge, Tooltip,
  Popconfirm, Tabs, List, Typography
} from 'antd'
import {
  PlayCircleOutlined, PauseCircleOutlined, EditOutlined, DeleteOutlined,
  PlusOutlined, ReloadOutlined, ClockCircleOutlined, SettingOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined, StopOutlined,
  Bar<PERSON><PERSON>Outlined, CalendarOutlined, ThunderboltOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { dataUpdateStrategyService } from '../services/dataUpdateStrategyService'

const { Option } = Select
const { TabPane } = Tabs
const { Text, Title } = Typography

interface UpdateStrategy {
  strategy_id?: string
  strategy_name: string
  strategy_type: string
  data_source: string
  target_tables: string[]
  config: Record<string, any>
  is_active: boolean
  created_at?: string
  last_run?: string
  next_run?: string
}

interface UpdateTask {
  task_id?: string
  strategy_id: string
  task_name: string
  status: string
  progress: number
  total_records: number
  processed_records: number
  error_message?: string
  started_at?: string
  completed_at?: string
}

const DataUpdateStrategyManager: React.FC = () => {
  const [strategies, setStrategies] = useState<UpdateStrategy[]>([])
  const [tasks, setTasks] = useState<UpdateTask[]>([])
  const [statistics, setStatistics] = useState<any>({})
  const [dataSources, setDataSources] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingStrategy, setEditingStrategy] = useState<UpdateStrategy | null>(null)
  const [activeTab, setActiveTab] = useState('strategies')
  const [form] = Form.useForm()

  useEffect(() => {
    loadData()
    // 定时刷新任务状态
    const interval = setInterval(loadTasks, 5000)
    return () => clearInterval(interval)
  }, [])

  const loadData = async () => {
    await Promise.all([
      loadStrategies(),
      loadTasks(),
      loadStatistics(),
      loadDataSources()
    ])
  }

  const loadStrategies = async () => {
    try {
      setLoading(true)
      const data = await dataUpdateStrategyService.getStrategies()
      setStrategies(data)
    } catch (error) {
      message.error('加载更新策略失败')
      console.error('加载更新策略失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadTasks = async () => {
    try {
      const data = await dataUpdateStrategyService.getTasks()
      setTasks(data)
    } catch (error) {
      console.error('加载任务列表失败:', error)
    }
  }

  const loadStatistics = async () => {
    try {
      const data = await dataUpdateStrategyService.getStatistics()
      setStatistics(data)
    } catch (error) {
      console.error('加载统计信息失败:', error)
    }
  }

  const loadDataSources = async () => {
    try {
      const data = await dataUpdateStrategyService.getDataSources()
      setDataSources(data)
    } catch (error) {
      console.error('加载数据源失败:', error)
    }
  }

  const handleCreateStrategy = () => {
    setEditingStrategy(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEditStrategy = (strategy: UpdateStrategy) => {
    setEditingStrategy(strategy)
    form.setFieldsValue({
      ...strategy,
      weekdays: strategy.config?.weekdays || [],
      interval_hours: strategy.config?.interval_hours || 24,
      start_time: strategy.config?.start_time ? dayjs(strategy.config.start_time, 'HH:mm') : null,
      end_time: strategy.config?.end_time ? dayjs(strategy.config.end_time, 'HH:mm') : null,
    })
    setModalVisible(true)
  }

  const handleSaveStrategy = async () => {
    try {
      const values = await form.validateFields()
      
      const strategyData: UpdateStrategy = {
        strategy_name: values.strategy_name,
        strategy_type: values.strategy_type,
        data_source: values.data_source,
        target_tables: values.target_tables,
        is_active: values.is_active,
        config: {
          interval_hours: values.interval_hours,
          start_time: values.start_time?.format('HH:mm'),
          end_time: values.end_time?.format('HH:mm'),
          weekdays: values.weekdays,
          priority: values.priority,
          timeout_minutes: values.timeout_minutes,
          check_interval_minutes: values.check_interval_minutes,
          batch_size: values.batch_size,
          max_records_per_batch: values.max_records_per_batch,
        }
      }

      if (editingStrategy?.strategy_id) {
        await dataUpdateStrategyService.updateStrategy(editingStrategy.strategy_id, strategyData)
        message.success('策略更新成功')
      } else {
        await dataUpdateStrategyService.createStrategy(strategyData)
        message.success('策略创建成功')
      }

      setModalVisible(false)
      loadStrategies()
    } catch (error) {
      message.error('保存策略失败')
      console.error('保存策略失败:', error)
    }
  }

  const handleDeleteStrategy = async (strategyId: string) => {
    try {
      await dataUpdateStrategyService.deleteStrategy(strategyId)
      message.success('策略删除成功')
      loadStrategies()
    } catch (error) {
      message.error('删除策略失败')
      console.error('删除策略失败:', error)
    }
  }

  const handleExecuteStrategy = async (strategyId: string) => {
    try {
      const result = await dataUpdateStrategyService.executeStrategy(strategyId)
      message.success(`更新任务已启动: ${result.task_id}`)
      loadTasks()
    } catch (error) {
      message.error('执行策略失败')
      console.error('执行策略失败:', error)
    }
  }

  const handleCancelTask = async (taskId: string) => {
    try {
      await dataUpdateStrategyService.cancelTask(taskId)
      message.success('任务已取消')
      loadTasks()
    } catch (error) {
      message.error('取消任务失败')
      console.error('取消任务失败:', error)
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'orange',
      running: 'blue',
      completed: 'green',
      failed: 'red',
      cancelled: 'gray'
    }
    return colors[status as keyof typeof colors] || 'default'
  }

  const getStatusIcon = (status: string) => {
    const icons = {
      pending: <ClockCircleOutlined />,
      running: <PlayCircleOutlined />,
      completed: <CheckCircleOutlined />,
      failed: <ExclamationCircleOutlined />,
      cancelled: <StopOutlined />
    }
    return icons[status as keyof typeof icons] || null
  }

  const strategyColumns = [
    {
      title: '策略名称',
      dataIndex: 'strategy_name',
      key: 'strategy_name',
      render: (text: string, record: UpdateStrategy) => (
        <Space>
          <Text strong>{text}</Text>
          <Tag color={record.is_active ? 'green' : 'red'}>
            {record.is_active ? '激活' : '停用'}
          </Tag>
        </Space>
      )
    },
    {
      title: '策略类型',
      dataIndex: 'strategy_type',
      key: 'strategy_type',
      render: (type: string) => {
        const typeMap = {
          scheduled: { text: '定时更新', icon: <CalendarOutlined />, color: 'blue' },
          on_demand: { text: '按需更新', icon: <PlayCircleOutlined />, color: 'green' },
          incremental: { text: '增量更新', icon: <BarChartOutlined />, color: 'orange' },
          real_time: { text: '实时更新', icon: <ThunderboltOutlined />, color: 'red' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { text: type, icon: null, color: 'default' }
        return (
          <Tag icon={config.icon} color={config.color}>
            {config.text}
          </Tag>
        )
      }
    },
    {
      title: '数据源',
      dataIndex: 'data_source',
      key: 'data_source',
      render: (source: string) => (
        <Tag color="geekblue">{source.toUpperCase()}</Tag>
      )
    },
    {
      title: '目标表',
      dataIndex: 'target_tables',
      key: 'target_tables',
      render: (tables: string[]) => (
        <Tooltip title={tables.join(', ')}>
          <Badge count={tables.length} size="small">
            <Text>数据表</Text>
          </Badge>
        </Tooltip>
      )
    },
    {
      title: '最后运行',
      dataIndex: 'last_run',
      key: 'last_run',
      render: (time: string) => (
        time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '从未运行'
      )
    },
    {
      title: '下次运行',
      dataIndex: 'next_run',
      key: 'next_run',
      render: (time: string) => (
        time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '手动触发'
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: UpdateStrategy) => (
        <Space>
          <Tooltip title="执行策略">
            <Button
              icon={<PlayCircleOutlined />}
              size="small"
              type="primary"
              onClick={() => handleExecuteStrategy(record.strategy_id!)}
              disabled={!record.is_active}
            />
          </Tooltip>
          <Tooltip title="编辑策略">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditStrategy(record)}
            />
          </Tooltip>
          <Tooltip title="删除策略">
            <Popconfirm
              title="确定删除此策略吗？"
              onConfirm={() => handleDeleteStrategy(record.strategy_id!)}
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ]

  const taskColumns = [
    {
      title: '任务名称',
      dataIndex: 'task_name',
      key: 'task_name',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag icon={getStatusIcon(status)} color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: UpdateTask) => (
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <Progress 
            percent={Math.round(progress)} 
            size="small" 
            status={record.status === 'failed' ? 'exception' : 'active'}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.processed_records}/{record.total_records}
          </Text>
        </Space>
      )
    },
    {
      title: '开始时间',
      dataIndex: 'started_at',
      key: 'started_at',
      render: (time: string) => (
        time ? dayjs(time).format('MM-DD HH:mm') : '-'
      )
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (time: string) => (
        time ? dayjs(time).format('MM-DD HH:mm') : '-'
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: UpdateTask) => (
        <Space>
          {record.status === 'running' && (
            <Tooltip title="取消任务">
              <Popconfirm
                title="确定取消此任务吗？"
                onConfirm={() => handleCancelTask(record.task_id!)}
              >
                <Button
                  icon={<StopOutlined />}
                  size="small"
                  danger
                />
              </Popconfirm>
            </Tooltip>
          )}
          {record.error_message && (
            <Tooltip title={record.error_message}>
              <Button
                icon={<ExclamationCircleOutlined />}
                size="small"
                type="text"
                danger
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  const renderOverview = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总策略数"
            value={statistics.total_strategies || 0}
            prefix={<SettingOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="激活策略"
            value={statistics.active_strategies || 0}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="总任务数"
            value={statistics.total_tasks || 0}
            prefix={<BarChartOutlined />}
            valueStyle={{ color: '#722ed1' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="成功率"
            value={statistics.success_rate || 0}
            suffix="%"
            precision={1}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
    </Row>
  )

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title="数据更新策略管理" 
        extra={
          <Space>
            <Button 
              icon={<PlusOutlined />} 
              type="primary" 
              onClick={handleCreateStrategy}
            >
              新建策略
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadData} 
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {renderOverview()}
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="更新策略" key="strategies">
            <Table
              columns={strategyColumns}
              dataSource={strategies}
              rowKey="strategy_id"
              loading={loading}
              size="small"
            />
          </TabPane>
          
          <TabPane tab="执行任务" key="tasks">
            <Table
              columns={taskColumns}
              dataSource={tasks}
              rowKey="task_id"
              loading={loading}
              size="small"
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 策略配置模态框 */}
      <Modal
        title={editingStrategy ? '编辑更新策略' : '新建更新策略'}
        open={modalVisible}
        onOk={handleSaveStrategy}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="strategy_name"
                label="策略名称"
                rules={[{ required: true, message: '请输入策略名称' }]}
              >
                <Input placeholder="输入策略名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="strategy_type"
                label="策略类型"
                rules={[{ required: true, message: '请选择策略类型' }]}
              >
                <Select placeholder="选择策略类型">
                  <Option value="scheduled">定时更新</Option>
                  <Option value="on_demand">按需更新</Option>
                  <Option value="incremental">增量更新</Option>
                  <Option value="real_time">实时更新</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="data_source"
                label="数据源"
                rules={[{ required: true, message: '请选择数据源' }]}
              >
                <Select placeholder="选择数据源">
                  <Option value="akshare">AKShare</Option>
                  <Option value="tushare">Tushare</Option>
                  <Option value="manual">手动导入</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="target_tables"
                label="目标表"
                rules={[{ required: true, message: '请选择目标表' }]}
              >
                <Select mode="multiple" placeholder="选择目标表">
                  <Option value="stock_basic_info">股票基础信息</Option>
                  <Option value="stock_kline_data">K线数据</Option>
                  <Option value="stock_realtime_data">实时行情</Option>
                  <Option value="stock_financial_data">财务数据</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="is_active" valuePropName="checked" initialValue={true}>
            <Switch checkedChildren="激活" unCheckedChildren="停用" />
            <span style={{ marginLeft: '8px' }}>策略状态</span>
          </Form.Item>

          {/* 根据策略类型显示不同的配置项 */}
          <Form.Item shouldUpdate={(prevValues, currentValues) => 
            prevValues.strategy_type !== currentValues.strategy_type
          }>
            {({ getFieldValue }) => {
              const strategyType = getFieldValue('strategy_type')
              
              if (strategyType === 'scheduled') {
                return (
                  <>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item name="interval_hours" label="更新间隔(小时)" initialValue={24}>
                          <InputNumber min={1} max={168} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name="start_time" label="开始时间">
                          <TimePicker format="HH:mm" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name="end_time" label="结束时间">
                          <TimePicker format="HH:mm" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Form.Item name="weekdays" label="工作日">
                      <Checkbox.Group>
                        <Checkbox value={1}>周一</Checkbox>
                        <Checkbox value={2}>周二</Checkbox>
                        <Checkbox value={3}>周三</Checkbox>
                        <Checkbox value={4}>周四</Checkbox>
                        <Checkbox value={5}>周五</Checkbox>
                        <Checkbox value={6}>周六</Checkbox>
                        <Checkbox value={0}>周日</Checkbox>
                      </Checkbox.Group>
                    </Form.Item>
                  </>
                )
              }
              
              if (strategyType === 'incremental') {
                return (
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item name="check_interval_minutes" label="检查间隔(分钟)" initialValue={30}>
                        <InputNumber min={1} max={1440} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="batch_size" label="批次大小" initialValue={1000}>
                        <InputNumber min={100} max={10000} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="max_records_per_batch" label="最大记录数" initialValue={5000}>
                        <InputNumber min={1000} max={50000} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                )
              }
              
              return null
            }}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default DataUpdateStrategyManager
