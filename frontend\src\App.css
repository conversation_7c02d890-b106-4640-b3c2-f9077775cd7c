/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* Ant Design Layout 样式优化 */
.ant-layout {
  background: #f0f2f5;
  width: 100%;
  min-height: 100vh;
}

.ant-layout-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  z-index: 10;
  position: sticky;
  top: 0;
  width: 100%;
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.ant-layout-sider {
  background: #001529;
  position: relative !important;
  height: auto !important;
}

.ant-layout-content {
  background: #fff;
  margin: 16px;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  min-height: calc(100vh - 64px - 32px);
  overflow: auto;
}

/* 确保侧边栏和内容区域正确布局 */
.ant-layout-has-sider {
  flex-direction: row !important;
}

/* 修复表格和其他组件的样式 */
.ant-table-wrapper {
  margin-bottom: 16px;
}

.ant-card {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    z-index: 999;
    height: 100vh !important;
  }

  .ant-layout-content {
    margin-left: 0;
  }
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
