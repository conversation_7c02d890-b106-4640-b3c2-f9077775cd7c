/**
 * MACD指标图表组件
 */

import React, { useMemo } from 'react'
import ReactECharts from 'echarts-for-react'

interface PriceData {
  timestamp: string
  close: number
}

interface MACDChartProps {
  data: PriceData[]
  height?: number
  timeFrame?: string
}

// 计算EMA
const calculateEMA = (data: number[], period: number): number[] => {
  const ema: number[] = []
  const multiplier = 2 / (period + 1)
  
  if (data.length === 0) return ema
  
  ema[0] = data[0]
  
  for (let i = 1; i < data.length; i++) {
    ema[i] = (data[i] - ema[i - 1]) * multiplier + ema[i - 1]
  }
  
  return ema
}

// 计算MACD
const calculateMACD = (data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) => {
  const fastEMA = calculateEMA(data, fastPeriod)
  const slowEMA = calculateEMA(data, slowPeriod)
  
  // MACD线 = 快线EMA - 慢线EMA
  const macdLine = fastEMA.map((fast, index) => fast - slowEMA[index])
  
  // Signal线 = MACD线的EMA
  const signalLine = calculateEMA(macdLine, signalPeriod)
  
  // Histogram = MACD线 - Signal线
  const histogram = macdLine.map((macd, index) => macd - signalLine[index])
  
  return {
    macd: macdLine,
    signal: signalLine,
    histogram: histogram
  }
}

const MACDChart: React.FC<MACDChartProps> = ({
  data,
  height = 250,
  timeFrame = '1D'
}) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null

    const closes = data.map(item => item.close)
    const macdData = calculateMACD(closes)

    // 准备时间轴
    const timeAxis = data.map(item => {
      const date = new Date(item.timestamp)
      if (timeFrame === '1m' || timeFrame === '5m' || timeFrame === '15m' || timeFrame === '30m' || timeFrame === '1h') {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    })

    return {
      timeAxis,
      ...macdData
    }
  }, [data, timeFrame])

  const option = useMemo(() => {
    if (!chartData) return {}

    return {
      backgroundColor: '#ffffff',
      grid: {
        left: '8%',
        right: '8%',
        top: '15%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.timeAxis,
        axisLabel: {
          fontSize: 10
        },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        textStyle: {
          color: '#000',
          fontSize: 12
        }
      },
      legend: {
        data: ['MACD', 'Signal', 'Histogram'],
        top: '5%',
        textStyle: {
          fontSize: 10
        }
      },
      series: [
        {
          name: 'MACD',
          type: 'line',
          data: chartData.macd,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          symbol: 'none'
        },
        {
          name: 'Signal',
          type: 'line',
          data: chartData.signal,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 2
          },
          symbol: 'none'
        },
        {
          name: 'Histogram',
          type: 'bar',
          data: chartData.histogram.map(value => ({
            value,
            itemStyle: {
              color: value >= 0 ? '#ff4d4f' : '#52c41a'
            }
          })),
          barWidth: '60%'
        }
      ]
    }
  }, [chartData])

  if (!chartData) {
    return (
      <div style={{
        height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>暂无数据</div>
      </div>
    )
  }

  return (
    <ReactECharts
      option={option}
      style={{ height, width: '100%' }}
      opts={{ renderer: 'canvas' }}
    />
  )
}

export default MACDChart
