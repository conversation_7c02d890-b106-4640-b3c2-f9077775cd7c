import React from 'react'
import {
  Card,
  Typography,
  Row,
  Col,
  Collapse,
  Steps,
  Tag,
  Space,
  Button,
  Divider,
} from 'antd'
import {
  QuestionCircleOutlined,
  BookOutlined,
  CustomerServiceOutlined,
  RocketOutlined,
  SafetyOutlined,
  BulbOutlined,
} from '@ant-design/icons'

const { Title, Paragraph, Text } = Typography
const { Panel } = Collapse
const { Step } = Steps

const Help: React.FC = () => {
  const faqData = [
    {
      key: '1',
      question: '如何开始使用智能股票分析平台？',
      answer: '首先注册账户，然后在仪表盘查看市场概况，使用股票分析功能搜索感兴趣的股票，设置预警规则，最后可以使用AI预测功能获得投资建议。'
    },
    {
      key: '2',
      question: 'AI预测的准确率如何？',
      answer: 'AI预测基于深度学习模型和大数据分析，历史准确率约为70-80%。但请注意，股市投资存在风险，AI预测仅供参考，不构成投资建议。'
    },
    {
      key: '3',
      question: '如何设置价格预警？',
      answer: '进入预警系统页面，点击"创建规则"，选择股票代码，设置价格上限和下限，选择通知方式，保存即可。当股价触及设定值时会自动通知您。'
    },
    {
      key: '4',
      question: '智能选股功能如何使用？',
      answer: '在智能选股页面，您可以选择预设策略（如价值投资、成长投资等），或自定义筛选条件（市盈率、市净率、ROE等），系统会自动筛选符合条件的股票。'
    },
    {
      key: '5',
      question: '如何查看投资组合表现？',
      answer: '在投资组合页面，您可以添加持仓股票，系统会自动计算总收益、收益率、风险指标等，并提供详细的表现分析报告。'
    },
    {
      key: '6',
      question: '数据更新频率是多少？',
      answer: '股价数据实时更新，技术指标每分钟更新一次，基本面数据每日更新，AI预测每小时更新一次。'
    }
  ]

  const features = [
    {
      icon: <RocketOutlined style={{ color: '#1890ff' }} />,
      title: 'AI智能预测',
      description: '基于深度学习的股价预测和投资建议'
    },
    {
      icon: <SafetyOutlined style={{ color: '#52c41a' }} />,
      title: '风险控制',
      description: '多维度风险评估和预警系统'
    },
    {
      icon: <BulbOutlined style={{ color: '#faad14' }} />,
      title: '智能选股',
      description: '多策略股票筛选和评分系统'
    },
    {
      icon: <BookOutlined style={{ color: '#722ed1' }} />,
      title: '专业分析',
      description: '技术分析、基本面分析和量化回测'
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <QuestionCircleOutlined /> 帮助中心
        </Title>
        <Paragraph type="secondary">
          欢迎使用智能股票分析平台，这里有您需要的所有帮助信息
        </Paragraph>
      </div>

      <Row gutter={[24, 24]}>
        <Col span={16}>
          <Card title="快速入门" style={{ marginBottom: 24 }}>
            <Steps direction="vertical" size="small">
              <Step
                title="注册账户"
                description="使用邮箱注册账户，完善个人信息"
                status="finish"
              />
              <Step
                title="浏览市场"
                description="在仪表盘查看市场概况和热门股票"
                status="finish"
              />
              <Step
                title="分析股票"
                description="使用股票分析功能深入研究感兴趣的股票"
                status="process"
              />
              <Step
                title="设置预警"
                description="创建价格预警和技术指标预警规则"
                status="wait"
              />
              <Step
                title="AI预测"
                description="获取AI智能预测和投资建议"
                status="wait"
              />
              <Step
                title="组合管理"
                description="管理投资组合，跟踪投资表现"
                status="wait"
              />
            </Steps>
          </Card>

          <Card title="常见问题">
            <Collapse>
              {faqData.map(item => (
                <Panel header={item.question} key={item.key}>
                  <Paragraph>{item.answer}</Paragraph>
                </Panel>
              ))}
            </Collapse>
          </Card>
        </Col>

        <Col span={8}>
          <Card title="核心功能" style={{ marginBottom: 24 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {features.map((feature, index) => (
                <div key={index} style={{ padding: '12px 0' }}>
                  <Space>
                    {feature.icon}
                    <div>
                      <Text strong>{feature.title}</Text>
                      <div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {feature.description}
                        </Text>
                      </div>
                    </div>
                  </Space>
                </div>
              ))}
            </Space>
          </Card>

          <Card title="技术支持">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>在线客服</Text>
                <div>
                  <Text type="secondary">工作日 9:00-18:00</Text>
                </div>
                <Button type="primary" icon={<CustomerServiceOutlined />} style={{ marginTop: 8 }}>
                  联系客服
                </Button>
              </div>
              
              <Divider />
              
              <div>
                <Text strong>邮箱支持</Text>
                <div>
                  <Text type="secondary"><EMAIL></Text>
                </div>
              </div>
              
              <Divider />
              
              <div>
                <Text strong>用户手册</Text>
                <div>
                  <Button type="link" style={{ padding: 0 }}>
                    下载完整用户手册
                  </Button>
                </div>
              </div>
            </Space>
          </Card>

          <Card title="版本信息">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>当前版本</Text>
                <div>
                  <Tag color="blue">v1.0.0</Tag>
                </div>
              </div>
              
              <div>
                <Text strong>更新日期</Text>
                <div>
                  <Text type="secondary">2025-07-27</Text>
                </div>
              </div>
              
              <div>
                <Text strong>新功能</Text>
                <div>
                  <Text type="secondary">AI预测、智能选股、风险评估</Text>
                </div>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Help
