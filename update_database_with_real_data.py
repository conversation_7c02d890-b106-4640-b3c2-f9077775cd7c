#!/usr/bin/env python3
"""
直接更新数据库中的股票数据为真实AKShare数据
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import get_db
from app.services.akshare_service import AKShareService
from app.models.stock import StockSpotData
from sqlalchemy import delete
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def update_spot_data():
    """更新实时行情数据"""
    try:
        # 获取数据库会话
        async for session in get_db():
            logger.info("🚀 开始更新实时行情数据...")
            
            # 创建AKShare服务实例
            akshare_service = AKShareService(session)
            
            # 获取真实的AKShare数据
            logger.info("📡 调用AKShare API获取实时行情数据...")
            spot_data = await akshare_service.fetch_spot_data()
            
            if spot_data and len(spot_data) > 0:
                logger.info(f"✅ 成功获取 {len(spot_data)} 条实时行情数据")
                
                # 检查是否是真实数据（非模拟数据）
                first_stock = spot_data[0]
                if first_stock.get("current_price") != 10.5:  # 模拟数据的固定价格
                    logger.info("🎉 获取到真实数据，开始更新数据库...")
                    
                    # 删除旧的实时行情数据
                    logger.info("🗑️  删除旧的实时行情数据...")
                    await session.execute(delete(StockSpotData))
                    
                    # 插入新的真实数据
                    logger.info("💾 插入新的真实数据...")
                    for data in spot_data:
                        spot_record = StockSpotData(
                            stock_code=data["stock_code"],
                            stock_name=data["stock_name"],
                            current_price=data["current_price"],
                            open_price=data["open_price"],
                            high_price=data["high_price"],
                            low_price=data["low_price"],
                            pre_close=data["pre_close"],
                            change_amount=data["change_amount"],
                            change_percent=data["change_percent"],
                            amplitude=data["amplitude"],
                            volume=data["volume"],
                            turnover=data["turnover"],
                            turnover_rate=data["turnover_rate"],
                            volume_ratio=data["volume_ratio"],
                            pe_ratio=data["pe_ratio"],
                            pb_ratio=data["pb_ratio"],
                            total_market_cap=data["total_market_cap"],
                            float_market_cap=data["float_market_cap"],
                            speed=data["speed"],
                            change_5min=data["change_5min"],
                            change_60day=data["change_60day"],
                            change_ytd=data["change_ytd"],
                            trade_date=data["trade_date"],
                            update_time=data["update_time"]
                        )
                        session.add(spot_record)
                    
                    # 提交事务
                    await session.commit()
                    logger.info("✅ 数据库更新完成!")
                    
                    # 验证更新结果
                    logger.info("🔍 验证更新结果...")
                    from sqlalchemy import select
                    result = await session.execute(select(StockSpotData).limit(3))
                    updated_data = result.scalars().all()
                    
                    if updated_data:
                        logger.info(f"📊 数据库中现有 {len(updated_data)} 条记录")
                        for record in updated_data[:3]:
                            logger.info(f"  {record.stock_code} {record.stock_name}: ¥{record.current_price}")
                    
                    return True
                else:
                    logger.warning("⚠️  获取到的仍然是模拟数据，数据库未更新")
                    return False
            else:
                logger.error("❌ 未能获取到有效的实时行情数据")
                return False
                
    except Exception as e:
        logger.error(f"❌ 更新数据库失败: {e}", exc_info=True)
        return False

async def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🔄 数据库股票数据更新工具")
    logger.info("=" * 80)
    
    success = await update_spot_data()
    
    if success:
        logger.info("🎉 数据库更新成功! 现在前端应该显示真实的股票数据了。")
    else:
        logger.error("❌ 数据库更新失败，请检查AKShare API连接。")
    
    logger.info("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
