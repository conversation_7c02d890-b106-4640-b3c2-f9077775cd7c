#!/usr/bin/env python3
"""
测试任务详情功能
"""

import asyncio
import requests
import json
from datetime import datetime

API_BASE = 'http://localhost:8001/api/v1/data-management'

async def test_task_details():
    """测试任务详情功能"""
    print("🚀 开始测试任务详情功能\n")
    
    try:
        # 1. 获取当前任务列表
        print("1. 获取当前任务列表")
        response = requests.get(f"{API_BASE}/update-tasks")
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 获取成功，共{len(tasks)}个任务")
            
            if tasks:
                # 选择第一个任务查看详情
                task = tasks[0]
                task_id = task['id']
                print(f"   选择任务: {task['name']} (ID: {task_id})")
                
                # 2. 获取任务详情
                print(f"\n2. 获取任务详情 (ID: {task_id})")
                detail_response = requests.get(f"{API_BASE}/update-tasks/{task_id}/details")
                
                if detail_response.status_code == 200:
                    details = detail_response.json()
                    print("✅ 获取任务详情成功")
                    
                    # 显示任务基本信息
                    task_info = details['task']
                    print(f"   任务名称: {task_info['name']}")
                    print(f"   任务状态: {task_info['status']}")
                    print(f"   任务进度: {task_info['progress']}%")
                    print(f"   开始时间: {task_info.get('start_time', '未开始')}")
                    print(f"   结束时间: {task_info.get('end_time', '未结束')}")
                    
                    # 显示执行信息
                    exec_info = details['execution_info']
                    print(f"   执行时长: {exec_info['duration']}")
                    
                    # 显示数据源信息
                    data_source = exec_info['data_source']
                    if data_source:
                        print(f"   数据源: {data_source['source']}")
                        print(f"   API接口: {data_source['api']}")
                        print(f"   更新频率: {data_source['update_frequency']}")
                        print(f"   描述: {data_source['description']}")
                    
                    # 显示更新详情
                    update_details = details['update_details']
                    print(f"   数据摘要: {update_details['data_summary']}")
                    
                    # 显示执行日志
                    logs = details['logs']
                    print(f"   执行日志: {len(logs)}条")
                    for log in logs[-3:]:  # 显示最后3条日志
                        print(f"     [{log['timestamp']}] {log['level']}: {log['message']}")
                    
                elif detail_response.status_code == 404:
                    print("❌ 任务不存在")
                else:
                    print(f"❌ 获取任务详情失败: {detail_response.status_code}")
                    print(f"   错误信息: {detail_response.text}")
            else:
                print("   暂无任务，创建一个新任务进行测试")
                
                # 3. 启动新任务
                print("\n3. 启动股票基础信息更新任务")
                start_response = requests.post(f"{API_BASE}/update-tasks/start", 
                    json={"type": "stock_basic"})
                
                if start_response.status_code == 200:
                    result = start_response.json()
                    new_task_id = result['task_id']
                    print(f"✅ 任务启动成功: {result['message']}")
                    print(f"   新任务ID: {new_task_id}")
                    
                    # 等待一下让任务开始执行
                    await asyncio.sleep(2)
                    
                    # 4. 获取新任务详情
                    print(f"\n4. 获取新任务详情 (ID: {new_task_id})")
                    detail_response = requests.get(f"{API_BASE}/update-tasks/{new_task_id}/details")
                    
                    if detail_response.status_code == 200:
                        details = detail_response.json()
                        print("✅ 获取新任务详情成功")
                        
                        # 显示详细信息
                        task_info = details['task']
                        print(f"   任务名称: {task_info['name']}")
                        print(f"   任务状态: {task_info['status']}")
                        print(f"   任务进度: {task_info['progress']}%")
                        
                        # 显示数据源信息
                        data_source = details['execution_info']['data_source']
                        print(f"   数据源: {data_source['source']}")
                        print(f"   数据字段: {', '.join(data_source['data_fields'])}")
                        
                        # 显示更新详情
                        update_details = details['update_details']
                        print(f"   更新类型: {update_details['update_type']}")
                        print(f"   数据摘要: {update_details['data_summary']}")
                        
                        # 显示执行日志
                        logs = details['logs']
                        print(f"   执行日志: {len(logs)}条")
                        for log in logs:
                            print(f"     [{log['timestamp']}] {log['level']}: {log['message']}")
                    else:
                        print(f"❌ 获取新任务详情失败: {detail_response.status_code}")
                else:
                    print(f"❌ 启动任务失败: {start_response.status_code}")
        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    print(f"\n✅ 任务详情功能测试完成！")

if __name__ == "__main__":
    asyncio.run(test_task_details())
