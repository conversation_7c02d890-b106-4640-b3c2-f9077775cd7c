"""
数据管理API端点
提供数据获取、更新、统计等功能
"""

from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel, Field

from app.services.data_manager import DataManager
from app.tasks.data_tasks import sync_stock_list_task, sync_kline_data_task, sync_realtime_data_task
# from app.core.deps import get_current_user  # TODO: 实现用户认证
from app.models.user import User

router = APIRouter()

# 请求模型
class DataUpdateRequest(BaseModel):
    stock_codes: Optional[List[str]] = Field(None, description="股票代码列表，为空则更新所有股票")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    period: str = Field("daily", description="数据周期")

class StockListUpdateRequest(BaseModel):
    force_update: bool = Field(False, description="是否强制更新")

class DataCleanupRequest(BaseModel):
    days_to_keep: int = Field(1095, description="保留的天数")

# 响应模型
class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str

class DataStatsResponse(BaseModel):
    stocks: Dict[str, Any]
    klines: Dict[str, Any]
    updated_at: str

@router.get("/stats", response_model=DataStatsResponse, summary="获取数据统计")
async def get_data_statistics():
    """获取数据库中的数据统计信息"""
    try:
        data_manager = DataManager()
        stats = await data_manager.get_data_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据统计失败: {str(e)}")

@router.post("/stocks/initialize", response_model=Dict[str, Any], summary="初始化股票列表")
async def initialize_stock_list(
    request: StockListUpdateRequest,
    background_tasks: BackgroundTasks
    # current_user: User = Depends(get_current_user)  # TODO: 添加用户认证
):
    """初始化或更新股票列表"""
    try:
        data_manager = DataManager()
        
        # 在后台任务中执行，避免阻塞
        if request.force_update:
            task = task_manager.trigger_stock_list_update()
            return {
                "message": "股票列表更新任务已启动",
                "task_id": task.id,
                "status": "pending"
            }
        else:
            # 快速检查是否需要初始化
            stats = await data_manager.get_data_statistics()
            if stats['stocks']['total'] > 0:
                return {
                    "message": "股票列表已存在，无需初始化",
                    "stats": stats['stocks']
                }
            else:
                task = task_manager.trigger_stock_list_update()
                return {
                    "message": "股票列表初始化任务已启动",
                    "task_id": task.id,
                    "status": "pending"
                }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"初始化股票列表失败: {str(e)}")

@router.post("/klines/update", response_model=TaskResponse, summary="更新K线数据")
async def update_kline_data(
    request: DataUpdateRequest
    # current_user: User = Depends(get_current_user)  # TODO: 添加用户认证
):
    """更新K线数据"""
    try:
        # 设置默认日期范围
        end_date = request.end_date or datetime.now().date()
        start_date = request.start_date or (end_date - timedelta(days=30))
        
        if request.stock_codes:
            # 更新指定股票
            task = task_manager.trigger_specific_stocks_update(
                request.stock_codes, 
                (end_date - start_date).days
            )
        else:
            # 更新所有股票
            task = task_manager.trigger_full_update((end_date - start_date).days)
        
        return TaskResponse(
            task_id=task.id,
            status="pending",
            message=f"K线数据更新任务已启动，日期范围: {start_date} 到 {end_date}"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新K线数据失败: {str(e)}")

@router.post("/klines/update-today", response_model=TaskResponse, summary="更新今日数据")
async def update_today_data(
    stock_codes: Optional[List[str]] = None
    # current_user: User = Depends(get_current_user)  # TODO: 添加用户认证
):
    """更新今日数据"""
    try:
        task = task_manager.trigger_daily_update(stock_codes)
        return TaskResponse(
            task_id=task.id,
            status="pending",
            message="今日数据更新任务已启动"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新今日数据失败: {str(e)}")

@router.post("/cleanup", response_model=TaskResponse, summary="清理旧数据")
async def cleanup_old_data(
    request: DataCleanupRequest
    # current_user: User = Depends(get_current_user)  # TODO: 添加用户认证
):
    """清理旧数据"""
    try:
        task = task_manager.trigger_cleanup(request.days_to_keep)
        return TaskResponse(
            task_id=task.id,
            status="pending",
            message=f"数据清理任务已启动，将保留最近 {request.days_to_keep} 天的数据"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理数据失败: {str(e)}")

@router.get("/tasks/{task_id}/status", summary="获取任务状态")
async def get_task_status(
    task_id: str
    # current_user: User = Depends(get_current_user)  # TODO: 添加用户认证
):
    """获取任务执行状态"""
    try:
        task_result = task_manager.get_task_status(task_id)
        
        return {
            "task_id": task_id,
            "status": task_result.status,
            "result": task_result.result,
            "traceback": task_result.traceback,
            "date_done": task_result.date_done
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.delete("/tasks/{task_id}", summary="取消任务")
async def cancel_task(
    task_id: str
    # current_user: User = Depends(get_current_user)  # TODO: 添加用户认证
):
    """取消正在执行的任务"""
    try:
        task_manager.cancel_task(task_id)
        return {"message": f"任务 {task_id} 已取消"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.post("/sync/immediate", summary="立即同步数据")
async def immediate_data_sync(
    stock_codes: Optional[List[str]] = Query(None, description="股票代码列表"),
    days_back: int = Query(7, description="回溯天数")
    # current_user: User = Depends(get_current_user)  # TODO: 添加用户认证
):
    """立即同步数据（同步执行，适合少量数据）"""
    try:
        data_manager = DataManager()
        
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days_back)
        
        # 限制同步的股票数量，避免超时
        if stock_codes and len(stock_codes) > 10:
            raise HTTPException(
                status_code=400, 
                detail="立即同步最多支持10只股票，请使用异步更新接口"
            )
        
        result = await data_manager.update_stock_data(
            stock_codes=stock_codes,
            start_date=start_date,
            end_date=end_date,
            period='daily'
        )
        
        return {
            "message": "数据同步完成",
            "stats": result,
            "date_range": f"{start_date} 到 {end_date}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"立即同步数据失败: {str(e)}")

@router.get("/health", summary="数据服务健康检查")
async def data_service_health():
    """检查数据服务的健康状态"""
    try:
        data_manager = DataManager()
        stats = await data_manager.get_data_statistics()
        
        # 检查数据新鲜度
        latest_date = stats['klines'].get('latest_date')
        if latest_date:
            latest_datetime = datetime.fromisoformat(latest_date)
            days_old = (datetime.now().date() - latest_datetime.date()).days
        else:
            days_old = float('inf')
        
        # 判断健康状态
        if days_old <= 1:
            health_status = "healthy"
        elif days_old <= 7:
            health_status = "warning"
        else:
            health_status = "unhealthy"
        
        return {
            "status": health_status,
            "latest_data_date": latest_date,
            "days_since_update": days_old if days_old != float('inf') else None,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/coverage", summary="数据覆盖率报告")
async def get_data_coverage():
    """获取数据覆盖率详细报告"""
    try:
        data_manager = DataManager()
        stats = await data_manager.get_data_statistics()
        
        # 计算覆盖率指标
        coverage_report = {
            "stock_coverage": {
                "total_stocks": stats['stocks']['total'],
                "active_stocks": stats['stocks']['active'],
                "stocks_with_data": stats['stocks']['with_data'],
                "coverage_percentage": stats['stocks']['coverage_rate']
            },
            "data_volume": {
                "total_klines": stats['klines']['total'],
                "by_period": stats['klines']['by_period'],
                "latest_date": stats['klines']['latest_date']
            },
            "quality_indicators": {
                "avg_records_per_stock": round(
                    stats['klines']['total'] / max(stats['stocks']['with_data'], 1), 2
                ),
                "data_freshness": "good" if stats['klines']['latest_date'] else "no_data"
            },
            "generated_at": datetime.now().isoformat()
        }
        
        return coverage_report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取覆盖率报告失败: {str(e)}")
