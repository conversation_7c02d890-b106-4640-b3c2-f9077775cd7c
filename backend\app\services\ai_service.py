"""
AI服务模块 - 集成DeepSeek AI进行股票分析和预测
"""

import json
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
import httpx
from openai import AsyncOpenAI

from app.core.config import settings
from app.core.logging import get_logger
from app.services.data_storage import DataStorageService
from app.services.indicator_storage import IndicatorStorageService

logger = get_logger(__name__)


class DeepSeekAIService:
    """DeepSeek AI服务"""
    
    def __init__(self):
        # 配置DeepSeek API
        self.client = AsyncOpenAI(
            api_key=settings.DEEPSEEK_API_KEY or "sk-placeholder",
            base_url="https://api.deepseek.com"
        )
        self.model = "deepseek-chat"
        
    async def _make_request(self, messages: List[Dict[str, str]], max_tokens: int = 2000) -> Optional[str]:
        """发送请求到DeepSeek API"""
        try:
            if not settings.DEEPSEEK_API_KEY:
                logger.warning("DeepSeek API Key未配置，返回模拟结果")
                return self._get_mock_response(messages)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=0.7,
                stream=False
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"DeepSeek API请求失败: {e}")
            return self._get_mock_response(messages)
    
    def _get_mock_response(self, messages: List[Dict[str, str]]) -> str:
        """生成模拟响应（用于测试）"""
        user_message = messages[-1]["content"] if messages else ""
        
        if "价格预测" in user_message or "price prediction" in user_message.lower():
            return json.dumps({
                "prediction": "上涨",
                "confidence": 0.75,
                "target_price": 12.50,
                "time_horizon": "5天",
                "reasoning": "基于技术指标分析，RSI显示超卖状态，MACD出现金叉信号，预计短期内有反弹机会。"
            }, ensure_ascii=False)
        
        elif "趋势分析" in user_message or "trend analysis" in user_message.lower():
            return json.dumps({
                "trend": "上升趋势",
                "strength": "中等",
                "duration": "短期",
                "key_levels": {
                    "support": 11.20,
                    "resistance": 12.80
                },
                "analysis": "当前处于上升通道中，短期均线向上发散，但需要关注12.80阻力位。"
            }, ensure_ascii=False)
        
        elif "形态识别" in user_message or "pattern recognition" in user_message.lower():
            return json.dumps({
                "patterns": [
                    {
                        "name": "上升三角形",
                        "confidence": 0.82,
                        "type": "看涨",
                        "completion": 0.75
                    }
                ],
                "recommendation": "等待突破确认后考虑买入",
                "risk_level": "中等"
            }, ensure_ascii=False)
        
        else:
            return json.dumps({
                "analysis": "基于当前市场数据，建议保持谨慎乐观态度",
                "recommendation": "持有",
                "confidence": 0.65
            }, ensure_ascii=False)
    
    async def predict_price(self, stock_code: str, days: int = 5) -> Dict[str, Any]:
        """股票价格预测"""
        try:
            logger.info(f"开始对股票 {stock_code} 进行 {days} 天价格预测...")
            
            # 获取历史数据
            async with DataStorageService() as storage:
                klines = await storage.get_kline_data(stock_code, "daily", limit=30)
            
            if not klines:
                return {"error": "无法获取历史数据"}
            
            # 获取技术指标数据
            async with IndicatorStorageService() as indicator_storage:
                indicators = await indicator_storage.get_indicators(stock_code, "daily", limit=10)
            
            # 构建分析数据
            recent_prices = [float(kline.close_price) for kline in klines[-10:]]
            current_price = recent_prices[-1]
            
            # 构建提示词
            prompt = f"""
作为专业的股票分析师，请基于以下数据对股票{stock_code}进行{days}天的价格预测：

历史价格数据（最近10天收盘价）：{recent_prices}
当前价格：{current_price}

技术指标信息：
"""
            
            if indicators:
                latest_indicator = indicators[-1]
                prompt += f"""
- RSI: {latest_indicator.rsi if latest_indicator.rsi else 'N/A'}
- MACD: {latest_indicator.macd if latest_indicator.macd else 'N/A'}
- MA20: {latest_indicator.ma20 if latest_indicator.ma20 else 'N/A'}
- 布林带上轨: {latest_indicator.boll_upper if latest_indicator.boll_upper else 'N/A'}
- 布林带下轨: {latest_indicator.boll_lower if latest_indicator.boll_lower else 'N/A'}
"""
            
            prompt += f"""
请提供JSON格式的预测结果，包含：
1. prediction: 预测方向（上涨/下跌/震荡）
2. confidence: 置信度（0-1）
3. target_price: 目标价格
4. price_range: 价格区间 {{"min": 最低价, "max": 最高价}}
5. time_horizon: 时间范围
6. reasoning: 分析理由
7. risk_factors: 风险因素
8. key_levels: 关键价位 {{"support": 支撑位, "resistance": 阻力位}}
"""
            
            messages = [
                {"role": "system", "content": "你是一位专业的股票分析师，擅长技术分析和价格预测。请基于提供的数据进行客观、专业的分析。"},
                {"role": "user", "content": prompt}
            ]
            
            response = await self._make_request(messages, max_tokens=1500)
            
            if response:
                try:
                    prediction_data = json.loads(response)
                    prediction_data.update({
                        "stock_code": stock_code,
                        "prediction_days": days,
                        "current_price": current_price,
                        "predicted_at": datetime.now().isoformat(),
                        "data_source": "deepseek_ai"
                    })
                    return prediction_data
                except json.JSONDecodeError:
                    logger.error("AI响应格式错误，无法解析JSON")
                    return {"error": "AI响应格式错误"}
            
            return {"error": "AI服务暂时不可用"}
            
        except Exception as e:
            logger.error(f"价格预测失败: {e}")
            return {"error": str(e)}
    
    async def analyze_trend(self, stock_code: str) -> Dict[str, Any]:
        """趋势分析"""
        try:
            logger.info(f"开始对股票 {stock_code} 进行趋势分析...")
            
            # 获取历史数据
            async with DataStorageService() as storage:
                klines = await storage.get_kline_data(stock_code, "daily", limit=60)
            
            if not klines:
                return {"error": "无法获取历史数据"}
            
            # 获取技术指标
            async with IndicatorStorageService() as indicator_storage:
                indicators = await indicator_storage.get_indicators(stock_code, "daily", limit=20)
            
            # 构建分析数据
            prices = [float(kline.close_price) for kline in klines]
            volumes = [kline.volume for kline in klines]
            
            prompt = f"""
作为专业的技术分析师，请对股票{stock_code}进行趋势分析：

价格数据（最近60天）：{prices[-20:]}  # 显示最近20天
成交量数据：{volumes[-20:]}

技术指标趋势：
"""
            
            if indicators and len(indicators) >= 5:
                ma20_trend = [float(ind.ma20) for ind in indicators[-5:] if ind.ma20]
                rsi_trend = [float(ind.rsi) for ind in indicators[-5:] if ind.rsi]
                
                prompt += f"""
- MA20趋势：{ma20_trend}
- RSI趋势：{rsi_trend}
"""
            
            prompt += """
请提供JSON格式的趋势分析，包含：
1. trend: 主要趋势（上升趋势/下降趋势/横盘整理）
2. strength: 趋势强度（强/中等/弱）
3. duration: 预期持续时间（短期/中期/长期）
4. key_levels: 关键价位
5. volume_analysis: 成交量分析
6. momentum: 动量分析
7. analysis: 详细分析
8. outlook: 后市展望
"""
            
            messages = [
                {"role": "system", "content": "你是一位专业的技术分析师，擅长趋势识别和市场分析。"},
                {"role": "user", "content": prompt}
            ]
            
            response = await self._make_request(messages, max_tokens=1500)
            
            if response:
                try:
                    trend_data = json.loads(response)
                    trend_data.update({
                        "stock_code": stock_code,
                        "analyzed_at": datetime.now().isoformat(),
                        "data_source": "deepseek_ai"
                    })
                    return trend_data
                except json.JSONDecodeError:
                    return {"error": "AI响应格式错误"}
            
            return {"error": "AI服务暂时不可用"}
            
        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            return {"error": str(e)}
    
    async def recognize_patterns(self, stock_code: str) -> Dict[str, Any]:
        """形态识别"""
        try:
            logger.info(f"开始对股票 {stock_code} 进行形态识别...")
            
            # 获取历史数据
            async with DataStorageService() as storage:
                klines = await storage.get_kline_data(stock_code, "daily", limit=50)
            
            if not klines:
                return {"error": "无法获取历史数据"}
            
            # 构建OHLC数据
            ohlc_data = []
            for kline in klines[-30:]:  # 最近30天数据
                ohlc_data.append({
                    "date": kline.trade_date.isoformat(),
                    "open": float(kline.open_price),
                    "high": float(kline.high_price),
                    "low": float(kline.low_price),
                    "close": float(kline.close_price),
                    "volume": kline.volume
                })
            
            prompt = f"""
作为专业的技术分析师，请对股票{stock_code}进行形态识别：

OHLC数据（最近30天）：
{json.dumps(ohlc_data[-10:], ensure_ascii=False, indent=2)}

请识别以下技术形态：
1. 经典形态：头肩顶/底、双顶/底、三角形、楔形、旗形等
2. K线形态：锤子线、十字星、吞没形态、启明星等
3. 支撑阻力：关键支撑位和阻力位

请提供JSON格式的识别结果：
1. patterns: 识别到的形态列表
2. confidence: 每个形态的置信度
3. implications: 形态含义
4. recommendations: 操作建议
5. risk_assessment: 风险评估
"""
            
            messages = [
                {"role": "system", "content": "你是一位专业的技术分析师，擅长图表形态识别和K线分析。"},
                {"role": "user", "content": prompt}
            ]
            
            response = await self._make_request(messages, max_tokens=1500)
            
            if response:
                try:
                    pattern_data = json.loads(response)
                    pattern_data.update({
                        "stock_code": stock_code,
                        "analyzed_at": datetime.now().isoformat(),
                        "data_source": "deepseek_ai"
                    })
                    return pattern_data
                except json.JSONDecodeError:
                    return {"error": "AI响应格式错误"}
            
            return {"error": "AI服务暂时不可用"}
            
        except Exception as e:
            logger.error(f"形态识别失败: {e}")
            return {"error": str(e)}


class AIAnalysisService:
    """AI分析服务管理器"""
    
    def __init__(self):
        self.deepseek = DeepSeekAIService()
    
    async def comprehensive_analysis(self, stock_code: str) -> Dict[str, Any]:
        """综合AI分析"""
        try:
            logger.info(f"开始对股票 {stock_code} 进行综合AI分析...")
            
            # 并行执行多种分析
            tasks = [
                self.deepseek.predict_price(stock_code, 5),
                self.deepseek.analyze_trend(stock_code),
                self.deepseek.recognize_patterns(stock_code)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整合分析结果
            analysis_result = {
                "stock_code": stock_code,
                "analyzed_at": datetime.now().isoformat(),
                "price_prediction": results[0] if not isinstance(results[0], Exception) else {"error": str(results[0])},
                "trend_analysis": results[1] if not isinstance(results[1], Exception) else {"error": str(results[1])},
                "pattern_recognition": results[2] if not isinstance(results[2], Exception) else {"error": str(results[2])}
            }
            
            # 生成综合评分
            analysis_result["overall_score"] = self._calculate_overall_score(analysis_result)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"综合AI分析失败: {e}")
            return {"error": str(e)}
    
    def _calculate_overall_score(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合评分"""
        try:
            scores = []
            
            # 价格预测评分
            if "confidence" in analysis.get("price_prediction", {}):
                scores.append(analysis["price_prediction"]["confidence"])
            
            # 趋势强度评分
            trend_strength = analysis.get("trend_analysis", {}).get("strength", "")
            if trend_strength == "强":
                scores.append(0.9)
            elif trend_strength == "中等":
                scores.append(0.7)
            elif trend_strength == "弱":
                scores.append(0.5)
            
            # 形态置信度评分
            patterns = analysis.get("pattern_recognition", {}).get("patterns", [])
            if patterns:
                pattern_scores = [p.get("confidence", 0.5) for p in patterns if isinstance(p, dict)]
                if pattern_scores:
                    scores.append(max(pattern_scores))
            
            overall_score = sum(scores) / len(scores) if scores else 0.5
            
            return {
                "score": round(overall_score, 3),
                "level": "高" if overall_score >= 0.8 else "中" if overall_score >= 0.6 else "低",
                "components": {
                    "prediction_confidence": scores[0] if len(scores) > 0 else None,
                    "trend_strength": scores[1] if len(scores) > 1 else None,
                    "pattern_confidence": scores[2] if len(scores) > 2 else None
                }
            }
            
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return {"score": 0.5, "level": "中", "error": str(e)}
