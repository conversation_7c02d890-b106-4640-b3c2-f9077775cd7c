import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import { Layout } from 'antd'

import ProtectedRoute from './components/ProtectedRoute'
import AppHeader from '@/components/layout/AppHeader'
import AppSider from '@/components/layout/AppSider'
import Login from '@/pages/Login'
import DashboardNew from '@/pages/DashboardNew'
import IntegratedStockAnalysis from '@/pages/IntegratedStockAnalysis'
import StockAnalysisOptimized from '@/pages/StockAnalysisOptimized'
import StockAnalysisAdvanced from '@/pages/StockAnalysisAdvanced'
import FundamentalAnalysis from '@/pages/FundamentalAnalysis'
import AIIntelligentAnalysis from '@/pages/AIIntelligentAnalysis'
import DataSettings from '@/pages/DataSettings'
import DataManagement from '@/pages/DataManagement'
import StockDataManagement from '@/pages/StockDataManagement'
import ProfessionalDatabaseManager from '@/pages/ProfessionalDatabaseManager'
import DataIntegrity from '@/pages/DataIntegrity'
import AKShareData from '@/pages/AKShareData'
import SmartScreenerEnhanced from '@/pages/SmartScreenerEnhanced'
import AlertSystem from '@/pages/AlertSystem'
import QuantitativeBacktest from '@/pages/QuantitativeBacktest'
import MarketOverview from '@/pages/MarketOverview'
import SectorAnalysis from '@/pages/SectorAnalysis'
import SystemStatus from '@/pages/SystemStatus'
import AIPredict from '@/pages/AIPredict'
import Screener from '@/pages/Screener'
import Alerts from '@/pages/Alerts'
import Analytics from '@/pages/Analytics'
import Settings from '@/pages/Settings'
import Help from '@/pages/Help'
import Portfolio from '@/pages/Portfolio'
import PortfolioCreate from '@/pages/PortfolioCreate'
import PortfolioBacktest from '@/pages/PortfolioBacktest'
import Watchlist from '@/pages/Watchlist'
import Profile from '@/pages/Profile'
import Subscription from '@/pages/Subscription'
import ApiUsage from '@/pages/ApiUsage'
import ColorTest from '@/pages/ColorTest'
import KLineTest from '@/pages/KLineTest'


import './App.css'

const { Content } = Layout

// 主应用布局组件
const AppLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Layout style={{ minHeight: '100vh', width: '100%' }}>
    <AppHeader />
    <Layout style={{ flex: 1 }}>
      <AppSider />
      <Layout style={{ padding: '0', background: '#f0f2f5' }}>
        <Content
          style={{
            padding: 24,
            margin: 16,
            minHeight: 'calc(100vh - 64px - 32px)',
            background: '#fff',
            borderRadius: 8,
            overflow: 'auto',
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  </Layout>
)

function App() {
  return (
    <Router>
      <Routes>
        {/* 公开路由 */}
        <Route path="/login" element={<Login />} />

        {/* 受保护的路由 */}
        <Route path="/" element={
          <ProtectedRoute>
            <AppLayout>
              <DashboardNew />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/analysis" element={
          <ProtectedRoute>
            <AppLayout>
              <IntegratedStockAnalysis />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/analysis-optimized" element={
          <ProtectedRoute>
            <AppLayout>
              <StockAnalysisOptimized />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/analysis-advanced" element={
          <ProtectedRoute>
            <AppLayout>
              <StockAnalysisAdvanced />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/fundamental-analysis" element={
          <ProtectedRoute>
            <AppLayout>
              <FundamentalAnalysis />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/ai-intelligent-analysis" element={
          <ProtectedRoute>
            <AppLayout>
              <AIIntelligentAnalysis />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/integrated-stock-analysis" element={
          <ProtectedRoute>
            <AppLayout>
              <IntegratedStockAnalysis />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/data-settings" element={
          <ProtectedRoute>
            <AppLayout>
              <DataSettings />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/data-management" element={
          <ProtectedRoute>
            <AppLayout>
              <DataManagement />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/stock-data-management" element={
          <ProtectedRoute>
            <AppLayout>
              <StockDataManagement />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/professional-database-manager" element={
          <ProtectedRoute>
            <AppLayout>
              <ProfessionalDatabaseManager />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/data-integrity" element={
          <ProtectedRoute>
            <AppLayout>
              <DataIntegrity />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/akshare-data" element={
          <ProtectedRoute>
            <AppLayout>
              <AKShareData />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/screener" element={
          <ProtectedRoute>
            <AppLayout>
              <SmartScreenerEnhanced />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/alert-system" element={
          <ProtectedRoute>
            <AppLayout>
              <AlertSystem />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/market-overview" element={
          <ProtectedRoute>
            <AppLayout>
              <MarketOverview />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/system-status" element={
          <ProtectedRoute>
            <AppLayout>
              <SystemStatus />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/ai-predict" element={
          <ProtectedRoute>
            <AppLayout>
              <AIPredict />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/screener" element={
          <ProtectedRoute>
            <AppLayout>
              <Screener />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/alerts" element={
          <ProtectedRoute>
            <AppLayout>
              <Alerts />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/analytics" element={
          <ProtectedRoute>
            <AppLayout>
              <Analytics />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/settings" element={
          <ProtectedRoute>
            <AppLayout>
              <Settings />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/help" element={
          <ProtectedRoute>
            <AppLayout>
              <Help />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/portfolio" element={
          <ProtectedRoute>
            <AppLayout>
              <Portfolio />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/portfolio/create" element={
          <ProtectedRoute>
            <AppLayout>
              <PortfolioCreate />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/portfolio/backtest" element={
          <ProtectedRoute>
            <AppLayout>
              <QuantitativeBacktest />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/sector-analysis" element={
          <ProtectedRoute>
            <AppLayout>
              <SectorAnalysis />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/watchlist" element={
          <ProtectedRoute>
            <AppLayout>
              <Watchlist />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/profile" element={
          <ProtectedRoute>
            <AppLayout>
              <Profile />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/subscription" element={
          <ProtectedRoute>
            <AppLayout>
              <Subscription />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/api-usage" element={
          <ProtectedRoute>
            <AppLayout>
              <ApiUsage />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/color-test" element={
          <ProtectedRoute>
            <AppLayout>
              <ColorTest />
            </AppLayout>
          </ProtectedRoute>
        } />

        <Route path="/kline-test" element={
          <ProtectedRoute>
            <AppLayout>
              <KLineTest />
            </AppLayout>
          </ProtectedRoute>
        } />


      </Routes>
    </Router>
  )
}

export default App
