#!/usr/bin/env python3
"""
数据同步状态监控脚本
监控数据的完整性、新鲜度和质量
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import json
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.data_manager import DataManager
from app.core.database import AsyncSessionLocal
from app.models.stock import Stock, KlineData
from sqlalchemy import select, func, and_

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataSyncMonitor:
    """数据同步监控器"""
    
    def __init__(self):
        self.data_manager = DataManager()
    
    async def check_data_freshness(self):
        """检查数据新鲜度"""
        logger.info("检查数据新鲜度...")
        
        async with AsyncSessionLocal() as db:
            # 获取最新数据日期
            result = await db.execute(
                select(func.max(KlineData.trade_date))
            )
            latest_date = result.scalar()
            
            if not latest_date:
                return {
                    'status': 'no_data',
                    'message': '没有K线数据',
                    'latest_date': None,
                    'days_old': float('inf')
                }
            
            # 计算数据延迟
            days_old = (datetime.now().date() - latest_date).days
            
            # 判断新鲜度状态
            if days_old == 0:
                status = 'fresh'
                message = '数据是最新的'
            elif days_old <= 1:
                status = 'good'
                message = '数据稍有延迟但可接受'
            elif days_old <= 7:
                status = 'stale'
                message = '数据有些过期'
            else:
                status = 'very_stale'
                message = '数据严重过期'
            
            return {
                'status': status,
                'message': message,
                'latest_date': latest_date.isoformat(),
                'days_old': days_old
            }
    
    async def check_data_completeness(self):
        """检查数据完整性"""
        logger.info("检查数据完整性...")
        
        async with AsyncSessionLocal() as db:
            # 获取股票总数
            result = await db.execute(
                select(func.count(Stock.id)).filter(Stock.is_active == True)
            )
            total_stocks = result.scalar()
            
            # 获取有数据的股票数
            result = await db.execute(
                select(func.count(func.distinct(KlineData.stock_code)))
            )
            stocks_with_data = result.scalar()
            
            # 计算覆盖率
            coverage_rate = (stocks_with_data / max(total_stocks, 1)) * 100
            
            # 检查数据缺失的股票
            result = await db.execute(
                select(Stock.stock_code, Stock.stock_name)
                .filter(Stock.is_active == True)
                .filter(~Stock.stock_code.in_(
                    select(func.distinct(KlineData.stock_code))
                ))
            )
            missing_stocks = result.all()
            
            # 判断完整性状态
            if coverage_rate >= 95:
                status = 'excellent'
                message = '数据覆盖率优秀'
            elif coverage_rate >= 80:
                status = 'good'
                message = '数据覆盖率良好'
            elif coverage_rate >= 60:
                status = 'fair'
                message = '数据覆盖率一般'
            else:
                status = 'poor'
                message = '数据覆盖率较差'
            
            return {
                'status': status,
                'message': message,
                'total_stocks': total_stocks,
                'stocks_with_data': stocks_with_data,
                'coverage_rate': round(coverage_rate, 2),
                'missing_stocks': [{'code': code, 'name': name} for code, name in missing_stocks[:10]]  # 只显示前10个
            }
    
    async def check_data_consistency(self):
        """检查数据一致性"""
        logger.info("检查数据一致性...")
        
        async with AsyncSessionLocal() as db:
            issues = []
            
            # 检查价格异常（开盘价、收盘价、最高价、最低价的逻辑关系）
            result = await db.execute(
                select(func.count(KlineData.id))
                .filter(
                    (KlineData.high_price < KlineData.low_price) |
                    (KlineData.high_price < KlineData.open_price) |
                    (KlineData.high_price < KlineData.close_price) |
                    (KlineData.low_price > KlineData.open_price) |
                    (KlineData.low_price > KlineData.close_price)
                )
            )
            price_anomalies = result.scalar()
            
            if price_anomalies > 0:
                issues.append({
                    'type': 'price_anomaly',
                    'count': price_anomalies,
                    'description': '价格数据逻辑异常'
                })
            
            # 检查成交量异常（负数或零）
            result = await db.execute(
                select(func.count(KlineData.id))
                .filter(KlineData.volume <= 0)
            )
            volume_anomalies = result.scalar()
            
            if volume_anomalies > 0:
                issues.append({
                    'type': 'volume_anomaly',
                    'count': volume_anomalies,
                    'description': '成交量数据异常'
                })
            
            # 检查重复数据
            result = await db.execute(
                select(func.count())
                .select_from(
                    select(KlineData.stock_code, KlineData.trade_date, KlineData.period)
                    .group_by(KlineData.stock_code, KlineData.trade_date, KlineData.period)
                    .having(func.count() > 1)
                    .subquery()
                )
            )
            duplicate_records = result.scalar()
            
            if duplicate_records > 0:
                issues.append({
                    'type': 'duplicate_records',
                    'count': duplicate_records,
                    'description': '重复的K线记录'
                })
            
            # 判断一致性状态
            if not issues:
                status = 'excellent'
                message = '数据一致性优秀'
            elif len(issues) <= 2 and sum(issue['count'] for issue in issues) < 10:
                status = 'good'
                message = '数据一致性良好，有少量异常'
            else:
                status = 'poor'
                message = '数据一致性较差，存在多个问题'
            
            return {
                'status': status,
                'message': message,
                'issues': issues,
                'total_issues': len(issues)
            }
    
    async def check_data_distribution(self):
        """检查数据分布"""
        logger.info("检查数据分布...")
        
        async with AsyncSessionLocal() as db:
            # 按股票统计记录数
            result = await db.execute(
                select(
                    KlineData.stock_code,
                    func.count(KlineData.id).label('record_count'),
                    func.min(KlineData.trade_date).label('earliest_date'),
                    func.max(KlineData.trade_date).label('latest_date')
                )
                .group_by(KlineData.stock_code)
                .order_by(func.count(KlineData.id).desc())
            )
            stock_stats = result.all()
            
            if not stock_stats:
                return {
                    'status': 'no_data',
                    'message': '没有数据',
                    'stats': {}
                }
            
            # 计算统计信息
            record_counts = [stat.record_count for stat in stock_stats]
            avg_records = sum(record_counts) / len(record_counts)
            min_records = min(record_counts)
            max_records = max(record_counts)
            
            # 找出数据量异常的股票
            threshold = avg_records * 0.5  # 低于平均值50%认为异常
            low_data_stocks = [
                {'code': stat.stock_code, 'count': stat.record_count}
                for stat in stock_stats
                if stat.record_count < threshold
            ]
            
            # 判断分布状态
            if min_records >= avg_records * 0.8:
                status = 'excellent'
                message = '数据分布均匀'
            elif min_records >= avg_records * 0.5:
                status = 'good'
                message = '数据分布良好'
            else:
                status = 'uneven'
                message = '数据分布不均匀'
            
            return {
                'status': status,
                'message': message,
                'stats': {
                    'total_stocks': len(stock_stats),
                    'avg_records_per_stock': round(avg_records, 1),
                    'min_records': min_records,
                    'max_records': max_records,
                    'low_data_stocks_count': len(low_data_stocks),
                    'low_data_stocks': low_data_stocks[:5]  # 只显示前5个
                }
            }
    
    async def generate_health_score(self, checks):
        """生成健康评分"""
        score = 0
        max_score = 100
        
        # 新鲜度评分 (30分)
        freshness = checks['freshness']
        if freshness['status'] == 'fresh':
            score += 30
        elif freshness['status'] == 'good':
            score += 25
        elif freshness['status'] == 'stale':
            score += 15
        elif freshness['status'] == 'very_stale':
            score += 5
        
        # 完整性评分 (30分)
        completeness = checks['completeness']
        if completeness['status'] == 'excellent':
            score += 30
        elif completeness['status'] == 'good':
            score += 25
        elif completeness['status'] == 'fair':
            score += 15
        elif completeness['status'] == 'poor':
            score += 5
        
        # 一致性评分 (25分)
        consistency = checks['consistency']
        if consistency['status'] == 'excellent':
            score += 25
        elif consistency['status'] == 'good':
            score += 20
        elif consistency['status'] == 'poor':
            score += 5
        
        # 分布评分 (15分)
        distribution = checks['distribution']
        if distribution['status'] == 'excellent':
            score += 15
        elif distribution['status'] == 'good':
            score += 12
        elif distribution['status'] == 'uneven':
            score += 8
        
        # 确定健康等级
        if score >= 90:
            health_level = 'A+'
            health_desc = '数据质量优秀'
        elif score >= 80:
            health_level = 'A'
            health_desc = '数据质量良好'
        elif score >= 70:
            health_level = 'B'
            health_desc = '数据质量一般'
        elif score >= 60:
            health_level = 'C'
            health_desc = '数据质量较差'
        else:
            health_level = 'D'
            health_desc = '数据质量很差'
        
        return {
            'score': score,
            'max_score': max_score,
            'percentage': round(score / max_score * 100, 1),
            'level': health_level,
            'description': health_desc
        }
    
    async def run_full_check(self):
        """运行完整检查"""
        logger.info("开始数据同步状态监控...")
        
        start_time = datetime.now()
        
        # 执行各项检查
        checks = {
            'freshness': await self.check_data_freshness(),
            'completeness': await self.check_data_completeness(),
            'consistency': await self.check_data_consistency(),
            'distribution': await self.check_data_distribution()
        }
        
        # 生成健康评分
        health_score = await self.generate_health_score(checks)
        
        # 生成建议
        recommendations = []
        
        if checks['freshness']['status'] in ['stale', 'very_stale']:
            recommendations.append("建议立即更新数据")
        
        if checks['completeness']['coverage_rate'] < 80:
            recommendations.append("建议补充缺失股票的数据")
        
        if checks['consistency']['total_issues'] > 0:
            recommendations.append("建议检查和修复数据一致性问题")
        
        if checks['distribution']['status'] == 'uneven':
            recommendations.append("建议平衡各股票的数据量")
        
        if not recommendations:
            recommendations.append("数据状态良好，继续保持")
        
        end_time = datetime.now()
        
        # 生成报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'duration': str(end_time - start_time),
            'health_score': health_score,
            'checks': checks,
            'recommendations': recommendations,
            'summary': {
                'overall_status': health_score['level'],
                'key_issues': [
                    check_name for check_name, check_result in checks.items()
                    if check_result['status'] in ['poor', 'very_stale', 'no_data']
                ]
            }
        }
        
        return report

async def main():
    """主函数"""
    try:
        monitor = DataSyncMonitor()
        report = await monitor.run_full_check()
        
        # 保存报告
        os.makedirs('logs', exist_ok=True)
        report_file = f"logs/sync_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print("\n" + "="*60)
        print("📊 数据同步状态监控报告")
        print("="*60)
        print(f"⏰ 检查时间: {report['timestamp']}")
        print(f"🏥 健康评分: {report['health_score']['score']}/{report['health_score']['max_score']} ({report['health_score']['percentage']}%)")
        print(f"📈 健康等级: {report['health_score']['level']} - {report['health_score']['description']}")
        
        print(f"\n📋 检查结果:")
        for check_name, check_result in report['checks'].items():
            status_emoji = {
                'fresh': '🟢', 'good': '🟢', 'excellent': '🟢',
                'stale': '🟡', 'fair': '🟡', 'uneven': '🟡',
                'very_stale': '🔴', 'poor': '🔴', 'no_data': '🔴'
            }.get(check_result['status'], '⚪')
            
            print(f"  {status_emoji} {check_name.title()}: {check_result['message']}")
        
        if report['summary']['key_issues']:
            print(f"\n⚠️ 关键问题: {', '.join(report['summary']['key_issues'])}")
        
        print(f"\n💡 建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"  {i}. {rec}")
        
        print(f"\n📄 详细报告已保存: {report_file}")
        print("="*60)
        
        return report
        
    except Exception as e:
        logger.error(f"监控检查失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
