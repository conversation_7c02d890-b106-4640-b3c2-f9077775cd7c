import React, { useState, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  Switch,
  InputNumber,
  Select,
  Button,
  Space,
  Alert,
  Divider,
  Typography,
  Card,
  Row,
  Col,
  message,
  Spin
} from 'antd'
import {
  ApiOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { dataManagementService, APIConfig } from '@/services/dataManagementService'

// 数据接口配置
interface DataAPIConfig {
  id: string
  name: string
  type: 'akshare' | 'tushare' | 'sina'
  enabled: boolean
  api_key?: string
  endpoint?: string
  rate_limit: number
  timeout: number
  priority: number
  status: 'connected' | 'disconnected' | 'error'
  lastUpdate?: string
  dataCount?: number
}

const { Text, Title } = Typography
const { Option } = Select

interface APIConfigModalProps {
  visible: boolean
  onCancel: () => void
  onOk: (config: APIConfig) => void
  config?: DataAPIConfig
  loading?: boolean
}

const APIConfigModal: React.FC<APIConfigModalProps> = ({
  visible,
  onCancel,
  onOk,
  config,
  loading = false
}) => {
  const [form] = Form.useForm()
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<{
    success: boolean
    message: string
  } | null>(null)

  useEffect(() => {
    if (visible && config) {
      form.setFieldsValue({
        name: config.name,
        type: config.type,
        enabled: config.enabled,
        apiKey: config.api_key,
        endpoint: config.endpoint,
        rateLimit: config.rate_limit,
        timeout: config.timeout,
        priority: config.priority
      })
    } else if (visible) {
      // 添加新配置时设置默认值
      form.setFieldsValue({
        type: 'akshare',
        name: '',
        enabled: true,
        apiKey: '',
        endpoint: '',
        rateLimit: 100,
        timeout: 30000,
        priority: 1
      })
    }
  }, [visible, config, form])

  const handleTestConnection = async () => {
    try {
      setTesting(true)
      setTestResult(null)
      
      const values = await form.validateFields()
      
      // 测试API连接
      const success = await dataManagementService.testAPIConnection(config?.id || values.type)
      
      setTestResult({
        success,
        message: success ? 'API连接测试成功！' : 'API连接测试失败，请检查配置。'
      })
    } catch (error) {
      setTestResult({
        success: false,
        message: '连接测试失败：' + (error as Error).message
      })
    } finally {
      setTesting(false)
    }
  }

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      const configData: APIConfig = {
        id: config?.id || values.type || 'akshare', // 确保ID不为空
        name: values.name || `${values.type || 'AKShare'} API`,
        type: values.type || 'akshare',
        enabled: values.enabled !== undefined ? values.enabled : true,
        api_key: values.apiKey || null,
        endpoint: values.endpoint || null,
        rate_limit: values.rateLimit || 100,
        timeout: values.timeout || 30000,
        priority: values.priority || 1
      }
      onOk(configData)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const getAPIDescription = (type: string) => {
    switch (type) {
      case 'akshare':
        return {
          title: 'AKShare',
          description: 'AKShare是基于Python的财经数据接口库，提供股票、期货、期权、基金、外汇、债券、指数、加密货币等金融产品的基本面数据、实时和历史行情数据、衍生数据。',
          features: ['免费使用', '数据丰富', '更新及时', '无需API Key'],
          docs: 'https://akshare.akfamily.xyz/'
        }
      case 'tushare':
        return {
          title: 'Tushare',
          description: 'Tushare是一个免费、开源的python财经数据接口包。主要实现对股票等金融数据从数据采集、清洗加工到数据存储的过程。',
          features: ['专业数据源', '高质量数据', '需要Token', '有调用限制'],
          docs: 'https://tushare.pro/'
        }
      case 'sina':
        return {
          title: '新浪财经',
          description: '新浪财经提供实时股票行情数据，包括股票价格、成交量、涨跌幅等基础行情信息。',
          features: ['实时数据', '免费使用', '响应快速', '数据稳定'],
          docs: 'https://finance.sina.com.cn/'
        }
      default:
        return {
          title: '未知接口',
          description: '请选择一个API类型',
          features: [],
          docs: ''
        }
    }
  }

  const apiType = Form.useWatch('type', form)
  const apiInfo = getAPIDescription(apiType)

  return (
    <Modal
      title={
        <Space>
          <ApiOutlined />
          {config ? `编辑 ${config.name} 配置` : '添加API配置'}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      confirmLoading={loading}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="test"
          icon={<SyncOutlined />}
          loading={testing}
          onClick={handleTestConnection}
          disabled={!apiType}
        >
          测试连接
        </Button>,
        <Button
          key="ok"
          type="primary"
          loading={loading}
          onClick={handleOk}
        >
          {config ? '更新配置' : '添加配置'}
        </Button>
      ]}
    >
      <Row gutter={24}>
        <Col span={14}>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              type: 'akshare', // 默认选择AKShare
              enabled: true,
              rateLimit: 100,
              timeout: 30000,
              priority: 1,
              endpoint: apiType === 'tushare' ? 'http://api.tushare.pro' :
                       apiType === 'sina' ? 'http://hq.sinajs.cn' : ''
            }}
          >
            <Form.Item
              name="type"
              label="API类型"
              rules={[{ required: true, message: '请选择API类型' }]}
            >
              <Select placeholder="选择API类型" disabled={!!config}>
                <Option value="akshare">AKShare</Option>
                <Option value="tushare">Tushare</Option>
                <Option value="sina">新浪财经</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="name"
              label="接口名称"
              rules={[{ required: true, message: '请输入接口名称' }]}
            >
              <Input placeholder="输入接口名称" />
            </Form.Item>

            <Form.Item
              name="enabled"
              label="启用状态"
              valuePropName="checked"
            >
              <Switch checkedChildren="启用" unCheckedChildren="禁用" />
            </Form.Item>

            {apiType === 'tushare' && (
              <Form.Item
                name="apiKey"
                label="API Token"
                rules={[{ required: true, message: '请输入Tushare API Token' }]}
              >
                <Input.Password placeholder="输入Tushare API Token" />
              </Form.Item>
            )}

            {apiType === 'tushare' && (
              <Form.Item
                name="endpoint"
                label="API端点"
                rules={[{ required: true, message: '请输入API端点地址' }]}
              >
                <Input placeholder="http://api.tushare.pro" />
              </Form.Item>
            )}

            {apiType === 'sina' && (
              <Form.Item
                name="endpoint"
                label="API端点"
                rules={[{ required: true, message: '请输入API端点地址' }]}
              >
                <Input placeholder="http://hq.sinajs.cn" />
              </Form.Item>
            )}

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="rateLimit"
                  label="速率限制 (次/分钟)"
                  rules={[{ required: true, message: '请输入速率限制' }]}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                    style={{ width: '100%' }}
                    placeholder="100"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="timeout"
                  label="超时时间 (毫秒)"
                  rules={[{ required: true, message: '请输入超时时间' }]}
                >
                  <InputNumber
                    min={1000}
                    max={60000}
                    style={{ width: '100%' }}
                    placeholder="30000"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="priority"
              label="优先级"
              rules={[{ required: true, message: '请输入优先级' }]}
            >
              <InputNumber
                min={1}
                max={10}
                style={{ width: '100%' }}
                placeholder="1"
              />
            </Form.Item>
          </Form>

          {testResult && (
            <Alert
              type={testResult.success ? 'success' : 'error'}
              message={testResult.message}
              icon={testResult.success ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
              style={{ marginTop: 16 }}
              showIcon
            />
          )}
        </Col>

        <Col span={10}>
          <Card size="small" title={
            <Space>
              <InfoCircleOutlined />
              API信息
            </Space>
          }>
            <Title level={5}>{apiInfo.title}</Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {apiInfo.description}
            </Text>
            
            <Divider />
            
            <div>
              <Text strong>特性：</Text>
              <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                {apiInfo.features.map((feature, index) => (
                  <li key={index} style={{ fontSize: '12px', margin: '4px 0' }}>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {apiInfo.docs && (
              <>
                <Divider />
                <Button
                  type="link"
                  size="small"
                  href={apiInfo.docs}
                  target="_blank"
                  style={{ padding: 0 }}
                >
                  查看官方文档 →
                </Button>
              </>
            )}
          </Card>
        </Col>
      </Row>
    </Modal>
  )
}

export default APIConfigModal
