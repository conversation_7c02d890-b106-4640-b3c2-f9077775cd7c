"""
AI预测结果存储服务模块
"""

import json
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.dialects.sqlite import insert

from app.core.logging import get_logger
from app.models.ai_prediction import AIPrediction, PatternRecognition
from app.core.database import AsyncSessionLocal

logger = get_logger(__name__)


class AIPredictionStorageService:
    """AI预测存储服务"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def save_prediction(self, prediction_data: Dict[str, Any]) -> int:
        """保存AI预测结果"""
        try:
            logger.info(f"保存股票 {prediction_data.get('stock_code')} 的AI预测结果...")
            
            # 解析预测数据
            stock_code = prediction_data["stock_code"]
            prediction_date = date.today()
            target_date = prediction_date + timedelta(days=prediction_data.get("prediction_days", 5))
            
            # 创建预测记录
            prediction = AIPrediction(
                stock_code=stock_code,
                prediction_date=prediction_date,
                target_date=target_date,
                model_version=prediction_data.get("data_source", "deepseek_ai"),
                predicted_price=Decimal(str(prediction_data.get("target_price", 0))),
                predicted_change=Decimal(str(prediction_data.get("predicted_change", 0))),
                confidence_score=Decimal(str(prediction_data.get("confidence", 0))),
                trend_direction=self._map_prediction_direction(prediction_data.get("prediction", "")),
                price_upper=Decimal(str(prediction_data.get("price_range", {}).get("max", 0))) if prediction_data.get("price_range") else None,
                price_lower=Decimal(str(prediction_data.get("price_range", {}).get("min", 0))) if prediction_data.get("price_range") else None,
                feature_importance=prediction_data.get("key_levels", {}),
                prediction_basis=prediction_data.get("reasoning", ""),
                risk_factors=json.dumps(prediction_data.get("risk_factors", []), ensure_ascii=False)
            )
            
            self.session.add(prediction)
            await self.session.commit()
            
            logger.info(f"成功保存AI预测结果，ID: {prediction.id}")
            return prediction.id
            
        except Exception as e:
            logger.error(f"保存AI预测结果失败: {e}")
            await self.session.rollback()
            return 0
    
    async def save_pattern_recognition(self, pattern_data: Dict[str, Any]) -> int:
        """保存形态识别结果"""
        try:
            logger.info(f"保存股票 {pattern_data.get('stock_code')} 的形态识别结果...")
            
            stock_code = pattern_data["stock_code"]
            recognition_date = date.today()
            
            saved_count = 0
            patterns = pattern_data.get("patterns", [])
            
            for pattern in patterns:
                if not isinstance(pattern, dict):
                    continue
                
                try:
                    pattern_record = PatternRecognition(
                        stock_code=stock_code,
                        recognition_date=recognition_date,
                        pattern_type=pattern.get("type", "unknown"),
                        pattern_name=pattern.get("name", ""),
                        start_date=recognition_date - timedelta(days=30),  # 假设形态开始于30天前
                        end_date=None,  # 形态可能还在进行中
                        confidence=Decimal(str(pattern.get("confidence", 0))),
                        completion_rate=Decimal(str(pattern.get("completion", 0))),
                        key_prices=pattern.get("key_levels", {}),
                        support_level=Decimal(str(pattern.get("support", 0))) if pattern.get("support") else None,
                        resistance_level=Decimal(str(pattern.get("resistance", 0))) if pattern.get("resistance") else None,
                        target_price=Decimal(str(pattern.get("target_price", 0))) if pattern.get("target_price") else None,
                        stop_loss=Decimal(str(pattern.get("stop_loss", 0))) if pattern.get("stop_loss") else None,
                        description=pattern.get("description", ""),
                        trading_suggestion=pattern_data.get("recommendation", "")
                    )
                    
                    self.session.add(pattern_record)
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"保存形态记录失败: {e}")
                    continue
            
            await self.session.commit()
            logger.info(f"成功保存 {saved_count} 条形态识别结果")
            return saved_count
            
        except Exception as e:
            logger.error(f"保存形态识别结果失败: {e}")
            await self.session.rollback()
            return 0
    
    async def get_predictions(
        self,
        stock_code: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100
    ) -> List[AIPrediction]:
        """查询AI预测结果"""
        try:
            stmt = select(AIPrediction)
            
            conditions = []
            if stock_code:
                conditions.append(AIPrediction.stock_code == stock_code)
            if start_date:
                conditions.append(AIPrediction.prediction_date >= start_date)
            if end_date:
                conditions.append(AIPrediction.prediction_date <= end_date)
            
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            stmt = stmt.order_by(desc(AIPrediction.prediction_date)).limit(limit)
            
            result = await self.session.execute(stmt)
            predictions = result.scalars().all()
            
            return list(predictions)
            
        except Exception as e:
            logger.error(f"查询AI预测结果失败: {e}")
            return []
    
    async def get_latest_prediction(self, stock_code: str) -> Optional[AIPrediction]:
        """获取最新的AI预测结果"""
        try:
            stmt = select(AIPrediction).where(
                AIPrediction.stock_code == stock_code
            ).order_by(desc(AIPrediction.prediction_date)).limit(1)
            
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取最新AI预测结果失败: {e}")
            return None
    
    async def get_pattern_recognitions(
        self,
        stock_code: Optional[str] = None,
        pattern_type: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100
    ) -> List[PatternRecognition]:
        """查询形态识别结果"""
        try:
            stmt = select(PatternRecognition)
            
            conditions = []
            if stock_code:
                conditions.append(PatternRecognition.stock_code == stock_code)
            if pattern_type:
                conditions.append(PatternRecognition.pattern_type == pattern_type)
            if start_date:
                conditions.append(PatternRecognition.recognition_date >= start_date)
            if end_date:
                conditions.append(PatternRecognition.recognition_date <= end_date)
            
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            stmt = stmt.order_by(desc(PatternRecognition.recognition_date)).limit(limit)
            
            result = await self.session.execute(stmt)
            patterns = result.scalars().all()
            
            return list(patterns)
            
        except Exception as e:
            logger.error(f"查询形态识别结果失败: {e}")
            return []
    
    async def get_prediction_accuracy(self, days: int = 30) -> Dict[str, Any]:
        """计算预测准确率"""
        try:
            # 获取指定天数内的预测
            cutoff_date = date.today() - timedelta(days=days)
            
            stmt = select(AIPrediction).where(
                and_(
                    AIPrediction.prediction_date >= cutoff_date,
                    AIPrediction.target_date <= date.today()  # 只统计已到期的预测
                )
            )
            
            result = await self.session.execute(stmt)
            predictions = result.scalars().all()
            
            if not predictions:
                return {"total_predictions": 0, "accuracy": 0}
            
            # TODO: 这里需要与实际价格对比来计算准确率
            # 现在返回模拟数据
            total_predictions = len(predictions)
            correct_predictions = int(total_predictions * 0.65)  # 模拟65%准确率
            
            return {
                "total_predictions": total_predictions,
                "correct_predictions": correct_predictions,
                "accuracy": round(correct_predictions / total_predictions, 4) if total_predictions > 0 else 0,
                "period_days": days,
                "calculated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"计算预测准确率失败: {e}")
            return {"error": str(e)}
    
    async def get_prediction_statistics(self) -> Dict[str, Any]:
        """获取预测统计信息"""
        try:
            # 总预测数量
            total_stmt = select(func.count(AIPrediction.id))
            total_result = await self.session.execute(total_stmt)
            total_predictions = total_result.scalar()
            
            # 最近7天预测数量
            recent_date = date.today() - timedelta(days=7)
            recent_stmt = select(func.count(AIPrediction.id)).where(
                AIPrediction.prediction_date >= recent_date
            )
            recent_result = await self.session.execute(recent_stmt)
            recent_predictions = recent_result.scalar()
            
            # 平均置信度
            confidence_stmt = select(func.avg(AIPrediction.confidence_score))
            confidence_result = await self.session.execute(confidence_stmt)
            avg_confidence = confidence_result.scalar()
            
            # 趋势方向分布
            direction_stmt = select(
                AIPrediction.trend_direction,
                func.count(AIPrediction.id)
            ).group_by(AIPrediction.trend_direction)
            direction_result = await self.session.execute(direction_stmt)
            direction_distribution = dict(direction_result.all())
            
            return {
                "total_predictions": total_predictions or 0,
                "recent_predictions": recent_predictions or 0,
                "average_confidence": float(avg_confidence) if avg_confidence else 0,
                "direction_distribution": direction_distribution,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取预测统计信息失败: {e}")
            return {"error": str(e)}
    
    def _map_prediction_direction(self, prediction: str) -> str:
        """映射预测方向"""
        prediction_lower = prediction.lower()
        if "上涨" in prediction or "up" in prediction_lower or "bull" in prediction_lower:
            return "up"
        elif "下跌" in prediction or "down" in prediction_lower or "bear" in prediction_lower:
            return "down"
        else:
            return "sideways"
    
    async def delete_old_predictions(self, days_to_keep: int = 90) -> int:
        """删除过期的预测数据"""
        try:
            cutoff_date = date.today() - timedelta(days=days_to_keep)
            
            from sqlalchemy import delete
            
            # 删除过期预测
            delete_predictions_stmt = delete(AIPrediction).where(
                AIPrediction.prediction_date < cutoff_date
            )
            predictions_result = await self.session.execute(delete_predictions_stmt)
            
            # 删除过期形态识别
            delete_patterns_stmt = delete(PatternRecognition).where(
                PatternRecognition.recognition_date < cutoff_date
            )
            patterns_result = await self.session.execute(delete_patterns_stmt)
            
            await self.session.commit()
            
            total_deleted = predictions_result.rowcount + patterns_result.rowcount
            logger.info(f"删除了 {total_deleted} 条过期AI数据")
            return total_deleted
            
        except Exception as e:
            logger.error(f"删除过期AI数据失败: {e}")
            await self.session.rollback()
            return 0
