#!/usr/bin/env python3
"""
检查数据库中的所有数据，包括详细时间信息
"""

import sqlite3
from datetime import datetime

def check_all_data_with_time():
    """检查数据库中的所有数据，包括详细时间信息"""
    try:
        print("🔍 检查数据库中的所有数据（包括时间信息）...")
        
        # 连接数据库
        conn = sqlite3.connect('data/stock_analyzer.db')
        cursor = conn.cursor()
        
        # 检查数据总数
        cursor.execute("SELECT COUNT(*) FROM stock_spot_data")
        total_count = cursor.fetchone()[0]
        print(f"📊 表中共有 {total_count} 条记录")
        
        # 查看所有数据，按更新时间排序
        cursor.execute("""
            SELECT stock_code, stock_name, current_price, change_percent, update_time, created_at
            FROM stock_spot_data 
            ORDER BY update_time DESC
        """)
        
        records = cursor.fetchall()
        print(f"📈 所有 {len(records)} 条数据 (按更新时间倒序):")
        
        for i, record in enumerate(records):
            stock_code, stock_name, current_price, change_percent, update_time, created_at = record
            print(f"  {i+1}. {stock_code} {stock_name}: ¥{current_price} ({change_percent:+.2f}%)")
            print(f"      更新时间: {update_time}")
            print(f"      创建时间: {created_at}")
            print()
        
        # 分析数据类型和时间分布
        real_data_count = 0
        mock_data_count = 0
        time_groups = {}
        
        for record in records:
            current_price = float(record[2])
            update_time = record[4]
            
            if current_price == 10.5:
                mock_data_count += 1
            else:
                real_data_count += 1
            
            # 按小时分组
            hour_key = update_time[:13]  # YYYY-MM-DD HH
            if hour_key not in time_groups:
                time_groups[hour_key] = []
            time_groups[hour_key].append(record)
        
        print(f"📊 数据分析:")
        print(f"  真实数据: {real_data_count} 条")
        print(f"  模拟数据: {mock_data_count} 条")
        
        print(f"\n⏰ 时间分布:")
        for hour_key in sorted(time_groups.keys(), reverse=True):
            records_in_hour = time_groups[hour_key]
            print(f"  {hour_key}: {len(records_in_hour)} 条记录")
            for record in records_in_hour[:3]:  # 只显示前3条
                print(f"    - {record[0]} {record[1]}: ¥{record[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    print("=" * 80)
    print("🗄️  数据库完整数据检查工具（含时间信息）")
    print("=" * 80)
    
    check_all_data_with_time()
    
    print("=" * 80)
