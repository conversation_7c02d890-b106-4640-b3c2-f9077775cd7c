#!/usr/bin/env python3
"""
每日数据更新脚本
用于定时更新股票数据，支持增量更新
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import logging
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.data_manager import DataManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/daily_update.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 热门股票列表 - 优先更新这些股票
PRIORITY_STOCKS = [
    "000001",  # 平安银行
    "000002",  # 万科A
    "600000",  # 浦发银行
    "600036",  # 招商银行
    "600519",  # 贵州茅台
    "000858",  # 五粮液
    "300015",  # 爱尔眼科
    "002415",  # 海康威视
    "000725",  # 京东方A
    "002594",  # 比亚迪
    "600276",  # 恒瑞医药
    "000063",  # 中兴通讯
    "002230",  # 科大讯飞
    "300059",  # 东方财富
    "600887",  # 伊利股份
    "000166",  # 申万宏源
    "600030",  # 中信证券
    "000776",  # 广发证券
    "002142",  # 宁波银行
    "600009",  # 上海机场
]

async def update_priority_stocks():
    """更新优先股票数据"""
    logger.info("🎯 开始更新优先股票数据")
    
    try:
        data_manager = DataManager()
        
        # 获取最近7天的数据
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=7)
        
        logger.info(f"📅 更新日期范围: {start_date} 到 {end_date}")
        logger.info(f"📊 优先股票数量: {len(PRIORITY_STOCKS)}")
        
        result = await data_manager.update_stock_data(
            stock_codes=PRIORITY_STOCKS,
            start_date=start_date,
            end_date=end_date,
            period='daily'
        )
        
        logger.info(f"✅ 优先股票更新完成:")
        logger.info(f"   新增记录: {result['added']}")
        logger.info(f"   更新记录: {result['updated']}")
        logger.info(f"   错误数量: {result['errors']}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 更新优先股票失败: {e}")
        raise

async def update_all_stocks():
    """更新所有股票数据"""
    logger.info("🌐 开始更新所有股票数据")
    
    try:
        data_manager = DataManager()
        
        # 获取最近3天的数据
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=3)
        
        logger.info(f"📅 更新日期范围: {start_date} 到 {end_date}")
        
        result = await data_manager.update_stock_data(
            stock_codes=None,  # 所有股票
            start_date=start_date,
            end_date=end_date,
            period='daily'
        )
        
        logger.info(f"✅ 全量股票更新完成:")
        logger.info(f"   新增记录: {result['added']}")
        logger.info(f"   更新记录: {result['updated']}")
        logger.info(f"   错误数量: {result['errors']}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 更新所有股票失败: {e}")
        raise

async def check_data_quality():
    """检查数据质量"""
    logger.info("🔍 开始数据质量检查")
    
    try:
        data_manager = DataManager()
        stats = await data_manager.get_data_statistics()
        
        # 检查数据新鲜度
        latest_date = stats['klines'].get('latest_date')
        if latest_date:
            latest_datetime = datetime.fromisoformat(latest_date)
            days_old = (datetime.now().date() - latest_datetime.date()).days
        else:
            days_old = float('inf')
        
        # 数据质量评估
        coverage_rate = stats['stocks']['coverage_rate']
        total_records = stats['klines']['total']
        
        logger.info(f"📊 数据质量报告:")
        logger.info(f"   最新数据日期: {latest_date}")
        logger.info(f"   数据延迟: {days_old} 天")
        logger.info(f"   覆盖率: {coverage_rate}%")
        logger.info(f"   总记录数: {total_records}")
        
        # 质量评级
        quality_score = 0
        if days_old <= 1:
            quality_score += 40
        elif days_old <= 3:
            quality_score += 20
        
        if coverage_rate >= 90:
            quality_score += 30
        elif coverage_rate >= 70:
            quality_score += 20
        elif coverage_rate >= 50:
            quality_score += 10
        
        if total_records >= 1000:
            quality_score += 30
        elif total_records >= 500:
            quality_score += 20
        elif total_records >= 100:
            quality_score += 10
        
        if quality_score >= 80:
            quality_level = "🟢 优秀"
        elif quality_score >= 60:
            quality_level = "🟡 良好"
        elif quality_score >= 40:
            quality_level = "🟠 一般"
        else:
            quality_level = "🔴 较差"
        
        logger.info(f"   质量评级: {quality_level} ({quality_score}/100)")
        
        return {
            'score': quality_score,
            'level': quality_level,
            'days_old': days_old,
            'coverage_rate': coverage_rate,
            'total_records': total_records
        }
        
    except Exception as e:
        logger.error(f"❌ 数据质量检查失败: {e}")
        raise

async def generate_update_report(priority_result, all_result, quality_info):
    """生成更新报告"""
    logger.info("📝 生成更新报告")
    
    try:
        report = {
            'timestamp': datetime.now().isoformat(),
            'priority_stocks': {
                'count': len(PRIORITY_STOCKS),
                'added': priority_result['added'],
                'updated': priority_result['updated'],
                'errors': priority_result['errors']
            },
            'all_stocks': {
                'added': all_result['added'],
                'updated': all_result['updated'],
                'errors': all_result['errors']
            },
            'data_quality': quality_info,
            'summary': {
                'total_added': priority_result['added'] + all_result['added'],
                'total_updated': priority_result['updated'] + all_result['updated'],
                'total_errors': priority_result['errors'] + all_result['errors']
            }
        }
        
        # 保存报告到文件
        os.makedirs('logs', exist_ok=True)
        report_file = f"logs/update_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 更新报告已保存: {report_file}")
        
        # 打印摘要
        logger.info(f"📊 更新摘要:")
        logger.info(f"   总新增记录: {report['summary']['total_added']}")
        logger.info(f"   总更新记录: {report['summary']['total_updated']}")
        logger.info(f"   总错误数量: {report['summary']['total_errors']}")
        logger.info(f"   数据质量: {quality_info['level']}")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ 生成更新报告失败: {e}")
        raise

async def main():
    """主函数"""
    logger.info("🚀 开始每日数据更新任务")
    start_time = datetime.now()
    
    try:
        # 步骤1: 更新优先股票
        priority_result = await update_priority_stocks()
        
        # 步骤2: 更新所有股票
        all_result = await update_all_stocks()
        
        # 步骤3: 数据质量检查
        quality_info = await check_data_quality()
        
        # 步骤4: 生成报告
        report = await generate_update_report(priority_result, all_result, quality_info)
        
        # 计算耗时
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"✅ 每日数据更新任务完成")
        logger.info(f"⏱️ 总耗时: {duration}")
        logger.info(f"📈 数据质量: {quality_info['level']}")
        
        # 如果数据质量较差，发出警告
        if quality_info['score'] < 60:
            logger.warning("⚠️ 数据质量较差，建议检查数据源和网络连接")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ 每日数据更新任务失败: {e}")
        raise

if __name__ == "__main__":
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    # 运行更新任务
    asyncio.run(main())
