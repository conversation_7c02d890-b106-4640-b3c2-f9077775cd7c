"""
预警系统相关API端点
"""

from typing import List, Optional
from datetime import date, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.database import get_db
from app.core.logging import get_logger
from app.services.alert_service import AlertRuleEngine, AlertNotificationService, AlertManagementService
from app.tasks.analysis_tasks import alert_monitoring_task

router = APIRouter()
logger = get_logger(__name__)


class AlertRuleCreate(BaseModel):
    """创建预警规则的请求模型"""
    stock_code: str
    rule_name: str
    rule_type: str  # price/indicator/ai/volume
    alert_level: str = "medium"  # high/medium/low
    condition_type: str  # >/</=/between/cross/change_pct/volume_spike
    target_value: Optional[float] = None
    target_value_max: Optional[float] = None
    indicator_name: Optional[str] = None
    rule_config: Optional[dict] = None
    trigger_frequency: str = "once"  # once/daily/always
    notification_methods: Optional[dict] = None
    message_template: Optional[str] = None


@router.post("/rules")
async def create_alert_rule(
    rule_data: AlertRuleCreate,
    background_tasks: BackgroundTasks
):
    """创建预警规则"""
    try:
        async with AlertManagementService() as alert_service:
            rule_id = await alert_service.create_alert_rule(rule_data.dict())

        if rule_id > 0:
            # 启动后台监控任务
            background_tasks.add_task(alert_monitoring_task.delay, rule_data.stock_code)

            return {
                "code": 200,
                "message": "预警规则创建成功",
                "data": {
                    "rule_id": rule_id
                }
            }
        else:
            raise HTTPException(status_code=400, detail="预警规则创建失败")

    except Exception as e:
        logger.error(f"创建预警规则失败: {e}")
        raise HTTPException(status_code=500, detail="创建预警规则失败")


@router.get("/rules")
async def get_alert_rules(
    user_id: Optional[int] = Query(None, description="用户ID"),
    stock_code: Optional[str] = Query(None, description="股票代码"),
    rule_type: Optional[str] = Query(None, description="规则类型"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制")
):
    """获取预警规则列表"""
    try:
        async with AlertManagementService() as alert_service:
            rules = await alert_service.get_alert_rules(user_id, stock_code, rule_type, is_active)

        rules_data = []
        for rule in rules[:limit]:
            rules_data.append({
                "id": rule.id,
                "user_id": rule.user_id,
                "stock_code": rule.stock_code,
                "rule_name": rule.rule_name,
                "rule_type": rule.rule_type,
                "alert_level": rule.alert_level,
                "condition_type": rule.condition_type,
                "target_value": float(rule.target_value) if rule.target_value else None,
                "target_value_max": float(rule.target_value_max) if rule.target_value_max else None,
                "indicator_name": rule.indicator_name,
                "rule_config": rule.rule_config,
                "trigger_frequency": rule.trigger_frequency,
                "notification_methods": rule.notification_methods,
                "is_active": rule.is_active,
                "last_triggered_at": rule.last_triggered_at.isoformat() if rule.last_triggered_at else None,
                "trigger_count": rule.trigger_count,
                "created_at": rule.created_at.isoformat()
            })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "rules": rules_data,
                "total": len(rules_data),
                "filters": {
                    "user_id": user_id,
                    "stock_code": stock_code,
                    "rule_type": rule_type,
                    "is_active": is_active
                }
            }
        }
    except Exception as e:
        logger.error(f"获取预警规则失败: {e}")
        raise HTTPException(status_code=500, detail="获取预警规则失败")


@router.get("/events")
async def get_alert_events(
    rule_id: Optional[int] = Query(None, description="规则ID"),
    stock_code: Optional[str] = Query(None, description="股票代码"),
    event_type: Optional[str] = Query(None, description="事件类型"),
    alert_level: Optional[str] = Query(None, description="预警级别"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=500, description="返回数量限制")
):
    """获取预警事件列表"""
    try:
        async with AlertManagementService() as alert_service:
            events = await alert_service.get_alert_events(
                rule_id, stock_code, event_type, alert_level, start_date, end_date, limit
            )

        events_data = []
        for event in events:
            events_data.append({
                "id": event.id,
                "rule_id": event.rule_id,
                "stock_code": event.stock_code,
                "event_type": event.event_type,
                "alert_level": event.alert_level,
                "event_title": event.event_title,
                "event_message": event.event_message,
                "trigger_value": float(event.trigger_value) if event.trigger_value else None,
                "trigger_data": event.trigger_data,
                "market_data": event.market_data,
                "status": event.status,
                "notification_status": event.notification_status,
                "triggered_at": event.triggered_at.isoformat(),
                "processed_at": event.processed_at.isoformat() if event.processed_at else None
            })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "events": events_data,
                "total": len(events_data),
                "filters": {
                    "rule_id": rule_id,
                    "stock_code": stock_code,
                    "event_type": event_type,
                    "alert_level": alert_level,
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None
                }
            }
        }
    except Exception as e:
        logger.error(f"获取预警事件失败: {e}")
        raise HTTPException(status_code=500, detail="获取预警事件失败")


@router.post("/{stock_code}/monitor")
async def monitor_stock_alerts(
    stock_code: str,
    background_tasks: BackgroundTasks,
    alert_types: Optional[List[str]] = Query(None, description="预警类型列表")
):
    """监控股票预警"""
    try:
        # 启动后台监控任务
        task = alert_monitoring_task.delay(stock_code, alert_types)

        # 同时进行同步检查
        async with AlertRuleEngine() as rule_engine:
            all_events = []

            if not alert_types or "price" in alert_types:
                price_events = await rule_engine.evaluate_price_alerts(stock_code)
                all_events.extend(price_events)

            if not alert_types or "indicator" in alert_types:
                indicator_events = await rule_engine.evaluate_indicator_alerts(stock_code)
                all_events.extend(indicator_events)

            if not alert_types or "ai" in alert_types:
                ai_events = await rule_engine.evaluate_ai_alerts(stock_code)
                all_events.extend(ai_events)

            if not alert_types or "volume" in alert_types:
                volume_events = await rule_engine.evaluate_volume_alerts(stock_code)
                all_events.extend(volume_events)

        # 发送触发的预警通知
        notification_count = 0
        async with AlertNotificationService() as notification_service:
            for event in all_events:
                if await notification_service.send_alert_notification(event):
                    notification_count += 1

        return {
            "code": 200,
            "message": "股票预警监控完成",
            "data": {
                "task_id": task.id,
                "stock_code": stock_code,
                "triggered_events": len(all_events),
                "notifications_sent": notification_count,
                "events": all_events
            }
        }
    except Exception as e:
        logger.error(f"监控股票预警失败: {e}")
        raise HTTPException(status_code=500, detail="监控股票预警失败")


@router.post("/templates/init")
async def initialize_alert_templates():
    """初始化预警模板"""
    try:
        async with AlertManagementService() as alert_service:
            await alert_service.create_default_templates()

        return {
            "code": 200,
            "message": "预警模板初始化完成",
            "data": {
                "initialized_at": date.today().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"初始化预警模板失败: {e}")
        raise HTTPException(status_code=500, detail="初始化预警模板失败")


@router.delete("/rules/{rule_id}")
async def delete_alert_rule(rule_id: int, db: AsyncSession = Depends(get_db)):
    """删除预警规则"""
    try:
        from app.models.alerts import AlertRule
        from sqlalchemy import select, update

        # 检查规则是否存在
        stmt = select(AlertRule).where(AlertRule.id == rule_id)
        result = await db.execute(stmt)
        rule = result.scalar_one_or_none()

        if not rule:
            raise HTTPException(status_code=404, detail="预警规则不存在")

        # 软删除：设置为不活跃
        update_stmt = update(AlertRule).where(AlertRule.id == rule_id).values(is_active=False)
        await db.execute(update_stmt)
        await db.commit()

        return {
            "code": 200,
            "message": "预警规则删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除预警规则失败: {e}")
        raise HTTPException(status_code=500, detail="删除预警规则失败")


@router.get("/statistics")
async def get_alert_statistics(
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取预警统计信息"""
    try:
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()

        async with AlertManagementService() as alert_service:
            # 获取规则统计
            all_rules = await alert_service.get_alert_rules()
            active_rules = [r for r in all_rules if r.is_active]

            # 获取事件统计
            all_events = await alert_service.get_alert_events(
                start_date=start_date, end_date=end_date, limit=1000
            )

            # 按类型统计
            rule_type_stats = {}
            for rule in all_rules:
                rule_type = rule.rule_type
                if rule_type not in rule_type_stats:
                    rule_type_stats[rule_type] = {"total": 0, "active": 0}
                rule_type_stats[rule_type]["total"] += 1
                if rule.is_active:
                    rule_type_stats[rule_type]["active"] += 1

            # 按级别统计事件
            level_stats = {}
            for event in all_events:
                level = event.alert_level
                level_stats[level] = level_stats.get(level, 0) + 1

            # 按日期统计事件
            daily_stats = {}
            for event in all_events:
                event_date = event.triggered_at.date().isoformat()
                daily_stats[event_date] = daily_stats.get(event_date, 0) + 1

        return {
            "code": 200,
            "message": "success",
            "data": {
                "summary": {
                    "total_rules": len(all_rules),
                    "active_rules": len(active_rules),
                    "total_events": len(all_events),
                    "period_days": (end_date - start_date).days + 1
                },
                "rule_type_distribution": rule_type_stats,
                "alert_level_distribution": level_stats,
                "daily_event_count": daily_stats,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
            }
        }
    except Exception as e:
        logger.error(f"获取预警统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取预警统计失败")
