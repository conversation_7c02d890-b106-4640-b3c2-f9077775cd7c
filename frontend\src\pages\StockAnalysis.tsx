import React, { useState, useEffect } from 'react'
import {
  Card,
  Input,
  Button,
  Table,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Tabs,
  message,
  Spin,
  Alert,
  Badge,
  Tooltip,
  Progress,
  Segmented,
  Radio,
} from 'antd'
import {
  SearchOutlined,
  StarOutlined,
  StarFilled,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  Line<PERSON>hartOutlined,
  Bar<PERSON>hartOutlined,
  DashboardOutlined,
} from '@ant-design/icons'

import { useRealtimeData } from '@/hooks/useRealtimeData'
import TrendChart from '@/components/charts/TrendChart'
import CandlestickChart from '@/components/charts/CandlestickChart'
import Enhanced<PERSON><PERSON><PERSON><PERSON>hart from '@/components/charts/EnhancedKLineChart'
import { stockService } from '@/services/apiService'

const { Title, Text } = Typography
const { TabPane } = Tabs

// 时间周期选项
const TIME_FRAME_OPTIONS = [
  { label: '分时', value: '1m' },
  { label: '5分钟', value: '5m' },
  { label: '15分钟', value: '15m' },
  { label: '30分钟', value: '30m' },
  { label: '1小时', value: '1h' },
  { label: '日线', value: '1D' },
  { label: '周线', value: '1W' },
  { label: '月线', value: '1M' }
]

// 图表类型选项
const CHART_TYPE_OPTIONS = [
  { label: 'K线图', value: 'candlestick' },
  { label: '分时图', value: 'line' },
  { label: '面积图', value: 'area' }
]

interface StockInfo {
  code: string
  name: string
  current_price: number
  change: number
  change_percent: number
  volume: number
  market_cap: number
  pe_ratio: number
  pb_ratio: number
  sector: string
  is_watched: boolean
  high: number
  low: number
  open: number
  close: number
  turnover_rate?: number
  amplitude?: number
}

interface TechnicalIndicators {
  ma: {
    ma5: number
    ma10: number
    ma20: number
    ma60: number
  }
  rsi: {
    rsi6: number
    rsi12: number
    rsi24: number
  }
  macd: {
    macd: number
    signal: number
    histogram: number
  }
  kdj: {
    k: number
    d: number
    j: number
  }
  boll: {
    upper: number
    middle: number
    lower: number
  }
}

interface HistoryData {
  date: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

const StockAnalysis: React.FC = () => {
  const [searchValue, setSearchValue] = useState('')
  const [selectedStock, setSelectedStock] = useState<StockInfo | null>(null)
  const [stockList, setStockList] = useState<StockInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [searchLoading, setSearchLoading] = useState(false)
  const [indicators, setIndicators] = useState<TechnicalIndicators | null>(null)
  const [historyData, setHistoryData] = useState<HistoryData[]>([])
  const [indicatorsLoading, setIndicatorsLoading] = useState(false)

  // 新增状态 - 时间周期和图表类型
  const [timeFrame, setTimeFrame] = useState<string>('1D') // 时间周期
  const [chartType, setChartType] = useState<string>('candlestick') // 图表类型

  // 实时数据Hook
  const { data: realtimeData, isConnected } = useRealtimeData({
    enableStockUpdates: true,
    stockCodes: selectedStock ? [selectedStock.code] : []
  })

  useEffect(() => {
    loadStockList()
  }, [])

  const loadStockList = async () => {
    setLoading(true)
    try {
      const response = await stockService.getStockList({ limit: 100 })
      if (response.code === 200 && response.data?.stocks) {
        // 转换数据格式以匹配前端接口
        const stocks: StockInfo[] = response.data.stocks.map((stock: any) => ({
          code: stock.stock_code,
          name: stock.stock_name,
          current_price: 0, // 需要从实时数据获取
          change: 0,
          change_percent: 0,
          volume: 0,
          market_cap: 0,
          pe_ratio: 0,
          pb_ratio: 0,
          sector: stock.industry || '未知',
          is_watched: false,
          high: 0,
          low: 0,
          open: 0,
          close: 0,
          turnover_rate: 0,
          amplitude: 0,
        }))
        setStockList(stocks)
      } else {
        // 如果API失败，使用模拟数据
        const mockStocks: StockInfo[] = [
        {
          code: '000001',
          name: '平安银行',
          current_price: 12.45,
          change: 0.23,
          change_percent: 1.88,
          volume: 123456789,
          market_cap: 241000000000,
          pe_ratio: 5.2,
          pb_ratio: 0.8,
          sector: '银行',
          is_watched: false,
          high: 12.68,
          low: 12.15,
          open: 12.22,
          close: 12.45,
          turnover_rate: 0.85,
          amplitude: 4.25,
        },
        {
          code: '000002',
          name: '万科A',
          current_price: 18.67,
          change: -0.45,
          change_percent: -2.35,
          volume: 98765432,
          market_cap: 206000000000,
          pe_ratio: 8.9,
          pb_ratio: 1.2,
          sector: '房地产',
          is_watched: true,
          high: 19.25,
          low: 18.45,
          open: 19.12,
          close: 18.67,
          turnover_rate: 1.23,
          amplitude: 4.18,
        },
        {
          code: '600000',
          name: '浦发银行',
          current_price: 9.87,
          change: 0.12,
          change_percent: 1.23,
          volume: 87654321,
          market_cap: 289000000000,
          pe_ratio: 4.8,
          pb_ratio: 0.6,
          sector: '银行',
          is_watched: false,
          high: 9.95,
          low: 9.72,
          open: 9.75,
          close: 9.87,
          turnover_rate: 0.67,
          amplitude: 2.36,
        },
        {
          code: '600036',
          name: '招商银行',
          current_price: 45.67,
          change: 1.23,
          change_percent: 2.77,
          volume: 76543210,
          market_cap: 1200000000000,
          pe_ratio: 6.5,
          pb_ratio: 1.1,
          sector: '银行',
          is_watched: true,
          high: 46.12,
          low: 44.89,
          open: 44.44,
          close: 45.67,
          turnover_rate: 0.45,
          amplitude: 2.77,
        },
        {
          code: '000858',
          name: '五粮液',
          current_price: 156.78,
          change: -2.34,
          change_percent: -1.47,
          volume: 65432109,
          market_cap: 607000000000,
          pe_ratio: 28.5,
          pb_ratio: 4.2,
          sector: '食品饮料',
          is_watched: false,
          high: 159.45,
          low: 155.23,
          open: 159.12,
          close: 156.78,
          turnover_rate: 0.32,
          amplitude: 2.65,
        },
        ]
        setStockList(mockStocks)
      }
    } catch (error) {
      console.error('加载股票列表失败:', error)
      message.error('加载股票列表失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchValue.trim()) {
      message.warning('请输入股票代码或名称')
      return
    }

    setSearchLoading(true)
    try {
      const response = await stockService.searchStocks(searchValue, 20)
      if (response.code === 200 && response.data?.stocks) {
        // 转换数据格式
        const searchResults: StockInfo[] = response.data.stocks.map((stock: any) => ({
          code: stock.stock_code,
          name: stock.stock_name,
          current_price: 0,
          change: 0,
          change_percent: 0,
          volume: 0,
          market_cap: 0,
          pe_ratio: 0,
          pb_ratio: 0,
          sector: stock.industry || '未知',
          is_watched: false,
          high: 0,
          low: 0,
          open: 0,
          close: 0,
          turnover_rate: 0,
          amplitude: 0,
        }))

        if (searchResults.length > 0) {
          setStockList(searchResults)
          setSelectedStock(searchResults[0])
          message.success(`找到 ${searchResults.length} 只相关股票`)
        } else {
          message.warning('未找到相关股票')
        }
      } else {
        message.warning('搜索失败，请重试')
      }
    } catch (error) {
      console.error('搜索失败:', error)
      message.error('搜索失败')
    } finally {
      setSearchLoading(false)
    }
  }

  const handleToggleWatchlist = async (stock: StockInfo) => {
    try {
      // 更新本地状态
      setStockList(prev =>
        prev.map(s =>
          s.code === stock.code
            ? { ...s, is_watched: !s.is_watched }
            : s
        )
      )

      if (selectedStock?.code === stock.code) {
        setSelectedStock(prev => prev ? { ...prev, is_watched: !prev.is_watched } : null)
      }

      message.success(stock.is_watched ? '已从自选股中移除' : '已添加到自选股')
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 加载技术指标
  const loadTechnicalIndicators = async (stockCode: string) => {
    setIndicatorsLoading(true)
    try {
      // 模拟技术指标数据
      const mockIndicators: TechnicalIndicators = {
        ma: {
          ma5: 12.34,
          ma10: 12.56,
          ma20: 12.78,
          ma60: 13.12,
        },
        rsi: {
          rsi6: 65.4,
          rsi12: 58.7,
          rsi24: 52.3,
        },
        macd: {
          macd: 0.23,
          signal: 0.18,
          histogram: 0.05,
        },
        kdj: {
          k: 72.5,
          d: 68.3,
          j: 80.9,
        },
        boll: {
          upper: 13.45,
          middle: 12.67,
          lower: 11.89,
        },
      }
      setIndicators(mockIndicators)
    } catch (error) {
      message.error('加载技术指标失败')
    } finally {
      setIndicatorsLoading(false)
    }
  }

  // 加载历史数据
  const loadHistoryData = async (stockCode: string, timeframe: string = '1D') => {
    try {
      // 根据时间周期生成不同数量和间隔的数据
      const getDataConfig = (timeframe: string) => {
        switch (timeframe) {
          case '1m': return { count: 240, interval: 1, unit: 'minute' } // 4小时分时数据
          case '5m': return { count: 288, interval: 5, unit: 'minute' } // 1天5分钟数据
          case '15m': return { count: 96, interval: 15, unit: 'minute' } // 1天15分钟数据
          case '30m': return { count: 48, interval: 30, unit: 'minute' } // 1天30分钟数据
          case '1h': return { count: 168, interval: 1, unit: 'hour' } // 1周小时数据
          case '1D': return { count: 30, interval: 1, unit: 'day' } // 30天日线数据
          case '1W': return { count: 52, interval: 7, unit: 'day' } // 52周周线数据
          case '1M': return { count: 24, interval: 30, unit: 'day' } // 24个月月线数据
          default: return { count: 30, interval: 1, unit: 'day' }
        }
      }

      const config = getDataConfig(timeframe)
      const mockHistory: HistoryData[] = Array.from({ length: config.count }, (_, i) => {
        const date = new Date()

        // 根据时间周期设置日期
        if (config.unit === 'minute') {
          date.setMinutes(date.getMinutes() - (config.count - 1 - i) * config.interval)
        } else if (config.unit === 'hour') {
          date.setHours(date.getHours() - (config.count - 1 - i) * config.interval)
        } else {
          date.setDate(date.getDate() - (config.count - 1 - i) * config.interval)
        }

        const basePrice = 12.0 + Math.random() * 2
        const open = basePrice + (Math.random() - 0.5) * 0.5
        const close = open + (Math.random() - 0.5) * 0.8
        const high = Math.max(open, close) + Math.random() * 0.3
        const low = Math.min(open, close) - Math.random() * 0.3

        return {
          date: config.unit === 'minute' || config.unit === 'hour'
            ? date.toISOString().slice(0, 16).replace('T', ' ') // 包含时间
            : date.toISOString().split('T')[0], // 只有日期
          open: Number(open.toFixed(2)),
          high: Number(high.toFixed(2)),
          low: Number(low.toFixed(2)),
          close: Number(close.toFixed(2)),
          volume: Math.floor(Math.random() * 100000000),
        }
      })
      setHistoryData(mockHistory)
    } catch (error) {
      message.error('加载历史数据失败')
    }
  }

  const stockColumns = [
    {
      title: '股票',
      key: 'stock',
      render: (record: StockInfo) => (
        <Space>
          <Button
            type="link"
            onClick={async () => {
              setSelectedStock(record)
              await loadTechnicalIndicators(record.code)
              await loadHistoryData(record.code, timeFrame)
            }}
          >
            <Text strong>{record.code}</Text>
          </Button>
          <Text>{record.name}</Text>
        </Space>
      ),
    },
    {
      title: '现价',
      dataIndex: 'current_price',
      key: 'current_price',
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '涨跌',
      key: 'change',
      render: (record: StockInfo) => (
        <Space>
          <Text type={record.change >= 0 ? 'success' : 'danger'}>
            {record.change >= 0 ? '+' : ''}{record.change.toFixed(2)}
          </Text>
          <Text type={record.change_percent >= 0 ? 'success' : 'danger'}>
            ({record.change_percent >= 0 ? '+' : ''}{record.change_percent.toFixed(2)}%)
          </Text>
        </Space>
      ),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      render: (value: number) => (value / 100000000).toFixed(2) + '亿',
    },
    {
      title: '市盈率',
      dataIndex: 'pe_ratio',
      key: 'pe_ratio',
      render: (value: number) => value.toFixed(1),
    },
    {
      title: '市净率',
      dataIndex: 'pb_ratio',
      key: 'pb_ratio',
      render: (value: number) => value.toFixed(1),
    },
    {
      title: '板块',
      dataIndex: 'sector',
      key: 'sector',
      render: (value: string) => <Tag>{value}</Tag>,
    },
    {
      title: '操作',
      key: 'action',
      render: (record: StockInfo) => (
        <Button
          type="text"
          icon={record.is_watched ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
          onClick={() => handleToggleWatchlist(record)}
        >
          {record.is_watched ? '已关注' : '关注'}
        </Button>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>股票分析</Title>
        <Space.Compact style={{ width: '100%', maxWidth: 400 }}>
          <Input
            placeholder="输入股票代码或名称"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onPressEnter={handleSearch}
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            loading={searchLoading}
            onClick={handleSearch}
          >
            搜索
          </Button>
        </Space.Compact>
      </div>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="股票列表" loading={loading}>
            <Table
              dataSource={stockList}
              columns={stockColumns}
              rowKey="code"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </Col>

        {selectedStock && (
          <Col span={24}>
            <Card
              title={
                <Space>
                  <Text strong>{selectedStock.code} - {selectedStock.name}</Text>
                  <Tag color={selectedStock.change >= 0 ? 'green' : 'red'}>
                    {selectedStock.change >= 0 ? '+' : ''}{selectedStock.change_percent.toFixed(2)}%
                  </Tag>
                </Space>
              }
              extra={
                <Button
                  type={selectedStock.is_watched ? 'default' : 'primary'}
                  icon={selectedStock.is_watched ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                  onClick={() => handleToggleWatchlist(selectedStock)}
                >
                  {selectedStock.is_watched ? '已关注' : '关注'}
                </Button>
              }
            >
              {/* 第一行统计信息 */}
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={4}>
                  <Statistic
                    title="现价"
                    value={selectedStock.current_price}
                    precision={2}
                    prefix="¥"
                    valueStyle={{
                      color: selectedStock.change >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="涨跌额"
                    value={selectedStock.change}
                    precision={2}
                    prefix={selectedStock.change >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    valueStyle={{
                      color: selectedStock.change >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="涨跌幅"
                    value={selectedStock.change_percent}
                    precision={2}
                    suffix="%"
                    valueStyle={{
                      color: selectedStock.change >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="今日最高"
                    value={selectedStock.high}
                    precision={2}
                    prefix="¥"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="今日最低"
                    value={selectedStock.low}
                    precision={2}
                    prefix="¥"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="今日开盘"
                    value={selectedStock.open}
                    precision={2}
                    prefix="¥"
                  />
                </Col>
              </Row>

              {/* 第二行统计信息 */}
              <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={4}>
                  <Statistic
                    title="成交量"
                    value={selectedStock.volume / 100000000}
                    precision={2}
                    suffix="亿"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="市值"
                    value={selectedStock.market_cap / 100000000}
                    precision={0}
                    suffix="亿"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="市盈率"
                    value={selectedStock.pe_ratio}
                    precision={1}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="市净率"
                    value={selectedStock.pb_ratio}
                    precision={1}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="换手率"
                    value={selectedStock.turnover_rate || 0}
                    precision={2}
                    suffix="%"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="振幅"
                    value={selectedStock.amplitude || 0}
                    precision={2}
                    suffix="%"
                  />
                </Col>
              </Row>

              {/* 图表工具栏 */}
              <div style={{ marginBottom: 16, padding: '12px', backgroundColor: '#fafafa', borderRadius: '6px' }}>
                <Row gutter={[16, 8]} align="middle">
                  <Col>
                    <Space>
                      <Text strong style={{ color: '#666' }}>时间周期:</Text>
                      <Segmented
                        options={TIME_FRAME_OPTIONS}
                        value={timeFrame}
                        onChange={(value) => {
                          setTimeFrame(value as string)
                          // 重新加载对应周期的数据
                          loadHistoryData(selectedStock.code, value as string)
                        }}
                        size="small"
                      />
                    </Space>
                  </Col>
                  <Col>
                    <Space>
                      <Text strong style={{ color: '#666' }}>图表类型:</Text>
                      <Radio.Group
                        value={chartType}
                        onChange={(e) => setChartType(e.target.value)}
                        size="small"
                      >
                        <Radio.Button value="candlestick">K线图</Radio.Button>
                        <Radio.Button value="line">分时图</Radio.Button>
                        <Radio.Button value="area">面积图</Radio.Button>
                      </Radio.Group>
                    </Space>
                  </Col>
                </Row>
              </div>

              {/* 实时连接状态 */}
              <div style={{ marginBottom: 16 }}>
                <Space>
                  <Badge
                    status={isConnected ? 'success' : 'error'}
                    text={isConnected ? '实时数据连接正常' : '实时数据连接断开'}
                  />
                  {realtimeData.stockUpdates.length > 0 && (
                    <Tag color="blue">
                      最新更新: {new Date(realtimeData.stockUpdates[0].timestamp).toLocaleTimeString()}
                    </Tag>
                  )}
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={() => {
                      loadTechnicalIndicators(selectedStock.code)
                      loadHistoryData(selectedStock.code, timeFrame)
                    }}
                  >
                    刷新数据
                  </Button>
                </Space>
              </div>

              <Tabs defaultActiveKey="chart">
                <TabPane tab={
                  <span>
                    <DashboardOutlined />
                    实时行情
                  </span>
                } key="chart">
                  <Row gutter={[16, 16]}>
                    <Col span={16}>
                      {historyData.length > 0 ? (
                        chartType === 'candlestick' ? (
                          <EnhancedKLineChart
                            title={`${selectedStock.name} (${selectedStock.code}) - ${timeFrame}`}
                            data={historyData.map(item => ({
                              timestamp: item.date,
                              date: item.date,
                              open: item.open,
                              high: item.high,
                              low: item.low,
                              close: item.close,
                              volume: item.volume
                            }))}
                            height={450}
                            showVolume={true}
                            showMA={true}
                            onTimeframeChange={(newTimeframe) => {
                              setTimeFrame(newTimeframe)
                              loadHistoryData(selectedStock.code, newTimeframe)
                            }}
                            onRefresh={() => loadHistoryData(selectedStock.code, timeFrame)}
                          />
                        ) : (
                          <TrendChart
                            title={`价格走势图 - ${timeFrame}`}
                            data={historyData.slice(-50).map(item => ({
                              time: item.date.includes(' ') ? item.date.slice(-8) : item.date.slice(-5), // 显示时间或日期
                              value: item.close,
                              change: item.close - item.open,
                              changePercent: ((item.close - item.open) / item.open) * 100,
                            }))}
                            height={450}
                            color={selectedStock.change >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'}
                          />
                        )
                      ) : (
                        <Card>
                          <div style={{ height: 450, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            <Spin size="large" />
                          </div>
                        </Card>
                      )}
                    </Col>
                    <Col span={8}>
                      <Card title="实时数据" size="small">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Alert
                            message={`当前价格: ¥${selectedStock.current_price}`}
                            type={selectedStock.change >= 0 ? 'error' : 'success'}
                            showIcon
                          />
                          <div>
                            <Text strong>今日表现:</Text>
                            <div style={{ marginTop: 8 }}>
                              <Progress
                                percent={Math.abs(selectedStock.change_percent) * 10}
                                strokeColor={selectedStock.change >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'}
                                format={() => `${selectedStock.change_percent.toFixed(2)}%`}
                              />
                            </div>
                          </div>
                          <div>
                            <Text strong>价格区间:</Text>
                            <div style={{ marginTop: 8 }}>
                              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                <Text type="secondary">最低</Text>
                                <Text>¥{selectedStock.low}</Text>
                              </div>
                              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                <Text type="secondary">最高</Text>
                                <Text>¥{selectedStock.high}</Text>
                              </div>
                              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                <Text type="secondary">振幅</Text>
                                <Text>{selectedStock.amplitude?.toFixed(2)}%</Text>
                              </div>
                            </div>
                          </div>
                          {realtimeData.stockUpdates.length > 0 && (
                            <div>
                              <Text strong>最新更新:</Text>
                              <div style={{ marginTop: 8 }}>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  {new Date(realtimeData.stockUpdates[0].timestamp).toLocaleString()}
                                </Text>
                              </div>
                            </div>
                          )}
                        </Space>
                      </Card>
                    </Col>
                  </Row>
                </TabPane>
                <TabPane tab={
                  <span>
                    <BarChartOutlined />
                    技术指标
                    {indicatorsLoading && <Spin size="small" style={{ marginLeft: 8 }} />}
                  </span>
                } key="indicators">
                  {indicators ? (
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Card size="small" title="移动平均线(MA)" extra={
                          <Tooltip title="移动平均线反映价格趋势">
                            <InfoCircleOutlined />
                          </Tooltip>
                        }>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>MA5:</Text>
                              <Text strong style={{ color: indicators.ma.ma5 > selectedStock.current_price ? '#cf1322' : '#3f8600' }}>
                                {indicators.ma.ma5.toFixed(2)}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>MA10:</Text>
                              <Text strong style={{ color: indicators.ma.ma10 > selectedStock.current_price ? '#cf1322' : '#3f8600' }}>
                                {indicators.ma.ma10.toFixed(2)}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>MA20:</Text>
                              <Text strong style={{ color: indicators.ma.ma20 > selectedStock.current_price ? '#cf1322' : '#3f8600' }}>
                                {indicators.ma.ma20.toFixed(2)}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>MA60:</Text>
                              <Text strong style={{ color: indicators.ma.ma60 > selectedStock.current_price ? '#cf1322' : '#3f8600' }}>
                                {indicators.ma.ma60.toFixed(2)}
                              </Text>
                            </div>
                          </Space>
                        </Card>
                      </Col>
                      <Col span={12}>
                        <Card size="small" title="相对强弱指标(RSI)" extra={
                          <Tooltip title="RSI指标衡量价格变动的速度和变化">
                            <InfoCircleOutlined />
                          </Tooltip>
                        }>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Text>RSI6:</Text>
                              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                <Progress
                                  percent={indicators.rsi.rsi6}
                                  size="small"
                                  style={{ width: 60 }}
                                  strokeColor={indicators.rsi.rsi6 > 70 ? '#cf1322' : indicators.rsi.rsi6 < 30 ? '#3f8600' : '#1890ff'}
                                />
                                <Text strong>{indicators.rsi.rsi6.toFixed(1)}</Text>
                              </div>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Text>RSI12:</Text>
                              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                <Progress
                                  percent={indicators.rsi.rsi12}
                                  size="small"
                                  style={{ width: 60 }}
                                  strokeColor={indicators.rsi.rsi12 > 70 ? '#cf1322' : indicators.rsi.rsi12 < 30 ? '#3f8600' : '#1890ff'}
                                />
                                <Text strong>{indicators.rsi.rsi12.toFixed(1)}</Text>
                              </div>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Text>RSI24:</Text>
                              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                <Progress
                                  percent={indicators.rsi.rsi24}
                                  size="small"
                                  style={{ width: 60 }}
                                  strokeColor={indicators.rsi.rsi24 > 70 ? '#cf1322' : indicators.rsi.rsi24 < 30 ? '#3f8600' : '#1890ff'}
                                />
                                <Text strong>{indicators.rsi.rsi24.toFixed(1)}</Text>
                              </div>
                            </div>
                          </Space>
                        </Card>
                      </Col>
                      <Col span={12}>
                        <Card size="small" title="MACD指标" extra={
                          <Tooltip title="MACD指标用于判断趋势变化">
                            <InfoCircleOutlined />
                          </Tooltip>
                        }>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>MACD:</Text>
                              <Text strong style={{ color: indicators.macd.macd > 0 ? '#3f8600' : '#cf1322' }}>
                                {indicators.macd.macd.toFixed(3)}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>信号线:</Text>
                              <Text strong style={{ color: indicators.macd.signal > 0 ? '#3f8600' : '#cf1322' }}>
                                {indicators.macd.signal.toFixed(3)}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>柱状图:</Text>
                              <Text strong style={{ color: indicators.macd.histogram > 0 ? '#3f8600' : '#cf1322' }}>
                                {indicators.macd.histogram.toFixed(3)}
                              </Text>
                            </div>
                          </Space>
                        </Card>
                      </Col>
                      <Col span={12}>
                        <Card size="small" title="KDJ指标" extra={
                          <Tooltip title="KDJ指标用于判断超买超卖">
                            <InfoCircleOutlined />
                          </Tooltip>
                        }>
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>K值:</Text>
                              <Text strong style={{ color: indicators.kdj.k > 80 ? '#cf1322' : indicators.kdj.k < 20 ? '#3f8600' : '#1890ff' }}>
                                {indicators.kdj.k.toFixed(1)}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>D值:</Text>
                              <Text strong style={{ color: indicators.kdj.d > 80 ? '#cf1322' : indicators.kdj.d < 20 ? '#3f8600' : '#1890ff' }}>
                                {indicators.kdj.d.toFixed(1)}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>J值:</Text>
                              <Text strong style={{ color: indicators.kdj.j > 100 ? '#cf1322' : indicators.kdj.j < 0 ? '#3f8600' : '#1890ff' }}>
                                {indicators.kdj.j.toFixed(1)}
                              </Text>
                            </div>
                          </Space>
                        </Card>
                      </Col>
                    </Row>
                  ) : (
                    <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Text type="secondary">请选择股票查看技术指标</Text>
                    </div>
                  )}
                </TabPane>
                <TabPane tab={
                  <span>
                    <LineChartOutlined />
                    K线图表
                  </span>
                } key="charts">
                  {historyData.length > 0 ? (
                    <Row gutter={[16, 16]}>
                      <Col span={24}>
                        <CandlestickChart
                          title={`${selectedStock.name} (${selectedStock.code})`}
                          data={historyData.map(item => ({
                            time: item.date,
                            open: item.open,
                            high: item.high,
                            low: item.low,
                            close: item.close,
                            volume: item.volume,
                          }))}
                          height={400}
                          showVolume={true}
                          showMA={true}
                        />
                      </Col>
                      <Col span={12}>
                        <TrendChart
                          title="价格趋势"
                          data={historyData.slice(-10).map(item => ({
                            time: item.date,
                            value: item.close,
                            change: item.close - item.open,
                            changePercent: ((item.close - item.open) / item.open) * 100,
                          }))}
                          height={200}
                          color="#1890ff"
                        />
                      </Col>
                      <Col span={12}>
                        <TrendChart
                          title="成交量趋势"
                          data={historyData.slice(-10).map(item => ({
                            time: item.date,
                            value: item.volume / 100000000,
                            change: 0,
                            changePercent: 0,
                          }))}
                          height={200}
                          color="#52c41a"
                          showChange={false}
                        />
                      </Col>
                    </Row>
                  ) : (
                    <div style={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Text type="secondary">请选择股票查看图表</Text>
                    </div>
                  )}
                </TabPane>
                <TabPane tab="基本面" key="fundamentals">
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Card size="small" title="估值指标">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Text>市盈率(PE):</Text>
                            <Text strong>{selectedStock.pe_ratio}</Text>
                          </div>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Text>市净率(PB):</Text>
                            <Text strong>{selectedStock.pb_ratio}</Text>
                          </div>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Text>总市值:</Text>
                            <Text strong>{(selectedStock.market_cap / 100000000).toFixed(0)}亿</Text>
                          </div>
                        </Space>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small" title="行业信息">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Text>所属板块:</Text>
                            <Tag>{selectedStock.sector}</Tag>
                          </div>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Text>行业排名:</Text>
                            <Text>前20%</Text>
                          </div>
                        </Space>
                      </Card>
                    </Col>
                  </Row>
                </TabPane>
              </Tabs>
            </Card>
          </Col>
        )}
      </Row>
    </div>
  )
}

export default StockAnalysis
