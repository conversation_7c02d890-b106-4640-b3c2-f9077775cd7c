#!/usr/bin/env python3
"""
检查数据库中的股票数据状态
"""

import requests
import json
import time

def check_spot_data():
    """检查实时行情数据"""
    url = "http://localhost:8000/api/v1/akshare/spot-data"
    
    try:
        print("🔍 检查实时行情数据...")
        response = requests.get(f"{url}?limit=5", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                print(f"✅ 成功获取 {len(data)} 条数据")
                
                # 检查是否是真实数据
                first_stock = data[0]
                current_price = first_stock.get("current_price")
                
                print(f"📊 第一条数据: {first_stock.get('stock_name')} ({first_stock.get('stock_code')})")
                print(f"💰 当前价格: ¥{current_price}")
                
                if current_price != 10.5:  # 模拟数据的固定价格
                    print("🎉 数据库包含真实数据!")
                    print("\n📈 前5条股票数据:")
                    for i, stock in enumerate(data):
                        print(f"  {i+1}. {stock.get('stock_name')} ({stock.get('stock_code')}): ¥{stock.get('current_price')} ({stock.get('change_percent'):+.2f}%)")
                    return True
                else:
                    print("⚠️  数据库仍然是模拟数据")
                    return False
            else:
                print("❌ 没有获取到数据")
                return False
        else:
            print(f"❌ 获取数据失败: {response.status_code}")
            print(f"📝 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")
        return False

def check_backend_logs():
    """检查后端日志状态"""
    url = "http://localhost:8000/api/v1/data-management/logs"
    
    try:
        print("\n📋 检查数据更新日志...")
        response = requests.get(f"{url}?limit=5", timeout=30)
        
        if response.status_code == 200:
            logs = response.json()
            if logs and len(logs) > 0:
                print(f"✅ 获取到 {len(logs)} 条日志记录")
                
                for i, log in enumerate(logs):
                    print(f"  {i+1}. {log.get('task_type')} - {log.get('status')} - {log.get('start_time')}")
                    if log.get('error_message'):
                        print(f"     错误: {log.get('error_message')}")
                
                return True
            else:
                print("❌ 没有获取到日志")
                return False
        else:
            print(f"❌ 获取日志失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")
        return False

def trigger_new_update():
    """触发新的数据更新"""
    url = "http://localhost:8000/api/v1/data-management/akshare/update-spot-data"
    
    try:
        print("\n🚀 触发新的数据更新...")
        response = requests.post(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 更新任务已启动!")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 启动更新失败: {response.status_code}")
            print(f"📝 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 触发更新失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 股票数据状态检查工具")
    print("=" * 80)
    
    # 1. 检查当前数据状态
    data_is_real = check_spot_data()
    
    # 2. 检查后端日志
    check_backend_logs()
    
    if not data_is_real:
        print("\n" + "=" * 40)
        print("⚠️  数据库仍然是模拟数据，尝试重新更新...")
        print("=" * 40)
        
        # 3. 触发新的更新
        update_started = trigger_new_update()
        
        if update_started:
            print("\n⏳ 等待120秒让更新任务完成...")
            time.sleep(120)
            
            print("\n🔍 重新检查数据状态...")
            final_check = check_spot_data()
            
            if final_check:
                print("\n🎉 数据更新成功! 数据库现在包含真实的股票数据。")
            else:
                print("\n❌ 数据更新可能仍在进行中或遇到问题。")
                print("💡 建议: 检查后端服务日志或网络连接状态。")
        else:
            print("\n❌ 无法启动数据更新任务。")
    else:
        print("\n🎉 数据库已包含真实数据，无需更新!")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()
