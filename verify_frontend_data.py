#!/usr/bin/env python3
"""
验证前端数据是否为真实数据
"""

import requests
import json

def verify_frontend_data():
    """验证前端数据"""
    try:
        print("🔍 验证前端数据...")
        
        # 获取前端数据
        response = requests.get("http://localhost:8000/api/v1/akshare/spot-data?limit=5", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if data and len(data) > 0:
                print("✅ 前端数据验证:")
                
                for stock in data:
                    stock_name = stock.get("stock_name")
                    stock_code = stock.get("stock_code")
                    current_price = stock.get("current_price")
                    change_percent = stock.get("change_percent")
                    
                    print(f"  {stock_name} ({stock_code}): ¥{current_price} ({change_percent:+.2f}%)")
                
                # 检查是否是真实数据
                first_price = float(data[0].get("current_price"))
                if first_price != 10.5:
                    print("\n🎉 数据库包含真实股票数据!")
                    print("✅ 前端现在应该正确显示真实的股票价格和涨跌幅。")
                    return True
                else:
                    print("\n⚠️  数据库仍然是模拟数据")
                    return False
            else:
                print("❌ 没有获取到数据")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("📊 前端数据验证工具")
    print("=" * 60)
    
    success = verify_frontend_data()
    
    if success:
        print("\n🎉 验证成功! 前端现在显示真实的股票数据。")
        print("💡 建议: 刷新个股分析页面，查看真实的股票价格和数据。")
    else:
        print("\n❌ 验证失败。")
    
    print("=" * 60)
