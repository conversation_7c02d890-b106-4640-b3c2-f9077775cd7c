import { useEffect, useState, useCallback } from 'react'
import { realtimeService, StockPriceUpdate, AISignal, AlertNotification } from '@/services/realtimeService'

interface UseRealtimeDataOptions {
  enableStockUpdates?: boolean
  enableAISignals?: boolean
  enableAlerts?: boolean
  stockCodes?: string[]
}

interface RealtimeData {
  stockUpdates: StockPriceUpdate[]
  aiSignals: AISignal[]
  alerts: AlertNotification[]
  isConnected: boolean
}

export const useRealtimeData = (options: UseRealtimeDataOptions = {}) => {
  const {
    enableStockUpdates = true,
    enableAISignals = true,
    enableAlerts = true,
    stockCodes = []
  } = options

  const [data, setData] = useState<RealtimeData>({
    stockUpdates: [],
    aiSignals: [],
    alerts: [],
    isConnected: false
  })

  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting')

  // 添加股票价格更新 - 移除stockCodes依赖避免循环
  const addStockUpdate = useCallback((update: StockPriceUpdate) => {
    setData(prev => {
      // 在这里检查stockCodes，避免依赖项循环
      if (stockCodes.length > 0 && !stockCodes.includes(update.stock_code)) {
        return prev
      }

      return {
        ...prev,
        stockUpdates: [update, ...prev.stockUpdates.slice(0, 99)] // 保持最多100条记录
      }
    })
  }, []) // 移除stockCodes依赖

  // 添加AI信号 - 移除stockCodes依赖避免循环
  const addAISignal = useCallback((signal: AISignal) => {
    setData(prev => {
      if (stockCodes.length > 0 && !stockCodes.includes(signal.stock_code)) {
        return prev
      }

      return {
        ...prev,
        aiSignals: [signal, ...prev.aiSignals.slice(0, 49)] // 保持最多50条记录
      }
    })
  }, []) // 移除stockCodes依赖

  // 添加预警 - 移除stockCodes依赖避免循环
  const addAlert = useCallback((alert: AlertNotification) => {
    setData(prev => {
      if (stockCodes.length > 0 && !stockCodes.includes(alert.stock_code)) {
        return prev
      }

      return {
        ...prev,
        alerts: [alert, ...prev.alerts.slice(0, 49)] // 保持最多50条记录
      }
    })
  }, []) // 移除stockCodes依赖

  useEffect(() => {
    // 检查连接状态
    const checkConnection = () => {
      const isConnected = realtimeService.isConnected()
      setConnectionStatus(isConnected ? 'connected' : 'disconnected')
      setData(prev => ({ ...prev, isConnected }))
    }

    // 初始检查
    checkConnection()

    // 定期检查连接状态
    const connectionCheckInterval = setInterval(checkConnection, 5000)

    // 事件处理函数
    const handleStockUpdate = (data: { data: StockPriceUpdate }) => {
      if (enableStockUpdates) {
        addStockUpdate(data.data)
      }
    }

    const handleAISignal = (data: { data: AISignal }) => {
      if (enableAISignals) {
        addAISignal(data.data)
      }
    }

    const handleAlert = (data: { data: AlertNotification }) => {
      if (enableAlerts) {
        addAlert(data.data)
      }
    }

    // 订阅事件
    if (enableStockUpdates) {
      realtimeService.on('stock_price_update', handleStockUpdate)
    }
    if (enableAISignals) {
      realtimeService.on('ai_signal', handleAISignal)
    }
    if (enableAlerts) {
      realtimeService.on('alert', handleAlert)
    }

    return () => {
      // 清理定时器
      clearInterval(connectionCheckInterval)

      // 取消订阅
      if (enableStockUpdates) {
        realtimeService.off('stock_price_update', handleStockUpdate)
      }
      if (enableAISignals) {
        realtimeService.off('ai_signal', handleAISignal)
      }
      if (enableAlerts) {
        realtimeService.off('alert', handleAlert)
      }
    }
  }, [enableStockUpdates, enableAISignals, enableAlerts]) // 移除函数依赖，避免循环

  // 获取特定股票的最新数据
  const getStockData = useCallback((stockCode: string) => {
    return {
      latestUpdate: data.stockUpdates.find(update => update.stock_code === stockCode),
      aiSignals: data.aiSignals.filter(signal => signal.stock_code === stockCode),
      alerts: data.alerts.filter(alert => alert.stock_code === stockCode)
    }
  }, [data])

  // 清除历史数据
  const clearData = useCallback(() => {
    setData(prev => ({
      ...prev,
      stockUpdates: [],
      aiSignals: [],
      alerts: []
    }))
  }, [])

  // 发送消息到服务器
  const sendMessage = useCallback((message: any) => {
    realtimeService.send(message)
  }, [])

  return {
    data,
    connectionStatus,
    getStockData,
    clearData,
    sendMessage,
    // 便捷的数据访问
    stockUpdates: data.stockUpdates,
    aiSignals: data.aiSignals,
    alerts: data.alerts,
    isConnected: data.isConnected,
    // 统计信息
    stats: {
      totalStockUpdates: data.stockUpdates.length,
      totalAISignals: data.aiSignals.length,
      totalAlerts: data.alerts.length,
      unreadAlerts: data.alerts.filter(alert => 
        new Date(alert.timestamp).getTime() > Date.now() - 5 * 60 * 1000 // 5分钟内的算作未读
      ).length
    }
  }
}

// 专门用于股票价格监控的Hook
export const useStockPriceMonitor = (stockCodes: string[]) => {
  return useRealtimeData({
    enableStockUpdates: true,
    enableAISignals: false,
    enableAlerts: false,
    stockCodes
  })
}

// 专门用于AI信号监控的Hook
export const useAISignalMonitor = (stockCodes?: string[]) => {
  return useRealtimeData({
    enableStockUpdates: false,
    enableAISignals: true,
    enableAlerts: false,
    stockCodes
  })
}

// 专门用于预警监控的Hook
export const useAlertMonitor = (stockCodes?: string[]) => {
  return useRealtimeData({
    enableStockUpdates: false,
    enableAISignals: false,
    enableAlerts: true,
    stockCodes
  })
}

export default useRealtimeData
