import apiClient, { ApiResponse } from './authService'

// 股票数据相关API
export const stockService = {
  // 获取股票列表
  async getStockList(params?: {
    page?: number
    limit?: number
    search?: string
    sector?: string
  }): Promise<ApiResponse> {
    const response = await apiClient.get('/stocks/list', { params })
    return response.data
  },

  // 搜索股票
  async searchStocks(query: string, limit?: number): Promise<ApiResponse> {
    const params = { q: query, limit }
    const response = await apiClient.get('/stocks/search', { params })
    return response.data
  },

  // 获取股票详情
  async getStockDetail(stockCode: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/stocks/${stockCode}/info`)
    return response.data
  },

  // 获取股票基本面数据
  async getStockFundamentals(stockCode: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/stocks/${stockCode}/fundamentals`)
    return response.data
  },

  // 获取K线数据
  async getKlineData(
    stockCode: string,
    period: string = 'daily',
    startDate?: string,
    endDate?: string,
    limit?: number
  ): Promise<ApiResponse> {
    const params = {
      period,
      start_date: startDate,
      end_date: endDate,
      limit,
    }
    const response = await apiClient.get(`/stocks/${stockCode}/kline`, { params })
    return response.data
  },

  // 获取技术指标
  async getIndicators(
    stockCode: string,
    period: string = 'daily',
    indicators?: string[],
    limit?: number
  ): Promise<ApiResponse> {
    const params = {
      period,
      indicators: indicators?.join(','),
      limit,
    }
    const response = await apiClient.get(`/stocks/${stockCode}/indicators`, { params })
    return response.data
  },

  // 获取实时行情
  async getRealTimeQuote(stockCodes: string[]): Promise<ApiResponse> {
    const response = await apiClient.post('/stocks/realtime', {
      stock_codes: stockCodes,
    })
    return response.data
  },

  // 获取单个股票实时数据
  async getRealtimeData(stockCode: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/stocks/${stockCode}/realtime`)
    return response.data
  },

  // 获取股票完整实时行情数据（包含成交量、换手率、市值等）
  async getStockSpotData(stockCode: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/akshare/spot-data?stock_codes=${stockCode}&limit=1`)
    return response.data
  },
}

// AI预测相关API
export const aiService = {
  // 获取AI预测
  async getPredictions(
    stockCode?: string,
    predictionType?: string,
    limit?: number
  ): Promise<ApiResponse> {
    const params = {
      stock_code: stockCode,
      prediction_type: predictionType,
      limit,
    }
    const response = await apiClient.get('/ai/predictions', { params })
    return response.data
  },

  // 生成AI预测
  async generatePrediction(data: {
    stock_code: string
    prediction_type: string
    time_horizon: number
  }): Promise<ApiResponse> {
    const response = await apiClient.post('/ai/predict', data)
    return response.data
  },

  // 获取AI分析报告
  async getAnalysisReport(stockCode: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/ai/analysis/${stockCode}`)
    return response.data
  },
}

// 智能选股相关API
export const screenerService = {
  // 获取选股策略
  async getStrategies(): Promise<ApiResponse> {
    const response = await apiClient.get('/screener/strategies')
    return response.data
  },

  // 运行选股
  async runScreener(data: {
    strategy_name: string
    filters: any
    limit?: number
  }): Promise<ApiResponse> {
    const response = await apiClient.post('/screener/run', data)
    return response.data
  },

  // 获取选股结果
  async getScreenerResults(
    strategyName?: string,
    limit?: number
  ): Promise<ApiResponse> {
    const params = {
      strategy_name: strategyName,
      limit,
    }
    const response = await apiClient.get('/screener/results', { params })
    return response.data
  },
}

// 预警系统相关API
export const alertService = {
  // 获取预警规则
  async getAlertRules(): Promise<ApiResponse> {
    const response = await apiClient.get('/alerts/rules')
    return response.data
  },

  // 创建预警规则
  async createAlertRule(data: {
    rule_name: string
    rule_type: string
    stock_code: string
    conditions: any
    notification_methods: string[]
  }): Promise<ApiResponse> {
    const response = await apiClient.post('/alerts/rules', data)
    return response.data
  },

  // 更新预警规则
  async updateAlertRule(ruleId: number, data: any): Promise<ApiResponse> {
    const response = await apiClient.put(`/alerts/rules/${ruleId}`, data)
    return response.data
  },

  // 删除预警规则
  async deleteAlertRule(ruleId: number): Promise<ApiResponse> {
    const response = await apiClient.delete(`/alerts/rules/${ruleId}`)
    return response.data
  },

  // 获取预警历史
  async getAlertHistory(
    ruleId?: number,
    limit?: number
  ): Promise<ApiResponse> {
    const params = {
      rule_id: ruleId,
      limit,
    }
    const response = await apiClient.get('/alerts/history', { params })
    return response.data
  },
}

// 统计分析相关API
export const analyticsService = {
  // 创建投资组合
  async createPortfolio(data: {
    name: string
    description?: string
    portfolio_type: string
    initial_capital: number
  }): Promise<ApiResponse> {
    const response = await apiClient.post('/analytics/portfolios', data)
    return response.data
  },

  // 获取投资组合列表
  async getPortfolios(): Promise<ApiResponse> {
    const response = await apiClient.get('/analytics/portfolios')
    return response.data
  },

  // 添加交易记录
  async addTransaction(portfolioId: number, data: {
    stock_code: string
    transaction_type: string
    shares: number
    price: number
    amount: number
    commission?: number
    tax?: number
    total_cost: number
    reason?: string
    strategy?: string
    transaction_date?: string
  }): Promise<ApiResponse> {
    const response = await apiClient.post(`/analytics/portfolios/${portfolioId}/transactions`, data)
    return response.data
  },

  // 获取投资组合表现
  async getPortfolioPerformance(
    portfolioId: number,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse> {
    const params = {
      start_date: startDate,
      end_date: endDate,
    }
    const response = await apiClient.get(`/analytics/portfolios/${portfolioId}/performance`, { params })
    return response.data
  },

  // 获取风险指标
  async getRiskMetrics(
    portfolioId: number,
    periodDays: number = 252
  ): Promise<ApiResponse> {
    const params = { period_days: periodDays }
    const response = await apiClient.get(`/analytics/portfolios/${portfolioId}/risk-metrics`, { params })
    return response.data
  },

  // 运行回测
  async runBacktest(data: {
    strategy_name: string
    start_date: string
    end_date: string
    initial_capital: number
    stock_pool?: string[]
    [key: string]: any
  }): Promise<ApiResponse> {
    const response = await apiClient.post('/analytics/backtest', data)
    return response.data
  },

  // 获取回测结果
  async getBacktestResult(backtestId: number): Promise<ApiResponse> {
    const response = await apiClient.get(`/analytics/backtest/${backtestId}`)
    return response.data
  },

  // 获取回测历史
  async getBacktestHistory(
    strategyName?: string,
    limit?: number
  ): Promise<ApiResponse> {
    const params = {
      strategy_name: strategyName,
      limit,
    }
    const response = await apiClient.get('/analytics/backtest', { params })
    return response.data
  },

  // 获取可用策略
  async getStrategies(): Promise<ApiResponse> {
    const response = await apiClient.get('/analytics/strategies')
    return response.data
  },
}

// 自选股相关API
export const watchlistService = {
  // 获取自选股列表
  async getWatchlist(): Promise<ApiResponse> {
    const response = await apiClient.get('/watchlist')
    return response.data
  },

  // 添加自选股
  async addToWatchlist(stockCode: string, notes?: string): Promise<ApiResponse> {
    const response = await apiClient.post('/watchlist', {
      stock_code: stockCode,
      notes,
    })
    return response.data
  },

  // 删除自选股
  async removeFromWatchlist(stockCode: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/watchlist/${stockCode}`)
    return response.data
  },

  // 更新自选股备注
  async updateWatchlistNotes(stockCode: string, notes: string): Promise<ApiResponse> {
    const response = await apiClient.put(`/watchlist/${stockCode}`, { notes })
    return response.data
  },
}
