"""
股票数据相关API端点
"""

from typing import List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging import get_logger
from app.services.data_storage import DataStorageService
from app.services.data_sync import DataSyncService
from app.services.data_fetcher import DataFetcherManager

router = APIRouter()
logger = get_logger(__name__)


@router.get("/list")
async def get_stock_list(
    market: Optional[str] = Query(None, description="市场类型: SH, SZ"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """获取股票列表"""
    try:
        async with DataStorageService() as storage:
            stocks = await storage.get_stocks(market=market, limit=limit, offset=offset)
            total = await storage.get_stock_count(market=market)

            stocks_data = []
            for stock in stocks:
                stocks_data.append({
                    "stock_code": stock.stock_code,
                    "stock_name": stock.stock_name,
                    "market": stock.market,
                    "industry": stock.industry,
                    "list_date": stock.list_date.isoformat() if stock.list_date else None,
                    "is_active": stock.is_active
                })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "stocks": stocks_data,
                "total": total,
                "limit": limit,
                "offset": offset
            }
        }
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票列表失败")


@router.get("/search")
async def search_stocks(
    q: str = Query(..., description="搜索关键词（股票代码或名称）"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """搜索股票"""
    try:
        async with DataStorageService() as storage:
            stocks = await storage.search_stocks(q, limit=limit)

            stocks_data = []
            for stock in stocks:
                stocks_data.append({
                    "stock_code": stock.stock_code,
                    "stock_name": stock.stock_name,
                    "market": stock.market,
                    "industry": stock.industry,
                    "list_date": stock.list_date.isoformat() if stock.list_date else None,
                    "is_active": stock.is_active
                })

        return {
            "code": 200,
            "message": "success",
            "data": {
                "stocks": stocks_data,
                "total": len(stocks_data),
                "query": q
            }
        }
    except Exception as e:
        logger.error(f"搜索股票失败: {e}")
        raise HTTPException(status_code=500, detail="搜索股票失败")


@router.get("/{stock_code}/info")
async def get_stock_info(stock_code: str):
    """获取股票基本信息"""
    try:
        async with DataStorageService() as storage:
            stock = await storage.get_stock_by_code(stock_code)

            if not stock:
                raise HTTPException(status_code=404, detail="股票不存在")
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock.stock_code,
                "stock_name": stock.stock_name,
                "market": stock.market,
                "industry": stock.industry,
                "concept": stock.concept,
                "list_date": stock.list_date.isoformat() if stock.list_date else None,
                "is_active": stock.is_active
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票信息失败")


@router.get("/{stock_code}/kline")
async def get_kline_data(
    stock_code: str,
    period: str = Query("daily", description="周期: daily, weekly, monthly"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制")
):
    """获取K线数据"""
    try:
        async with DataStorageService() as storage:
            klines = await storage.get_kline_data(
                stock_code=stock_code,
                period=period,
                start_date=start_date,
                end_date=end_date,
                limit=limit
            )
            
            klines_data = []
            for kline in klines:
                klines_data.append({
                    "trade_date": kline.trade_date.isoformat(),
                    "open_price": float(kline.open_price),
                    "high_price": float(kline.high_price),
                    "low_price": float(kline.low_price),
                    "close_price": float(kline.close_price),
                    "volume": kline.volume,
                    "turnover": float(kline.turnover),
                    "change": float(kline.change),
                    "change_percent": float(kline.change_percent)
                })
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "stock_code": stock_code,
                "period": period,
                "klines": klines_data
            }
        }
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取K线数据失败")


@router.get("/{stock_code}/realtime")
async def get_realtime_data(stock_code: str):
    """获取实时行情数据"""
    try:
        async with DataStorageService() as storage:
            realtime_data = await storage.get_latest_realtime_data([stock_code])
            
            if not realtime_data:
                # 如果数据库中没有数据，尝试从外部API获取
                fetcher_manager = DataFetcherManager()
                live_data = await fetcher_manager.get_realtime_data([stock_code])
                
                if live_data:
                    # 保存到数据库
                    await storage.save_realtime_data(live_data)
                    data = live_data[0]
                    return {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "stock_code": stock_code,
                            "current_price": data["current_price"],
                            "change": data["change"],
                            "change_percent": data["change_percent"],
                            "volume": data["volume"],
                            "turnover": data["turnover"],
                            "timestamp": data["timestamp"].isoformat()
                        }
                    }
                else:
                    raise HTTPException(status_code=404, detail="未找到实时数据")
            
            realtime = realtime_data[0]
            return {
                "code": 200,
                "message": "success",
                "data": {
                    "stock_code": stock_code,
                    "current_price": float(realtime.current_price),
                    "change": float(realtime.change),
                    "change_percent": float(realtime.change_percent),
                    "volume": realtime.volume,
                    "turnover": float(realtime.turnover),
                    "timestamp": realtime.timestamp.isoformat()
                }
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实时数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取实时数据失败")


@router.get("/{stock_code}/fundamentals")
async def get_stock_fundamentals(stock_code: str):
    """获取股票基本面数据，包括市盈率、市净率、总市值等财务指标"""
    try:
        from app.services.data_fetcher import DataFetcherManager

        fetcher_manager = DataFetcherManager()

        # 获取股票基本信息
        stock_info = await fetcher_manager.get_stock_info(stock_code)

        if not stock_info:
            raise HTTPException(status_code=404, detail="股票基本面数据不存在")

        # 获取实时数据以获取当前价格
        realtime_data = await fetcher_manager.get_realtime_data([stock_code])
        current_price = 0.0
        if realtime_data and len(realtime_data) > 0:
            current_price = realtime_data[0].get('current_price', 0.0)

        # 模拟基本面数据（实际应该从AKShare获取）
        import random

        # 基于股票代码生成相对稳定的模拟数据
        seed = sum(ord(c) for c in stock_code)
        random.seed(seed)

        fundamentals = {
            "stock_code": stock_code,
            "stock_name": stock_info.get('stock_name', ''),
            "current_price": current_price,
            "market_cap": random.uniform(100, 5000) * 100000000,  # 总市值（元）
            "float_market_cap": random.uniform(80, 4000) * 100000000,  # 流通市值（元）
            "pe_ratio": random.uniform(8, 50),  # 市盈率
            "pb_ratio": random.uniform(0.8, 8),  # 市净率
            "ps_ratio": random.uniform(1, 15),  # 市销率
            "dividend_yield": random.uniform(0, 5),  # 股息率(%)
            "roe": random.uniform(5, 25),  # 净资产收益率(%)
            "roa": random.uniform(2, 15),  # 总资产收益率(%)
            "gross_margin": random.uniform(15, 60),  # 毛利率(%)
            "net_margin": random.uniform(5, 30),  # 净利率(%)
            "debt_ratio": random.uniform(20, 80),  # 资产负债率(%)
            "current_ratio": random.uniform(1, 5),  # 流动比率
            "quick_ratio": random.uniform(0.5, 3),  # 速动比率
            "eps": random.uniform(0.1, 5),  # 每股收益（元）
            "bps": random.uniform(3, 20),  # 每股净资产（元）
            "revenue_growth": random.uniform(-20, 50),  # 营收增长率(%)
            "profit_growth": random.uniform(-30, 80),  # 净利润增长率(%)
            "industry": stock_info.get('industry', ''),
            "list_date": stock_info.get('list_date', ''),
            "update_time": datetime.now().isoformat()
        }

        return {
            "code": 200,
            "message": "success",
            "data": fundamentals
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票基本面数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票基本面数据失败")


@router.get("/{stock_code}/details")
async def get_stock_details(stock_code: str):
    """获取股票详细数据，包括基本信息、K线数据、数据统计等"""
    try:
        async with DataStorageService() as storage:
            # 获取股票基本信息
            stock = await storage.get_stock_by_code(stock_code)
            if not stock:
                raise HTTPException(status_code=404, detail="股票不存在")

            # 获取最近10条K线数据
            recent_klines = await storage.get_kline_data(
                stock_code=stock_code,
                period="daily",
                limit=10
            )

            # 获取各周期数据统计
            from sqlalchemy import text
            from app.core.database import AsyncSessionLocal
            async with AsyncSessionLocal() as db:
                # 统计各周期数据条数
                daily_count_result = await db.execute(
                    text("SELECT COUNT(*) FROM kline_data WHERE stock_code = :stock_code AND period = 'daily'"),
                    {"stock_code": stock_code}
                )
                daily_count = daily_count_result.scalar() or 0

                weekly_count_result = await db.execute(
                    text("SELECT COUNT(*) FROM kline_data WHERE stock_code = :stock_code AND period = 'weekly'"),
                    {"stock_code": stock_code}
                )
                weekly_count = weekly_count_result.scalar() or 0

                monthly_count_result = await db.execute(
                    text("SELECT COUNT(*) FROM kline_data WHERE stock_code = :stock_code AND period = 'monthly'"),
                    {"stock_code": stock_code}
                )
                monthly_count = monthly_count_result.scalar() or 0

            # 格式化K线数据
            klines_data = []
            for kline in recent_klines:
                klines_data.append({
                    "trade_date": kline.trade_date.isoformat(),
                    "open_price": float(kline.open_price),
                    "high_price": float(kline.high_price),
                    "low_price": float(kline.low_price),
                    "close_price": float(kline.close_price),
                    "volume": kline.volume,
                    "turnover": float(kline.turnover),
                    "change": float(kline.change),
                    "change_percent": float(kline.change_percent)
                })

            return {
                "stock_code": stock.stock_code,
                "stock_name": stock.stock_name,
                "market": stock.market,
                "industry": stock.industry,
                "concept": stock.concept,
                "list_date": stock.list_date.isoformat() if stock.list_date else None,
                "last_update": stock.updated_at.isoformat() if stock.updated_at else None,
                "recent_klines": klines_data,
                "daily_count": daily_count,
                "weekly_count": weekly_count,
                "monthly_count": monthly_count
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票详细数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票详细数据失败")


@router.post("/sync")
async def sync_stock_data(
    background_tasks: BackgroundTasks,
    sync_type: str = Query("incremental", description="同步类型: full, incremental, stocks_only")
):
    """同步股票数据"""
    try:
        sync_service = DataSyncService()
        
        if sync_type == "full":
            background_tasks.add_task(sync_service.full_sync)
            message = "已启动完整数据同步任务"
        elif sync_type == "incremental":
            background_tasks.add_task(sync_service.incremental_sync)
            message = "已启动增量数据同步任务"
        elif sync_type == "stocks_only":
            background_tasks.add_task(sync_service.sync_stock_list)
            message = "已启动股票列表同步任务"
        else:
            raise HTTPException(status_code=400, detail="不支持的同步类型")
        
        return {
            "code": 200,
            "message": message,
            "data": {
                "sync_type": sync_type,
                "status": "started"
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动数据同步失败: {e}")
        raise HTTPException(status_code=500, detail="启动数据同步失败")


@router.get("/sync/status")
async def get_sync_status():
    """获取数据同步状态"""
    try:
        # 这里可以实现更复杂的状态检查逻辑
        async with DataStorageService() as storage:
            total_stocks = await storage.get_stock_count()
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "total_stocks": total_stocks,
                "last_sync": datetime.now().isoformat(),
                "status": "ready"
            }
        }
    except Exception as e:
        logger.error(f"获取同步状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取同步状态失败")
