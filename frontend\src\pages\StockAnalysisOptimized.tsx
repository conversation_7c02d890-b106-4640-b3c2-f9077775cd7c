import React, { useState, useEffect } from 'react'
import {
  Card,
  Input,
  Button,
  Table,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Tabs,
  message,
  Spin,
  Alert,
  Badge,
  Tooltip,
  Progress,
} from 'antd'
import {
  SearchOutlined,
  StarOutlined,
  StarFilled,
  ArrowUpOutlined,
  ArrowDownOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  LineChartOutlined,
  Bar<PERSON>hartOutlined,
  DashboardOutlined,
} from '@ant-design/icons'
import { useRealtimeData } from '@/hooks/useRealtimeData'
import { useStockDataSimple } from '@/hooks/useStockDataSimple'
import TrendChart from '@/components/charts/TrendChart'
import CandlestickChart from '@/components/charts/CandlestickChart'

const { Title, Text } = Typography
const { TabPane } = Tabs

const StockAnalysisOptimized: React.FC = () => {
  const [searchValue, setSearchValue] = useState('')
  const [searchLoading, setSearchLoading] = useState(false)
  
  // 使用简化的股票数据Hook
  const {
    stockList,
    selectedStock,
    indicators,
    historyData,
    loading,
    stats,
    fetchStockList,
    selectStock,
    toggleWatchlist,
    refreshData,
  } = useStockDataSimple()
  
  // 实时数据Hook
  const { data: realtimeData, isConnected } = useRealtimeData({
    enableStockUpdates: true,
    stockCodes: selectedStock ? [selectedStock.code] : []
  })

  // 初始化加载股票列表
  useEffect(() => {
    fetchStockList()
  }, []) // 移除依赖项，只在组件挂载时执行一次

  // 搜索处理
  const handleSearch = async () => {
    if (!searchValue.trim()) {
      message.warning('请输入股票代码或名称')
      return
    }
    
    setSearchLoading(true)
    try {
      await fetchStockList(searchValue)
      if (stockList.length === 0) {
        message.warning('未找到相关股票')
      }
    } finally {
      setSearchLoading(false)
    }
  }

  // 股票列表表格列定义
  const columns = [
    {
      title: '股票代码',
      dataIndex: 'code',
      key: 'code',
      width: 100,
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      render: (text: string, record: any) => (
        <Button
          type="link"
          onClick={() => selectStock(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '现价',
      dataIndex: 'current_price',
      key: 'current_price',
      width: 80,
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '涨跌幅',
      dataIndex: 'change_percent',
      key: 'change_percent',
      width: 80,
      render: (percent: number, record: any) => (
        <Tag color={percent >= 0 ? 'red' : 'green'}>
          {percent >= 0 ? '+' : ''}{percent.toFixed(2)}%
        </Tag>
      ),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      width: 100,
      render: (volume: number) => `${(volume / 100000000).toFixed(2)}亿`,
    },
    {
      title: '市值',
      dataIndex: 'market_cap',
      key: 'market_cap',
      width: 100,
      render: (cap: number) => `${(cap / 100000000).toFixed(0)}亿`,
    },
    {
      title: '板块',
      dataIndex: 'sector',
      key: 'sector',
      width: 80,
      render: (sector: string) => <Tag>{sector}</Tag>,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: any) => (
        <Button
          type="text"
          icon={record.is_watched ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
          onClick={() => toggleWatchlist(record)}
        />
      ),
    },
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>股票分析</Title>
      
      {/* 搜索区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Space.Compact style={{ width: '100%' }}>
          <Input
            placeholder="请输入股票代码或名称"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onPressEnter={handleSearch}
            style={{ width: 'calc(100% - 120px)' }}
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            loading={searchLoading}
            onClick={handleSearch}
          >
            搜索
          </Button>
        </Space.Compact>
        
        {/* 统计信息 */}
        <div style={{ marginTop: 16 }}>
          <Space>
            <Badge count={stats.totalStocks} showZero>
              <Tag>总股票数</Tag>
            </Badge>
            <Badge count={stats.watchedStocks} showZero>
              <Tag>自选股</Tag>
            </Badge>
            <Badge count={stats.gainers} showZero>
              <Tag color="green">上涨</Tag>
            </Badge>
            <Badge count={stats.losers} showZero>
              <Tag color="red">下跌</Tag>
            </Badge>
            <Badge 
              status={isConnected ? 'success' : 'error'} 
              text={isConnected ? '实时连接' : '连接断开'}
            />
            <Button size="small" onClick={refreshData} icon={<ReloadOutlined />}>
              刷新
            </Button>
          </Space>
        </div>
      </Card>

      <Row gutter={24}>
        {/* 股票列表 */}
        <Col span={10}>
          <Card title="股票列表" size="small">
            <Table
              columns={columns}
              dataSource={stockList}
              rowKey="code"
              loading={loading.stockList}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
              }}
              size="small"
            />
          </Card>
        </Col>

        {/* 股票详情 */}
        <Col span={14}>
          {selectedStock ? (
            <Card
              title={
                <Space>
                  <Text strong>{selectedStock.name} ({selectedStock.code})</Text>
                  <Tag color={selectedStock.change >= 0 ? 'green' : 'red'}>
                    {selectedStock.change >= 0 ? '+' : ''}{selectedStock.change_percent.toFixed(2)}%
                  </Tag>
                  <Button
                    type="text"
                    icon={selectedStock.is_watched ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                    onClick={() => toggleWatchlist(selectedStock)}
                  />
                </Space>
              }
              size="small"
            >
              {/* 统计信息 */}
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={4}>
                  <Statistic
                    title="现价"
                    value={selectedStock.current_price}
                    precision={2}
                    prefix="¥"
                    valueStyle={{
                      color: selectedStock.change >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="涨跌额"
                    value={selectedStock.change}
                    precision={2}
                    prefix={selectedStock.change >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    valueStyle={{
                      color: selectedStock.change >= 0 ? 'rgb(214, 10, 34)' : 'rgb(3, 123, 102)'
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="最高"
                    value={selectedStock.high}
                    precision={2}
                    prefix="¥"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="最低"
                    value={selectedStock.low}
                    precision={2}
                    prefix="¥"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="成交量"
                    value={selectedStock.volume / 100000000}
                    precision={2}
                    suffix="亿"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="振幅"
                    value={selectedStock.amplitude || 0}
                    precision={2}
                    suffix="%"
                  />
                </Col>
              </Row>

              {/* 实时连接状态 */}
              <div style={{ marginBottom: 16 }}>
                <Space>
                  <Badge 
                    status={isConnected ? 'success' : 'error'} 
                    text={isConnected ? '实时数据连接正常' : '实时数据连接断开'}
                  />
                  {realtimeData.stockUpdates.length > 0 && (
                    <Tag color="blue">
                      最新更新: {new Date(realtimeData.stockUpdates[0].timestamp).toLocaleTimeString()}
                    </Tag>
                  )}
                </Space>
              </div>

              {/* 标签页内容 */}
              <Tabs defaultActiveKey="chart">
                <TabPane tab={
                  <span>
                    <DashboardOutlined />
                    实时行情
                  </span>
                } key="chart">
                  {historyData.length > 0 ? (
                    <TrendChart
                      title="价格走势图"
                      data={historyData.slice(-20).map(item => ({
                        time: item.date.slice(-5),
                        value: item.close,
                        change: item.close - item.open,
                        changePercent: ((item.close - item.open) / item.open) * 100,
                      }))}
                      height={300}
                      color={selectedStock.change >= 0 ? '#3f8600' : '#cf1322'}
                    />
                  ) : (
                    <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Spin size="large" />
                    </div>
                  )}
                </TabPane>

                <TabPane tab={
                  <span>
                    <BarChartOutlined />
                    技术指标
                    {loading.indicators && <Spin size="small" style={{ marginLeft: 8 }} />}
                  </span>
                } key="indicators">
                  {indicators ? (
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Card size="small" title="移动平均线(MA)">
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>MA5:</Text>
                              <Text strong>{indicators.ma.ma5.toFixed(2)}</Text>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text>MA20:</Text>
                              <Text strong>{indicators.ma.ma20.toFixed(2)}</Text>
                            </div>
                          </Space>
                        </Card>
                      </Col>
                      <Col span={12}>
                        <Card size="small" title="RSI指标">
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text>RSI14:</Text>
                            <Progress 
                              percent={indicators.rsi.rsi12} 
                              size="small" 
                              style={{ width: 100 }}
                            />
                          </div>
                        </Card>
                      </Col>
                    </Row>
                  ) : (
                    <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Text type="secondary">加载技术指标中...</Text>
                    </div>
                  )}
                </TabPane>
              </Tabs>
            </Card>
          ) : (
            <Card>
              <div style={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Text type="secondary">请选择股票查看详细信息</Text>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  )
}

export default StockAnalysisOptimized
