"""
任务管理相关API端点
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging import get_logger
from app.core.celery_app import celery_app
from app.tasks import data_tasks, analysis_tasks, notification_tasks

router = APIRouter()
logger = get_logger(__name__)


@router.get("/status")
async def get_celery_status():
    """获取Celery状态"""
    try:
        # 检查Celery连接状态
        inspect = celery_app.control.inspect()
        
        # 获取活跃任务
        active_tasks = inspect.active()
        
        # 获取已注册任务
        registered_tasks = inspect.registered()
        
        # 获取统计信息
        stats = inspect.stats()
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "celery_status": "connected" if active_tasks is not None else "disconnected",
                "active_tasks": active_tasks or {},
                "registered_tasks": registered_tasks or {},
                "worker_stats": stats or {},
                "queues": ["data", "analysis", "notifications", "celery"]
            }
        }
    except Exception as e:
        logger.error(f"获取Celery状态失败: {e}")
        return {
            "code": 500,
            "message": "Celery连接失败",
            "data": {
                "celery_status": "disconnected",
                "error": str(e)
            }
        }


@router.post("/data/sync-stocks")
async def trigger_sync_stocks():
    """触发股票列表同步任务"""
    try:
        task = data_tasks.sync_stock_list_task.delay()
        
        return {
            "code": 200,
            "message": "股票列表同步任务已启动",
            "data": {
                "task_id": task.id,
                "task_name": "sync_stock_list_task",
                "status": "pending"
            }
        }
    except Exception as e:
        logger.error(f"启动股票列表同步任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")


@router.post("/data/sync-klines")
async def trigger_sync_klines(
    stock_codes: Optional[List[str]] = Query(None, description="股票代码列表"),
    days: int = Query(1, ge=1, le=30, description="同步天数")
):
    """触发K线数据同步任务"""
    try:
        task = data_tasks.sync_kline_data_task.delay(stock_codes, days)
        
        return {
            "code": 200,
            "message": "K线数据同步任务已启动",
            "data": {
                "task_id": task.id,
                "task_name": "sync_kline_data_task",
                "stock_codes": stock_codes,
                "days": days,
                "status": "pending"
            }
        }
    except Exception as e:
        logger.error(f"启动K线数据同步任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")


@router.post("/data/sync-realtime")
async def trigger_sync_realtime(
    stock_codes: Optional[List[str]] = Query(None, description="股票代码列表")
):
    """触发实时数据同步任务"""
    try:
        task = data_tasks.sync_realtime_data_task.delay(stock_codes)
        
        return {
            "code": 200,
            "message": "实时数据同步任务已启动",
            "data": {
                "task_id": task.id,
                "task_name": "sync_realtime_data_task",
                "stock_codes": stock_codes,
                "status": "pending"
            }
        }
    except Exception as e:
        logger.error(f"启动实时数据同步任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")


@router.post("/data/quality-check")
async def trigger_quality_check():
    """触发数据质量检查任务"""
    try:
        task = data_tasks.data_quality_check_task.delay()
        
        return {
            "code": 200,
            "message": "数据质量检查任务已启动",
            "data": {
                "task_id": task.id,
                "task_name": "data_quality_check_task",
                "status": "pending"
            }
        }
    except Exception as e:
        logger.error(f"启动数据质量检查任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")


@router.post("/analysis/technical-indicators")
async def trigger_technical_analysis(
    stock_code: str = Query(..., description="股票代码"),
    indicators: Optional[List[str]] = Query(None, description="指标列表")
):
    """触发技术指标计算任务"""
    try:
        task = analysis_tasks.calculate_technical_indicators_task.delay(stock_code, indicators)
        
        return {
            "code": 200,
            "message": "技术指标计算任务已启动",
            "data": {
                "task_id": task.id,
                "task_name": "calculate_technical_indicators_task",
                "stock_code": stock_code,
                "indicators": indicators,
                "status": "pending"
            }
        }
    except Exception as e:
        logger.error(f"启动技术指标计算任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")


@router.post("/analysis/ai-prediction")
async def trigger_ai_prediction(
    stock_code: str = Query(..., description="股票代码"),
    prediction_days: int = Query(5, ge=1, le=30, description="预测天数")
):
    """触发AI预测任务"""
    try:
        task = analysis_tasks.ai_prediction_task.delay(stock_code, prediction_days)
        
        return {
            "code": 200,
            "message": "AI预测任务已启动",
            "data": {
                "task_id": task.id,
                "task_name": "ai_prediction_task",
                "stock_code": stock_code,
                "prediction_days": prediction_days,
                "status": "pending"
            }
        }
    except Exception as e:
        logger.error(f"启动AI预测任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")


@router.get("/result/{task_id}")
async def get_task_result(task_id: str):
    """获取任务结果"""
    try:
        # 获取任务结果
        result = celery_app.AsyncResult(task_id)
        
        task_info = {
            "task_id": task_id,
            "status": result.status,
            "result": result.result if result.ready() else None,
            "traceback": result.traceback if result.failed() else None,
            "date_done": result.date_done.isoformat() if result.date_done else None
        }
        
        return {
            "code": 200,
            "message": "success",
            "data": task_info
        }
    except Exception as e:
        logger.error(f"获取任务结果失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务结果失败")


@router.delete("/cancel/{task_id}")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        return {
            "code": 200,
            "message": "任务已取消",
            "data": {
                "task_id": task_id,
                "action": "cancelled"
            }
        }
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail="取消任务失败")


@router.get("/active")
async def get_active_tasks():
    """获取活跃任务列表"""
    try:
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()
        
        if active_tasks is None:
            return {
                "code": 500,
                "message": "无法连接到Celery worker",
                "data": {"active_tasks": []}
            }
        
        # 格式化活跃任务信息
        formatted_tasks = []
        for worker, tasks in active_tasks.items():
            for task in tasks:
                formatted_tasks.append({
                    "worker": worker,
                    "task_id": task["id"],
                    "task_name": task["name"],
                    "args": task["args"],
                    "kwargs": task["kwargs"],
                    "time_start": task["time_start"]
                })
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "active_tasks": formatted_tasks,
                "total_count": len(formatted_tasks)
            }
        }
    except Exception as e:
        logger.error(f"获取活跃任务失败: {e}")
        raise HTTPException(status_code=500, detail="获取活跃任务失败")


@router.get("/scheduled")
async def get_scheduled_tasks():
    """获取定时任务列表"""
    try:
        # 获取定时任务配置
        beat_schedule = celery_app.conf.beat_schedule
        
        scheduled_tasks = []
        for task_name, config in beat_schedule.items():
            task_info = {
                "name": task_name,
                "task": config["task"],
                "schedule": str(config["schedule"]),
                "enabled": True,  # 简化处理，实际可以从数据库获取
                "last_run": None,  # 需要从beat数据库获取
                "next_run": None   # 需要计算
            }
            scheduled_tasks.append(task_info)
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "scheduled_tasks": scheduled_tasks,
                "total_count": len(scheduled_tasks)
            }
        }
    except Exception as e:
        logger.error(f"获取定时任务失败: {e}")
        raise HTTPException(status_code=500, detail="获取定时任务失败")
