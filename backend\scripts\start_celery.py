#!/usr/bin/env python3
"""
Celery启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from app.core.logging import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


def start_celery_worker():
    """启动Celery Worker"""
    logger.info("启动Celery Worker...")
    
    cmd = [
        "celery", "-A", "app.core.celery_app", "worker",
        "--loglevel=info",
        "--queues=data,analysis,notifications,celery",
        "--concurrency=4"
    ]
    
    try:
        subprocess.run(cmd, cwd=project_root, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Celery Worker启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Celery Worker已停止")


def start_celery_beat():
    """启动Celery Beat (定时任务调度器)"""
    logger.info("启动Celery Beat...")
    
    cmd = [
        "celery", "-A", "app.core.celery_app", "beat",
        "--loglevel=info"
    ]
    
    try:
        subprocess.run(cmd, cwd=project_root, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Celery Beat启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Celery Beat已停止")


def start_celery_flower():
    """启动Celery Flower (监控界面)"""
    logger.info("启动Celery Flower...")
    
    cmd = [
        "celery", "-A", "app.core.celery_app", "flower",
        "--port=5555"
    ]
    
    try:
        subprocess.run(cmd, cwd=project_root, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Celery Flower启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Celery Flower已停止")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python start_celery.py [worker|beat|flower]")
        print("  worker - 启动任务执行器")
        print("  beat   - 启动定时任务调度器")
        print("  flower - 启动监控界面")
        sys.exit(1)
    
    component = sys.argv[1].lower()
    
    if component == "worker":
        start_celery_worker()
    elif component == "beat":
        start_celery_beat()
    elif component == "flower":
        start_celery_flower()
    else:
        print(f"未知组件: {component}")
        print("支持的组件: worker, beat, flower")
        sys.exit(1)


if __name__ == "__main__":
    main()
