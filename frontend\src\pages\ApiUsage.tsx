import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Table,
  Progress,
  Typography,
  Space,
  Tag,
  Button,
  Select,
  DatePicker,
  Statistic,
  Alert,
  Tooltip,
} from 'antd'
import {
  ApiOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface ApiEndpoint {
  name: string
  path: string
  method: string
  calls: number
  limit: number
  avgResponseTime: number
  successRate: number
  lastUsed: string
}

interface ApiUsageRecord {
  id: string
  timestamp: string
  endpoint: string
  method: string
  status: number
  responseTime: number
  ip: string
}

const ApiUsage: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('today')
  const [endpoints, setEndpoints] = useState<ApiEndpoint[]>([])
  const [usageRecords, setUsageRecords] = useState<ApiUsageRecord[]>([])

  useEffect(() => {
    loadApiUsage()
  }, [timeRange])

  const loadApiUsage = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const mockEndpoints: ApiEndpoint[] = [
        {
          name: '股票基本信息',
          path: '/api/v1/stocks/info',
          method: 'GET',
          calls: 45,
          limit: 1000,
          avgResponseTime: 120,
          successRate: 99.2,
          lastUsed: '2024-01-28 19:30:15',
        },
        {
          name: '股票价格查询',
          path: '/api/v1/stocks/price',
          method: 'GET',
          calls: 123,
          limit: 1000,
          avgResponseTime: 85,
          successRate: 100,
          lastUsed: '2024-01-28 19:25:32',
        },
        {
          name: '技术指标计算',
          path: '/api/v1/indicators',
          method: 'POST',
          calls: 67,
          limit: 500,
          avgResponseTime: 340,
          successRate: 98.5,
          lastUsed: '2024-01-28 19:20:45',
        },
        {
          name: '历史数据查询',
          path: '/api/v1/stocks/history',
          method: 'GET',
          calls: 23,
          limit: 200,
          avgResponseTime: 450,
          successRate: 95.7,
          lastUsed: '2024-01-28 18:45:21',
        },
        {
          name: 'AI预测接口',
          path: '/api/v1/ai/predict',
          method: 'POST',
          calls: 8,
          limit: 50,
          avgResponseTime: 1200,
          successRate: 100,
          lastUsed: '2024-01-28 17:30:12',
        },
      ]

      const mockRecords: ApiUsageRecord[] = Array.from({ length: 50 }, (_, i) => ({
        id: `${i + 1}`,
        timestamp: dayjs().subtract(i * 10, 'minute').format('YYYY-MM-DD HH:mm:ss'),
        endpoint: mockEndpoints[i % mockEndpoints.length].path,
        method: mockEndpoints[i % mockEndpoints.length].method,
        status: Math.random() > 0.05 ? 200 : Math.random() > 0.5 ? 400 : 500,
        responseTime: Math.floor(Math.random() * 1000 + 50),
        ip: '*************',
      }))

      setEndpoints(mockEndpoints)
      setUsageRecords(mockRecords)
    } catch (error) {
      console.error('Failed to load API usage:', error)
    } finally {
      setLoading(false)
    }
  }

  const endpointColumns = [
    {
      title: 'API接口',
      key: 'api',
      render: (record: ApiEndpoint) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.name}</Text>
          <Text code style={{ fontSize: '12px' }}>
            {record.method} {record.path}
          </Text>
        </Space>
      ),
    },
    {
      title: '调用次数',
      key: 'usage',
      render: (record: ApiEndpoint) => (
        <Space direction="vertical" size={0}>
          <Text>{record.calls} / {record.limit}</Text>
          <Progress
            percent={(record.calls / record.limit) * 100}
            size="small"
            strokeColor={
              (record.calls / record.limit) > 0.8 ? '#ff4d4f' : 
              (record.calls / record.limit) > 0.6 ? '#faad14' : '#52c41a'
            }
          />
        </Space>
      ),
      sorter: (a: ApiEndpoint, b: ApiEndpoint) => a.calls - b.calls,
    },
    {
      title: '平均响应时间',
      dataIndex: 'avgResponseTime',
      key: 'responseTime',
      render: (time: number) => (
        <Space>
          <Text>{time}ms</Text>
          {time > 1000 && (
            <Tooltip title="响应时间较长">
              <WarningOutlined style={{ color: '#faad14' }} />
            </Tooltip>
          )}
        </Space>
      ),
      sorter: (a: ApiEndpoint, b: ApiEndpoint) => a.avgResponseTime - b.avgResponseTime,
    },
    {
      title: '成功率',
      dataIndex: 'successRate',
      key: 'successRate',
      render: (rate: number) => (
        <Space>
          <Text>{rate.toFixed(1)}%</Text>
          {rate >= 99 ? (
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
          ) : rate >= 95 ? (
            <WarningOutlined style={{ color: '#faad14' }} />
          ) : (
            <ClockCircleOutlined style={{ color: '#ff4d4f' }} />
          )}
        </Space>
      ),
      sorter: (a: ApiEndpoint, b: ApiEndpoint) => a.successRate - b.successRate,
    },
    {
      title: '最后使用',
      dataIndex: 'lastUsed',
      key: 'lastUsed',
    },
  ]

  const recordColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 150,
    },
    {
      title: '接口',
      key: 'endpoint',
      render: (record: ApiUsageRecord) => (
        <Text code>{record.method} {record.endpoint}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <Tag color={
          status === 200 ? 'green' :
          status >= 400 && status < 500 ? 'orange' : 'red'
        }>
          {status}
        </Tag>
      ),
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      width: 100,
      render: (time: number) => `${time}ms`,
      sorter: (a: ApiUsageRecord, b: ApiUsageRecord) => a.responseTime - b.responseTime,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
    },
  ]

  const totalCalls = endpoints.reduce((sum, ep) => sum + ep.calls, 0)
  const totalLimit = endpoints.reduce((sum, ep) => sum + ep.limit, 0)
  const avgResponseTime = endpoints.reduce((sum, ep) => sum + ep.avgResponseTime, 0) / endpoints.length
  const overallSuccessRate = endpoints.reduce((sum, ep) => sum + ep.successRate, 0) / endpoints.length

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>
          <ApiOutlined /> API使用情况
        </Title>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value="today">今天</Option>
            <Option value="week">本周</Option>
            <Option value="month">本月</Option>
            <Option value="custom">自定义</Option>
          </Select>
          {timeRange === 'custom' && (
            <RangePicker />
          )}
          <Button onClick={loadApiUsage} loading={loading}>
            刷新
          </Button>
        </Space>
      </div>

      {/* 使用量警告 */}
      {(totalCalls / totalLimit) > 0.8 && (
        <Alert
          message="API使用量警告"
          description={`您的API调用量已达到 ${((totalCalls / totalLimit) * 100).toFixed(1)}%，请注意合理使用以避免超出限制。`}
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
          action={
            <Button size="small" type="link">
              查看升级方案
            </Button>
          }
        />
      )}

      {/* 概览统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="总调用次数"
              value={totalCalls}
              suffix={`/ ${totalLimit}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="使用率"
              value={(totalCalls / totalLimit) * 100}
              precision={1}
              suffix="%"
              valueStyle={{ 
                color: (totalCalls / totalLimit) > 0.8 ? '#cf1322' : '#3f8600' 
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="平均响应时间"
              value={avgResponseTime.toFixed(0)}
              suffix="ms"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small">
            <Statistic
              title="整体成功率"
              value={overallSuccessRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* API接口使用情况 */}
      <Card title="API接口使用情况" style={{ marginBottom: 24 }}>
        <Table
          dataSource={endpoints}
          columns={endpointColumns}
          rowKey="path"
          loading={loading}
          pagination={false}
          size="small"
        />
      </Card>

      {/* 详细调用记录 */}
      <Card
        title="调用记录"
        extra={
          <Space>
            <Tooltip title="显示最近的API调用记录">
              <InfoCircleOutlined />
            </Tooltip>
          </Space>
        }
      >
        <Table
          dataSource={usageRecords}
          columns={recordColumns}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 800 }}
          size="small"
        />
      </Card>
    </div>
  )
}

export default ApiUsage
