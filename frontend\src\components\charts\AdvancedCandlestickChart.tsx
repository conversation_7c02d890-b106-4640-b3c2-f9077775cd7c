import React, { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts'
import { Card, Select, Space, Button, Tooltip } from 'antd'
import { FullscreenOutlined, SettingOutlined, ReloadOutlined } from '@ant-design/icons'
import { STOCK_COLORS } from '@/config/colors'

const { Option } = Select

interface CandlestickData {
  timestamp: string
  open: number
  close: number
  high: number
  low: number
  volume: number
}

interface AdvancedCandlestickChartProps {
  data: CandlestickData[]
  title?: string
  height?: number
  showVolume?: boolean
  showMA?: boolean
  maLines?: number[]
  onPeriodChange?: (period: string) => void
  onChartTypeChange?: (type: string) => void
}

const AdvancedCandlestickChart: React.FC<AdvancedCandlestickChartProps> = ({
  data = [],
  title = 'K线图',
  height = 500,
  showVolume = true,
  showMA = true,
  maLines = [5, 10, 20, 30],
  onPeriodChange,
  onChartTypeChange
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)
  const [period, setPeriod] = useState('1D')
  const [chartType, setChartType] = useState('candlestick')

  // 计算移动平均线 - 基于ECharts官方示例
  const calculateMA = (values: number[][], dayCount: number) => {
    const result: (number | string)[] = []
    for (let i = 0; i < values.length; i++) {
      if (i < dayCount) {
        result.push('-')
        continue
      }
      let sum = 0
      for (let j = 0; j < dayCount; j++) {
        sum += values[i - j][1] // 使用收盘价 (close)
      }
      result.push(+(sum / dayCount).toFixed(2))
    }
    return result
  }

  // 数据处理函数 - 基于ECharts官方示例
  const splitData = (rawData: CandlestickData[]) => {
    const categoryData: string[] = []
    const values: number[][] = []

    rawData.forEach(item => {
      categoryData.push(item.timestamp)
      // ECharts K线数据格式: [open, close, low, high]
      values.push([item.open, item.close, item.low, item.high])
    })

    return { categoryData, values }
  }

  useEffect(() => {
    if (!chartRef.current || data.length === 0) return

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }

    const chart = chartInstance.current

    // 准备数据 - 使用ECharts标准格式
    const { categoryData, values } = splitData(data)
    const volumes = data.map(item => item.volume)

    // 计算移动平均线数据
    const maData: { [key: number]: (number | string)[] } = {}
    if (showMA) {
      maLines.forEach(period => {
        maData[period] = calculateMA(values, period)
      })
    }

    // 配置图表选项
    const option: echarts.EChartsOption = {
      animation: true,
      animationDuration: 300,
      animationEasing: 'cubicOut',
      backgroundColor: '#ffffff',
      title: {
        text: title,
        left: 'left',
        textStyle: {
          color: '#333',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      legend: {
        data: ['K线', ...(showMA ? maLines.map(ma => `MA${ma}`) : []), ...(showVolume ? ['成交量'] : [])],
        top: 30,
        textStyle: {
          color: '#666'
        }
      },
      grid: [
        {
          left: '10%',
          right: '8%',
          top: '15%',
          height: showVolume ? '50%' : '70%'
        },
        ...(showVolume ? [{
          left: '10%',
          right: '8%',
          top: '70%',
          height: '16%'
        }] : [])
      ],
      xAxis: [
        {
          type: 'category',
          data: categoryData,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax',
          axisPointer: {
            z: 100
          }
        },
        ...(showVolume ? [{
          type: 'category',
          gridIndex: 1,
          data: categoryData,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        }] : [])
      ],
      yAxis: [
        {
          scale: true,
          splitArea: {
            show: true
          }
        },
        ...(showVolume ? [{
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }] : [])
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: showVolume ? [0, 1] : [0],
          start: 50,
          end: 100
        },
        {
          show: true,
          xAxisIndex: showVolume ? [0, 1] : [0],
          type: 'slider',
          top: showVolume ? '85%' : '85%',
          start: 50,
          end: 100
        }
      ],
      series: [
        // K线图 - 基于ECharts官方示例
        {
          name: '日K',
          type: 'candlestick',
          data: values,
          itemStyle: {
            color: STOCK_COLORS.UP,
            color0: STOCK_COLORS.DOWN,
            borderColor: STOCK_COLORS.UP,
            borderColor0: STOCK_COLORS.DOWN
          },
          markPoint: {
            label: {
              formatter: function (param: any) {
                return param != null ? Math.round(param.value) + '' : ''
              }
            },
            data: [
              {
                name: '最高值',
                type: 'max',
                valueDim: 'highest',
                symbol: 'pin',
                symbolSize: 50,
                itemStyle: {
                  color: '#e74c3c'
                },
                label: {
                  color: '#fff',
                  fontWeight: 'bold'
                }
              },
              {
                name: '最低值',
                type: 'min',
                valueDim: 'lowest',
                symbol: 'pin',
                symbolSize: 50,
                itemStyle: {
                  color: '#27ae60'
                },
                label: {
                  color: '#fff',
                  fontWeight: 'bold'
                }
              },
              {
                name: '平均值',
                type: 'average',
                valueDim: 'close',
                symbol: 'diamond',
                symbolSize: 40,
                itemStyle: {
                  color: '#3498db'
                },
                label: {
                  color: '#fff',
                  fontWeight: 'bold'
                }
              }
            ],
            tooltip: {
              formatter: function (param: any) {
                return param.name + '<br>' + (param.data.coord || '')
              }
            }
          },
          markLine: {
            symbol: ['none', 'none'],
            data: [
              [
                {
                  name: '最低到最高连线',
                  type: 'min',
                  valueDim: 'lowest',
                  symbol: 'circle',
                  symbolSize: 10,
                  label: {
                    show: false
                  },
                  emphasis: {
                    label: {
                      show: false
                    }
                  }
                },
                {
                  type: 'max',
                  valueDim: 'highest',
                  symbol: 'circle',
                  symbolSize: 10,
                  label: {
                    show: false
                  },
                  emphasis: {
                    label: {
                      show: false
                    }
                  }
                }
              ],
              {
                name: '收盘价最低线',
                type: 'min',
                valueDim: 'close'
              },
              {
                name: '收盘价最高线',
                type: 'max',
                valueDim: 'close'
              }
            ]
          }
        },
        // 移动平均线 - 基于ECharts官方示例
        ...(showMA ? maLines.map((ma, index) => ({
          name: `MA${ma}`,
          type: 'line',
          data: maData[ma],
          smooth: true,
          lineStyle: {
            width: 1.5,
            opacity: 0.8,
            color: ['#1890ff', '#52c41a', '#faad14', '#f5222d'][index % 4]
          },
          showSymbol: false,
          hoverAnimation: false
        })) : []),
        // 成交量
        ...(showVolume ? [{
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: volumes,
          itemStyle: {
            color: function (params: any) {
              const dataIndex = params.dataIndex
              return data[dataIndex].close >= data[dataIndex].open ? STOCK_COLORS.UP : STOCK_COLORS.DOWN
            }
          }
        }] : [])
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#000'
        },
        formatter: function (params: any) {
          const data = params[0]
          const index = data.dataIndex
          const item = values[index]
          const volume = volumes[index]

          let result = `${categoryData[index]}<br/>`
          result += `开盘: ${item[0]}<br/>`
          result += `收盘: ${item[1]}<br/>`
          result += `最低: ${item[2]}<br/>`
          result += `最高: ${item[3]}<br/>`
          result += `成交量: ${volume.toLocaleString()}<br/>`

          // 添加MA数据
          if (showMA) {
            maLines.forEach(ma => {
              const maValue = maData[ma][index]
              if (maValue !== '-' && maValue !== null) {
                result += `MA${ma}: ${maValue}<br/>`
              }
            })
          }

          return result
        }
      }
    }

    chart.setOption(option, true)

    // 处理窗口大小变化
    const handleResize = () => {
      chart.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data, showVolume, showMA, maLines, title, height])

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
        chartInstance.current = null
      }
    }
  }, [])

  const handlePeriodChange = (value: string) => {
    setPeriod(value)
    onPeriodChange?.(value)
  }

  const handleChartTypeChange = (value: string) => {
    setChartType(value)
    onChartTypeChange?.(value)
  }

  return (
    <Card
      title={
        <Space>
          <span>{title}</span>
          <Select
            value={period}
            onChange={handlePeriodChange}
            size="small"
            style={{ width: 80 }}
          >
            <Option value="1m">分时</Option>
            <Option value="5m">5分钟</Option>
            <Option value="15m">15分钟</Option>
            <Option value="30m">30分钟</Option>
            <Option value="1h">1小时</Option>
            <Option value="1D">日线</Option>
            <Option value="1W">周线</Option>
            <Option value="1M">月线</Option>
          </Select>
          <Select
            value={chartType}
            onChange={handleChartTypeChange}
            size="small"
            style={{ width: 80 }}
          >
            <Option value="candlestick">K线图</Option>
            <Option value="line">分时图</Option>
            <Option value="area">面积图</Option>
          </Select>
        </Space>
      }
      extra={
        <Space>
          <Tooltip title="全屏显示">
            <Button icon={<FullscreenOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="图表设置">
            <Button icon={<SettingOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="刷新数据">
            <Button icon={<ReloadOutlined />} size="small" />
          </Tooltip>
        </Space>
      }
      bodyStyle={{ padding: '12px' }}
    >
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: `${height}px`,
          backgroundColor: '#ffffff'
        }}
      />
    </Card>
  )
}

export default AdvancedCandlestickChart
