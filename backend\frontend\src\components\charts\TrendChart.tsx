import React, { useMemo } from 'react'
import { Card, Typography, Space, Tag } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { CHART_CONFIG } from '@/config/dataConfig'

const { Text } = Typography

interface TrendChartProps {
  title: string
  data: Array<{
    time: string
    value: number
    change?: number
    changePercent?: number
  }>
  height?: number
  color?: string
  showChange?: boolean
}

const TrendChart: React.FC<TrendChartProps> = ({
  title,
  data,
  height = 200,
  color = '#1890ff',
  showChange = true
}) => {
  if (!data || data.length === 0) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">暂无数据</Text>
        </div>
      </Card>
    )
  }

  const latestData = data[data.length - 1]
  const previousData = data.length > 1 ? data[data.length - 2] : null

  const change = latestData.change || (previousData ? latestData.value - previousData.value : 0)
  const changePercent = latestData.changePercent || (previousData ? ((latestData.value - previousData.value) / previousData.value) * 100 : 0)

  const isPositive = change >= 0
  const changeColor = isPositive ? CHART_CONFIG.CANDLESTICK.UP_COLOR : CHART_CONFIG.CANDLESTICK.DOWN_COLOR

  // 准备ECharts配置
  const echartsOption = useMemo(() => {
    const xAxisData = data.map(item => item.time)
    const seriesData = data.map(item => item.value)

    return {
      animation: true,
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          const data = params[0]
          if (data) {
            return `
              <div>
                <div>${data.axisValue}</div>
                <div>数值: ${data.value.toFixed(2)}</div>
              </div>
            `
          }
          return ''
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666'
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      series: [
        {
          name: title,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          data: seriesData,
          lineStyle: {
            color: color,
            width: 2
          },
          itemStyle: {
            color: color
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: color + '40' // 透明度
                },
                {
                  offset: 1,
                  color: color + '10'
                }
              ]
            }
          }
        }
      ]
    }
  }, [data, color, title])

  return (
    <Card
      title={
        <Space>
          <span>{title}</span>
          {showChange && (
            <Tag color={isPositive ? 'red' : 'green'}>
              {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              {change.toFixed(2)} ({changePercent.toFixed(2)}%)
            </Tag>
          )}
        </Space>
      }
      size="small"
    >
      <ReactECharts
        option={echartsOption}
        style={{ height: height }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  )
}

export default TrendChart